package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "停用备件入参")
public class DisableGivenDetailReqDTO {

    @NotEmpty(message = "备件ID")
    @Schema(description = "备件ID")
    private String givenId;

    @Schema(description = "修改人")
    private String updatedBy;

    @Schema(description = "修改人姓名")
    private String updatedName;

}
