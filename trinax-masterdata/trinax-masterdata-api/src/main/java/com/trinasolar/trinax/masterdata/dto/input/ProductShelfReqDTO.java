package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 产品下架;
 * <AUTHOR>
 * created on 2023/10/17
 */
@Data
@Schema(description = " 产品下架信息")
public class ProductShelfReqDTO {
    @Schema(description = "UP、DOWN")
    String operation;
    @NotEmpty(message = "至少需有一个productId")
    List<String> productIds;
}
