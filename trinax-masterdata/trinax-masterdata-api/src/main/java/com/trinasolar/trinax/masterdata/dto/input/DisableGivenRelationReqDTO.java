package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
@Schema(description = "停用赠品关系入参")
public class DisableGivenRelationReqDTO {

    @NotEmpty(message = "赠品关系数据库主键")
    @Schema(description = "赠品关系数据库主键")
    private Long id;

    @Schema(description = "修改人")
    private String updatedBy;

    @Schema(description = "修改人姓名")
    private String updatedName;

}
