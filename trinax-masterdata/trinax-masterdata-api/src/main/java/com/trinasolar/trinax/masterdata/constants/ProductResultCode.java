package com.trinasolar.trinax.masterdata.constants;

import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.dto.output.ResultCodeArtist;

public class ProductResultCode extends ResultCode {
    public static final ResultCodeArtist DATA_VALIDATE_FAILURE = new ResultCodeArtist(false, "DATA_VALIDATE_FAILURE", "数据不一致");
    public static final ResultCodeArtist SHELF_STATUS = new ResultCodeArtist(false, "SHELF_STATUS", "操作必须是UP或DOWN");
    public static final ResultCodeArtist POWER_SHELF_STATUS = new ResultCodeArtist(false, "POWER_SHELF_STATUS", "功率上下架操作失败");
    public static final ResultCodeArtist IMAGE_VALIDATE_FAILURE = new ResultCodeArtist(false, "IMAGE_VALIDATE_FAILURE", "图片校验失败");
    public static final ResultCodeArtist BANNERIMAGE_VALIDATE_FAILURE = new ResultCodeArtist(false, "BANNERIMAGE_VALIDATE_FAILURE", "BANNERIMAGE校验失败");
    public static final ResultCodeArtist UP_NOT_VALID = new ResultCodeArtist(false, "UP_NOT_VALID", "不满足上架条件");
    public static final ResultCodeArtist OFF_NOT_VALID = new ResultCodeArtist(false, "OFF_NOT_VALID", "不满足下架条件");
    public static final ResultCodeArtist POWER_AT_LEAST_ONE = new ResultCodeArtist(false, "POWER_AT_LEAST_ONE", "至少有一个产品功率");

    @Override
    protected void setValueMap() {
        valueMap.put(SHELF_STATUS.getCode(), SHELF_STATUS);
        valueMap.put(POWER_SHELF_STATUS.getCode(), POWER_SHELF_STATUS);
        valueMap.put(IMAGE_VALIDATE_FAILURE.getCode(), IMAGE_VALIDATE_FAILURE);
        valueMap.put(DATA_VALIDATE_FAILURE.getCode(), DATA_VALIDATE_FAILURE);
        valueMap.put(BANNERIMAGE_VALIDATE_FAILURE.getCode(), BANNERIMAGE_VALIDATE_FAILURE);
    }

}
