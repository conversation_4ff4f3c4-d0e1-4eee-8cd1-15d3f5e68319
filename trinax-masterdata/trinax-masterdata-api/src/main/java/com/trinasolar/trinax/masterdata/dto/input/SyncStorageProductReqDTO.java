package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 同步产品族信息入参
 * <AUTHOR>
 * created on 2023/9/25
 */
@ApiModel(value = "同步工商储产品族信息入参")
public class SyncStorageProductReqDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotEmpty(message = "number不能为空")
    @Schema(description = "产品型号")
    private String number;

    @NotEmpty(message = "productName不能为空")
    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品系列")
    private String sProductSeries;

    @Schema(description = "产品分类")
    private String sProductType;

    @Schema(description = "应用领域")
    private String application;

    @Schema(description = "产品销售区域")
    private String salesArea;

    @Schema(description = "产品状态")
    private String productStatus;

    @NotEmpty(message = "briefIntroduction不能为空")
    @Schema(description = "简要说明")
    private String briefIntroduction;

    @Schema(description = "产品属性")
    private Map<String, String> props;

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getsProductSeries() {
        return sProductSeries;
    }

    public void setsProductSeries(String sProductSeries) {
        this.sProductSeries = sProductSeries;
    }

    public String getsProductType() {
        return sProductType;
    }

    public void setsProductType(String sProductType) {
        this.sProductType = sProductType;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getSalesArea() {
        return salesArea;
    }

    public void setSalesArea(String salesArea) {
        this.salesArea = salesArea;
    }

    public String getProductStatus() {
        return productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    public String getBriefIntroduction() {
        return briefIntroduction;
    }

    public void setBriefIntroduction(String briefIntroduction) {
        this.briefIntroduction = briefIntroduction;
    }

    public Map<String, String> getProps() {
        return props;
    }

    public void setProps(Map<String, String> props) {
        this.props = props;
    }
}
