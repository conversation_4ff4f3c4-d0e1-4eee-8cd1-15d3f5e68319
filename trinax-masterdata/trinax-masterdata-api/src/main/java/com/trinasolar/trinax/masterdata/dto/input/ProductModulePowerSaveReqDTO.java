package com.trinasolar.trinax.masterdata.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 产品功率相关信息;
 * <AUTHOR>
 * created on 2023/10/17
 */
@Data
@Schema(description = " 产品功率相关信息")
public class ProductModulePowerSaveReqDTO {
    private  Long id;
    private Boolean clicked;
    /** 产品ID */
    private String productId;
    private String outputPower;
    @Schema(description = "是否紧俏;1：紧俏 0：不紧俏")
    private String hardToGet;
    @Schema(description = "UP、DOWN")
    private String upStatus;
    @Schema(description = "上架时间")
    private LocalDateTime upTime;
    @Schema(description = "下降架时间")
    private LocalDateTime downTime;
    /** 更新时间 */
    private LocalDateTime updatedTime;

}
