package com.trinasolar.trinax.masterdata.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 启用状态
 */
@AllArgsConstructor
@Getter
public enum EnableEnum {

    TODO_ENABLE("TODO_ENABLE", "待启用"),
    ENABLE("ENABLE", "启用"),
    DISABLE("DISABLE", "停用");

    /** 编码 */
    private final String code;
    /** 描述 */
    private final String desc;

    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

    public static String getDesc(String code) {
        for (EnableEnum em : EnableEnum.values()) {
            if (em.equalsCode(code)) {
                return em.getDesc();
            }
        }
        return null;
    }

}
