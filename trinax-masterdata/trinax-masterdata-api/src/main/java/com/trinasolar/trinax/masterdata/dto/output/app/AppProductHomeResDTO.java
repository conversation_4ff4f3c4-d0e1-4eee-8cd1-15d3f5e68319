package com.trinasolar.trinax.masterdata.dto.output.app;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "APP-首页产品列表")
public class AppProductHomeResDTO {

    @Schema(description = "产品ID")
    private String productId;
    @Schema(description = "产品名称（产品族）")
    private String productName;
    @Schema(description = "产品类别，目前应该都是module")
    private String productCategory;
    @Schema(description = " 版本 - 提供给详情中查询文档列表")
    private String productVersion;
    @Schema(description = " 地区 - 提供给详情中查询文档列表")
    private String regions;
    @Schema(description = "参考价格")
    private BigDecimal referencePrice;
    @Schema(description = "小图")
    private String icon;
    @Schema(description = "首页图片")
    private String homeImage;

}
