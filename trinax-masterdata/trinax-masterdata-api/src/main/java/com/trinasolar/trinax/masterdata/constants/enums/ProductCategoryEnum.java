package com.trinasolar.trinax.masterdata.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类别
 */
@AllArgsConstructor
@Getter
public enum ProductCategoryEnum {

    MODULE("Module", "组件"),
    STORAGE("Storage", "工商储");

    /** 产品类别 */
    private final String code;
    /** 名称 */
    private final String desc;

    public boolean equalsCode(String code) {
        return this.code.equals(code);
    }

}