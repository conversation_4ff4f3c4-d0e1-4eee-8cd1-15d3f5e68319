package com.trinasolar.trinax.masterdata.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品-组件-扩展信息表;
 * <AUTHOR>
 * created on 2023/10/20
 */
@Data
@TableName(value = "product_module")
public class ProductModulePO {

    /** 自增主键 */
    @TableId(type = IdType.AUTO)
    private Long id;
    /** 产品ID */
    private String productId;
    /** 最大系统电压 */
    private String maxSystemVoltage;
    /** 产品类型（组件类型） */
    private String moduleType;
    /** 产品结构 */
    private String productStructure;
    /** 电池片数量 */
    private String cellQuantity;
    /** 主栅数 */
    private String busBar;
    /** 晶体管数量 */
    private String diodesNumber;
    /** 组件重量(Kg) */
    private BigDecimal moduleWeight;
    /** 组件长（mm）整数 */
    private BigDecimal moduleLength;
    /** 组件宽（mm）整数 */
    private BigDecimal moduleWidth;
    /** 背板 */
    private String backsheet;
    /** 组件颜色 */
    private String moduleColor;
    /** 后侧颜色 */
    private String rearSideColor;
    /** Frame */
    private String frame;
    /** 边框厚度（mm）整数 */
    private Integer frameThickness;
    /** 边框材料 */
    private String frameMaterial;
    /** 边框颜色 */
    private String frameColor;
    /** 线缆长度-横装（mm） */
    private String cableLengthLandscape;
    /** 线缆长度-竖装（mm） */
    private String cableLengthPortrait;
    /** 线缆长度-竖装正极（M） */
    private BigDecimal cableLengthPortraitPositive;
    /** 线缆长度-竖装负极（M） */
    private BigDecimal cableLengthPortraitCathode;
    /** 连接器 */
    private String plugConnector;
    /** 组件效率（%） */
    private BigDecimal componentEfficiency;
    /** 输出功率 */
    private String outputPower;
    /** 应用场景 */
    private String application;
    /** 电池片尺寸 */
    private String waferSize;
    /** 是否已删除;1：已删除 0：未删除 */
    private int isDeleted;
    /** 创建人 */
    private String createdBy;
    /** 创建人姓名 */
    private String createdName;
    /** 创建时间 */
    private LocalDateTime createdTime;
    /** 更新人 */
    private String updatedBy;
    /** 更新人姓名 */
    private String updatedName;
    /** 更新时间 */
    private LocalDateTime updatedTime;

}
