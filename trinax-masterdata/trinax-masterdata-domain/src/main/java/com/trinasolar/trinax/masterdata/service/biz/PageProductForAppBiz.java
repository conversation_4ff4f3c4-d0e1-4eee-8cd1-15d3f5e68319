package com.trinasolar.trinax.masterdata.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.masterdata.constants.ImageTypeEnum;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.constants.enums.DeletedEnum;
import com.trinasolar.trinax.masterdata.constants.enums.FavouriteEnum;
import com.trinasolar.trinax.masterdata.constants.enums.ProductCategoryEnum;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductAppPageReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import com.trinasolar.trinax.masterdata.dto.output.app.AppProductMainRecommendPageResDTO;
import com.trinasolar.trinax.masterdata.manager.ProductFavouriteManager;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductDetailMapper;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductImageMapper;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductModuleMapper;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductModulePowerMapper;
import com.trinasolar.trinax.masterdata.repository.po.ProductFavouritePO;
import com.trinasolar.trinax.masterdata.repository.po.ProductImagePO;
import com.trinasolar.trinax.masterdata.repository.po.ProductModulePO;
import com.trinasolar.trinax.masterdata.repository.po.ProductModulePowerPO;
import com.trinasolar.trinax.masterdata.utils.ProductBuUtil;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import dtt.cache.redisclient.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PageProductForAppBiz {

    private final RedisUtil redisUtil;
    private final ProductImageMapper productImageMapper;
    private final ProductDetailMapper productDetailMapper;
    private final ProductFavouriteManager productFavouriteManager;
    private final ProductModulePowerMapper productModulePowerMapper;
    private final ProductModuleMapper productModuleMapper;

    /**
     * 内部用户：同一产品族下若存在不同上架产品，则按照多个上架产品进行匹配
     * 外部用户：同一产品族下仅按照主推产品进行匹配
     * 功率、组件长、组件宽都依据上述逻辑
     */
    public PageResponse<AppProductMainRecommendPageResDTO> execute(PageRequest<ProductAppPageReqDTO> req) {
        ProductAppPageReqDTO query = ObjectUtil.defaultIfNull(req.getQuery(), new ProductAppPageReqDTO());

        // 有缓存直接返回
        String cacheKey = "ProList-" + SecureUtil.sha256(JacksonUtil.bean2Json(req));
        String cacheJson = redisUtil.get(cacheKey);
        if (CharSequenceUtil.isNotEmpty(cacheJson)) {
            PageResponse<AppProductMainRecommendPageResDTO> pageResponse = JacksonUtil.json2Bean(cacheJson, new TypeReference<PageResponse<AppProductMainRecommendPageResDTO>>() {});
            List<AppProductMainRecommendPageResDTO> products = pageResponse.getRecords();
            // 补全收藏标识
            fillFavorite(query, products);
            return pageResponse;
        }

        Page<AppProductMainRecommendPageResDTO> page = new Page<>(req.getIndex(), req.getSize());
        page.setOptimizeCountSql(true);
        IPage<AppProductMainRecommendPageResDTO> pageResult = productDetailMapper.findProdcutsByCondition(page, query);
        List<AppProductMainRecommendPageResDTO> products = pageResult.getRecords();

        // 构建返回对象
        PageResponse<AppProductMainRecommendPageResDTO> pageResponse = PageResponse.toResult(
                req.getIndex(), req.getSize(), (int) pageResult.getTotal(), products);
        if (CollectionUtils.isEmpty(products)) {
            redisUtil.set(cacheKey, JacksonUtil.bean2Json(pageResponse), 10);
            return pageResponse;
        }

        // 补全产品小图、组件重量（带单位）
        List<String> productNames = products.stream().map(AppProductMainRecommendPageResDTO::getProductName).toList();
        LambdaQueryWrapper<ProductImagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductImagePO::getProductName, productNames)
                .eq(ProductImagePO::getImageType, ImageTypeEnum.ICON.getCode())
                .eq(ProductImagePO::getIsDeleted, DeletedEnum.UN_DEL.getCode());
        Map<String, String> imageMap = productImageMapper.selectList(wrapper).stream().collect(Collectors.toMap(
                ProductImagePO::getProductName, ProductImagePO::getImageUrl, (oldVal, newVal)-> newVal)
        );
        products.forEach(product -> {
            product.setIcon(imageMap.get(product.getProductName()));
            if(ObjectUtil.equals(product.getProductCategory(), ProductCategoryEnum.MODULE.getCode())){
                product.setModuleWeightWithUnit(ProductBuUtil.calcModuleWeightWithUnit(product.getModuleWeight()));
            }
        });

        //PC端需求填充信息
        if(CollUtil.isNotEmpty(products)){
            fillData(products);
        }

        redisUtil.set(cacheKey, JacksonUtil.bean2Json(pageResponse), 10);

        // 补全收藏标识
        fillFavorite(query, products);
        return pageResponse;
    }

    /**
     * 填充信息
     * @param products
     */
    private void fillData(List<AppProductMainRecommendPageResDTO> products){

        List<String> productIds = products.stream().map(AppProductMainRecommendPageResDTO::getProductId).toList();
        LambdaUpdateWrapper<ProductModulePowerPO> newPowerWrapper = new LambdaUpdateWrapper<ProductModulePowerPO>()
                .in(ProductModulePowerPO::getProductId, productIds)
                .eq(ProductModulePowerPO::getUpStatus, UpStatusEnum.UP.getCode())
                .last(" order by cast(output_power as signed) asc");
        List<ProductModulePowerPO> outputPowerPOList = productModulePowerMapper.selectList(newPowerWrapper);

        List<ProductModulePowerDTO> outputPowerDTOList = BeanUtil.copyToList(outputPowerPOList, ProductModulePowerDTO.class);

        Map<String, List<ProductModulePowerDTO>> outputPowerDTOMap = outputPowerDTOList.stream()
                .collect(Collectors.groupingBy(ProductModulePowerDTO::getProductId,Collectors.toList()));

        LambdaUpdateWrapper<ProductModulePO> moduleWrapper = new LambdaUpdateWrapper<ProductModulePO>().
                in(ProductModulePO::getProductId, productIds);
        List<ProductModulePO> module = productModuleMapper.selectList(moduleWrapper);
        Map<String, List<ProductModulePO>> moduleMap = module.stream()
                .collect(Collectors.groupingBy(ProductModulePO::getProductId,Collectors.toList()));

        products.forEach(e->{
            if(CollUtil.isNotEmpty(outputPowerDTOMap.get(e.getProductId()))){
                e.setFamilyOutputPowers(outputPowerDTOMap.get(e.getProductId()));
            }
            if(CollUtil.isNotEmpty(moduleMap.get(e.getProductId()))){
                ProductModulePO modulePo = moduleMap.get(e.getProductId()).get(0);
                e.setCableLengthPortraitCathode(modulePo.getCableLengthPortraitCathode());
                e.setCableLengthPortraitPositive(modulePo.getCableLengthPortraitPositive());
                String powerRange = modulePo.getOutputPower();
                if (CharSequenceUtil.contains(powerRange, "~")) {
                    String[] powerPair = powerRange.split("~");
                    e.setMinOutputPower(Long.valueOf(powerPair[0]));
                    e.setMaxOutputPower(Long.valueOf(powerPair[1]));
                }
            }
        });
    }
    /**
     * 补全收藏标识
     */
    private void fillFavorite(ProductAppPageReqDTO query, List<AppProductMainRecommendPageResDTO> products) {
        if (query.getUserId() == null) {
            products.forEach(product -> {
                product.setFavourite(FavouriteEnum.NO.getCode());
            });
            return;
        }

        if (CharSequenceUtil.equals(query.getUserType(), SysUserTypeEnum.EXTERNAL.getType())) {
            List<String> productNames = products.stream().map(AppProductMainRecommendPageResDTO::getProductName).toList();
            List<ProductFavouritePO> favouriteList = productFavouriteManager.listFamilyByUserIdAndProductNames(query.getUserId(), productNames);
            Map<String, ProductFavouritePO> favouriteMap = favouriteList.stream().collect(
                    Collectors.toMap(ProductFavouritePO::getProductName, Function.identity(), (oldVal, newVal)-> newVal));
            products.forEach(product -> {
                ProductFavouritePO favourite = favouriteMap.get(product.getProductName());
                if (favourite != null) {
                    product.setFavourite(FavouriteEnum.YES.getCode());
                    product.setProductFavouriteId(favourite.getProductFavouriteId());
                } else {
                    product.setFavourite(FavouriteEnum.NO.getCode());
                }
            });
        } else {
            List<String> productIds = products.stream().map(AppProductMainRecommendPageResDTO::getProductId).toList();
            List<ProductFavouritePO> favouriteList = productFavouriteManager.listVersionByUserIdAndProductIds(query.getUserId(), productIds);
            Map<String, ProductFavouritePO> favouriteMap = favouriteList.stream().collect(
                    Collectors.toMap(ProductFavouritePO::getProductId, Function.identity(), (oldVal, newVal)-> newVal));
            products.forEach(product -> {
                ProductFavouritePO favourite = favouriteMap.get(product.getProductId());
                if (favourite != null) {
                    product.setFavourite(FavouriteEnum.YES.getCode());
                    product.setProductFavouriteId(favourite.getProductFavouriteId());
                } else {
                    product.setFavourite(FavouriteEnum.NO.getCode());
                }
            });
        }
    }

}
