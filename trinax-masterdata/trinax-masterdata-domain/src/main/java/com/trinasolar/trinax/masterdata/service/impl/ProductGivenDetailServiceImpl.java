package com.trinasolar.trinax.masterdata.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.masterdata.constants.ProductResultCode;
import com.trinasolar.trinax.masterdata.constants.enums.DeletedEnum;
import com.trinasolar.trinax.masterdata.constants.enums.EnableEnum;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailPageResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductGivenDetailResDTO;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductGivenDetailMapperService;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductGivenExtendMapperService;
import com.trinasolar.trinax.masterdata.repository.po.ProductGivenDetailPO;
import com.trinasolar.trinax.masterdata.repository.po.ProductGivenExtendPO;
import com.trinasolar.trinax.masterdata.service.ProductGivenDetailService;
import dtt.segment.id.generator.service.IdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductGivenDetailServiceImpl implements ProductGivenDetailService {

    private final IdService idService;
    private final ProductGivenDetailMapperService productGivenDetailMapperService;
    private final ProductGivenExtendMapperService productGivenExtendMapperService;

    @Override
    public String add(ProductGivenDetailAddReqDTO req) {
        String givenId = genGivenId();
        LocalDateTime currentTime = LocalDateTime.now();

        ProductGivenDetailPO detail = BeanUtil.copyProperties(req, ProductGivenDetailPO.class);
        detail.setGivenId(givenId);
        detail.setGivenStatus(EnableEnum.TODO_ENABLE.getCode());
        detail.setCreatedTime(currentTime);
        detail.setUpdatedBy(detail.getCreatedBy());
        detail.setUpdatedName(detail.getUpdatedName());
        detail.setUpdatedTime(currentTime);
        detail.setIsDeleted(DeletedEnum.UN_DEL.getCode());
        productGivenDetailMapperService.save(detail);

        productGivenExtendMapperService.saveBatch(givenId, req.getSfdcNames(), detail.getCreatedBy(), detail.getCreatedName(), currentTime);
        return givenId;
    }

    @Override
    public void delete(String givenId) {
        // 非待启用状态，无法删除
        ProductGivenDetailPO detail = productGivenDetailMapperService.lambdaQuery()
                .eq(ProductGivenDetailPO::getGivenId, givenId).one();
        if (!EnableEnum.TODO_ENABLE.equalsCode(detail.getGivenStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), "非待启用状态，无法删除");
        }

        LambdaQueryWrapper<ProductGivenDetailPO> wrapper = Wrappers.lambdaQuery(ProductGivenDetailPO.class);
        wrapper.eq(ProductGivenDetailPO::getGivenId, givenId);
        productGivenDetailMapperService.remove(wrapper);

        LambdaQueryWrapper<ProductGivenExtendPO> extendWrapper = Wrappers.lambdaQuery(ProductGivenExtendPO.class);
        extendWrapper.eq(ProductGivenExtendPO::getGivenId, givenId);
        productGivenExtendMapperService.remove(extendWrapper);
    }

    @Override
    public PageResponse<ProductGivenDetailPageResDTO> page(PageRequest<ProductGivenDetailPageReqDTO> req) {
        Page<ProductGivenDetailPO> page = new Page<>(req.getIndex(), req.getSize());
        LambdaQueryWrapper<ProductGivenDetailPO> wrapper = Wrappers.lambdaQuery(ProductGivenDetailPO.class);
        if (req.getQuery() != null) {
            wrapper.like(CharSequenceUtil.isNotEmpty(req.getQuery().getName()), ProductGivenDetailPO::getName, req.getQuery().getName());
            wrapper.eq(CharSequenceUtil.isNotEmpty(req.getQuery().getGivenStatus()), ProductGivenDetailPO::getGivenStatus, req.getQuery().getGivenStatus());
        }
        IPage<ProductGivenDetailPO> iPage = productGivenDetailMapperService.page(page, wrapper);

        List<ProductGivenDetailPageResDTO> records = BeanUtil.copyToList(iPage.getRecords(), ProductGivenDetailPageResDTO.class);
        fill(records); //填充SFDC NAME以及GivenStatusName
        return PageResponse.toResult(req.getIndex(), req.getSize(), (int)iPage.getTotal(), records);
    }

    @Override
    public ProductGivenDetailResDTO detail(String givenId) {
        ProductGivenDetailPO detail = productGivenDetailMapperService.lambdaQuery()
                .eq(ProductGivenDetailPO::getGivenId, givenId).one();
        return BeanUtil.toBean(detail, ProductGivenDetailResDTO.class);
    }

    @Override
    @Transactional
    public void edit(ProductGivenDetailEditReqDTO req) {
        // 非待启用状态，无法修改
        ProductGivenDetailPO detail = productGivenDetailMapperService.lambdaQuery()
                .eq(ProductGivenDetailPO::getGivenId, req.getGivenId()).one();
        if (!EnableEnum.TODO_ENABLE.equalsCode(detail.getGivenStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), "非待启用状态，无法修改");
        }

        LocalDateTime currentTime = LocalDateTime.now();
        productGivenDetailMapperService.lambdaUpdate()
                .set(ProductGivenDetailPO::getDataSource, req.getDataSource())
                .set(ProductGivenDetailPO::getName, req.getName())
                .set(ProductGivenDetailPO::getDescription, req.getDescription())
                .set(ProductGivenDetailPO::getUpdatedTime, currentTime)
                .set(ProductGivenDetailPO::getUpdatedBy, req.getUpdatedBy())
                .set(ProductGivenDetailPO::getUpdatedName, req.getUpdatedName())
                .eq(ProductGivenDetailPO::getGivenId, req.getGivenId())
                .update();

        // 删除完后全部重新插入
        LambdaQueryWrapper<ProductGivenExtendPO> queryWrapper = Wrappers.lambdaQuery(ProductGivenExtendPO.class);
        queryWrapper.eq(ProductGivenExtendPO::getGivenId, req.getGivenId());
        productGivenExtendMapperService.remove(queryWrapper);
        productGivenExtendMapperService.saveBatch(req.getGivenId(), req.getSfdcNames(), req.getUpdatedBy(), req.getUpdatedName(), currentTime);
    }

    @Override
    public void enable(EnableGivenDetailReqDTO req) {
        ProductGivenDetailPO detail = productGivenDetailMapperService.lambdaQuery()
                .eq(ProductGivenDetailPO::getGivenId, req.getGivenId()).one();
        if (!CharSequenceUtil.equalsAny(detail.getGivenStatus(), EnableEnum.TODO_ENABLE.getCode(), EnableEnum.DISABLE.getCode())) {
            throw new BizException(ResultCode.FAIL.getCode(), "备件当前状态不能启用");
        }

        LocalDateTime currentTime = LocalDateTime.now();
        productGivenDetailMapperService.updateGivenStatus(req.getGivenId(),  EnableEnum.ENABLE.getCode(),
                req.getUpdatedBy(), req.getUpdatedName(), currentTime);
    }

    @Override
    public void disable(DisableGivenDetailReqDTO req) {
        ProductGivenDetailPO detail = productGivenDetailMapperService.lambdaQuery()
                .eq(ProductGivenDetailPO::getGivenId, req.getGivenId()).one();
        if (!EnableEnum.ENABLE.equalsCode(detail.getGivenStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), "备件当前状态不能停用");
        }

        LocalDateTime currentTime = LocalDateTime.now();
        productGivenDetailMapperService.updateGivenStatus(req.getGivenId(),  EnableEnum.DISABLE.getCode(),
                req.getUpdatedBy(), req.getUpdatedName(), currentTime);
    }

    private void fill(List<ProductGivenDetailPageResDTO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        List<String> givenIds = records.stream().map(ProductGivenDetailPageResDTO::getGivenId).toList();
        List<ProductGivenExtendPO> givenExtends = productGivenExtendMapperService.lambdaQuery().in(ProductGivenExtendPO::getGivenId, givenIds).list();
        Map<String, String> sfdcNameMap = new HashMap<>();
        for (ProductGivenExtendPO givenExtend:givenExtends) {
            String givenId = givenExtend.getGivenId();
            String sfdcName = sfdcNameMap.get(givenId);
            if (sfdcName == null) {
                sfdcNameMap.put(givenId, givenExtend.getSfdcName());
            } else {
                sfdcNameMap.put(givenId, sfdcName + "," + givenExtend.getSfdcName());
            }
        }
        for (ProductGivenDetailPageResDTO detail:records) {
            String givenId = detail.getGivenId();
            detail.setSfdcName(sfdcNameMap.get(givenId));
            detail.setGivenStatusName(EnableEnum.getDesc(detail.getGivenStatus()));
        }
    }

    private String genGivenId() {
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long id = idService.nextId("given-id-" + date, "  given-id");
        return date + String.format("%06d", id);
    }

}
