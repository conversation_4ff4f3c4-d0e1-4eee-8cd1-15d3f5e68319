package com.trinasolar.trinax.masterdata.service;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.masterdata.dto.input.favourite.AppProductFavouriteQueryDTO;
import com.trinasolar.trinax.masterdata.dto.input.favourite.ProductFavouriteAddReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.favourite.StatisticFavouriteReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.favourite.AppProductFavouriteRespDTO;
import com.trinasolar.trinax.masterdata.dto.output.favourite.StatisticFavouriteRespDTO;

import java.util.List;

/**
 * 产品收藏 服务类
 *
 * <AUTHOR> Yang
 */
public interface ProductFavouriteService {

    String add(ProductFavouriteAddReqDTO reqDTO);

    PageResponse<AppProductFavouriteRespDTO> appPageProductFavourite(PageRequest<AppProductFavouriteQueryDTO> pageReqDTO);

    List<StatisticFavouriteRespDTO> statisticFavourite(StatisticFavouriteReqDTO reqDTO);
}
