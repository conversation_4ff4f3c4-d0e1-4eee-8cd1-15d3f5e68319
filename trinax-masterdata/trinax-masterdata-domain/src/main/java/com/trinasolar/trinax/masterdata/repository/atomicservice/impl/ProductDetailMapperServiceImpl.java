package com.trinasolar.trinax.masterdata.repository.atomicservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductDetailMapperService;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductDetailMapper;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductDetailMapperServiceImpl extends ServiceImpl<ProductDetailMapper, ProductDetailPO> implements ProductDetailMapperService {

    @Override
    public ProductDetailPO getByProductId(String productId) {
        return this.lambdaQuery().eq(ProductDetailPO::getProductId, productId).one();
    }

    @Override
    public List<ProductDetailPO> listByProductIds(List<String> productIds) {
        return this.lambdaQuery().in(ProductDetailPO::getProductId, productIds).list();
    }

    @Override
    public List<ProductDetailPO> listUpByProductName(String productName) {
        return this.lambdaQuery().eq(ProductDetailPO::getProductName, productName)
                .in(ProductDetailPO::getUpStatus, CollUtil.newArrayList(UpStatusEnum.UP.getCode(), UpStatusEnum.PART_UP.getCode()))
                .list();
    }

    @Override
    public List<ProductDetailPO> listByProductName(String productName) {
        return this.lambdaQuery().eq(ProductDetailPO::getProductName, productName).list();
    }

    @Override
    public List<String> queryAllRegion() {
        return this.baseMapper.queryAllRegion();
    }

}
