package com.trinasolar.trinax.masterdata.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.masterdata.constants.enums.DeletedEnum;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductGivenExtendMapperService;
import com.trinasolar.trinax.masterdata.repository.mapper.ProductGivenExtendMapper;
import com.trinasolar.trinax.masterdata.repository.po.ProductGivenExtendPO;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 赠品扩展表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Service
public class ProductGivenExtendMapperServiceImpl extends ServiceImpl<ProductGivenExtendMapper, ProductGivenExtendPO> implements ProductGivenExtendMapperService {

    @Override
    public void saveBatch(String givenId, List<String> sfdcNames, String createdBy, String createdName, LocalDateTime currentTime) {
        List<ProductGivenExtendPO> givenExtends = new ArrayList<>();
        for (String sfdcName:sfdcNames) {
            ProductGivenExtendPO givenExtend = new ProductGivenExtendPO();
            givenExtend.setGivenId(givenId);
            givenExtend.setSfdcName(sfdcName);
            givenExtend.setCreatedBy(createdBy);
            givenExtend.setCreatedName(createdName);
            givenExtend.setCreatedTime(currentTime);
            givenExtend.setUpdatedBy(createdBy);
            givenExtend.setUpdatedName(createdName);
            givenExtend.setUpdatedTime(currentTime);
            givenExtend.setIsDeleted(DeletedEnum.UN_DEL.getCode());
            givenExtends.add(givenExtend);
        }
        this.saveBatch(givenExtends);
    }

}
