package com.trinasolar.trinax.masterdata.repository.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductGivenInfoDTO {

    /**
     * 赠品关系主键ID
     */
    private Long relationId;

    /**
     * 赠品类型（组件、备件）
     */
    private String givenType;

    /**
     * 意向产品ID
     */
    private String intentProductId;

    /**
     * 赠品ID（实际可为产品ID、赠品ID）
     */
    private String givenProductId;

    /**
     * 赠品名称（
     */
    private String givenProductName;

    /**
     * 每满多少
     */
    private BigDecimal perBuyQuantity;

    /**
     * 每满多少-单位：瓦 W、片 P、个 N
     */
    private String perBuyUnit;

    /**
     * 赠送数量
     */
    private Integer givenQuantity;

    /**
     * 赠送单位类型（片、瓦、个）
     */
    private String givenUnit;

    /**
     * 是否已删除;1：已删除 0：未删除
     */
    private Integer isDeleted;

}
