package com.trinasolar.trinax.masterdata.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;

import java.util.List;

/**
 * <AUTHOR>
 * created on 2023/9/25
 */
public interface ProductDetailMapperService extends IService<ProductDetailPO> {

    ProductDetailPO getByProductId(String productId);

    List<ProductDetailPO> listByProductIds(List<String> productIds);

    /**
     * 查询产品族下上架或部分上架状态的产品
     * @return
     */
    List<ProductDetailPO> listUpByProductName(String productName);

    List<ProductDetailPO> listByProductName(String productName);

    List<String> queryAllRegion();

}
