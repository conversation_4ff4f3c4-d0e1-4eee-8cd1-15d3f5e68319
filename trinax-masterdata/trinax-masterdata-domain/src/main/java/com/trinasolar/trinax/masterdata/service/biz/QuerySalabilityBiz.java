package com.trinasolar.trinax.masterdata.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.trinasolar.trinax.masterdata.constants.ImageTypeEnum;
import com.trinasolar.trinax.masterdata.constants.UpStatusEnum;
import com.trinasolar.trinax.masterdata.constants.enums.SalabilityEnum;
import com.trinasolar.trinax.masterdata.dto.input.ProductSalabilityQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductImageItemResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductSalabilityResDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductSalabilityResDTO.ProductSalabilityItemDTO;
import com.trinasolar.trinax.masterdata.manager.ProductImageManager;
import com.trinasolar.trinax.masterdata.manager.ProductManager;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductDetailMapperService;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductModulePowerMapperService;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;
import com.trinasolar.trinax.masterdata.repository.po.ProductFamilyPO;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class QuerySalabilityBiz {

    private final ProductDetailMapperService productDetailMapperService;
    private final ProductManager productManager;
    private final ProductImageManager productImageManager;
    private final ProductModulePowerMapperService productModulePowerMapperService;

    public ProductSalabilityResDTO execute(ProductSalabilityQueryReqDTO req) {
        List<String> productIds = req.getProductIds();
        if (CollectionUtils.isEmpty(productIds)) {
            return buildEmpty(req.getUserType());
        }

        List<ProductDetailPO> productDetails = productDetailMapperService.lambdaQuery()
                .in(ProductDetailPO::getProductId, productIds).list();
        if (CollectionUtils.isEmpty(productDetails)) {
            return buildEmpty(req.getUserType());
        }

        // 查询参考价格
        Map<String, ProductFamilyPO> familyMap = productManager.mapFamily(productDetails);

        // 查询产品小图
        List<ProductImageItemResDTO> images = productImageManager.getImages(productIds, ImageTypeEnum.ICON.getCode());
        Map<String, ProductImageItemResDTO> imageMap = images.stream().collect(
                Collectors.toMap(ProductImageItemResDTO::getProductId, Function.identity(), (oldVal, newVal)-> newVal));

        // 查询所有上架状态的功率
        Map<String, List<String>> outputPowerMap = productModulePowerMapperService.listUpStatus(productIds);

        // 补全参考价格、产品小图、产品是否可售标识、功率是否可售、上架状态的功率
        List<ProductSalabilityItemDTO> products = BeanUtil.copyToList(productDetails, ProductSalabilityItemDTO.class);
        for (ProductSalabilityItemDTO product : products) {
            String productId = product.getProductId();

            ProductImageItemResDTO image = imageMap.get(productId);
            if (image != null) {
                product.setImageUrl(image.getImageUrl());
            }

            ProductFamilyPO family = familyMap.get(product.getProductName());
            if (family != null) {
                product.setReferencePrice(family.getReferencePrice());
            }
            product.setProductSalability(calcSalability(req.getUserType(), product, family));
            product.setOutPowers(outputPowerMap.getOrDefault(productId, CollUtil.newArrayList()));
        }

        ProductSalabilityResDTO result = buildEmpty(req.getUserType());
        result.setProducts(products);
        return result;
    }

    private ProductSalabilityResDTO buildEmpty(String userType) {
        ProductSalabilityResDTO salabilityRes = new ProductSalabilityResDTO();
        salabilityRes.setUserType(userType);
        salabilityRes.setProducts(new ArrayList<>());
        return salabilityRes;
    }

    /**
     * 计算产品是否可售
     * 1.产品是否处于上架状态
     * 2.外部用户自主下单，需判断是否是主推版本
     * 3.此方法并不判断产品功率是否在当前的上架功率范围内
     */
    private Integer calcSalability(String userType, ProductSalabilityItemDTO product, ProductFamilyPO family) {
        if (family == null) {
            return SalabilityEnum.NO.getCode();
        }

        if (!CharSequenceUtil.equalsAny(product.getUpStatus(), UpStatusEnum.UP.getCode(), UpStatusEnum.PART_UP.getCode())) {
            return SalabilityEnum.NO.getCode();
        }

        // 外部用户自己只能下单主推版本
        if (CharSequenceUtil.equals(userType, SysUserTypeEnum.EXTERNAL.getType())) {
            if (!CharSequenceUtil.equals(product.getProductId(), family.getMainRecommendVersion())) {
                return SalabilityEnum.NO.getCode();
            }
        }
        return SalabilityEnum.YES.getCode();
    }

}
