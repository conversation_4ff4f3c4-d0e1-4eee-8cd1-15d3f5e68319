package com.trinasolar.trinax.masterdata.service.biz.banner;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.masterdata.constants.BannerImageEnum;
import com.trinasolar.trinax.masterdata.dto.input.BannerImageQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.BannerImageResDTO;
import com.trinasolar.trinax.masterdata.repository.atomicservice.BannerImageMapperService;
import com.trinasolar.trinax.masterdata.repository.mapper.BannerImageMapper;
import com.trinasolar.trinax.masterdata.repository.po.BannerImagePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class BannerImageQueryBiz {
    @Autowired
    BannerImageMapperService bannerImageMapperService;
    @Autowired
    BannerImageMapper bannerImageMapper;

    public List<BannerImageResDTO> list(BannerImageQueryReqDTO reqDTO) {
        List<BannerImageResDTO> result = new ArrayList<>();
        LambdaQueryWrapper<BannerImagePO> query = processQueryParameter(reqDTO);

        List<BannerImagePO> list = bannerImageMapperService.list(query);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        result = translateConstant(list);
        return result;
    }

    /**
     * 枚举翻译
     */
    private List<BannerImageResDTO> translateConstant(List<BannerImagePO> list) {

        List<BannerImageResDTO> result = new ArrayList<>();

        Map<String, BannerImageEnum> typeMap = BannerImageEnum.getBannerImageMap().get(BannerImageEnum.HOME_HEADER.getKey());
        Map<String, BannerImageEnum> bizTypeMap = BannerImageEnum.getBannerImageMap().get(BannerImageEnum.MODULE.getKey());
        Map<String, BannerImageEnum> linkTypeMap = BannerImageEnum.getBannerImageMap().get(BannerImageEnum.LINK_TYPE_IN.getKey());
        list.forEach(dto -> {
            BannerImageResDTO target = new BannerImageResDTO();
            BeanUtils.copyProperties(dto, target);
            String bizTypeName = "";
            if (StringUtils.isNotBlank(target.getBizType())) {
                BannerImageEnum imageEnum = bizTypeMap.get(target.getBizType());
                if (ObjectUtils.isNotEmpty(imageEnum)) {
                    bizTypeName = imageEnum.getDesc();
                }
            }
            String typeName = "";
            if (StringUtils.isNotBlank(target.getType())) {
                BannerImageEnum imageEnum = typeMap.get(target.getType());
                if (ObjectUtils.isNotEmpty(imageEnum)) {
                    typeName = imageEnum.getDesc();
                }
            }
            String linkTypeName = "";
            if (StringUtils.isNotBlank(target.getLinkType())) {
                BannerImageEnum imageEnum = linkTypeMap.get(target.getLinkType());
                if (ObjectUtils.isNotEmpty(imageEnum)) {
                    linkTypeName = imageEnum.getDesc();
                }
            }
            target.setBizTypeName(bizTypeName);
            target.setTypeName(typeName);
            target.setLinkTypeName(linkTypeName);
            result.add(target);
        });
        return result;
    }


    /**
     * 查询参数初始化
     */
    private LambdaQueryWrapper<BannerImagePO> processQueryParameter(BannerImageQueryReqDTO reqDTO) {
        LambdaQueryWrapper<BannerImagePO> query = new LambdaQueryWrapper<>();
        query.eq(BannerImagePO::getIsDeleted, 0);
        if (StringUtils.isNotBlank(reqDTO.getImageUrl())) {
            query.like(BannerImagePO::getImageUrl, reqDTO.getImageUrl());
        }
        if (Objects.nonNull(reqDTO.getSeq())) {
            query.eq(BannerImagePO::getSeq, reqDTO.getSeq());
        }
        if (StringUtils.isNotBlank(reqDTO.getUrl())) {
            query.like(BannerImagePO::getUrl, reqDTO.getUrl());
        }
        if (StringUtils.isNotBlank(reqDTO.getTitle())) {
            query.like(BannerImagePO::getTitle, reqDTO.getTitle());
        }
        if (Objects.nonNull(reqDTO.getLinkType())) {
            query.eq(BannerImagePO::getLinkType, reqDTO.getLinkType());
        }
        if (StringUtils.isNotBlank(reqDTO.getType())) {
            query.eq(BannerImagePO::getType, reqDTO.getType());
        } else {
            //默认查询首页顶部
            query.eq(BannerImagePO::getType, BannerImageEnum.HOME_HEADER.getValue());
        }
        if (StringUtils.isNotBlank(reqDTO.getBizType())) {
            query.eq(BannerImagePO::getBizType, reqDTO.getBizType());
        }
        query.orderByAsc(BannerImagePO::getSeq);

        return query;
    }

    /**
     * 分页查询
     */
    public PageResponse<BannerImageResDTO> listPage(PageRequest<BannerImageQueryReqDTO> reqDTO) {
        List<BannerImageResDTO> result = new ArrayList<>();

        LambdaQueryWrapper<BannerImagePO> lambdaQuery = processQueryParameter(reqDTO.getQuery());
        Page<BannerImagePO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());

        IPage<BannerImagePO> itemPage = bannerImageMapper.selectPage(page, lambdaQuery);
        if (ObjectUtils.isNotEmpty(itemPage.getRecords())) {
            result = translateConstant(itemPage.getRecords());
        }
        return PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) itemPage.getTotal(),
                result);
    }
}
