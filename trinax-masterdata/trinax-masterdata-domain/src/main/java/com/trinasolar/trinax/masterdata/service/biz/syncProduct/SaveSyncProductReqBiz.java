package com.trinasolar.trinax.masterdata.service.biz.syncProduct;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.masterdata.constants.MasterdataTopic;
import com.trinasolar.trinax.masterdata.constants.enums.ProductCategoryEnum;
import com.trinasolar.trinax.masterdata.constants.enums.ProductEms.SyncStatusEm;
import com.trinasolar.trinax.masterdata.dto.input.SyncProductReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.SyncStorageProductReqDTO;
import com.trinasolar.trinax.masterdata.dto.mq.SyncProductMqDTO;
import com.trinasolar.trinax.masterdata.repository.atomicservice.ProductDetailPbiMapperService;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPbiPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import dtt.segment.id.generator.service.IdService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 同步产品族信息
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SaveSyncProductReqBiz {

    private final ProductDetailPbiMapperService productDetailPbiMapperService;
    private final MqManager mqManager;
    private final IdService idService;

    @Transactional
    public void execute(SyncProductReqDTO req) {
        log.info("保存同步产品请求开始，productId={}，req={}", req.getProductId(), JacksonUtil.bean2Json(req));
        String syncOrderNo = genSyncOrderNo();
        LocalDateTime now = LocalDateTime.now();
        ProductDetailPbiPO productDetailPbi = new ProductDetailPbiPO();
        productDetailPbi.setSyncOrderNo(syncOrderNo);
        productDetailPbi.setProductId(req.getProductId());
        productDetailPbi.setProductCategory(ProductCategoryEnum.MODULE.getCode());
        productDetailPbi.setRawData(JacksonUtil.bean2Json(req));
        productDetailPbi.setSyncStatus(SyncStatusEm.INIT.getCode());
        productDetailPbi.setCreatedTime(now);
        productDetailPbi.setUpdatedTime(now);
        productDetailPbiMapperService.save(productDetailPbi);

        SyncProductMqDTO mqBody = new SyncProductMqDTO();
        mqBody.setSyncOrderNo(syncOrderNo);
        mqManager.sendTopic(MasterdataTopic.SYNC_PRODUCT, JacksonUtil.bean2Json(mqBody));
        log.info("保存同步产品请求完成，syncOrderNo={}", syncOrderNo);
    }

    private String genSyncOrderNo() {
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long id = idService.nextId("sync-order-no-" + date, "sync-order-no");
        return date + String.format("%06d", id);
    }

    public void execute(SyncStorageProductReqDTO req) {
        log.info("保存同步工商储产品请求开始，productId={}，req={}", req.getProductName(), JacksonUtil.bean2Json(req));
        String syncOrderNo = genSyncOrderNo();
        LocalDateTime now = LocalDateTime.now();
        ProductDetailPbiPO productDetailPbi = new ProductDetailPbiPO();
        productDetailPbi.setSyncOrderNo(syncOrderNo);
        productDetailPbi.setProductId(req.getProductName());
        productDetailPbi.setProductCategory(ProductCategoryEnum.STORAGE.getCode());
        productDetailPbi.setRawData(JacksonUtil.bean2Json(req));
        productDetailPbi.setSyncStatus(SyncStatusEm.INIT.getCode());
        productDetailPbi.setCreatedTime(now);
        productDetailPbi.setUpdatedTime(now);
        productDetailPbiMapperService.save(productDetailPbi);

        SyncProductMqDTO mqBody = new SyncProductMqDTO();
        mqBody.setSyncOrderNo(syncOrderNo);
        mqManager.sendTopic(MasterdataTopic.SYNC_PRODUCT, JacksonUtil.bean2Json(mqBody));
        log.info("保存同步产品请求完成，syncOrderNo={}", syncOrderNo);
    }
}