package com.trinasolar.trinax.masterdata.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.masterdata.dto.input.*;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductAppPageReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.app.ProductDocReqDTO;
import com.trinasolar.trinax.masterdata.dto.mq.SyncProductCompletedMqDTO;
import com.trinasolar.trinax.masterdata.dto.output.*;
import com.trinasolar.trinax.masterdata.dto.output.app.*;
import com.trinasolar.trinax.masterdata.repository.po.ProductDetailPO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ProductDetailService extends IService<ProductDetailPO> {

    void saveSyncReq(SyncProductReqDTO req);

    void saveSyncReq(SyncStorageProductReqDTO req);

    void syncProduct(String syncOrderNo);

    PageResponse<ProductPageForAdminResDTO> pageProductForAdmin(PageRequest<ProductPageForAdminReqDTO> req);

    List<ProductPageForAdminResDTO> listProductForAdmin(@RequestBody ProductPageForAdminReqDTO req);

    PageResponse<AppProductMainRecommendPageResDTO> pageProductForApp(PageRequest<ProductAppPageReqDTO> req);

    Result<Map<String, List<ProductModulePowerDTO>>> getFamilyOutputPower(List<String> productIdList);

    AppProductDetailResDTO getProductForApp(String productId, String productCategory, String userId, String userType);

    ProductAppHomeRespDTO getProductAppHome();

    ProductAppRespDTO getProductAdmin(String productId,String productCategory);

    List<ProductDocAppRespDTO> getProductDocs(ProductDocReqDTO reqDTO);

    Result<String> saveProduct(ProductAdminSaveReqDTO req);

    List<ProductDetailResDTO> productDetailByProductId(List<String> productIdList);

    /**
     * 查询产品是否可售
     */
    ProductSalabilityResDTO querySalability(ProductSalabilityQueryReqDTO req);

    /**
     * 发送提醒（产品待上架）
     */
    void sendMessageForProductTobeUp(SyncProductCompletedMqDTO req);

    /**
     * 产品批量上架
     */
    List<String> upShelfProductBatch(ProductShelfReqDTO req);

    /**
     * 产品批量下架
     */
    List<String> offShelfProductBatch(ProductShelfReqDTO req);

    /**
     * 查询产品里的所有regions
     */
    List<String> queryAllRegion();

    PageResponse<ProductStoragePageForAdminResDTO> pageStorageProductForAdmin(PageRequest<ProductPageForAdminReqDTO> req);

    List<ProductStoragePageForAdminResDTO> listStorageProductForAdmin(ProductPageForAdminReqDTO req);

    Result<String> saveProductStorage(ProductSaveReqStorageDTO req);

    ProductDetailStorageResDTO detailByStorage(String productId);
    /**
     * 产品批量上架
     */
    List<String> upShelfProductStorage(ProductShelfReqDTO req);
    /**
     * 产品批量下架
     */
    List<String> offShelfProductStorage(ProductShelfReqDTO req);

    AppProductStorageDetailResDTO getProductStorageDetail(String productId,String userId, String userType);

    List<ProductDocAppRespDTO> getStorageProductDocs(String productFamily);
}
