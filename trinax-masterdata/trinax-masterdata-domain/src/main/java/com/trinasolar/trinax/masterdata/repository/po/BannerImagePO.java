package com.trinasolar.trinax.masterdata.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 首页图片表(BannerImage)实体类
 *
 * <AUTHOR>
 * @since 2023/11/6 17:51
 */
@Data
@TableName(value = "banner_image")
public class BannerImagePO {

    /**
     * 自增主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 图片地址
     */
    private String imageUrl;
    /**
     * 排序号
     */
    private Integer seq;
    /**
     * 跳转路径
     */
    private String url;
    /**
     * 标题
     */
    private String title;
    /**
     * 首页顶部-HOME_HEADER
     */
    @Schema(description = "类型")
    private String type;

    @Schema(description = "业务数据ID")
    private String bizId;

    @Schema(description = "链接界面")
    private String bizType;

    @Schema(description = "链接类型：LINK_TYPE_IN-内部链接；LINK_TYPE_EX-外部链接")
    private String linkType;
    /**
     * 是否已删除;1：已删除 0：未删除
     */
    private int isDeleted;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建人姓名
     */
    private String createdName;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新人姓名
     */
    private String updatedName;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

}
