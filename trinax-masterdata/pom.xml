<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.trinasolar</groupId>
		<artifactId>trinax-shared</artifactId>
		<version>0.0.2.RELEASE</version>
		<relativePath>../trinax-shared</relativePath>
	</parent>

	<packaging>pom</packaging>
	<artifactId>trinax-masterdata-root</artifactId>
	<version>0.0.4-SNAPSHOT</version>
	<name>trinax-masterdata-root</name>
	<description>Demo project for Spring Boot</description>
	<properties>
		<java.version>17</java.version>
	</properties>
	<modules>
		<module>trinax-masterdata-api</module>
		<module>trinax-masterdata-domain</module>
		<module>trinax-masterdata-boot</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-masterdata-api</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-masterdata-domain</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-common</artifactId>
				<version>0.0.2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-integration-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-basic-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-user-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-contract-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
		</dependencies>
	</dependencyManagement>


</project>
