#!/bin/bash

set -e

# =====================
# 1. 变量定义
# =====================
WORKDIR="$HOME/data/dtt"
PROJECT_DIR="$WORKDIR/dtt-trinasolar"
REPO_URL="*************************************:ec/Trinasolar/dtt-trinasolar"   # 请替换为实际仓库地址
BRANCH="dev-boot2.7"
# 使用当前项目目录下的 settings.xml，如果不存在则使用默认的 Maven settings
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MAVEN_SETTINGS="$CURRENT_DIR/settings.xml"

# =====================
# 2. 拉取或更新代码
# =====================
if [ ! -d "$PROJECT_DIR/.git" ]; then
  echo "📦 首次克隆仓库到 $WORKDIR"
  cd "$WORKDIR"
  git clone "$REPO_URL"
  cd dtt-trinasolar
else
  echo "📦 更新仓库到最新 $BRANCH 分支"
  cd "$PROJECT_DIR"
  git fetch origin
  git checkout $BRANCH
  git pull origin $BRANCH
fi

# =====================
# 3. 打包公共依赖并上传到私有仓库
# =====================
echo "🔨 打包并安装/上传公共依赖到私有仓库..."

# 检查 settings.xml 是否存在
if [ -f "$MAVEN_SETTINGS" ]; then
  echo "📦 使用 Maven settings: $MAVEN_SETTINGS"
  MAVEN_SETTINGS_PARAM="-s $MAVEN_SETTINGS"
else
  echo "⚠️  未找到 settings.xml 文件: $MAVEN_SETTINGS，使用默认 Maven 配置"
  MAVEN_SETTINGS_PARAM=""
fi

# 3.1 打包 dtt-component
if [ -d "dtt-component" ]; then
  echo "➡️ 处理公共依赖目录: dtt-component"
  cd dtt-component
  if [ -n "$MAVEN_SETTINGS_PARAM" ]; then
    mvn clean install deploy -DskipTests $MAVEN_SETTINGS_PARAM
  else
    mvn clean install deploy -DskipTests
  fi
  cd ..
fi

# 3.2 打包 trinax-shared
if [ -d "trinax-shared" ]; then
  echo "➡️ 处理共享依赖目录: trinax-shared"
  cd trinax-shared
  if [ -n "$MAVEN_SETTINGS_PARAM" ]; then
    mvn clean install deploy -DskipTests $MAVEN_SETTINGS_PARAM
  else
    mvn clean install deploy -DskipTests
  fi
  cd ..
fi

# =====================
# 4. 编译并构建所有业务服务镜像
# =====================
echo "🔨 开始编译并构建所有业务服务镜像..."

# 检查 settings.xml 是否存在
if [ -f "$MAVEN_SETTINGS" ]; then
  echo "📦 使用 Maven settings: $MAVEN_SETTINGS"
  MAVEN_SETTINGS_PARAM="-s $MAVEN_SETTINGS"
else
  echo "⚠️  未找到 settings.xml 文件: $MAVEN_SETTINGS，使用默认 Maven 配置"
  MAVEN_SETTINGS_PARAM=""
fi

find . -type f -name "Dockerfile" | while read dockerfile; do
  service_dir=$(dirname "$dockerfile")
  # 跳过公共依赖目录
  if [[ "$service_dir" == ./dtt-component* ]] || [[ "$service_dir" == ./dtt-support* ]]; then
    continue
  fi
  # 只编译有pom.xml的目录
  if [ -f "$service_dir/pom.xml" ]; then
    echo "➡️ 处理服务目录: $service_dir"
    original_dir=$(pwd)
    cd "$service_dir"
    echo "📍 当前工作目录: $(pwd)"

    # 根据是否有 settings.xml 决定是否使用 -s 参数
    if [ -n "$MAVEN_SETTINGS_PARAM" ]; then
      mvn clean install -T 1C -Dmaven.test.skip=true $MAVEN_SETTINGS_PARAM
    else
      mvn clean install -T 1C -Dmaven.test.skip=true
    fi

    image_name=$(basename "$service_dir")
    docker build -t "$image_name:latest" .
    cd "$original_dir"
  fi
done

# =====================
# 5. 启动所有服务（统一 docker-compose.yml）
# =====================
COMPOSE_FILE="$PROJECT_DIR/docker-compose.yml"
if [ ! -f "$COMPOSE_FILE" ]; then
  echo "❌ 未找到根目录下的 docker-compose.yml: $COMPOSE_FILE"
  exit 1
fi

echo "🚀 启动所有服务（docker compose up -d）..."
SPRING_PROFILES_ACTIVE=local docker compose -f "$COMPOSE_FILE" up -d
echo "✅ 所有服务部署完成。"