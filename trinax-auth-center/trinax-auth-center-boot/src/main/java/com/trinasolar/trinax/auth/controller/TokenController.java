package com.trinasolar.trinax.auth.controller;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.auth.api.SysAppClientFeign;
import com.trinasolar.trinax.auth.domain.common.utils.ShiroSimpleHashUtil;
import com.trinasolar.trinax.auth.domain.manager.SysClient;
import com.trinasolar.trinax.auth.dto.input.SysApiClientReqDTO;
import com.trinasolar.trinax.auth.dto.output.SysApiClientDetailResDTO;
import com.trinasolar.trinax.auth.dto.output.SysApiClientResDTO;
import com.trinasolar.trinax.auth.dto.output.SysApiTokenResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.dto.input.SysClientInfoReqDTO;
import com.trinasolar.trinax.user.dto.output.SysClientDetailResDTO;
import com.trinasolar.trinax.user.dto.output.SysClientInfoResDTO;
import com.trinasolar.trinax.user.dto.output.SysClientResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 21/11/2023
 * @description
 */
@Slf4j
@RestController
@Tag(name = "API接口应用类")
public class TokenController implements SysAppClientFeign {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SysClient sysClient;


    @Override
    public Result<SysApiClientResDTO> findClientByAppId(String appId) {
        log.info("TokenController.findClientByAppId req={}",appId);
        Result<SysClientResDTO> client = sysClient.findClientByAppId(appId);
        return Result.ok(BeanUtil.copyProperties(client.getData(), SysApiClientResDTO.class));
    }

    @Override
    public Result<SysApiClientDetailResDTO> findClientDetailByAppId(String appId) {
        log.info("TokenController.findClientDetailByAppId req={}",appId);
        Result<SysClientDetailResDTO> detail = sysClient.findClientDetailByAppId(appId);
        return Result.ok(BeanUtil.copyProperties(detail.getData(), SysApiClientDetailResDTO.class));
    }

    /**
     * 获取应用API Token
     *
     * @return
     */
    @Operation(summary = "获取应用API Token")
    public Result<SysApiTokenResDTO> apiToken(@Validated @RequestBody SysApiClientReqDTO dto) {
        log.info("TokenController.apiToken req={}", JacksonUtil.bean2Json(dto));
        if (null == dto.getAccessTokenValidity() || dto.getAccessTokenValidity() <= 0) {
            throw new BizException(ResultCode.REQ_VALIDATE_ERROR.getCode(), "有效期不能为空或小于0");
        }
        String token = UUID.randomUUID().toString();

        SysClientInfoReqDTO sysClientInfoReqDTO = new SysClientInfoReqDTO();
        sysClientInfoReqDTO.setAccessToken(token);
        sysClientInfoReqDTO.setAppName(dto.getAppName());
        sysClientInfoReqDTO.setAccessTokenValidity(dto.getAccessTokenValidity());
        Result<SysClientInfoResDTO> clientInfo = sysClient.createClientInfo(sysClientInfoReqDTO);
        if (null != clientInfo && clientInfo.getSuccess()) {
            SysClientInfoResDTO sysClientInfoResDTO = clientInfo.getData();

            SysApiTokenResDTO apiTokenResDTO = BeanUtil.copyProperties(sysClientInfoResDTO, SysApiTokenResDTO.class);
            ValueOperations<String, SysApiTokenResDTO> operations = redisTemplate.opsForValue();
            String key = ShiroSimpleHashUtil.simpleHash(apiTokenResDTO.getAppId() + apiTokenResDTO.getAccessToken());
            operations.set(key, apiTokenResDTO, dto.getAccessTokenValidity(), TimeUnit.DAYS);
            return Result.ok(apiTokenResDTO);
        }

        return Result.fail("获取token失败，请稍后重试！");
    }


}
