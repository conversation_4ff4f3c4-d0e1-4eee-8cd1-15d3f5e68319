package com.trinasolar.trinax.auth.core.utils;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <pre>
 * Title: IP工具类.
 *
 * Description:
 * </pre>
 */
public final class IPUtils {

	private static final String IP_V6_LOCAL = "0:0:0:0:0:0:0:1";

	private static final String IP_V4_LOCAL = "127.0.0.1";

	private IPUtils() {
	}

	public static String getClientIpAddr(HttpServletRequest request) {
		String ip = null;

		String[] headers = new String[]{"X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP", "HTTP_CLIENT_IP",
				"HTTP_X_FORWARDED_FOR"};

		for (String header : headers) {
			ip = getIp(request.getHeader(header));
			if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
				break;
			}
		}

		if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
			ip = getIp(request.getRemoteAddr());
		}

		if (IP_V6_LOCAL.equals(ip)) {
			ip = IP_V4_LOCAL;
		}

		return ip;
	}

	private static String getIp(String ip) {
		return StringUtils.substringBefore(ip, ",");
	}

}