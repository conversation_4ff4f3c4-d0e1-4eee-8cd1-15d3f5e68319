package com.trinasolar.trinax.auth.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * <pre>
 * Title:Basic解码工具类
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Slf4j
public class BasicDecoderUtils {

    /**
     * base64解码
     *
     * @param input
     * @return
     */
    public static String decoder(String input) {
        String output;
        try {
            output = new String(Base64.getDecoder().decode(input.split(" ")[1]), StandardCharsets.UTF_8);
            return output;
        } catch (Exception e) {
            log.error("Class(BasicDecoderUtils) -> Method(decoder) exception: {}", e.getMessage());
        }
        return null;
    }
}
