<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trinax-shared</artifactId>
        <version>0.0.2.RELEASE</version>
        <relativePath>../trinax-shared</relativePath>
    </parent>

    <modules>
        <module>trinax-auth-center-api</module>
        <module>trinax-auth-center-boot</module>
        <module>trinax-auth-center-domain</module>
        <module>trinax-auth-center-core</module>
    </modules>

    <packaging>pom</packaging>
    <artifactId>trinax-auth-center</artifactId>
    <name>trinax-auth-center</name>
    <version>0.0.4-SNAPSHOT</version>
    <description>trina认证中心</description>
    <properties>
        <java.version>17</java.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-auth-center-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-auth-center-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-common</artifactId>
                <version>0.0.2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-user-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-integration-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-basic-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-auth-center-core</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>