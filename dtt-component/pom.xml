<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>dtt.asset</groupId>
  <artifactId>dtt-root</artifactId>
  <version>0.0.2.RELEASE</version>
  <packaging>pom</packaging>
  <name>dtt root POM</name>
  <description>dtt root project</description>
  <modules>
    <module>dtt-base</module>
    <module>dtt-cache</module>
    <module>dtt-dbconnection</module>
    <module>dtt-high-performance</module>
    <module>dtt-message-queue</module>
    <module>dtt-object-storage</module>
    <module>dtt-rpc</module>
    <module>dtt-scheduler</module>
    <module>dtt-security</module>
    <module>dtt-support</module>
    <module>dtt-workflow</module>
    <module>dtt-notification</module>
    <module>dtt-segment</module>
    <module>dtt-limiting</module>


  </modules>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.2.2.RELEASE</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
<!--  <repositories>-->
<!--    <repository>-->
<!--      <id>trina-nexus</id>-->
<!--      <name>trina-nexus</name>-->
<!--      <url>http://**********:18081/nexus/content/groups/public/</url>-->
<!--      <releases>-->
<!--        <enabled>true</enabled>-->
<!--      </releases>-->
<!--      <snapshots>-->
<!--        <enabled>true</enabled>-->
<!--      </snapshots>-->
<!--    </repository>-->
<!--  </repositories>-->

<!--  <distributionManagement>-->
<!--    <repository>-->
<!--      <id>trina-release</id>-->
<!--      <name>Releases</name>-->
<!--      <url>http://**********:18081/nexus/content/repositories/releases/</url>-->
<!--    </repository>-->
<!--    <snapshotRepository>-->
<!--      <id>trina-snapshot</id>-->
<!--      <name>Snapshot</name>-->
<!--      <url>http://**********:18081/nexus/content/repositories/snapshots/</url>-->
<!--    </snapshotRepository>-->
<!--  </distributionManagement>-->

  <repositories>
    <repository>
      <id>atb-ecp-nexus</id>
      <name>atb-ecp-nexus</name>
      <url>http://10.173.61.218:8081/repository/ecp-public/</url>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>

  <distributionManagement>
    <repository>
      <id>atb-ecp-release</id>
      <name>Releases</name>
      <url>http://10.173.61.218:8081/repository/ecp-releases/</url>
    </repository>
    <snapshotRepository>
      <id>atb-ecp-snapshot</id>
      <name>Snapshot</name>
      <url>http://10.173.61.218:8081/repository/ecp-snapshots/</url>
    </snapshotRepository>
  </distributionManagement>
</project>
