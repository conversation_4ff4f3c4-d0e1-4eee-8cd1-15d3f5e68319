package dtt.notification.wechat.mail;

import dtt.workflow.drools.entity.EmailEntity;
import dtt.workflow.drools.utils.EmailUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.mail.MessagingException;
import java.security.GeneralSecurityException;
import java.util.Collections;

/**
 * 邮件单元测试
 *
 * <AUTHOR> <PERSON>ian<PERSON>
 * @date 2023-07-18
 */
@SpringBootTest(classes = NotificationTestApplication.class)
public class EmailUtilsTest {
    @Test
    public void sendMail() throws MessagingException, GeneralSecurityException {
        EmailEntity email = new EmailEntity();
        email.setToList(Collections.singletonList("<EMAIL>"));
        email.setCcList(Collections.singletonList("<EMAIL>"));
        email.setBccList(Collections.singletonList("<EMAIL>"));
        email.setTitle("title");
        email.setHtmlContent(mockMailContext());
        EmailUtils.sendMail(email);
    }

    /**
     * mockMailContext
     *
     * @return java.lang.String
     * <AUTHOR> Alex Jiexiang
     * @date 2023-07-18
     */
    private String mockMailContext() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh\" xmlns=\"http://www.w3.org/1999/html\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <title>${title}</title>\n" +
                "</head>\n" +
                "<style>\n" +
                "    table {\n" +
                "        border-collapse: collapse;\n" +
                "    }\n" +
                "\n" +
                "    th {\n" +
                "        border-width: 1px;\n" +
                "        padding: 8px;\n" +
                "        border-style: solid;\n" +
                "    }\n" +
                "\n" +
                "    td {\n" +
                "        border: solid 1px;\n" +
                "        padding: 8px;\n" +
                "    }\n" +
                "</style>\n" +
                "<body>\n" +
                "\n" +
                "\n" +
                "<p>质量任务数量:<span>1</span>，\n" +
                "    异常任务数量：<span style=\"color: red\">1</span>，\n" +
                "    异常任务比例：<span style=\"color: red\">100.00</span>%</p>\n" +
                "<p><span><a href=\"http://www.baidu.com\">点击链接</a>可进入查看</span>\n" +
                "<div>\n" +
                "    <p>数据资产名称：<span>ads_tsp_notifications_cbs_events_api</span></p>\n" +
                "    <p>质量任务名称：<span>ads_tsp_notifications_cbs_events_api</span></p>\n" +
                "    <p>质量任务运行时间：<span>2023-07-17 11:53:26</span></p>\n" +
                "    <p>规则总数：<span>1</span></p>\n" +
                "    <p>异常规则数：<span>1</span></p>\n" +
                "    <div>\n" +
                "        <p>内置模板规则：</p>\n" +
                "        <table>\n" +
                "            <thead>\n" +
                "            <tr>\n" +
                "                <th>规则名称</th>\n" +
                "                <th>备注</th>\n" +
                "                <th>字段名称</th>\n" +
                "                <th>检验状态</th>\n" +
                "                <th>异常值%</th>\n" +
                "                <th>正常值%</th>\n" +
                "                <th>红色阈值%</th>\n" +
                "                <th>异常数量</th>\n" +
                "                <th>数据总数</th>\n" +
                "            </tr>\n" +
                "            </thead>\n" +
                "            <tbody>\n" +
                "            <tr>\n" +
                "                <td>列值都是唯一的</td>\n" +
                "                <td></td>\n" +
                "                <td>ods_is_deleted</td>\n" +
                "                <td><span style=\"color: red\">异常</span></td>\n" +
                "                \n" +
                "                <td>100.00%</td>\n" +
                "                <td>0.00%</td>\n" +
                "                <td>15.00%</td>\n" +
                "                <td>5183327</td>\n" +
                "                <td>5183456</td>\n" +
                "            </tr>\n" +
                "            </tbody>\n" +
                "        </table>\n" +
                "    </div>\n" +
                "    <br/>\n" +
                "    <p></p>\n" +
                "    \n" +
                "</div>\n" +
                "</body>\n" +
                "</html>";
    }


}