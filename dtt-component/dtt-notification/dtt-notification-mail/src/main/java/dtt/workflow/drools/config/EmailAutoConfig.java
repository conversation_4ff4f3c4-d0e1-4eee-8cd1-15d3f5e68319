package dtt.workflow.drools.config;

import dtt.workflow.drools.utils.EmailUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 消息服务自动装配
 *
 * <AUTHOR> <PERSON>iang
 * @date 2023-07-17
 */
@Import({EmailConfig.class})
@Configuration
public class EmailAutoConfig {

    /**
     * emailUtils 自动装配
     *
     * @return dtt.notification.utils.EmailUtils
     * <AUTHOR> Alex Jiexiang
     * @date 2023-07-18
     * // 只指定name或者value，因为配置项为true，havingValue为false，所以不会加载SwaggerAutoConfiguration。
     */
    @Bean
    @ConditionalOnProperty(name = "notification.config.email.enable", havingValue = "true")
    public EmailUtils emailUtil() {
        return new EmailUtils();
    }


}
