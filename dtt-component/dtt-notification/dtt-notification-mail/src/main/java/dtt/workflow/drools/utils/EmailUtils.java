package dtt.workflow.drools.utils;

import com.sun.mail.util.MailSSLSocketFactory;
import dtt.workflow.drools.config.EmailConfig;
import dtt.workflow.drools.entity.EmailEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import java.security.GeneralSecurityException;
import java.util.Properties;

/**
 * 邮件发送工具类
 *
 * <AUTHOR> <PERSON>iang
 * @date 2023-07-18
 */
@Slf4j
public class EmailUtils {

    @Resource
    private EmailConfig emailConfig;
    /**
     * 发件账户
     */
    private static String emailAccount;
    /**
     * 发件密码
     */
    private static String emailPassword;

    /**
     * 服务器host
     */
    private static String emailSMTPHost;

    /**
     * 发件端口
     */
    private static String emailPort;

    /**
     * debug模式
     */
    private static Boolean debug;

    @PostConstruct
    public void init() {
        emailAccount = emailConfig.getEmailAccount();
        emailPassword = emailConfig.getEmailPassword();
        emailSMTPHost = emailConfig.getEmailSmtpHost();
        emailPort = emailConfig.getEmailPort();
        debug = emailConfig.getDebug();
    }


    /**
     * sendMail
     *
     * @param email 邮件实体
     * <AUTHOR> Alex Jiexiang
     * @date 2023-07-18
     */
    public static void sendMail(EmailEntity email) throws MessagingException, GeneralSecurityException {
        checkParameter();
        log.info("Start send email");
        //1.创建参数配置, 用于连接邮件服务器的参数配置
        Properties props = new Properties();
        //参数配置
        props.setProperty("mail.transport.protocol", "smtp");
        //使用的协议（JavaMail规范要求）
        props.setProperty("mail.smtp.host", emailSMTPHost);
        //发件人的邮箱的 SMTP 服务器地址
        props.setProperty("mail.smtp.starttls.enable", "true");
        //需要请求认证
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
        //设置端口
        props.setProperty("mail.smtp.socketFactory.port", emailPort);
        MailSSLSocketFactory sf = new MailSSLSocketFactory();
        sf.setTrustAllHosts(true);
        props.put("mail.smtp.ssl.socketFactory", sf);
        log.info("Set mail port-> {}", props.getProperty("mail.smtp.socketFactory.port"));
        //创建一个session对象
        Session session = Session.getDefaultInstance(props, new Authenticator() {
            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailAccount, emailPassword);
            }
        });

        //开启debug模式
        session.setDebug(debug != null && debug);
        //获取连接对象
        Transport transport = session.getTransport();
        //连接服务器
        transport.connect(emailSMTPHost, emailAccount, emailPassword);
        //创建邮件对象
        MimeMessage mimeMessage = new MimeMessage(session);
        //邮件发送人
        mimeMessage.setFrom(new InternetAddress(emailAccount));
        if (CollectionUtils.isEmpty(email.getToList())) {
            throw new RuntimeException("To list must not empty!");
        }
        //邮件接收人
        InternetAddress[] toAddressArr = new InternetAddress[email.getToList().size()];
        for (int i = 0; i < email.getToList().size(); i++) {
            toAddressArr[i] = new InternetAddress(email.getToList().get(i));
        }
        mimeMessage.setRecipients(Message.RecipientType.TO, toAddressArr);

        // 邮件抄送人
        if (CollectionUtils.isNotEmpty(email.getCcList())) {
            InternetAddress[] ccAddress = new InternetAddress[email.getCcList().size()];
            for (int i = 0; i < email.getCcList().size(); i++) {
                ccAddress[i] = new InternetAddress(email.getCcList().get(i));
            }
            mimeMessage.setRecipients(Message.RecipientType.CC, ccAddress);
        }
        // 邮件密送人
        if (CollectionUtils.isNotEmpty(email.getBccList())) {
            InternetAddress[] bccAddress = new InternetAddress[email.getBccList().size()];
            for (int i = 0; i < email.getBccList().size(); i++) {
                bccAddress[i] = new InternetAddress(email.getBccList().get(i));
            }
            mimeMessage.setRecipients(Message.RecipientType.BCC, bccAddress);
        }
        //邮件标题
        mimeMessage.setSubject(email.getTitle());

        //设置邮件内容
        Multipart multipart = new MimeMultipart();
        //支持html内容
        MimeBodyPart html = new MimeBodyPart();
        html.setContent(email.getHtmlContent(), "text/html;charset=utf-8");
        multipart.addBodyPart(html);

        mimeMessage.setContent(multipart);
        mimeMessage.setFlag(Flags.Flag.RECENT, true);
        mimeMessage.saveChanges();
        //发送邮件
        transport.sendMessage(mimeMessage, mimeMessage.getAllRecipients());
        //关闭连接
        transport.close();
        log.info("Send mail success");
    }

    /**
     * 校验邮件配置是否完整
     *
     * <AUTHOR> Alex Jiexiang
     * @date 2023-07-18
     */
    private static void checkParameter() {
        if (StringUtils.isBlank(emailAccount)) {
            throw new RuntimeException("The emailAccount must not be empty");
        }
        if (StringUtils.isBlank(emailSMTPHost)) {
            throw new RuntimeException("The emailSmtpHost must not be empty");
        }
        if (StringUtils.isBlank(emailPort)) {
            throw new RuntimeException("The emailPort must not be empty");
        }
    }
}