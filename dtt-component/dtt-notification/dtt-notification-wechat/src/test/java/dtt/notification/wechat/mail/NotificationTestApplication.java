package dtt.notification.wechat.mail;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Notification UT 启动类
 *
 * <AUTHOR> <PERSON>
 * @date 2023-07-18
 */
@SpringBootApplication
public class NotificationTestApplication {
    /**
     * Notification UT 启动类
     *
     * @param args
     * <AUTHOR> <PERSON>iang
     * @date 2023-07-18
     */
    public static void main(String[] args) {
        SpringApplication.run(NotificationTestApplication.class, args);
    }
}
