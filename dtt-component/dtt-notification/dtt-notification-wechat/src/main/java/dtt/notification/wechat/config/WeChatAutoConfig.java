package dtt.notification.wechat.config;

import dtt.notification.wechat.utils.WeChatUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * 消息服务自动装配
 *
 * <AUTHOR> <PERSON>iang
 * @date 2023-07-17
 */
@Import({WeChatConfig.class})
@Configuration
public class WeChatAutoConfig {


    /**
     * weChatUtils自动装配
     *
     * @return dtt.notification.utils.WeChatUtils
     * <AUTHOR> Alex Jiexiang
     * @date 2023-07-19
     */
    @Bean
    @ConditionalOnProperty(name = "notification.config.wechat.enable", havingValue = "true")
    public WeChatUtils weChatUtils() {
        return new WeChatUtils();
    }


}
