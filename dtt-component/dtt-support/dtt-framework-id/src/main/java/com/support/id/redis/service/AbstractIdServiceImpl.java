package com.support.id.redis.service;

import com.support.id.redis.constants.Constants;
import com.support.id.redis.facade.IdService;
import org.springframework.stereotype.Service;

/**
 * 订单编号实现
 *
 */
@Service
public abstract class AbstractIdServiceImpl implements IdService {

    public void init(long machineId) {
        validateParams(machineId);
    }

    /**
     * 验证启动参数合法性
     */
    public void validateParams(long machineId) {

        if (machineId > Constants.MAX_MACHINE_NUM || machineId < 0) {
            throw new IllegalArgumentException("机器id不合法");
        }
    }

    @Override
    public abstract String genId(String prefix);

    @Override
    public abstract String genBizNo(String prefix);

    @Override
    public abstract String populateSeqNo(String prefix) throws Exception;

    @Override
    public abstract String populateIncrementSeqNo(String prefix, int len, String dateFormat);

    @Override
    public abstract String populateIncrementSeqNoWithMachineId(int len, String dateFormat);
}
