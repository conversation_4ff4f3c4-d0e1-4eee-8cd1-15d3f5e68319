package com.support.id.redis.facade;


/**
 * 订单号生成接口
 *
 * <AUTHOR>
 */
public interface IdService {
    /**
     * 生成订单no
     *
     * @param prefix
     * @return
     */
    String genId(String prefix);

    /**
     * 生成业务订单号
     * 格式：前缀 + yyyyMMdd +序列编号(5位)
     *
     * @param prefix
     * @return
     */
    String genBizNo(String prefix);

    /**
     * 生成序列编号
     * 格式：前缀 + 序列编号(6位)
     *
     * @param prefix
     * @return
     */
    String populateSeqNo(String prefix) throws Exception;

    /**
     * 根据参数生成唯一编号
     *
     * @param prefix
     * @param len
     * @param dateFormat
     * @return
     */
    String populateIncrementSeqNo(String prefix, int len, String dateFormat);

    /**
     * 根据参数生成唯一编号
     *
     * @param len
     * @param dateFormat
     * @return
     */
    String populateIncrementSeqNoWithMachineId(int len, String dateFormat);
}
