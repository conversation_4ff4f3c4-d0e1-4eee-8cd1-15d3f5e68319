package com.support.id.redis.service;


import com.support.id.redis.facade.IdPopulator;
import com.support.id.snow.SnowFlakeProperties;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * id生成器实例化
 */
public class IdServiceImpl extends AbstractIdServiceImpl {

    @Autowired
    protected IdPopulator idPopulator;

    /** 机器标识 */
    @Autowired
    private SnowFlakeProperties snowFlakeProperties;

    public void init(){
        super.init(this.snowFlakeProperties.getMachineId());
        initPopulator();
    }

    public void initPopulator() {
        if (idPopulator == null){
            idPopulator = new AtomicPopulator();
        }
    }

    @Override
    public String genId(String prefix) {
        return idPopulator.populateId(prefix,this.snowFlakeProperties.getMachineId());
    }

    @Override
    public String genBizNo(String prefix) {
        return idPopulator.populateBizNo(prefix,this.snowFlakeProperties.getMachineId());
    }

    @Override
    public String populateSeqNo(String prefix) throws Exception {
        return idPopulator.populateSeqNo(prefix,snowFlakeProperties.getMachineId());
    }

    @Override
    public String populateIncrementSeqNo(String prefix, int len, String dateFormat) {
        return idPopulator.populateIncrementSeqNo(prefix,len,dateFormat);
    }

    @Override
    public String populateIncrementSeqNoWithMachineId(int len, String dateFormat) {
        return idPopulator.populateIncrementSeqNo(String.valueOf(this.snowFlakeProperties.getMachineId()),len,dateFormat);
    }

}
