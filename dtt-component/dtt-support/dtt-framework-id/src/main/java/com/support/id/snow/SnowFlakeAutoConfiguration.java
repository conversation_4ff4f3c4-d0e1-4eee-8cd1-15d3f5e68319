package com.support.id.snow;

import com.support.id.redis.distributed.redis.RedisDistributionLock;
import com.support.id.redis.service.AtomicPopulator;
import com.support.id.redis.service.IdServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

/**
 * @program:
 * @description: 雪花算法ID配置注入
 * @author: vijay
 **/
@Configuration
@Import(SnowFlakeProperties.class)
@Data
@Slf4j
public class SnowFlakeAutoConfiguration {

    @Autowired
    private SnowFlakeProperties snowFlakeProperties;

    @Bean
    public SnowFlake snowFlake() {
        log.info("创建雪花算法计算器：数据中心ID:{} @ 机器ID:{}", snowFlakeProperties.getDataCenterId(), snowFlakeProperties.getMachineId());
        return new SnowFlake(snowFlakeProperties.getDataCenterId(), snowFlakeProperties.getMachineId());
    }

    @Bean
    public IdGenerator idGenerator(SnowFlake snowFlake){
        return new IdGenerator(snowFlake);
    }

    @Bean(initMethod = "init")
    public IdServiceImpl getIdService() {
        return new IdServiceImpl();
    }

    @Bean
    public AtomicPopulator atomicPopulator(){
        return new AtomicPopulator();
    }

    @Bean
    @Primary
    public RedisDistributionLock getDistribute(){

        return new RedisDistributionLock();
    }
}
