package com.support.id.snow;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "dtt.cloud.snowflake")
@Data
public class SnowFlakeProperties {
    /**
     * 数据中心
     */
    private int dataCenterId = 0;
    /**
     * 机器标识
     */
    private int machineId = 0;
}
