package com.support.id.redis.distributed.redis;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Component;

import java.nio.charset.Charset;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * 分布式锁redis实现
 *
 */
@Component
@Slf4j
public class RedisDistributionLock {

    @Autowired
    private RedisTemplate redisTemplate;

    private ThreadLocal<String> lockFlag = new ThreadLocal<>();

    public static final String UNLOCK_LUA;

    private static final long EXPIRE = 1;


    /**
     * 释放锁脚本，原子操作
     */
    static {
        StringBuilder sb = new StringBuilder();
        sb.append("if redis.call(\"get\",KEYS[1]) == ARGV[1] ");
        sb.append("then ");
        sb.append("    return redis.call(\"del\",KEYS[1]) ");
        sb.append("else ");
        sb.append("    return 0 ");
        sb.append("end ");
        UNLOCK_LUA = sb.toString();
    }


    public boolean tryLock(String lockKey) {
        return tryLock(lockKey,EXPIRE,TimeUnit.MINUTES,3);
    }


    /**
     * 在传入次数内获取锁，成功则返回true,在指定次数获取失败返回false
     *
     * @param lockKey
     * @param expire
     * @param timeUnit
     * @param times
     * @return
     */
    public boolean tryLock(String lockKey, long expire, TimeUnit timeUnit, int times) {

        int count =1;
        if (count > times) {
            throw new IllegalArgumentException("times must greater than 1");
        }

        while (count <= times) {
            if (tryLock(lockKey, expire, timeUnit)) {
                return true;
            }
            count ++;
        }
        return false;
    }

    /**
     * 获取分布式锁，原子操作
     *
     * @param lockKey
     * @param expire
     * @param timeUnit
     * @return
     */
    public boolean tryLock(String lockKey, long expire, TimeUnit timeUnit) {

        try {
            String requestId = UUID.randomUUID().toString();
            lockFlag.set(requestId);

            RedisCallback<Boolean> callback = (connection) -> connection.set(lockKey.getBytes(Charset.forName("UTF-8")), requestId.getBytes(Charset.forName("UTF-8")), Expiration.seconds(timeUnit.toSeconds(expire)), RedisStringCommands.SetOption.SET_IF_ABSENT);
            return (Boolean) redisTemplate.execute(callback);
        } catch (Exception e) {
            log.error("redis lock error.", e);
        }
        return false;
    }

    /**
     * 释放锁
     *
     * @param lockKey
     * @return
     */
    public boolean releaseLock(String lockKey) {

        try {
            RedisCallback<Boolean> callback = (connection) -> {
                String requestId = lockFlag.get();
                return connection.eval(UNLOCK_LUA.getBytes(), ReturnType.BOOLEAN, 1, lockKey.getBytes(Charset.forName("UTF-8")), requestId.getBytes(Charset.forName("UTF-8")));
            };
            return (Boolean) redisTemplate.execute(callback);
        } catch (Exception e) {
            log.error("release lock exception", e);
        } finally {
            lockFlag.remove();
        }
        return false;
    }

    /**
     * 获取Redis锁的value值
     *
     * @param lockKey
     * @return
     */
    public String get(String lockKey) {
        try {
            RedisCallback<String> callback = (connection) -> {
                return new String(connection.get(lockKey.getBytes()), Charset.forName("UTF-8"));
            };
            return (String) redisTemplate.execute(callback);
        } catch (Exception e) {
            log.error("get redis occurred an exception", e);
        }
        return null;
    }
}
