package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部门：
 * 功能：付款申请状态
 * 描述：
 * 作成者：李磊
 * 作成时间：2020/1/19
 **/
@Getter
@AllArgsConstructor
public enum PaymentApplyRequestStatusEnum {
    SAVED("Saved","已保存"),
    APPROVING("Approving","审批中"),
    REJECTED("Rejected","已驳回"),
    APPROVED("Approved","已通过"),
    SUBMITTED("Submitted","已提交付款"),
    CLOSED("Closed","已关闭"),
    CLOSEDAPPROVING("CloseApproving","关闭审批中");

    private String code;

    private String name;
}
