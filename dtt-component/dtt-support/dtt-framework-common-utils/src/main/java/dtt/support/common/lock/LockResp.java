package dtt.support.common.lock;

import com.alibaba.fastjson2.annotation.J<PERSON><PERSON>ield;
import com.fasterxml.jackson.annotation.JsonProperty;
import dtt.support.common.context.SecurityContextHolder;
import lombok.Data;

import java.io.Serializable;

@Data
public class LockResp implements Serializable {

    @J<PERSON><PERSON>ield(name = "USER_NAME")
    @JsonProperty("USER_NAME")
    private String userName;

    @JSO<PERSON>ield(name = "USER_ACCOUNT")
    @JsonProperty("USER_ACCOUNT")
    private String userAccount;

    @JSONField(name = "EDITABLE")
    @JsonProperty("EDITABLE")
    private boolean editable;

    public LockResp(boolean editable){
        this.editable = editable;
    }

    public LockResp(boolean editable,String userName){
        this.editable = editable;
        this.userName = userName;
    }

    public LockResp(boolean editable, String userName, String userAccount){
        this.editable = editable;
        this.userAccount = userAccount;
        this.userName = userName;
    }

    public static LockResp selfLock(String userAccount){
        LockResp lockResp = new LockResp(true);
        lockResp.setUserName(SecurityContextHolder.getUserName());
        lockResp.setUserAccount(userAccount);
        return lockResp;
    }

    public static LockResp editable(String userName, String userAccount){
        return new LockResp(true,userName,userAccount);
    }

    public static LockResp locked(String userName, String userAccount){
        return new LockResp(false,userName,userAccount);
    }


}
