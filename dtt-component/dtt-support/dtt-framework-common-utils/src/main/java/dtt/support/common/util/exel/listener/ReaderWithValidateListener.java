package dtt.support.common.util.exel.listener;

import com.alibaba.excel.context.AnalysisContext;

import dtt.support.common.util.exel.entity.ErrorCell;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Slf4j
public class ReaderWithValidateListener<E> extends ReaderListener<E> {

    @Getter
    @Setter
    protected List<ErrorCell> errorMsg;

    private Method validateMethod;

    public ReaderWithValidateListener(Class<E> targetClass){
        super();
        try {
            this.validateMethod = targetClass.getDeclaredMethod("validate");
            errorMsg = new ArrayList<>();
        }catch (NoSuchMethodException e){
            log.warn("excel target class :'"+targetClass.getName()+"' does not implement 'ExcelValidatable', input data will not be validate");
        }
    }

    @Override
    public void invoke(E data, AnalysisContext analysisContext) {
        if(null != validateMethod) {
            try {
               Object object =  validateMethod.invoke(data);
               if(null != object){
                   errorMsg.addAll(castList(object,ErrorCell.class));
               }
            } catch (IllegalAccessException | InvocationTargetException e) {
                log.error("error when invoke validate method",e);
            } catch (Exception e){
                log.error("un-except error:",e);
            }
        }
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }

    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if(obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

}
