package dtt.support.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description: 信用引擎单据
 * @Author: chxiang
 * @Date: 16/03/2022 16:36
 **/
@Getter
@AllArgsConstructor
public enum CreditEngineEntity {

    //采购管理
    PO_BUSINESS_RECEIPT("PO_BUSINESS_RECEIPT", "业务入库单"),
    PO_CONTRACT("PO_CONTRACT", "采购合同"),
    PO_RETURN("PO_RETURN", "采购退货"),
    PO_SUPPLIER("PO_SUPPLIER", "供应商发货指令"),

    //销售管理
    OM_SALE_CONTRACT("OM_SALE_CONTRACT", "销售合同"),
    OM_SERVICE_CONTRACT("OM_SERVICE_CONTRACT", "服务合同"),
    OM_PREMIUM_CONTRACT("OM_PREMIUM_CONTRACT", "升贴水合同"),
    OM_SALE_BILL("OM_SALE_BILL", "销售提单"),
    OM_SALE_RETURN("OM_SALE_RETURN", "销售退货"),

    //转款单
    ZKD_TRANSFER_ORDER("ZKD_TRANSFER_ORDER", "转款单"),

    //应付管理
    YFM_INVOICE("YFM_INVOICE", "采购发票"),
    YFM_PAYMENTS("YFM_PAYMENTS", "付款申请"),

    //应收管理
    YSM_SALE_INVOICE("YSM_SALE_INVOICE", "销售发票"),

    //收款管理
    SKM_REGISTER("SKM_REGISTER", "收款登记"),
    SKM_CLAIM("SKM_CLAIM", "收款认领"),
    SKM_BILL("SKM_BILL", "收款单"),

    //付款管理
    FKM_BILL("FKM_BILL", "付款单"),

    //应收票据管理
    YSPJM_BILL("YSPJM_BILL", "应收票据"),

    //应付票据管理
    YMPJM_BILL("YMPJM_BILL", "应付票据"),

    //信用证管理
    XYZM_OPEN("XYZM_OPEN", "信用证开证"),
    XYZM_RECEIPT("XYZM_RECEIPT", "信用证收证"),

    //销售发货管理
    XSFHM_OUTBOUND_ORDER("XSFHM_OUTBOUND_ORDER", "物流出库单"),

    //采购入库管理
    CGRKM_WAREHOUSING_ORDER("CGRKM_WAREHOUSING_ORDER", "物流入库单"),

    //物流运输管理
    WLYSM_TRANSPORTRTION_ORDER("WLYSM_TRANSPORTRTION_ORDER", "物流运输单"),

    //物流合同管理
    WLHTM_WAREHOUSE_CONTRACT_MAINTENANCE("WLHTM_WAREHOUSE_CONTRACT_MAINTENANCE", "仓储合同维护"),
    WLHTM_LOGISTICS_CONTRACT_MAINTENANCE("WLHTM_LOGISTICS_CONTRACT_MAINTENANCE", "物流合同维护"),
    WLHTM_WHARF_CONTRACT_MAINTENANCE("WLHTM_WHARF_CONTRACT_MAINTENANCE", "码头作业合同维护"),
    WLHTM_TRSPORT_CONTRACT_MAINTENANCE("WLHTM_TRSPORT_CONTRACT_MAINTENANCE", "运输合同维护");

    private String code;

    private String name;

    public static String getName(String code) {
        for (CreditEngineEntity processTypeEnum : CreditEngineEntity.values()) {
            if (processTypeEnum.getCode().equalsIgnoreCase(code)) {
                return processTypeEnum.getName();
            }
        }
        return null;
    }

}
