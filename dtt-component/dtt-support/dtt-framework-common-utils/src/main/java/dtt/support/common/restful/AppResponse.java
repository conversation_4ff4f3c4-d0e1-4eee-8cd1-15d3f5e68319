package dtt.support.common.restful;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;

import java.io.Serializable;

/**
 * <p>系统统一返回格式</p>
 * <p>创建日期：2018-01-01</p>
 *
 * <AUTHOR> <EMAIL>
 */
@ApiModel(description = "系统统一返回格式")
@Builder
public class AppResponse<T> implements Serializable {

    /**
     * 返回编码
     */
    @ApiModelProperty("返回编码")
    private int code;

    /**
     * 消息描述
     */
    @ApiModelProperty("消息描述")
    private String msg;

    /**
     * 返回内容
     */
    @ApiModelProperty("返回内容")
    private T data;

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }


    public static <T> AppResponse<T> succeed(T data){
        return AppResponse.<T>builder().code(AppResponseStatus.SUCCESS.code).msg("success").data(data).build();
    }
    

    /**
     * 构建操作成功返回体
     *
     * @return 结果
     */
    public static <T> AppResponse<T> success() {
        return AppResponse.<T>builder().code(AppResponseStatus.SUCCESS.code).build();
    }

    /**
     * 构建操作成功返回体
     * <p>
     * * @param msg  操作说明
     *
     * @return 结果
     */
    public static<T> AppResponse<T> success(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.SUCCESS.code).msg(msg).build();
    }

    /**
     * 构建操作成功返回体
     *
     * @param msg  操作说明
     * @param data 返回结果
     * @return 结果
     */
    public static <T> AppResponse<T> success(String msg, T data) {
        return AppResponse.<T>builder().code(AppResponseStatus.SUCCESS.code).msg(msg).data(data).build();
    }

    /**
     * 构建没有查询到数据返回体
     *
     * @return 结果
     */
    public static <T> AppResponse<T> notFound() {
        return AppResponse.<T>builder().code(AppResponseStatus.NOT_FOUND.code).build();
    }

    /**
     * 构建没有查询到数据返回体
     *
     * @param msg 结果说明
     * @return 结果
     */
    public static <T> AppResponse<T> notFound(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.NOT_FOUND.code).msg(msg).build();
    }

    /**
     * 构建访问被拒绝返回体
     *
     * @return 结果
     */
    public static <T> AppResponse<T> serversAreTooBusy(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.SERVERS_ARE_TOO_BUSY.code).msg(msg).build();
    }

    /**
     * 构建无权限错误返回体
     *
     * @param msg 错误说明
     * @return 结果
     */
    public static <T> AppResponse<T> noPermission(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.NO_PERMISSION.code).build();
    }

    /**
     * 构建未知异常返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> unknownError() {
        return AppResponse.<T>builder().code(AppResponseStatus.UNKNOWN_EXCEPTION.code).build();
    }

    /**
     * 构建未知异常返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> unknownError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.UNKNOWN_EXCEPTION.code).msg(msg).build();
    }

    /**
     * 构建调用端异常返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> clientError() {
        return AppResponse.<T>builder().code(AppResponseStatus.CLIENT_EXCEPTION.code).build();
    }

    /**
     * 构建调用端异常返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> clientError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.CLIENT_EXCEPTION.code).msg(msg).build();
    }

    /**
     * 构建调用端参数错误返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> paramError() {
        return AppResponse.<T>builder().code(AppResponseStatus.PARAM_EXCEPTION.code).build();
    }

    /**
     * 构建调用端参数错误返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> paramError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.PARAM_EXCEPTION.code).msg(msg).build();
    }

    /**
     * 构建服务端参数错误返回体
     *
     * @return 返回体
     */
    public static <T> AppResponse<T> serverError() {
        return AppResponse.<T>builder().code(AppResponseStatus.SERVER_EXCEPTION.code).build();
    }

    /**
     * 构建服务端参数错误返回体
     *
     * @param msg 错误描述
     * @return 返回体
     */
    public static <T> AppResponse<T> serverError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.SERVER_EXCEPTION.code).msg(msg).build();
    }


    /**
     * 构建业务异常返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> bizError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.BIZ_ERROR.code).msg(msg).build();
    }
    
    /**
     * 构建业务弱控提示返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> bizTip(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.BIZ_TIP.code).msg(msg).build();
    }
    /**
     * 构建业务异常返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> bizTip(String msg, T data) {
        return AppResponse.<T>builder().code(AppResponseStatus.BIZ_TIP.code).msg(msg).data(data).build();
    }
    
    /**
     * 构建业务异常返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> bizError(String msg, T data) {
        return AppResponse.<T>builder().code(AppResponseStatus.BIZ_ERROR.code).msg(msg).data(data).build();
    }

    /**
     * 构建数据版本变化异常返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> versionError(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.ERROR.code).msg(msg).build();
    }
    /**
     * @Description: 强弱管控提示 AppResponseStatus： （10：弱管控）
     * @Author: chxiang
     * @Date: 18/03/2022 15:34
     **/
    public static <T> AppResponse<T> controlTip(String msg,AppResponseStatus code) {
        return AppResponse.<T>builder().code(code.code).msg(msg).build();
    }
    public static <T> AppResponse<T> controlTip(String msg,AppResponseStatus code, T data) {
        return AppResponse.<T>builder().code(code.code).msg(msg).data(data).build();
    }

    /**
     * 构建业务弱控提示返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> errorFresh(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.ERROR_FRESH.code).msg(msg).build();
    }
    /**
     * 验证码校验失败异常返回体
     *
     * @param msg 异常描述
     * @return
     */
    public static <T> AppResponse<T> invalidImage(String msg) {
        return AppResponse.<T>builder().code(AppResponseStatus.INVALID_IMAGE.code).msg(msg).build();
    }

    private AppResponse() {
    }

    private AppResponse(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

//    public static <T> AppResponseBuilder<T> builder() {
//        return new AppResponseBuilder<>();
//    }

//    @Override
//    public String toString() {
//        return ReflectionToStringBuilder.toString(this);
//    }

    @Override
    public String toString() {
        return "AppResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }


//    public String toJson() {
//        return JsonUtils.toJson(this);
//    }
//
//    public static class AppResponseBuilder<T> {
//
//        private int code;
//
//        private String msg;
//
//        private T data;
//
//        public AppResponse build() {
//            AppResponse appResponse = new AppResponse<>(this.code, this.msg, this.data);
//            return appResponse;
//        }
//
//        public AppResponseBuilder code(AppResponseStatus status) {
//            this.code = status.code;
//            return this;
//        }
//
//        public AppResponseBuilder msg(String msg) {
//            this.msg = msg;
//            return this;
//        }
//
//        public AppResponseBuilder data(T data) {
//            this.data = data;
//            return this;
//        }
//    }
}
