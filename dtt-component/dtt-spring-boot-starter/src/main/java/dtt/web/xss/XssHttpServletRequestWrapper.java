package dtt.web.xss;

import cn.hutool.http.ContentType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.nodes.Document;
import org.jsoup.safety.Cleaner;
import org.jsoup.safety.Whitelist;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;

import static org.jsoup.Jsoup.parseBodyFragment;

/**
 * XSS过滤
 *
 * <AUTHOR> Xiaohua
 * @date 02/08/2021 14:42
 */
@Slf4j
public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }

    @Override
    public String getHeader(String name) {
        String header = super.getHeader(name);
        if (StringUtils.isBlank(header)) {
            return header;
        }
        String text = xssClean(header);
        if (log.isDebugEnabled()) {
            log.debug("http header {} value {} to --> {}", name, header, text);
        }
        return text;
    }

    @Override
    public String getParameter(String name) {
        String paramVal = super.getParameter(name);
        if (StringUtils.isBlank(paramVal)) {
            return paramVal;
        }
        String text = xssClean(paramVal);
        if (log.isDebugEnabled()) {
            log.debug("http parameter {} value {} to --> {}", name, paramVal, text);
        }
        return text;
    }

    @Override
    public String getQueryString() {
        String queryStr = super.getQueryString();
        if (StringUtils.isBlank(queryStr)) {
            return queryStr;
        }
        String text = xssClean(queryStr);
        if (log.isDebugEnabled()) {
            log.debug("http query string value {} to --> {}", queryStr, text);
        }
        return text;
    }

    @Override
    public String[] getParameterValues(String name) {
        String[] parameterValues = super.getParameterValues(name);
        if (parameterValues == null) {
            return null;
        }
        String logStr = "";
        if (log.isDebugEnabled()) {
            logStr = Arrays.toString(parameterValues);
        }
        for (int i = 0; i < parameterValues.length; i++) {
            parameterValues[i] = xssClean(parameterValues[i]);
        }
        if (log.isDebugEnabled()) {
            log.debug("http parameter {} value {} to --> {}", name, logStr, Arrays.toString(parameterValues));
        }
        return parameterValues;
    }

    /**
     * 处理 application/json
     *
     * @return InputStream
     * @throws IOException ex
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {

        String contentType = getContentType();

        if (log.isDebugEnabled()) {
            log.debug("Content-Type {}", contentType);
        }

        if (StringUtils.equals(contentType, ContentType.JSON.getValue())
                || StringUtils.startsWith(contentType, ContentType.JSON.getValue())) {

            if (log.isDebugEnabled()) {
                log.debug("Content-Type {} XSS 过滤", contentType);
            }

            String encoding = getCharacterEncoding();

            if (log.isDebugEnabled()) {
                log.debug("http charset encoding {}", encoding);
            }

            encoding = StringUtils.isBlank(encoding) ? StandardCharsets.UTF_8.name() : encoding;

            ServletInputStream servletInputStream = super.getInputStream();
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            //servletInputStream.transferTo(bos);
            byte[] bytes = xssClean(bos.toByteArray(), encoding);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);

            return new ServletInputStream() {

                @Override
                public boolean isFinished() {
                    return servletInputStream.isFinished();
                }

                @Override
                public boolean isReady() {
                    return servletInputStream.isReady();
                }

                @Override
                public void setReadListener(ReadListener listener) {

                }

                @Override
                public int read() throws IOException {
                    return bis.read();
                }

                @Override
                public void close() throws IOException {
                    servletInputStream.close();
                }
            };
        }

        return super.getInputStream();
    }

    private byte[] xssClean(byte[] bytes, String charset) throws IOException {
        String s = IOUtils.toString(bytes, charset);
        String str = xssClean(s);
        if (log.isInfoEnabled() && !StringUtils.equals(s, str)) {
            String uri = getRequestURI();
            log.info("uri {} body {} to --> {}", uri, s, str);
        }
        return StringUtils.getBytes(str, charset);
    }

    private String xssClean(String text) {
        Document dirty = parseBodyFragment(text, "");
        Cleaner cleaner = new Cleaner(Whitelist.relaxed());
        Document clean = cleaner.clean(dirty);
        return clean.body().text();
    }
}
