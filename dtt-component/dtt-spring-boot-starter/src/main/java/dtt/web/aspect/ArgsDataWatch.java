package dtt.web.aspect;

import java.lang.annotation.*;

/**
 * 记录输入输出参数
 *
 * <AUTHOR>
 * @date 2022/02/09
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ArgsDataWatch {

    /**
     * 接口标识
     *
     * @return String
     */
    boolean logInputArgs() default true;
    boolean logOutputArgs() default true;
    boolean logException() default true;
    /*
    * 位标志 1 仅存储到控制台 ，2 仅存储到自定义ES
    * */
     int storeFlags() default 2;



}
