package dtt.web.aspect;

import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Component
@EnableAsync
public class ArgsAsyncManager {

    @Autowired
    private ArgsLogInterceptor argsLogInterceptor;


//    @Async(ArgsConfigurableAdvisorConfig.EXECUTOR_NAME)
//    public void asyncSendToKafka(String topic, final ArgsLogData argsLogData)
//    {
//        if(argsLogInterceptor!= null)
//            argsLogInterceptor.sendToKafka(topic, argsLogData);
//    }

    @Async(ArgsConfigurableAdvisorConfig.EXECUTOR_NAME)
    public void asyncSendToKafka(MethodInvocation invocation, ArgsDataWatch annoData ,String url, Map<String,String> headers, Object result , Date dtStart, Date dtEnd, Exception exp)
    {
        if(argsLogInterceptor!= null)
            argsLogInterceptor.sendLogData(invocation,annoData,url, headers,result,dtStart,dtEnd , exp);
    }
}
