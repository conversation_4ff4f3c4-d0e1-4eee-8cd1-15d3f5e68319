package dtt.web.aspect;


import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import dtt.web.config.util.HttpServletUtil;
import dtt.web.config.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.*;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Component
@Slf4j
public class ArgsLogInterceptor implements MethodInterceptor {

    public static String ARGS_LOG = "ARGS-LOG";
   // @Autowired
    private ArgsLogESRepository argsLogESRepository;
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {

        Date dtStart = new Date();

        ArgsDataWatch annoData =  null;


        annoData = getTargetAnno(invocation);

       // log.info("**annodata is {}", JSONUtil.toJsonStr(logargs));


        log.trace("Before method");//这里做你的before操作
        //logInput(invocation,annoData);

        Exception exp = null;
        Object result = null;
        try {
            result = invocation.proceed();
        }
        catch (Exception ex)
        {
            //logError(ex,invocation,annoData);
            exp = ex;
            throw ex;
        }
        finally {
            Date dtEnd = new Date();
            try {

                Map<String, String> headers = getHeaders();

                ArgsAsyncManager proxy = SpringContextUtil.getBean(ArgsAsyncManager.class);
                if (proxy != null) {
                    String url = getUrl();
                    proxy.asyncSendToKafka(invocation, annoData, url, headers, result, dtStart, dtEnd, exp);
                } else
                    log.error("{} 未获取到 ArgsAsyncManager bean实例来异步发送消息", invocation.getMethod().toGenericString());
            }catch(Exception ex)
            {
                log.error("asncSendToKafka error {}",ex.getMessage());
            }

//            ArgsLogData argsLogData =  buildArgsLogData(invocation,annoData,headers, result, dtStart,dtEnd ,exp);
//            logOutput(argsLogData ,annoData);
        }

        //logOutput(invocation,annoData,result);
        log.trace("After method");//这里做你的after操作
        return result;
    }

    public void sendLogData(MethodInvocation invocation,ArgsDataWatch annoData ,String url, Map<String,String> headers, Object result ,Date dtStart,Date dtEnd,Exception exp)
    {
        String methodname = invocation.getMethod().toGenericString();
        log.trace("{} begin sendLogData",methodname);
        ArgsLogData argsLogData =  buildArgsLogData(invocation,annoData,url, headers, result, dtStart,dtEnd ,exp);
        logOutput(argsLogData ,annoData);
        log.trace("{} end sendLogData",methodname);
    }

    private ArgsDataWatch getTargetAnno(MethodInvocation invocation) {
        try {
//            // 获取连接点签名的方法名
            Method method = invocation.getMethod();
//            String methodName = point.getSignature().getName();
//            //获取连接点参数
            Object[] args = invocation.getArguments();
//            Method method = ReflectUtil.getMethodOfObj(point.getTarget(), methodName, args);
            Method parentMethod = null;
            Class[] classes = Arrays.stream(args).map(Object::getClass).toArray(Class[]::new);
            parentMethod = ReflectUtil.getMethod(invocation.getClass().getClass().getSuperclass(), method.getName(), classes);

            ArgsDataWatch annotation = null;
            if (method != null) {
                annotation = method.getAnnotation(ArgsDataWatch.class);
            }
            if (annotation == null && parentMethod != null) {
                annotation = parentMethod.getAnnotation(ArgsDataWatch.class);
            }
            return annotation;
        } catch (Exception e) {
            return null;
        }
    }


//    private void logInput(MethodInvocation invocation,ArgsDataWatch annoData)
//    {
//        if(annoData.logInputArgs())
//        {
//            Object[] args =  invocation.getArguments();
//            log.info("****method {} , inputs are :{}" ,invocation.getMethod().toGenericString(), JSONUtil.toJsonStr(args));
//
//        }
//    }
//
//    private void logInput2(MethodInvocation invocation,ArgsDataWatch annoData)
//    {
//        if(annoData.logOutputArgs())
//        {
//            Object[] args =  invocation.getArguments();
//            log.info("******method {} , inputs are :{}**" ,invocation.getMethod().getName(), JSONUtil.toJsonStr(args));
//
//        }
//    }
//
//
//    private void logOutput(MethodInvocation invocation,ArgsDataWatch annoData, Object obj)
//    {
//        if(annoData.logOutputArgs())
//        {
//            Object oo = invokeMethod(obj,"getData",null);
//
//            log.info("****method {} , outputs are :{}" ,invocation.getMethod().getName(), JSONUtil.toJsonStr(oo));
//
//        }
//    }
//
//    private void logError(Throwable ex ,MethodInvocation invocation, ArgsDataWatch annoData)
//    {
//        log.error("****method {} ,error: {} ",invocation.getMethod().getName(),ex.toString() +ex.getMessage());
//    }

    private Object invokeMethod( Object instance, String methodName,Object[] args)
    {
        Object rt = instance;
        try {
            if(instance == null)
            {
                log.error("******methodname {} ,return obj is null",methodName);
                return rt;
            }

            //Class<?> clz = Class.forName(clsname);
            Class<?> clz = instance.getClass();
            //Object o = clz.newInstance();
            Method m = clz.getDeclaredMethod(methodName, null);
            if(m != null)
            {
                rt = m.invoke(instance,args);
            }
            else {
                log.error("*****methodname {} response is not ApiResult ,return response directly",methodName);
                rt = instance;
            }

        }
        catch(NoSuchMethodException ex)
        {
            log.error("*****methodname {} response is not ApiResult ,no getData method,return response directly",methodName);
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
        }

        return rt;
    }

    private boolean isTrue(int flagValue, int checkValue)
    {
        return ((flagValue & checkValue) > 0)?true:false;
    }

    private void logOutput(ArgsLogData logData , ArgsDataWatch argsDataWatch)
    {
        try
        {
            if(isTrue(argsDataWatch.storeFlags(), 1)) {
                log.info("{}, argsLogData is {}",logData.getFullMethodName(), JSONUtil.toJsonStr(logData));
            }
            if(isTrue(argsDataWatch.storeFlags(),2))
            {
                //存储到es

                log.info("{} *** begin store to ES",logData.getFullMethodName());
                //log.debug("store to es argsLogData is {}", JSONUtil.toJsonStr(logData));

                log.info(" kafkatemplat is {} ",kafkaTemplate);

                this.sendToKafka(ARGS_LOG,logData);

//                ArgsAsyncManager proxy =  SpringContextUtil.getBean(ArgsAsyncManager.class);
//                if(proxy != null)
//                {
//                    proxy.asyncSendToKafka(KafkaTopicConstants.ARGS_LOG,logData);
//                }
//                else
//                    log.error("{} 未获取到 ArgsAsyncManager bean实例来异步发送消息", logData.getFullMethodName());

                //sendToKafka(KafkaTopicConstants.ARGS_LOG,logData);
//                if(argsLogESRepository != null)
//                {
//                    log.info("{} save log to custom ES ",logData.getFullMethodName());
//                    argsLogESRepository.save(logData);
//                }
//                else
//                    log.error("{}, argsLogESRepository is null",logData.getFullMethodName());
            }
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
            logData.setOutputArgs(null);
            log.error("{},possibly hit error when save to ES ,so log to console ,argsLogData is {}",logData.getFullMethodName(), JSONUtil.toJsonStr(logData));
        }


    }
    private ArgsLogData buildArgsLogData(MethodInvocation invocation,ArgsDataWatch annoData ,String url, Map<String,String> headers, Object result ,Date dtStart,Date dtEnd,Exception exp)
    {

        ArgsLogData logData = new ArgsLogData();
        logData.setFullMethodName(invocation.getMethod().toGenericString());
        if(annoData.logInputArgs())
            logData.setInputArgs(JSONUtil.toJsonStr(invocation.getArguments()) );

        if(annoData.logOutputArgs()) {
            Object oo = invokeMethod(result, "getData", null);
            logData.setOutputArgs(JSONUtil.toJsonStr(oo));
        }
        logData.setMethodStartTime(dtStart);
        logData.setMethodEndTime(dtEnd);
        if(exp != null & annoData.logException())
        {
            logData.setErrorMessage(exp.getMessage());
            if(exp.getStackTrace()!= null && exp.getStackTrace().length>0)
            {

                logData.setStackTraceElements(exp.getStackTrace().toString());
            }
        }

        logData.setHeaders(JSONUtil.toJsonStr(headers) );

        logData.setRequestUrl(url);

        return logData;
    }

    private String getUrl()
    {
        String url = "";
        try {
            HttpServletRequest request= null;
            try {
                request = HttpServletUtil.getRequest();
            }
            catch (Exception ex)
            {
                log.debug("{}",ex.getMessage());

            }
            if(request != null) {
                url = request.getRequestURI().toString();
            }
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
        }
        return  url;
    }

    private Map<String,String> getHeaders()
    {
        Map<String,String> map = new HashMap<String, String>();

        try {
            HttpServletRequest request = HttpServletUtil.getRequest();
            if (request != null) {
                Enumeration headerNames = request.getHeaderNames();
                String key = null;
                String val = null;
                while (headerNames.hasMoreElements()) {
                    key = (String) headerNames.nextElement();
                    val = (String) request.getHeader(key);
                    map.put(key, val);


                }
            }
        }
        catch(Exception ex)
        {
            ex.printStackTrace();
        }

        return  map;


    }


    public void sendToKafka(String topic, final ArgsLogData argsLogData)
    {
        if(kafkaTemplate != null)
        {
            if(argsLogData != null)
            {

                ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(topic, JSONUtil.toJsonStr(argsLogData));
                future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {

                    //发送消息成功回调
                    @Override
                    public void onSuccess(SendResult<String, String> result) {
                        //System.out.println(result.getProducerRecord());
                        //getRecordMetadata里面存在发送的topic和partition等信息
                        //System.out.println(result.getRecordMetadata());
                        log.info("{} 参数日志发送成功", argsLogData.getFullMethodName());
                    }

                    //发送消息失败回调
                    @Override
                    public void onFailure(Throwable ex) {
                        ex.printStackTrace();
                        argsLogData.setOutputArgs(null);
                        log.error("{}, hit error when send to kafka ,so log to console ,argsLogData is {}",argsLogData.getFullMethodName(), JSONUtil.toJsonStr(argsLogData));
                    }


                });


            }
        }
        else
        {

            log.error("kafkaTemplate is null");
        }
    }

}
