package dtt.web.aspect;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.UuidUtils;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.Date;

@Data
@Document(indexName = "argslogdata", shards = 3)
public class ArgsLogData implements Serializable {

    private static final long serialVersionUID = 6461252326841221744L;

    @Id
    private String uuid;
    @Field(type = FieldType.Text)
    private String requestUrl;
    @Field(type = FieldType.Text)
    private String fullMethodName;

    @Field(type = FieldType.Text)
    private String inputArgs;
    @Field(type = FieldType.Text)
    private String outputArgs;

    @Field(type = FieldType.Text)
    private String headers;


    @Field(format = DateFormat.basic_date_time)
    private Date methodStartTime;
    @Field(format = DateFormat.basic_date_time)
    private Date methodEndTime;
    private String errorMessage;
    @Field(type = FieldType.Text)
    private String stackTraceElements;

    @Field(type = FieldType.Text)
    private String methodStartTimeStr;
    @Field(type = FieldType.Text)
    private String methodEndTimeStr;
    public String getMethodStartTimeStr()
    {
        methodStartTimeStr = DateUtil.format(methodStartTime,"yyyy-MM-dd hh:mm:ss");
        return methodStartTimeStr;
    }
    public String getMethodEndTimeStr()
    {
        methodEndTimeStr = DateUtil.format(methodEndTime, "yyyy-MM-dd hh:mm:ss");
        return methodEndTimeStr;

    }

    public String getUuid()
    {
        if(uuid == null || uuid.equals(""))
            uuid = UuidUtils.generateUuid();
        return uuid;
    }
}
