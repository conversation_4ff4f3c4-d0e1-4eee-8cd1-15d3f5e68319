package dtt.web.filter;



import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

@Component
@Slf4j
public class FeignInfoRequestFilter extends OncePerRequestFilter {

    @Override
	protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			FilterChain filterChain) throws ServletException, IOException {
//		String feignInfo = httpServletRequest.getHeader(CommonConstant.FEIGN_REQUEST_HEADER);
//		if (CharSequenceUtil.isNotBlank(feignInfo)) {
//			String decodeInfo = URLDecoder.decode(feignInfo, StandardCharsets.UTF_8);
//			if (JSONUtil.isJson(decodeInfo)) {
//				JSONObject jsonObj = JSONUtil.parseObj(decodeInfo);
//				CommonContextHolder.set(CommonConstant.OWNER_USER_ID, jsonObj.get(CommonConstant.OWNER_USER_ID));
//				CommonContextHolder.set(CommonConstant.OWNER_ORG_ID, jsonObj.get(CommonConstant.OWNER_ORG_ID));
//				CommonContextHolder.set(CommonConstant.OWNER_USER_NAME, jsonObj.get(CommonConstant.OWNER_USER_NAME));
//				CommonContextHolder.set(CommonConstant.OWNER_USER_STAFF_NAME, jsonObj.get(CommonConstant.OWNER_USER_STAFF_NAME));
//			}
//		}

		try {
			filterChain.doFilter(httpServletRequest, httpServletResponse);
		}catch(Exception e) {
//			log.error(StrUtil.format("filterChain.doFilter Error:[{}]{}",
//					httpServletRequest.getMethod(), httpServletRequest.getRequestURI()), e);
			throw e;
		}
	}
}
