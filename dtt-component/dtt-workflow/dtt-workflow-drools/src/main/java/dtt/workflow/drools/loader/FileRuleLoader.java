package dtt.workflow.drools.loader;

import dtt.workflow.drools.config.DroolsFileConfig;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.builder.model.KieModuleModel;
import org.kie.api.runtime.KieContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Import;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

@Import({DroolsFileConfig.class})
public class FileRuleLoader implements RuleLoader {

    @Autowired
    DroolsFileConfig droolsFileConfig;


    /**
     * loadKieContainer 加载文件系统规则加载器
     *
     * @return org.kie.api.runtime.KieContainer
     * <AUTHOR> <PERSON>ian<PERSON>
     * @date 2023-07-27
     */
    @Override
    public KieContainer loadKieContainer(KieServices kieServices) {
        try {

            KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
            // 获取所有.drl文件资源
            String pattern = droolsFileConfig.getPath() + "/*.drl";
            PathMatchingResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resourcePatternResolver.getResources(pattern);
            for (Resource resource : resources) {
                kieFileSystem.write(resource.getFile().getPath(), kieServices.getResources().newInputStreamResource(resource.getInputStream()));
            }
            KieModuleModel kieModuleModel = kieServices.newKieModuleModel();
            kieFileSystem.writeKModuleXML(kieModuleModel.toXML());
            KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
            kieBuilder.buildAll();
            KieModule kieModule = kieBuilder.getKieModule();
            KieContainer kieContainer = kieServices.newKieContainer(kieModule.getReleaseId());
            return kieContainer;
        } catch (Exception e) {
            throw new RuntimeException("loadKieContainer error");
        }
    }

}
