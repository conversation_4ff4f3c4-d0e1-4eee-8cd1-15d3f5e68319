<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>dtt.asset</groupId>
  <artifactId>dtt-message-queue-root</artifactId>
  <version>0.0.2.RELEASE</version>
  <packaging>pom</packaging>
  <name>dtt.message-queue root POM</name>
  <description>dtt.message-queue root project</description>
  <modules>
    <module>dtt-framework-rocketmq</module>
    <module>dtt-framework-rabbitmq</module>
    <module>dtt-framework-kafka</module>
    <module>dtt-framework-queue</module>
    <module>dtt-framework-mq</module>

  </modules>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.2.2.RELEASE</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
