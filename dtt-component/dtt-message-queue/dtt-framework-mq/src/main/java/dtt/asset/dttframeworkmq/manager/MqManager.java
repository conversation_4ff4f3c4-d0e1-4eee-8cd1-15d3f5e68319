package dtt.asset.dttframeworkmq.manager;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import dtt.asset.dttframeworkmq.config.MqConfig;
import dtt.asset.dttframeworkmq.config.MqConfiguration;
import dtt.asset.dttframeworkmq.config.constant.enums.MqStatusEnum;
import dtt.asset.dttframeworkmq.entity.MqSend;
import dtt.asset.dttframeworkmq.mapper.MqSendMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class MqManager implements ApplicationContextAware {
    @Autowired
    RocketMQTemplate rocketMQTemplate;

    @Autowired
    private MqSendMapper mqSendMapper;

    @Autowired
    private MqConfiguration mqConfiguration;

    private volatile MqManager thisProxy;

    private ApplicationContext applicationContext = null;

    /**
     * @param topic
     * @param data
     */
    @Transactional
    public void sendTopic(String topic, Object data) {
        sendTopic(topic, data, false);
    }

    /**
     * @param topic
     * @param data
     * @param async
     */
    @Transactional
    public void sendTopic(String topic, Object data, boolean async) {
        String message = JSONUtil.toJsonStr(data);
        //先存db，即使后续发生未知异常，也能重试
        Long id = saveMq(topic, message);
        // 判断是否开启了事务
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            // 将操作注册到“afterCommit”(事务提交之后)
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                // 事务提交后执行回调
                @Override
                public void afterCommit() {
                    if (async) {
                        getCurrentProxy().asyncSendTopic(id, topic, message);
                    } else {
                        sendTopic(id, topic, null, message);
                    }
                }
            });
        } else {
            // 没有开启事务的直接执行(因为在本方法添加了@Transactional注解，其实不存在没有开启事务的情况)
            if (async) {
                getCurrentProxy().asyncSendTopic(id, topic, message);
            } else {
                sendTopic(id, topic, null, message);
            }
        }
    }

    /**
     * 按分区生产消息
     * 使用此方法要注意如下几点：
     *      1）如果要保证DB和mq发送的一致性，则外层的DB操作必须要有事务包裹
     *      2）此处不需要@Transactional注解，因为如果外层有事务则会传播到内层，如果外层没事务，只在内层加事务也达不到内外层数据一致的要求；
     *          另外，有些场景不要求数据强一致，允许消息丢失，可以不保存消息
     * @param topic
     * @param key
     * @param data
     */
//    @Transactional
    public void sendTopic(String topic, String key, Object data) {
        Assert.notBlank(topic, "MqManager.sendTopic，topic不能为空");
        Assert.notNull(data, "MqManager.sendTopic，data不能为空");
        String message = JSONUtil.toJsonStr(data);
        //先存db，即使后续发生未知异常，也能重试
        Long id = saveMq(topic, key, message);
        log.debug("保存MQ信息成功，topic={}，key={}, message={}", topic, key, message);

        // 判断是否开启了事务
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            // 将操作注册到“afterCommit”(事务提交之后)
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                // 事务提交后执行回调
                @Override
                public void afterCommit() {
                    sendTopic(id, topic, key, message);
                }
            });
        } else {
            // 如果外层没有事务，不要求数据一致性，则直接sendTopic
            sendTopic(id, topic, key, message);
        }
    }

    /**
     * 用spring线程池异步发送MQ，其实kafkaTemplate.send本身就是异步的
     * @param id
     * @param topic
     * @param message
     */
    @Async(MqConfig.EXECUTOR_NAME)
    void asyncSendTopic(Long id, String topic, String message) {
        sendTopic(id, topic, null, message);
    }

    /**
     * 真正执行kafkaTemplate.send动作，key为null则相当于执行kafkaTemplate.send(topic, message)
     * @param id
     * @param topic
     * @param key
     * @param message
     */
    private void sendTopic(Long id, String topic, String key, String message) {
        if (log.isDebugEnabled()) {
            log.debug("发送MQ开始，topic={}，key={}, message={}", topic, key, message);
        } else {
            log.info("发送MQ开始，topic={}", topic);
        }
        SendCallback sendCallback = new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("发送MQ成功，topic={}", topic);
                mqSendMapper.deleteById(id);
            }
            @Override
            public void onException(Throwable throwable) {
                log.error("发送MQ失败，topic={}", topic);
            }
        };
        rocketMQTemplate.asyncSend(topic, message,sendCallback);
        CompletableFuture.completedFuture(sendCallback);

    }

    /**
     * 拉取投递失败的消息, 重新投递
     */
//    @XxlJob("resendJobHandler")
    public void resend() {
        log.info("重新投递消息任务开始");

        // 获取超过重试时间的待投递消息
        List<MqSend> mqList = mqSendMapper.selectTimeoutMq();
        while (!CollectionUtils.isEmpty(mqList)) {
            mqList.forEach(mq -> {
                Long id = mq.getId();
                try {
                    if (mq.getRetryCount() >= mqConfiguration.getMaxCount()) {
                        updateMqStatus(id, MqStatusEnum.DELIVER_FAIL);
                        log.info("超过最大重试次数, 消息投递失败, id={}", id);
                    } else {
                        Date currentTime = new Date();
                        mqSendMapper.updateRetryCount(id, caluRetryTime(currentTime)); // 投递次数+1

                        // 重新投递
                        sendTopic(mq.getId(), mq.getTopic(), mq.getPartitionKey(), mq.getMessage());
                    }
                } catch (Exception e) {
                    log.error("MQ消息重新投递异常，id={}，errorMsg={}", id, e.getMessage());
                }
            });
            mqList = mqSendMapper.selectTimeoutMq();
        }
        log.info("重新投递消息任务结束");
    }

//    private String composeAlertContent(List<MqSend> failedMessages) {
//        if(CollectionUtils.isEmpty(failedMessages)){
//            return "";
//        }
//        StringBuilder sb = new StringBuilder("Total failed message count has been up to: ");
//        sb.append(failedMessages.size()).append("\n");
//        sb.append(JSONUtil.toJsonPrettyStr(failedMessages));
//        return sb.toString();
//    }

//    private boolean checkIfMeetAlertCondition(List<MqSend> failedMessages) {
//        if(CollectionUtils.isEmpty(failedMessages)){
//            return false;
//        }
//        int count = failedMessages.size();
//        if (count < mqConfiguration.getFailedMessageThreshold()) {
//            return false;
//        }
//        log.debug("faileMessagesCount {} is larger than failedMessageThreshold {}", count, mqConfiguration.getFailedMessageThreshold());
//        return true;
//    }

    /**
     * 保存MQ消息
     */
    private Long saveMq(String topic, String message) {
        return saveMq(topic, null, message);
    }

    /**
     * 保存MQ消息
     */
    public Long saveMq(String topic, String key, String message) {
        Date currentTime = new Date();
        MqSend entity = new MqSend();
        entity.setRetryCount(0);
        entity.setRetryTime(caluRetryTime(currentTime));
        entity.setStatus(MqStatusEnum.DELIVER_INIT.getStatus());
        entity.setTopic(topic);
        entity.setPartitionKey(key);
        entity.setMessage(message);
        entity.setCreateTime(currentTime);
        entity.setUpdateTime(currentTime);
        mqSendMapper.insert(entity);
        return entity.getId();
    }

    /**
     * 更新MQ消息状态
     */
    private void updateMqStatus(Long id, MqStatusEnum statusEnum) {
        MqSend entity = new MqSend();
        entity.setId(id);
        entity.setStatus(statusEnum.getStatus());
        entity.setUpdateTime(new Date());
        mqSendMapper.updateById(entity);
    }

    private Date caluRetryTime(Date preTime) {
        return DateUtils.addMinutes(preTime, mqConfiguration.getInterval());
    }

    private MqManager getCurrentProxy() {
        if (thisProxy != null) {
            return thisProxy;
        }

        synchronized (this) {
            if (thisProxy != null) {
                return thisProxy;
            }
            thisProxy = applicationContext.getBean(MqManager.class);
        }
        return thisProxy;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}