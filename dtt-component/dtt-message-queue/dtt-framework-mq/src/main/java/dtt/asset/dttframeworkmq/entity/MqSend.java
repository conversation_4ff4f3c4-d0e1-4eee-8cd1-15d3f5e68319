package dtt.asset.dttframeworkmq.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> Hongyi
 * @Date 2021-6-3
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class MqSend implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主题
     */
    private String topic;

    /**
     * 分区key
     */
    private String partitionKey;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 重试时间
     */
    private Date retryTime;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
