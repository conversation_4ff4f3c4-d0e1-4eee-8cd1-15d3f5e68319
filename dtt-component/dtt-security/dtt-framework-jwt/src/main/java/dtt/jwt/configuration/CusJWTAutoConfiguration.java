package dtt.jwt.configuration;

import dtt.jwt.service.JWTService;
import dtt.jwt.service.impl.JWTServiceImpl;
import dtt.secret.config.PropertyPreparedListener;
import dtt.secret.service.SecretService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

@Configuration
@EnableConfigurationProperties(CusJWTAutoConfiguration.RSAConfigProperties.class)
@ConditionalOnProperty(value = "spring.jwt.enabled", havingValue = "true")
public class CusJWTAutoConfiguration {

    @ConditionalOnMissingBean
    @Bean
    public JWTService getJWTService(RSAConfigProperties properties) throws NoSuchAlgorithmException, InvalidKeySpecException {
        SecretService secretService = PropertyPreparedListener.secretService;
        return new JWTServiceImpl(properties,secretService);
    }

    @Setter
    @Getter
    @ConfigurationProperties(prefix = "spring.jwt")
    public static class RSAConfigProperties {
        /**
         *是否启用
         */
        private boolean enabled = false;
        /**
         * RSA私钥 base64字符串
         */
        private String privateKey;
        /**
         * RSA公钥 base64字符串
         */
        private String publicKey;
        /**
         * RSA算法
         */
        private String algorithm="RSA";
        /**
         * 从云厂商KMS服务读取密钥
         */
        /**
         * 从云厂商KMS服务读取密钥，RSA私钥名称
         */
        private String privateKeyKMSName;
        /**
         * 从云厂商KMS服务读取密钥，RSA公钥名称
         */
        private String publicKeyKMSName;
        /**
         * 从云厂商KMS服务读取密钥，RSA算法名称
         */
        private String algorithmKMSName="RSA";
    }


}
