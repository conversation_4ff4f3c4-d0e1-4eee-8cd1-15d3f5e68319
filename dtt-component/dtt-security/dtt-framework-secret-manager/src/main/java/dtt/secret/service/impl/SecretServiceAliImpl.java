package dtt.secret.service.impl;

import cn.hutool.core.io.FileUtil;
import com.aliyuncs.auth.AlibabaCloudCredentialsProvider;
import com.aliyuncs.kms.secretsmanager.client.SecretCacheClient;
import com.aliyuncs.kms.secretsmanager.client.SecretCacheClientBuilder;
import com.aliyuncs.kms.secretsmanager.client.exception.CacheSecretException;
import com.aliyuncs.kms.secretsmanager.client.model.SecretInfo;
import com.aliyuncs.kms.secretsmanager.client.service.BaseSecretManagerClientBuilder;
import com.aliyuncs.kms.secretsmanager.client.utils.CredentialsProviderUtils;
import dtt.secret.config.SecretProperty;
import dtt.secret.service.SecretService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * sdk文档地址:
 * https://help.aliyun.com/document_detail/28956.html
 *
 * https://github.com/aliyun/alibabacloud-secretsmanager-client-java
 * https://github.com/aliyun/alibabacloud-secretsmanager-client-java/blob/master/README.zh-cn.md
 *
 */
@Slf4j
public class SecretServiceAliImpl implements SecretService {

    private SecretProperty secretProperty;
    private SecretCacheClient secretCacheClient;

    public SecretServiceAliImpl(SecretProperty secretProperty) throws CacheSecretException, IOException {
        this.secretProperty = secretProperty;
        secretCacheClient = getSecretCacheClient();
    }

    @Override
    public String getParameter(String name) {
        try{
            SecretInfo secretInfo = secretCacheClient.getSecretInfo(name);
            log.debug("Ali Parameter[" + name + "]:" +secretInfo.getSecretValue());
            return secretInfo.getSecretValue();
        }catch(Exception e){
            //throw new RuntimeException("AWS Parameter Not Found!");
            log.error("Ali Parameter[" + name + "] Error!", e);
            return null;
        }
    }

    /**
     * https://github.com/aliyun/alibabacloud-secretsmanager-client-java/blob/master/README_config.zh-cn.md
     * 1、采用阿里云AK SK作为访问鉴权方式
     * # 访问凭据类型
     * credentials_type=ak
     * # AK
     * credentials_access_key_id=#access key id#
     * # SK
     * credentials_access_secret=#access key secret#
     * # 关联的KMS服务地域
     * cache_client_region_id=[{"regionId":"#regionId#"}]
     *
     * 2、采用STS作为访问鉴权方式
     * # 访问凭据类型
     * credentials_type=sts
     * # 角色名称
     * credentials_role_session_name=#role name#
     * # 资源短名称
     * credentials_role_arn=#role arn#
     * # AK
     * credentials_access_key_id=#access key id#
     * # SK
     * credentials_access_secret=#access key secret#
     * # 关联的KMS服务地域
     * cache_client_region_id=[{"regionId":"#regionId#"}]
     *
     * 3、采用阿里云ECS Ram Role作为访问鉴权方式
     *# 访问凭据类型
     * credentials_type=ram_role
     * # 角色名称
     * credentials_role_session_name=#role name#
     * # 资源短名称
     * credentials_role_arn=#role arn#
     * # AK
     * credentials_access_key_id=#access key id#
     * # SK
     * credentials_access_secret=#access key secret#
     * # 关联的KMS服务地域
     * cache_client_region_id=[{"regionId":"#regionId#"}]
     *
     * 4、采用阿里云ECS Ram Role作为访问鉴权方式
     * # 访问凭据类型
     * credentials_type=ecs_ram_role
     * # ECS RAM Role名称
     * credentials_role_name=#credentials_role_name#
     * # 关联的KMS服务地域
     * cache_client_region_id=[{"regionId":"#regionId#"}]
     *
     * 5、采用阿里云Client Key作为访问鉴权方式
     * # 访问凭据类型
     * credentials_type=client_key
     *
     * # 读取client key的解密密码：支持从环境变量或者文件读取
     * client_key_password_from_env_variable=#your client key private key password environment variable name#
     * client_key_password_from_file_path=#your client key private key password file path#
     *
     * # Client Key私钥文件路径
     * client_key_private_key_path=#your client key private key file path#
     *
     * # 关联的KMS服务地域
     * cache_client_region_id=[{"regionId":"#regionId#"}]
     *
     */
    private SecretCacheClient getSecretCacheClient() throws CacheSecretException, IOException {
        String type = secretProperty.getPropertyAsString(SecretProperty.AL_CREDENTIALS_TYPE,"ecs_ram_role");
        String accessKeyId = secretProperty.getPropertyAsString(SecretProperty.AL_ACCESS_KEY);
        String accessKeySecret = secretProperty.getPropertyAsString(SecretProperty.AL_SECRET_KEY);
        String regionId = secretProperty.getPropertyAsString(SecretProperty.AL_REGION);
        String roleSessionName = secretProperty.getPropertyAsString(SecretProperty.AL_ROLE_NAME);
        String roleArn = secretProperty.getPropertyAsString(SecretProperty.AL_ROLE_ARN);
        String policy = secretProperty.getPropertyAsString(SecretProperty.AL_POLICY);
        String clientKeyPath = secretProperty.getPropertyAsString(SecretProperty.AL_CLIENT_KEY_FILE_PATH);
        String passPhrase = secretProperty.getPropertyAsString(SecretProperty.AL_PASS_PHRASE);
        String passPhrasePath = secretProperty.getPropertyAsString(SecretProperty.AL_PASS_PHRASE_FILE_PATH);

        AlibabaCloudCredentialsProvider provider = null;
        switch (type){
            case "ak": {
                provider = CredentialsProviderUtils.withAccessKey(accessKeyId, accessKeySecret);
                break;
            }
            case "sts":
            case "ram_role":{
                provider = CredentialsProviderUtils.withRamRoleArnOrSts(accessKeyId,accessKeySecret,regionId.split(",")[0],roleSessionName,roleArn,policy);
                break;
            }
            case "ecs_ram_role":{
                provider = CredentialsProviderUtils.withEcsRamRole(roleSessionName);
                break;
            }
            case "client_key":{
                if( StringUtils.isBlank(passPhrase) && StringUtils.isNotBlank(passPhrasePath) ){
                    passPhrase = FileUtil.getReader(new File(passPhrasePath), StandardCharsets.UTF_8).readLine();
                }
                provider = CredentialsProviderUtils.getCredentialsProvider(clientKeyPath,passPhrase);
                break;
            }
            default:{
                throw new RuntimeException("spring.secret.ali.type : "+ type+" not support");
            }
        }
        return SecretCacheClientBuilder.newCacheClientBuilder(
                BaseSecretManagerClientBuilder.standard()
                .withCredentialsProvider(provider)
                .withRegion(regionId.split(",")).build()).build();
    }
}
