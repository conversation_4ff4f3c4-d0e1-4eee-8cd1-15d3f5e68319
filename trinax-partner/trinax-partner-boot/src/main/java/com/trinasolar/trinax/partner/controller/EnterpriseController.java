package com.trinasolar.trinax.partner.controller;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseBizMapperService;
import com.trinasolar.trinax.partner.domain.contract.service.*;
import com.trinasolar.trinax.partner.dto.input.*;
import com.trinasolar.trinax.partner.dto.input.address.EnterpriseAddressQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseSaveReqDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseSearchReqDTO;
import com.trinasolar.trinax.partner.dto.input.statistic.StatisticEnterpriseNumReqDTO;
import com.trinasolar.trinax.partner.dto.output.*;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRespDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseSearchResDTO;
import com.trinasolar.trinax.partner.dto.output.statistic.StatisticEnterpriseNumRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

import static com.trinasolar.trinax.common.dto.output.Result.ok;


@RestController
@Slf4j
@Tag(name = "企业信息接口")
public class EnterpriseController implements EnterpriseFeign {

    @Autowired
    private EnterpriseService enterpriseService;
    @Autowired
    private EnterpriseAddressService enterpriseAddressService;
    @Autowired
    private EnterpriseAccountService enterpriseAccountService;
    @Autowired
    private MarketingTaxService marketingTaxService;

    @Autowired
    private EnterpriseBizRelationService enterpriseBizRelationService;

    @Autowired
    private EnterpriseBizMapperService enterpriseBizMapperService;

    @Override
    public Result<List<EnterpriseDTO>> listByQuery(EnterpriseQueryDTO queryDTO) {
        return Result.ok(enterpriseService.listByQuery(queryDTO));
    }

    @Override
    public Result<PageResponse<EnterpriseDTO>> pageByQuery(PageRequest<EnterpriseQueryDTO> pageReqDTO) {
        return Result.ok(enterpriseService.pageByQuery(pageReqDTO));
    }

    @Override
    public Result<List<EnterpriseInfoDTO>> getEnterpriseInfoBySales(String userId) {
        return Result.ok(enterpriseService.getEnterpriseInfoBySales(userId));
    }

    @Override
    public Result<List<EnterpriseInfoDTO>> getEnterpriseInfoByDealer(String userId) {
        return Result.ok(enterpriseService.getEnterpriseInfoByDealer(userId));
    }

    @Override
    public EnterpriseBriefDTO getEnterpriseBriefByEnterpriseId(String enterpriseId) {
        return enterpriseService.getEnterpriseByEnterpriseId(enterpriseId);
    }

    @Override
    public Result<List<EnterpriseBriefDTO>> getEnterpriseBriefByEnterpriseIdList(List<String> enterpriseIds) {
        List<EnterpriseBriefDTO> enterprises = enterpriseService.getEnterpriseByEnterpriseIds(enterpriseIds);
        return ok(enterprises);
    }

    @Override
    public Result<List<EnterpriseBriefDTO>> getAllEnterpriseBriefs() {
        return ok(enterpriseService.getAllEnterpriseBriefs());
    }

    @Override
    public Result<List<EnterpriseBriefDTO>> listWithCustomerCategory(String customerCategory) {
        return ok(enterpriseService.listWithCustomerCategory(customerCategory));
    }

    @Override
    public Result<List<EnterpriseBriefDTO>> listEnterpriseEditAndAddPc() {
        return ok(enterpriseService.listEnterpriseEditAndAddPc());
    }

    @Override
    public Result<List<EnterpriseBriefDTO>> questionnaireEnterpriseEditAndAdd(String name) {
        return ok(enterpriseService.questionnaireEnterpriseEditAndAdd(name));
    }

    @Override
    public EnterpriseDTO getEnterpriseByEnterpriseId(String enterpriseId) {
        return enterpriseService.getEnterpriseDTO(enterpriseId);
    }

    @Override
    public EnterpriseAccessDetailDTO getEnterpriseAccess(String enterpriseId) {
        return enterpriseService.getEnterpriseAccess(enterpriseId);
    }

    @Override
    public Result<List<EnterpriseAddressRespDTO>> getEnterpriseAddressList(EnterpriseAddressQueryReqDTO reqDTO) {
        return enterpriseAddressService.getEnterpriseAddressList(reqDTO);
    }

    @Override
    public Result<List<EnterpriseAddressRespDTO>> getEnterpriseDetailAddressList(EnterpriseAddressQueryReqDTO reqDTO) {
        return enterpriseAddressService.getEnterpriseDetailAddressList(reqDTO);
    }

    @Override
    public Result<EnterpriseAddressRespDTO> getEnterpriseAddressByAddressId(String addressId) {
        return enterpriseAddressService.getEnterpriseAddressByAddressId(addressId);
    }

    @Override
    public Result<List<EnterpriseAddressRespDTO>> getAddressListByAddressIdList(List<String> addressIdList) {
        return enterpriseAddressService.getAddressListByAddressIdList(addressIdList);
    }

    @Override
    public Result<String> saveEnterpriseAddress(@Valid EnterpriseAddressSaveReqDTO reqDTO) {
        return enterpriseAddressService.saveEnterpriseAddress(reqDTO);
    }

    @Override
    public Result<String> updateEnterpriseAddress(@Valid EnterpriseAddressUpdateReqDTO reqDTO) {
        return enterpriseAddressService.updateEnterpriseAddress(reqDTO);
    }

    @Override
    public Result<String> deleteEnterpriseAddress(EnterpriseAddressDeleteReqDTO deleteReqDTO) {
        return Result.ok(enterpriseAddressService.deleteEnterpriseAddress(deleteReqDTO));
    }

    @Override
    public Result<EnterpriseAccountRespDTO> getEnterpriseAccountList(String enterpriseId) {
        return Result.ok(enterpriseAccountService.getEnterpriseAccountList(enterpriseId));
    }

    @Override
    public Result<String> getEnterpriseTaxId(String accountId) {
        return Result.ok(enterpriseAccountService.getEnterpriseTaxId(accountId));
    }

    @Override
    public Result<MarketingTaxRespDTO> getMarketingTaxByTaxId(String taxId) {
        return Result.ok(marketingTaxService.getMarketingTaxByTaxId(taxId));
    }

    @Override
    public Result<List<EnterpriseBizRelationResDTO>> getEnterpriseBizRelationByEnterpriseId(String enterpriseId) {
        return Result.ok(enterpriseBizRelationService.getEnterpriseBizRelationByEnterpriseId(enterpriseId));
    }

    @Override
    public Result<List<EnterpriseDTO>> getLinkEnterprise(LinkEnterpriseQueryReqDTO reqDTO) {
        return Result.ok(enterpriseService.getLinkEnterprise(reqDTO));
    }

    @Override
    public Result<List<EnterpriseDTO>> getLinkEnterpriseUnion(LinkEnterpriseUnionQueryReqDTO reqDTO) {
        return Result.ok(enterpriseService.getLinkEnterpriseUnion(reqDTO));
    }

    @Override
    public Result<EnterpriseDTO> getEnterpriseBySfId(String sfId) {
        return Result.ok(enterpriseService.getEnterpriseBySfId(sfId));
    }

    @Override
    public Result<List<EnterpriseDTO>> listByEnterpriseIds(List<String> enterpriseIds) {
        if (ObjectUtil.isEmpty(enterpriseIds)) {
            return Result.ok(Collections.emptyList());
        }
        return Result.ok(enterpriseService.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(enterpriseIds).build()));
    }

    @Override
    public Result<PageResponse<EnterpriseResPcDTO>> pageEnterpriseResPC(PageRequest<EnterpriseQueryReqDTO> reqDTO) {
        return Result.ok(enterpriseService.pageEnterpriseResPC(reqDTO));
    }

    @Override
    public Result<List<EnterpriseResPcExcelDTO>> listEnterpriseResPcExcel(EnterpriseQueryReqDTO reqDTO) {
        return Result.ok(enterpriseService.listEnterpriseResPcExcel(reqDTO));
    }

    @Override
    public Result<EnterpriseMainSalesDTO> getEnterpriseForMainSales(String enterpriseId, String userId) {
        return Result.ok(enterpriseService.getEnterpriseForMainSales(enterpriseId, userId));
    }

    @Override
    public Result<String> syncEnterprise(EnterpriseSaveReqDTO req) {
        return enterpriseService.syncEnterprise(req);
    }

    @Override
    public Result<Void> updateMainSales(AllocateMainSalesDTO allocateMainSalesDTO) {
        enterpriseService.updateMainSales(allocateMainSalesDTO);
        return Result.ok();
    }

    @Override
    public Result<Void> allocateMainSales(AllocateMainSalesDTO allocateMainSalesDTO) {
        enterpriseService.allocateMainSales(allocateMainSalesDTO);
        return Result.ok();
    }

    @Override
    public Result<EnterprisePcDetailDTO> pcEnterpriseByEnterpriseId(String enterpriseId) {
        return Result.ok(enterpriseService.pcEnterpriseByEnterpriseId(enterpriseId));
    }

    @Override
    public Result<PageResponse<EnterpriseSearchResDTO>> searchEnterprise(EnterpriseSearchReqDTO req) {
        return enterpriseService.searchEnterprise(req);
    }

    @Override
    public Result<List<String>> listByMainSalesId(String mainSalesUserId) {
        return Result.ok(enterpriseService.listByMainSalesId(mainSalesUserId));
    }

    @Override
    public Result<PageResponse<EnterpriseCapitalResPcDTO>> pageEnterpriseCapitalResPC(PageRequest<EnterpriseCapitalQueryReqDTO> pageReqDTO) {
        return Result.ok(enterpriseService.pageEnterpriseCapitalResPC(pageReqDTO));
    }

    @Override
    public Result<List<String>> qryRelCo(String enterpriseId) {
        return Result.ok(enterpriseService.qryRelCo(enterpriseId));
    }

    @Override
    public Result<StatisticEnterpriseNumRespDTO> statisticEnterpriseNum(StatisticEnterpriseNumReqDTO reqDTO) {
        return Result.ok(enterpriseService.statisticEnterpriseNum(reqDTO));
    }

    @Override
    public List<String> listEnterpriseIdsByCustomerCategory(String customerCategory) {
        return enterpriseBizMapperService.listEnterpriseIdsByCustomerCategory(customerCategory);
    }

    @Override
    public Result<Void> updateEnterpriseApply(EnterpriseApplyReqDTO reqDTO) {
        enterpriseService.updateEnterpriseApply(reqDTO);
        return Result.ok();
    }

    @Override
    public Result<List<EnterpriseDTO>> queryByLinkId(EnterpriseQueryDTO queryDTO) {
        return Result.ok(enterpriseService.getEnterpriseListByLinkId(queryDTO));
    }
}
