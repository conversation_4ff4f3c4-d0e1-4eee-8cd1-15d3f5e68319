package com.trinasolar.trinax.partner.controller;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.api.EnterpriseAddressFeign;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseAddressService;
import com.trinasolar.trinax.partner.dto.input.address.CoAddressQryReqDTO;
import com.trinasolar.trinax.partner.dto.input.address.EnterpriseAddressQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRegRespDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRespDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "企业地址信息接口")
public class EnterpriseAddressController implements EnterpriseAddressFeign {

    private final EnterpriseAddressService enterpriseAddressService;

    @Override
    public List<EnterpriseAddressRegRespDTO> listRegistByEnterpriseIds(List<String> enterpriseIds) {
        return enterpriseAddressService.listRegistByEnterpriseIds(enterpriseIds);
    }

    @Override
    public EnterpriseAddressRespDTO qryAddressByAddressId(CoAddressQryReqDTO req) {
        return enterpriseAddressService.qryAddressByAddressId(req);
    }

    @Override
    public Result<List<EnterpriseAddressRespDTO>> getBillingAddressByEnterpriseIdAndUser(EnterpriseAddressQueryReqDTO req) {
        return Result.ok(enterpriseAddressService.getBillingAddressByEnterpriseIdAndUser(req));
    }
}
