package com.trinasolar.trinax.partner.controller;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.api.EnterpriseBizFeign;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseBizService;
import com.trinasolar.trinax.partner.dto.input.EnterpriseBizAuthorizeQueryDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseBizQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseChangeOwnerReqDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.*;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizAuthorizeDTO;
import com.trinasolar.trinax.partner.dto.output.QuickOrderAuthDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseBusinessPCResDTO;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@Slf4j
@Tag(name = "客户业务信息接口")
public class EnterpriseBizController implements EnterpriseBizFeign {

    private final EnterpriseBizService enterpriseBizService;


    @Override
    public Result<String> saveEnterpriseBusiness(EnterpriseBizSaveReqDTO req) {
        return enterpriseBizService.saveEnterpriseBusiness(req);
    }

    @Override
    public Result<List<EnterpriseBizAuthorizeDTO>> queryBizAuthorizeList(EnterpriseBizAuthDTO req) {
        return enterpriseBizService.queryEnterpriseAuthorizeList(req.getEnterpriseId(), req.getAuthYear(), req.getAuthUserId(), req.getAuthStatus());
    }

    @Override
    public Result<PageResponse<EnterpriseBizAuthorizeDTO>> queryAuthorizeByPage(PageRequest<EnterpriseBizAuthorizeQueryDTO> pageReqDTO) {
        return Result.ok(enterpriseBizService.queryAuthorizeByPage(pageReqDTO));
    }

    @Override
    public Result<Boolean> checkAuthorizeDateRemind() {
        return enterpriseBizService.checkAuthorizeDateRemind();
    }

    @Override
    public Result<String> getAdmissionTemplate() {
        return enterpriseBizService.getAdmissionTemplate();
    }

    @Override
    public Result<EnterpriseBizAuthorizeDTO> queryBizAuthorizeDetail(EnterpriseBizAuthDTO req) {
        return enterpriseBizService.queryEnterpriseAuthorizeDetail(req.getId());
    }

    @Override
    public Result<String> saveOrUpdateAuthorize(EnterpriseBizAuthDTO req) {
        return enterpriseBizService.saveOrUpdateAuthorize(req);
    }

    @Override
    public Result<String> modifyEnterpriseBusiness(EnterpriseBizSaveReqDTO req) {
        return enterpriseBizService.modifyEnterpriseBusiness(req);
    }

    @Override
    public Result<EnterpriseBusinessPCResDTO> qryCoBusinessByCoId(String enterpriseId) {
        return enterpriseBizService.qryCoBusinessByCoId(enterpriseId);
    }

    @Override
    public Result<String> syncBusinessBuild(SyncBusinessBuildReqDTO req) {
        return enterpriseBizService.syncBusinessBuild(req);
    }

    @Override
    public Result<String> changeCustomerCategory(ChangeCustomerCategoryReqDTO req) {
        return enterpriseBizService.changeCustomerCategory(req);
    }

    @Override
    public Result<String> updateCoInfo(ReceiveCoInfoReqDTO req) {
        return enterpriseBizService.updateCoInfo(req);
    }

    @Override
    public Result<Boolean> verifyCoInfo(String enterpriseId) {
        return enterpriseBizService.verifyCoInfo(enterpriseId);
    }

    @Override
    public Result<String> admissionSuccess(String enterpriseId, String admissionStatus) {
        return enterpriseBizService.admissionSuccess(enterpriseId, admissionStatus);
    }

    @Override
    public Result<String> enterpriseChangeOwner(EnterpriseChangeOwnerReqDTO reqDTO) {
        return enterpriseBizService.enterpriseChangeOwner(reqDTO);
    }

    @Override
    public Result<QuickOrderAuthDTO> canQuickOrder(@RequestParam(name = "userId") String userId){
        return Result.ok(enterpriseBizService.canQuickOrder(userId));
    }

    @Override
    public Result<List<EnterpriseBusinessPCResDTO>> queryEnterpriseBizList(EnterpriseBizQueryReqDTO reqDTO) {
        return Result.ok(enterpriseBizService.queryEnterpriseBizList(reqDTO));
    }

}
