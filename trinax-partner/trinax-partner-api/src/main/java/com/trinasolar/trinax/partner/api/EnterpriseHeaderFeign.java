package com.trinasolar.trinax.partner.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.constants.ServiceIds;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderSaveOrUpdateReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseHeaderQueryResDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;

@FeignClient(value = ServiceIds.PARTNER_SERVICE)
public interface EnterpriseHeaderFeign {

    @Operation(summary = "外部用户查询企业抬头列表")
    @PostMapping("/enterprise-header/authPassedEnterpriseHeaderByDealer")
    Result<List<EnterpriseHeaderQueryResDTO>> authPassedEnterpriseHeaderByDealer(@RequestBody EnterpriseHeaderQueryReqDTO reqDTO);

    @Operation(summary = "新增/编辑企业抬头信息")
    @PostMapping("/enterprise-header/saveOrUpdateEnterpriseHeader")
    Result<String> saveOrUpdateEnterpriseAddress(@RequestBody EnterpriseHeaderSaveOrUpdateReqDTO reqDTO);

    @Operation(summary = "通过id删除企业抬头")
    @DeleteMapping("/enterprise-header/removeById")
    Result<String> removeById(@NotBlank(message = "id不为空")
                              @RequestParam Long id);

    @Operation(summary = "通过id获取企业抬头详情")
    @GetMapping("/enterprise-header/getDetailByHeaderId")
    Result<EnterpriseHeaderQueryResDTO> getDetailByHeaderId(@NotBlank(message = "id不为空")
                              @RequestParam String headerId);

}
