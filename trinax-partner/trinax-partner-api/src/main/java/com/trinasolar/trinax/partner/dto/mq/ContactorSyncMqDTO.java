package com.trinasolar.trinax.partner.dto.mq;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
@Schema(description = "联系人同步 Mq")
public class ContactorSyncMqDTO {

    @Schema(description = "企业 id 方式1、2必传")
    private String enterpriseId;

    @Schema(description = "同步方式 1：enterpriseId下的所有联系人全量同步 " +
            "2：enterpriseId+传入联系人更新 " +
            "3：以联系人的方式 更新所有企业")
    private Integer syncType;

    @Schema(description = "方式2必传")
    private List<String> userIds;

    @Schema(description = "方式3必传")
    private String userId;

    @Schema(description = "发起人")
    private String createdBy;

    @Schema(description = "发起人名字")
    private String createdName;
}
