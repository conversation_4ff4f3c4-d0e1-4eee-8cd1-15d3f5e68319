package com.trinasolar.trinax.partner.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum BusinessLineEnum {

    TRACKER("Tracker","支架"),
    MODULE("Module","组件"),

    ;

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 描述说明
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (BusinessLineEnum value : BusinessLineEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
