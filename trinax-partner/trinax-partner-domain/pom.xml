<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.trinasolar</groupId>
		<artifactId>trinax-partner-root</artifactId>
		<version>0.0.4-SNAPSHOT</version>
	</parent>

	<packaging>jar</packaging>
	<artifactId>trinax-partner-domain</artifactId>
	<name>trinax-partner-domain</name>
	<description>trinax-partner-domain project for Spring Boot</description>
	<properties>
		<maven.deploy.skip>true</maven.deploy.skip>
		<java.version>17</java.version>
		<mybatis-starter.version>0.0.2.RELEASE</mybatis-starter.version>
		<id-generator.version>0.0.2.RELEASE</id-generator.version>
		<redisclient.version>0.0.2.RELEASE</redisclient.version>
		<dtt-mq.version>0.0.2.RELEASE</dtt-mq.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-partner-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-basic-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-contract-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-integration-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-user-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.trinasolar</groupId>
			<artifactId>trinax-masterdata-api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>dtt.asset</groupId>
			<artifactId>dtt-db-mybatis-starter</artifactId>
			<version>${mybatis-starter.version}</version>
		</dependency>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
        <dependency>
            <groupId>dtt.asset</groupId>
            <artifactId>dtt-segment-id-generator</artifactId>
            <version>${id-generator.version}</version>
        </dependency>
		<dependency>
			<groupId>dtt.asset</groupId>
			<artifactId>dtt-framework-redisclient</artifactId>
			<version>${redisclient.version}</version>
		</dependency>
		<dependency>
			<groupId>io.github.biezhi</groupId>
			<artifactId>TinyPinyin</artifactId>
		</dependency>
		<dependency>
			<groupId>dtt.asset</groupId>
			<artifactId>dtt-framework-mq</artifactId>
			<version>${dtt-mq.version}</version>
		</dependency>
    </dependencies>



</project>
