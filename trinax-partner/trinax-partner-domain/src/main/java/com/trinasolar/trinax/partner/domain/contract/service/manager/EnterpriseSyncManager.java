package com.trinasolar.trinax.partner.domain.contract.service.manager;

import com.trinasolar.trinax.basic.api.RegionFeign;
import com.trinasolar.trinax.basic.dto.Region.RegionQueryResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.api.EnterpriseSyncFeign;
import com.trinasolar.trinax.integration.dto.input.enterprise.EnterpriseDataSyncReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CreateBusinessResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.EnterpriseDataSyncResDTO;
import com.trinasolar.trinax.partner.constants.enums.BuildEnterpriseSituationEnum;
import com.trinasolar.trinax.partner.constants.enums.CustomerStatusEnum;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseAddressType;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseAddressMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseBizMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseAddressPO;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseBizPO;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterprisePO;
import com.trinasolar.trinax.partner.dto.mq.BuildEnterpriseMessageMqDTO;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysInternalUserDetailRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class EnterpriseSyncManager {

    private final EnterpriseSyncFeign enterpriseSyncFeign;
    private final EnterpriseBizMapperService enterpriseBizMapperService;
    private final EnterpriseAddressMapperService enterpriseAddressMapperService;
    private final SysOrganizationFeign sysOrganizationFeign;
    private final RegionFeign regionFeign;
    private final SysUserFeign sysUserFeign;

    public EnterpriseDataSyncResDTO syncEnterprise(EnterprisePO enterprise, EnterpriseBizPO enterpriseBizInfo, EnterpriseAddressPO address) {

        checkAccessInfo(enterpriseBizInfo, address);
        List<String> codes = new ArrayList<>();
        codes.add(address.getProvinceCode());
        codes.add(address.getCityCode());

        List<RegionQueryResDTO> reigonList = regionFeign.getRegionByCodes(codes).getData();

        SysInternalUserDetailRespDTO sysUser = sysUserFeign.getInternalUserByUserId(enterpriseBizInfo.getMainSalesUserId()).getData();

        EnterpriseDataSyncReqDTO req = EnterpriseDataSyncReqDTO.builder()
                .setAccountRole(enterpriseBizInfo.getCustomerRole().replace(",", ";"))
                .setSfId(enterpriseBizInfo.getSfId())
                .setName(enterprise.getName())
                .setMdmOrganizationType("Company")
                .setBusinessLine(enterpriseBizInfo.getBusinessLine().replace("|", ";"))
                .setRegion("CHN")
                .setSubRegion(reigonList.stream().
                        filter(a -> StringUtils.equals(a.getCode(), address.getProvinceCode()))
                        .findFirst()
                        .orElse(new RegionQueryResDTO())
                        .getSfdcCode())
                .setArea(reigonList.stream()
                        .filter(a -> StringUtils.equals(a.getCode(), address.getCityCode()))
                        .findFirst()
                        .orElse(new RegionQueryResDTO())
                        .getSfdcCode())
                .setTaxVATCode(enterprise.getCreditCode())
                .setErpOperatingUnit(enterpriseBizInfo.getErpBusinessEntity())
                .setErpTaxClassification(enterpriseBizInfo.getErpTaxRate())
                .setBillingStreet(address.getStreetName())
                .setBillingCountry("China")
                .setCurrencyCode(enterpriseBizInfo.getCurrency())
                .setBillingCity(address.getCityName())
                .setBillingProvince(address.getProvinceName())
                .setBillingPostalCode(address.getPostalCode())
                .setSalesRep(sysUser.getUserCode())
                .setIndustry(enterpriseBizInfo.getIndustry())
                .setResource("ZhiZunBao");
        log.info("企业同步日志：{} ", JacksonUtil.bean2Json(req));
        EnterpriseDataSyncResDTO enterpriseDataSyncRes = enterpriseSyncFeign.enterpriseDataSyncSF(req).getData();

        if (ObjectUtils.isEmpty(enterpriseDataSyncRes)) {
            throw new BizException(ResultCode.FAIL.getCode(), "同步salesForce 异常");
        }

        if (StringUtils.isEmpty(enterpriseDataSyncRes.getSfId())) {
            log.info("同步Salesforce异常", JacksonUtil.bean2Json(enterpriseDataSyncRes));
            String errMsg = enterpriseDataSyncRes.getMessage() + enterpriseDataSyncRes.getErrmsg();
            throw new BizException(ResultCode.FAIL.getCode(), errMsg);
        }
        return enterpriseDataSyncRes;
    }

    public void enterpriseAccess(String sfId, String enterpriseId, String currentUserId, String currentUserName) {
        if (StringUtils.isEmpty(sfId)) {
            throw new BizException(ResultCode.FAIL.getCode(), "企业信息未完善，不允许建商：sfId");
        }
        CreateBusinessResDTO response = enterpriseSyncFeign.createBusiness(sfId).getData();
        if (Boolean.FALSE.equals(response.isSuccess())) {
            if (StringUtils.isNotBlank(response.getMessage()) && response.getMessage().contains("E013")) {
                throw new BizException(ResultCode.FAIL.getCode(), "该客户已在MDM存在，无法创建重复客户，请联系管理员处理");
            }
            //报错信息封装 编辑企业信息后，会异步同步联系人信息，联系人同步还在 mq 中时，点击了同步建商就会报错
            if (StringUtils.isNotBlank(response.getMessage()) && response.getMessage().contains("Please add at least one contact for this Account")) {
                throw new BizException(ResultCode.FAIL.getCode(), "联系人还未同步，请稍后再试");
            }
            throw new BizException(ResultCode.FAIL.getCode(), response.getMessage());
        }
        enterpriseBizMapperService.lambdaUpdate()
                .set(EnterpriseBizPO::getMdmId, response.getMdmID())
                .set(EnterpriseBizPO::getBusinessCreatedBy, currentUserId)
                .set(EnterpriseBizPO::getBusinessCreatedName, currentUserName)
                .set(EnterpriseBizPO::getCustomerStatus, CustomerStatusEnum.SUBMITTED.getCode())
                .set(EnterpriseBizPO::getBusinessCreatedTime, LocalDateTime.now())
                .eq(EnterpriseBizPO::getEnterpriseId, enterpriseId)
                .update();
    }

    public BuildEnterpriseMessageMqDTO messageMqInit(EnterprisePO enterprise, String currentUserId,
                                                     String currentUserName, String customerStatus) {

        BuildEnterpriseMessageMqDTO messageMq = BuildEnterpriseMessageMqDTO.builder()
                .setEnterpriseId(enterprise.getEnterpriseId())
                .setEnterpriseName(enterprise.getName())
                .setCurrentUserId(currentUserId)
                .setCurrentUserName(currentUserName);

        String organizationType = sysOrganizationFeign.getOrganizationType(currentUserId).getData();
        log.info("当前用户:{}, 用户名:{}, 组织:{}", currentUserId, currentUserName, organizationType);
        log.info("当前建商状态 {}", customerStatus);

        if (SysOrganizationTypeEnum.SALES.getCode().equals(organizationType)) {
            messageMq.setSalesInternalUserId(currentUserId);
            messageMq.setSalesInternalUserName(currentUserName);

            if (CustomerStatusEnum.BPM_APPROVED.getCode().equals(customerStatus)) {
                messageMq.setSituationType(BuildEnterpriseSituationEnum.ENTERPRISE_SALES_SUBMIT_SUCCESS.getCode());
            }
            if (CustomerStatusEnum.BP_DECLINED.getCode().equals(customerStatus)) {
                messageMq.setSituationType(BuildEnterpriseSituationEnum.ENTERPRISE_SALES_SUBMIT_FAIL.getCode());
            }
        } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(organizationType)
                || SysOrganizationTypeEnum.COMMON.getCode().equals(organizationType)) {
            messageMq.setOperationInternalUserId(currentUserId);
            messageMq.setOperationInternalUserName(currentUserName);

            if (CustomerStatusEnum.BPM_APPROVED.getCode().equals(customerStatus)) {
                messageMq.setSituationType(BuildEnterpriseSituationEnum.ENTERPRISE_OPERATION_SUBMIT_SUCCESS.getCode());
            }
            if (CustomerStatusEnum.BP_DECLINED.getCode().equals(customerStatus)) {
                messageMq.setSituationType(BuildEnterpriseSituationEnum.ENTERPRISE_OPERATION_SUBMIT_FAIL.getCode());
            }

        } else {
            throw new BizException(ResultCode.FAIL.getCode(), "禁止非销售/运营 同步建商");
        }
        if (StringUtils.isBlank(customerStatus)) {
            messageMq.setSituationType(BuildEnterpriseSituationEnum.ENTERPRISE_SALES_APPLY.getCode());
        }
        return messageMq;
    }

    public void checkAccessInfo(String enterpriseId) {
        EnterpriseBizPO enterpriseBiz = enterpriseBizMapperService.lambdaQuery().eq(EnterpriseBizPO::getEnterpriseId, enterpriseId).one();
        EnterpriseAddressPO address = enterpriseAddressMapperService.lambdaQuery()
                .eq(EnterpriseAddressPO::getEnterpriseId, enterpriseId)
                .eq(EnterpriseAddressPO::getAddressType, EnterpriseAddressType.BILLING.getCode())
                .eq(EnterpriseAddressPO::getIsDeleted, 0)
                .eq(EnterpriseAddressPO::getIsDefault, 1)
                .one();
        checkAccessInfo(enterpriseBiz, address);
    }

    public void checkAccessInfo(EnterpriseBizPO enterpriseBiz, EnterpriseAddressPO address) {
        if (ObjectUtils.isEmpty(enterpriseBiz)) {
            throw new BizException(ResultCode.FAIL.getCode(), "企业业务信息为空");
        }
        if (ObjectUtils.isEmpty(address)) {
            throw new BizException(ResultCode.FAIL.getCode(), "企业开单信息为空");
        }
        checkEmptyParam(enterpriseBiz.getBusinessLine(), "业务线不能为空");
        checkEmptyParam(enterpriseBiz.getCustomerOrgNature(), "请完善企业信息：客户组织性质");
        checkEmptyParam(enterpriseBiz.getIndustry(), "请完善企业信息：行业");
        checkEmptyParam(enterpriseBiz.getTransactionScale(), "请完善企业信息：交易规模");
        checkEmptyParam(enterpriseBiz.getErpBusinessEntity(), "请完善企业信息：ERP业务实体");
    }

    private void checkEmptyParam(String paramStr, String errDescription) {
        if (StringUtils.isBlank(paramStr)) {
            throw new BizException(ResultCode.FAIL.getCode(), errDescription);
        }
    }


    public Result<Boolean> verifyCoInfo(String enterpriseId) {
        EnterpriseBizPO enterpriseBiz = enterpriseBizMapperService.lambdaQuery().eq(EnterpriseBizPO::getEnterpriseId, enterpriseId).one();
        EnterpriseAddressPO address = enterpriseAddressMapperService.lambdaQuery()
                .eq(EnterpriseAddressPO::getEnterpriseId, enterpriseId)
                .eq(EnterpriseAddressPO::getAddressType, EnterpriseAddressType.BILLING.getCode())
                .eq(EnterpriseAddressPO::getIsDeleted, 0)
                .eq(EnterpriseAddressPO::getIsDefault, 1)
                .one();
        boolean supplementCoInfo = false;
        try {
            checkAccessInfo(enterpriseBiz, address);
        } catch (Exception e) {
            supplementCoInfo = true;
        }
        return Result.ok(supplementCoInfo);
    }
}
