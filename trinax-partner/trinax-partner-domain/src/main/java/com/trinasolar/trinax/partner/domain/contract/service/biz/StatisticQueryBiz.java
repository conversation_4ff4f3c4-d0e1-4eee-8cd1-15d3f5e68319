package com.trinasolar.trinax.partner.domain.contract.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.partner.constants.enums.StatisticEnterpriseApiType;
import com.trinasolar.trinax.partner.dto.input.statistic.StatisticEnterpriseNumReqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.*;
import java.time.temporal.TemporalAdjusters;


@RequiredArgsConstructor
@Service
@Slf4j
public class StatisticQueryBiz {


    public void handleStatisticEnterpriseNumReqDTO(StatisticEnterpriseNumReqDTO reqDTO) {
        LocalDateTime createTimeStart = null;
        LocalDateTime createTimeEnd;
        Year year = Year.of(reqDTO.getYear());
        if (reqDTO.getApiType().equals(StatisticEnterpriseApiType.accumulate)) {
            if (ObjectUtil.isNotNull(reqDTO.getMonth())) {
                // 截止时间为选择月份的最后一天的23:59:59
                createTimeEnd = YearMonth.of(reqDTO.getYear(), reqDTO.getMonth()).atEndOfMonth().atTime(23, 59, 59);
            } else {
                // 截止时间为选择年份的最后一天的23:59:59
                createTimeEnd = year.atMonth(12).atEndOfMonth().atTime(23, 59, 59);
            }
        } else if (reqDTO.getApiType().equals(StatisticEnterpriseApiType.add)) {
            if (ObjectUtil.isNotNull(reqDTO.getMonth())) {
                YearMonth yearMonth = YearMonth.of(reqDTO.getYear(), reqDTO.getMonth());
                // 起始时间为选择月份的第一天的00:00:00
                createTimeStart = yearMonth.atDay(1).atStartOfDay();
                // 截止时间为选择月份的最后一天的23:59:59
                createTimeEnd = yearMonth.atEndOfMonth().atTime(23, 59, 59);
            } else {
                // 起始时间为选择年份的第一天的00:00:00
                createTimeStart = year.atDay(1).atStartOfDay();
                // 截止时间为选择年份的最后一天的23:59:59
                createTimeEnd = year.atMonth(12).atEndOfMonth().atTime(23, 59, 59);                }
        } else {
            throw new BizException(ResultCode.FAIL.getCode(), "接口类型错误");
        }
        reqDTO.setCreateTimeStart(createTimeStart);
        reqDTO.setCreateTimeEnd(createTimeEnd);
    }
}
