package com.trinasolar.trinax.partner.domain.contract.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseBizRelationPO;

import java.util.List;

/**
 * <p>
 * 企业业务区域（战区）关系 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-15
 */
public interface EnterpriseBizRelationMapperService extends IService<EnterpriseBizRelationPO> {

    List<EnterpriseBizRelationPO> listByBizOrganizationCode(String bizOrganizationCode);

    List<EnterpriseBizRelationPO> listByBizOrganizationCodes(List<String> bizOrganizationCodes);

    List<EnterpriseBizRelationPO> listByEnterpriseIds(List<String> enterpriseIds);

    EnterpriseBizRelationPO getByEnterpriseId(String enterpriseId);
}
