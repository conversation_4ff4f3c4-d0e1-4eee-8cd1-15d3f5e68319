package com.trinasolar.trinax.partner.domain.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.MarketingAccountMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.MarketingAccountPO;
import com.trinasolar.trinax.partner.domain.contract.service.MarketingAccountService;
import com.trinasolar.trinax.partner.dto.output.MarketingAccountRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class MarketingAccountServiceImpl implements MarketingAccountService {

    @Autowired
    private MarketingAccountMapperService marketingAccountMapperService;

    @Override
    public MarketingAccountRespDTO getMarketingAccountRespDTO(String marketingAccountId) {
        MarketingAccountPO marketingAccountPO = marketingAccountMapperService.findByAccountId(marketingAccountId);
        MarketingAccountRespDTO marketingAccountRespDTO = BeanUtil.toBean(marketingAccountPO, MarketingAccountRespDTO.class);
        return marketingAccountRespDTO;
    }

    @Override
    public List<MarketingAccountRespDTO> listMarketingAccountRespDTO(List<String> marketingAccountIdList) {
        List<MarketingAccountPO> marketingAccountPOS = marketingAccountMapperService.listByAccountIdList(marketingAccountIdList);
        List<MarketingAccountRespDTO> marketingAccountRespDTOS = BeanUtil.copyToList(marketingAccountPOS, MarketingAccountRespDTO.class);
        return marketingAccountRespDTOS;
    }

    @Override
    public Result<List<MarketingAccountRespDTO>> listMarketAccount() {
        List<MarketingAccountPO> maketList = marketingAccountMapperService.lambdaQuery().list();
        return Result.ok(BeanUtil.copyToList(maketList, MarketingAccountRespDTO.class));
    }
}
