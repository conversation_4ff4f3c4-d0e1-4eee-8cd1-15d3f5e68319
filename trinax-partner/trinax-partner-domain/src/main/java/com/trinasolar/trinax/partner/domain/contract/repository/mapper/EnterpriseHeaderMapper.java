package com.trinasolar.trinax.partner.domain.contract.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseHeaderPO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseHeaderQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseHeaderQueryResDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 企业抬头表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-03
 */
@Mapper
public interface EnterpriseHeaderMapper extends BaseMapper<EnterpriseHeaderPO> {
    List<EnterpriseHeaderQueryResDTO> enterpriseHeaderList(@Param("req") EnterpriseHeaderQueryReqDTO reqDTO);
}
