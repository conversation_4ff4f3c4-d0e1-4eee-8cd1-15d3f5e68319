package com.trinasolar.trinax.partner.domain.contract.service;

import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseRelationPcQueryDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationAddDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationPcDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationRemoveDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationRestoreDTO;

import java.util.List;

public interface EnterpriseRelationService {

    PageResponse<EnterpriseRelationPcDTO> pageEnterpriseRelationPcDTO(PageRequest<EnterpriseRelationPcQueryDTO> pageReqDTO);

    String add(EnterpriseRelationAddDTO addDTO);

    void remove(EnterpriseRelationRemoveDTO removeDTO);

    void restore(EnterpriseRelationRestoreDTO restoreDTO);

    void expire();

    List<EnterpriseRelationPcDTO> getByLinkEnterpriseIds(List<String> linkIds);
}
