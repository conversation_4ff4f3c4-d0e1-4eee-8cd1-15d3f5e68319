package com.trinasolar.trinax.partner.domain.contract.service;

import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;

import java.util.List;

public interface EnterpriseBizRelationService {

    List<EnterpriseBizRelationResDTO> getEnterpriseBizRelationByEnterpriseId(String enterpriseId);

    List<EnterpriseBizRelationResDTO> listByBizOrganizationCode(String bizOrganizationCode);

    List<EnterpriseBizRelationResDTO> listByBizOrganizationCodes(List<String> bizOrganizationCodes);

    List<EnterpriseBizRelationResDTO> listByEnterpriseIds(List<String> enterpriseIds);
}
