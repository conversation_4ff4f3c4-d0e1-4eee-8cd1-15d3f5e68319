package com.trinasolar.trinax.partner.domain.contract.service.biz.enterprise;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.contract.api.ContractFrameFeign;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.dto.output.contract.frame.ContractFrameResDTO;
import com.trinasolar.trinax.partner.constants.PartnerResultCode;
import com.trinasolar.trinax.partner.constants.enums.AdmissionStatusEnum;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.EnterpriseBizMapperService;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseBizPO;
import com.trinasolar.trinax.partner.dto.input.enterprise.ChangeCustomerCategoryReqDTO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.dto.output.SysDealerSalesRelationRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class ChangeCustomerCategoryBiz {
    private final EnterpriseBizMapperService enterpriseBizMapperService;
    private final ContractFrameFeign contractFrameFeign;
    private final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;


    public Result<String> changeCustomerCategory(ChangeCustomerCategoryReqDTO req) {

        EnterpriseBizPO enterpriseBiz = enterpriseBizMapperService.lambdaQuery().eq(EnterpriseBizPO::getEnterpriseId, req.getEnterpriseId()).one();

        if (ObjectUtils.isEmpty(enterpriseBiz)) {
            throw new BizException(ResultCode.FAIL.getCode(), "企业客户信息异常");
        }
        String admissionStatus = "";
        if (EnterpriseTypeEnum.BUSINESS.getCode().equals(req.getCustomerCategory())) {
            admissionStatus = AdmissionStatusEnum.PENDING_ADMITTANCE.getCode();
        }
        if (EnterpriseTypeEnum.PARTNER.getCode().equals(req.getCustomerCategory())) {
            admissionStatus = AdmissionStatusEnum.ACCESS.getCode();
            if (StringUtils.isBlank(enterpriseBiz.getMdmId())) {
                throw new BizException(PartnerResultCode.CHANGE_CUSTOMER_CATEGORY_ERR.getCode(), "此客户还没有完成建商，所以还无法更新它的客户类型为生态伙伴");
            }
            if (StringUtils.equals(enterpriseBiz.getAdmissionStatus(), AdmissionStatusEnum.APPLYING.getCode())
                    && !req.isSkipCheckStatus()) {
                throw new BizException(PartnerResultCode.CHANGE_CUSTOMER_CATEGORY_ERR.getCode(), "此客户已发起准入，所以还无法更新它的客户类型为生态伙伴");
            }
        }

        if (Boolean.FALSE.equals(req.isSkipCheck())
                && StringUtils.equals(req.getCustomerCategory(), EnterpriseTypeEnum.PARTNER.getCode())) {
            //未忽略校验 需要校验框架合同 状态
            List<ContractFrameResDTO> contractFrameResDTOS = contractFrameFeign.listByEnterpriseIdAndContractStatus(req.getEnterpriseId(), ContractStatusEnum.SINGLE_SIGNED.getCode());
            if (ObjectUtils.isEmpty(contractFrameResDTOS)) {
                throw new BizException(PartnerResultCode.FRAME_CONTRACT_ERR.getCode(), PartnerResultCode.FRAME_CONTRACT_ERR.getMessage());
            }
        }

        enterpriseBizMapperService.lambdaUpdate()
                .set(EnterpriseBizPO::getCustomerCategory, req.getCustomerCategory())
                .set(EnterpriseBizPO::getAdmissionStatus, admissionStatus)
                .set(EnterpriseBizPO::getAdmissionTime, LocalDateTime.now())
                .eq(EnterpriseBizPO::getEnterpriseId, req.getEnterpriseId())
                .update();
        // 更新sys_dealer_sales_relation
        sysDealerSalesRelationFeign.updateEnterpriseCustomerCategory(req.getEnterpriseId(), req.getCustomerCategory());
        return Result.ok();
    }
}
