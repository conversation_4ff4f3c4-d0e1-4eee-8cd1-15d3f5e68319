package com.trinasolar.trinax.partner.domain.contract.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseAddressType;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterpriseAddressPO;

import java.util.List;

public interface EnterpriseAddressMapperService extends IService<EnterpriseAddressPO> {
    List<EnterpriseAddressPO> listByEnterpriseIdsAndAddressType(List<String> enterpriseIds, EnterpriseAddressType enterpriseAddressType);

    EnterpriseAddressPO getRegistByEnterpriseId(String enterpriseId);
}
