package com.trinasolar.trinax.partner.domain.contract.service.biz;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.constants.enums.OptionGroupConstant;
import com.trinasolar.trinax.basic.dto.input.option.OptionItemQueryReqDTO;
import com.trinasolar.trinax.basic.dto.output.OptionItemResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.masterdata.api.BannerImageFeign;
import com.trinasolar.trinax.partner.constants.PartnerResultCode;
import com.trinasolar.trinax.partner.constants.enums.BusinessLineEnum;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseInfoConstants;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.constants.enums.QuickOrderTypeEnum;
import com.trinasolar.trinax.partner.domain.common.util.DateUtil;
import com.trinasolar.trinax.partner.domain.contract.repository.atomicservice.*;
import com.trinasolar.trinax.partner.domain.contract.repository.mapper.EnterpriseFullDataMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.mapper.EnterpriseMapper;
import com.trinasolar.trinax.partner.domain.contract.repository.po.*;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseBizRelationService;
import com.trinasolar.trinax.partner.domain.contract.service.EnterpriseService;
import com.trinasolar.trinax.partner.dto.input.EnterpriseBizAuthorizeQueryDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.EnterpriseBizAuthDTO;
import com.trinasolar.trinax.partner.dto.input.enterprise.FileData;
import com.trinasolar.trinax.partner.dto.output.*;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseBusinessPCResDTO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.EnterpriseUserForSalesQueryDTO;
import com.trinasolar.trinax.user.dto.input.PageExternalEnterpriseQueryDTO;
import com.trinasolar.trinax.user.dto.output.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
@Slf4j
@RefreshScope
public class EnterpriseBizQueryBiz {
    private final EnterpriseFullDataMapper enterpriseFullDataMapper;
    private final EnterpriseFileRelMapperService enterpriseFileRelMapperService;
    private final OptionFeign optionFeign;
    private final MarketingAccountMapperService marketingAccountMapperService;

    private final EnterpriseBizAuthorizeMapperService enterpriseBizAuthorizeMapperService;
    private final EnterpriseBizAuthorizeFileRelationMapperService enterpriseBizAuthorizeFileRelationMapperService;

    private final SysUserFeign sysUserFeign;
    private final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;
    private final EnterpriseMapper enterpriseMapper;
    private final EnterpriseService enterpriseService;
    private final EnterpriseRelationMapperService enterpriserelationMapperService;
    private final EnterpriseBizRelationService enterpriseBizRelationService;

    @Value("${trina.user.permission.check_org_code}")
    private String checkOrgCode;

    public Result<EnterpriseBusinessPCResDTO> qryCoBusinessByCoId(String enterpriseId) {
        EnterpriseBusinessPCResDTO enterpriseFullData = enterpriseFullDataMapper.qryCoBusinessByCoId(enterpriseId);

        //初始化字典数据
        EnterpriseBusinessPCResDTO result = resultLoadOptionDesc(enterpriseFullData);

        List<EnterpriseBizFileRelationPO> fileRelList = enterpriseFileRelMapperService.lambdaQuery()
                .eq(EnterpriseBizFileRelationPO::getEnterpriseId, enterpriseId)
                .eq(EnterpriseBizFileRelationPO::getIsDeleted, 0)
                .list();

        if (ObjectUtils.isNotEmpty(fileRelList)) {
            result.setFileDataList(BeanUtil.copyToList(fileRelList, FileData.class));
        }
        List<EnterpriseBizAuthorizePO> authDatas =enterpriseBizAuthorizeMapperService.lambdaQuery()
                .eq(EnterpriseBizAuthorizePO::getEnterpriseId, enterpriseId)
                .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).list();
        if (CollectionUtils.isNotEmpty(authDatas)) {
            Long targetId = authDatas.get(0).getId();
            EnterpriseBizAuthDTO authDTO = new EnterpriseBizAuthDTO();
            BeanUtil.copyProperties(authDatas.get(0), authDTO);
            List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                    .eq(EnterpriseBizAuthorizeFileRelationPO::getTargetId, targetId)
                    .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
            if (ObjectUtils.isNotEmpty(authRelList)) {
                authDTO.setAuthorizeDataList(BeanUtil.copyToList(authRelList, FileData.class));
            }
            result.setAuthDTO(authDTO);
        }
        String erpBusinessEntityName = qryMarketAccount(result.getErpBusinessEntity());

        result.setErpBusinessEntityName(erpBusinessEntityName);
        return Result.ok(result);
    }

    private String qryMarketAccount(String erpBusinessEntity) {
        MarketingAccountPO marketingAccountPO = marketingAccountMapperService.lambdaQuery()
                .eq(MarketingAccountPO::getAccountId, erpBusinessEntity)
                .one();
        if (ObjectUtils.isEmpty(marketingAccountPO)) {
            return "";
        }
        return marketingAccountPO.getAccountOu();
    }


    /**
     * 初始化字典数据
     */
    private EnterpriseBusinessPCResDTO resultLoadOptionDesc(EnterpriseBusinessPCResDTO result) {
        OptionItemQueryReqDTO req = new OptionItemQueryReqDTO();
        req.setOptionGroup(OptionGroupConstant.CUSTOMER_ORG_NATURE);
        List<OptionItemResDTO> orgNature = optionFeign.qryOptionItemList(req).getData();
        if (ObjectUtils.isNotEmpty(orgNature)) {
            String customerOrgNatureName = orgNature.stream()
                    .filter(a -> StringUtils.equals(a.getOptionValue(), result.getCustomerOrgNature()))
                    .findFirst()
                    .orElse(new OptionItemResDTO())
                    .getOptionDesc();
            result.setCustomerOrgNatureDesc(customerOrgNatureName);
        }
        req.setOptionGroup(OptionGroupConstant.INDUSTRY);
        List<OptionItemResDTO> industryList = optionFeign.qryOptionItemList(req).getData();

        if (ObjectUtils.isNotEmpty(industryList)) {
            String industryName = industryList.stream()
                    .filter(a -> StringUtils.equals(a.getOptionValue(), result.getIndustry()))
                    .findFirst()
                    .orElse(new OptionItemResDTO())
                    .getOptionDesc();
            result.setIndustryName(industryName);
        }

        req.setOptionGroup(OptionGroupConstant.TRANSACTION_SCALE);
        List<OptionItemResDTO> scaleList = optionFeign.qryOptionItemList(req).getData();

        if (ObjectUtils.isNotEmpty(scaleList)) {
            String transactionScale = scaleList.stream()
                    .filter(a -> StringUtils.equals(a.getOptionValue(), result.getTransactionScale()))
                    .findFirst()
                    .orElse(new OptionItemResDTO())
                    .getOptionDesc();
            result.setTransactionScaleDesc(transactionScale);
        }

        req.setOptionGroup(OptionGroupConstant.MAIN_BUSINESS);
        List<OptionItemResDTO> businessList = optionFeign.qryOptionItemList(req).getData();


        Map<String, String> map = businessList.stream().collect(
                Collectors.toMap(OptionItemResDTO::getOptionValue, OptionItemResDTO::getOptionDesc));
        if (ObjectUtils.isNotEmpty(businessList) && StringUtils.isNotBlank(result.getMainBusiness())) {
            String[] business = result.getMainBusiness().split("\\|");
            StringBuilder mainBusinessStr = new StringBuilder();
            for (int i = 0; i < business.length; i++) {
                mainBusinessStr.append(map.get(business[i])).append("/");
            }
            if (StringUtils.isNotBlank(mainBusinessStr.toString())) {
                result.setMainBusinessDesc(mainBusinessStr.toString().substring(0, mainBusinessStr.toString().length() - 1));
            }
        }
        req.setOptionGroup(OptionGroupConstant.CUSTOMER_STATUS);
        List<OptionItemResDTO> statusList = optionFeign.qryOptionItemList(req).getData();

        if (ObjectUtils.isNotEmpty(statusList)) {
            String status = statusList.stream()
                    .filter(a -> StringUtils.equals(a.getOptionValue(), result.getCustomerStatus()))
                    .findFirst()
                    .orElse(new OptionItemResDTO())
                    .getOptionDesc();
            result.setCustomerStatusStr(status);
        }

        if (StringUtils.isNotBlank(result.getBusinessLine())) {
            String[] businessLineArr = result.getBusinessLine().split("\\|");
            StringBuilder businessLineStr = new StringBuilder();
            for (int i = 0; i < businessLineArr.length; i++) {
                businessLineStr.append(BusinessLineEnum.getDescByCode(businessLineArr[i])).append("/");
            }
            if (StringUtils.isNotBlank(businessLineStr.toString())) {
                result.setBusinessLineName(businessLineStr.toString().substring(0, businessLineStr.toString().length() - 1));
            }
        }
        return result;
    }

    public Result<List<EnterpriseBizAuthorizeDTO>> queryEnterpriseAuthorizeList(String enterpriseId, String year, String currentUserId, String status) {
        SysUserRespDTO userInfo = getUserInfo(currentUserId);
        List<EnterpriseBizAuthorizePO> authDatas = Lists.newArrayList();
        Map<Long, List<EnterpriseBizAuthorizeFileRelationPO>> relMap = Maps.newHashMap();
        if (userInfo.externalUser()) {
            String nextYear = String.valueOf(Integer.parseInt(year) + 1);
            authDatas = enterpriseBizAuthorizeMapperService.lambdaQuery()
                    .eq(EnterpriseBizAuthorizePO::getEnterpriseId, enterpriseId)
                    .eq(EnterpriseBizAuthorizePO::getAuthUserId, currentUserId)
                    .eq(StringUtils.isNotBlank(status), EnterpriseBizAuthorizePO::getAuthStatus, status)
                    .in(EnterpriseBizAuthorizePO::getAuthYear, Lists.newArrayList(year, nextYear))
                    .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).orderByAsc(EnterpriseBizAuthorizePO::getAuthYear).list();
            if (CollectionUtils.isNotEmpty(authDatas)) {
                List<Long> targetIds = authDatas.stream().map(EnterpriseBizAuthorizePO::getId).toList();
                List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                        .in(EnterpriseBizAuthorizeFileRelationPO::getTargetId, targetIds)
                        .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
                relMap = authRelList.stream().collect(Collectors.groupingBy(EnterpriseBizAuthorizeFileRelationPO::getTargetId));
            } else {
                // 查询往年的记录
                authDatas = enterpriseBizAuthorizeMapperService.lambdaQuery()
                        .eq(EnterpriseBizAuthorizePO::getEnterpriseId, enterpriseId)
                        .eq(EnterpriseBizAuthorizePO::getAuthUserId, currentUserId)
                        .eq(StringUtils.isNotBlank(status), EnterpriseBizAuthorizePO::getAuthStatus, status)
                        .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0)
                        .orderByDesc(EnterpriseBizAuthorizePO::getAuthYear)
                        .list();
                if (CollectionUtils.isNotEmpty(authDatas)) {
                    authDatas = authDatas.subList(0, 1);
                    List<Long> targetIds = authDatas.stream().map(EnterpriseBizAuthorizePO::getId).toList();
                    List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                            .in(EnterpriseBizAuthorizeFileRelationPO::getTargetId, targetIds)
                            .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
                    relMap = authRelList.stream().collect(Collectors.groupingBy(EnterpriseBizAuthorizeFileRelationPO::getTargetId));
                }
            }
        } else if (userInfo.internalUser()) {
            authDatas = enterpriseBizAuthorizeMapperService.lambdaQuery()
                    .eq(EnterpriseBizAuthorizePO::getEnterpriseId, enterpriseId)
                    .eq(StringUtils.isNotBlank(year), EnterpriseBizAuthorizePO::getAuthYear, year)
                    .eq(StringUtils.isNotBlank(status), EnterpriseBizAuthorizePO::getAuthStatus, status)
                    .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).list();
            EnterpriseUserForSalesQueryDTO q = new EnterpriseUserForSalesQueryDTO();
            q.setLoginUserId(currentUserId);
            q.setEnterpriseId(enterpriseId);
//            q.setAuthStatus(DealerSalesRelationAuthStatus.SUCCESS.getCode());
            Result<List<SysEnterpriseUserRespDTO>> deals = sysDealerSalesRelationFeign.listUserByEnterpriseIdForSales(q);
            List<String> dealerIds = deals.getData().stream().map(SysEnterpriseUserRespDTO::getUserId).toList();
            authDatas = authDatas.stream().filter(data -> dealerIds.contains(data.getAuthUserId())).toList();
            if (CollectionUtils.isNotEmpty(authDatas)) {
                List<Long> targetIds = authDatas.stream().map(EnterpriseBizAuthorizePO::getId).toList();
                List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                        .in(EnterpriseBizAuthorizeFileRelationPO::getTargetId, targetIds)
                        .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
                relMap = authRelList.stream().collect(Collectors.groupingBy(EnterpriseBizAuthorizeFileRelationPO::getTargetId));
            }
        }
        List<EnterpriseBizAuthorizeDTO> result = Lists.newArrayList();
        for (EnterpriseBizAuthorizePO po : authDatas) {
            EnterpriseBizAuthorizeDTO dto = new EnterpriseBizAuthorizeDTO();
            BeanUtil.copyProperties(po, dto);
            List<EnterpriseBizAuthorizeFileRelationPO> relPOs = relMap.get(dto.getId());
            dto.setAuthorizeDataList(BeanUtil.copyToList(relPOs, EnterpriseBizAuthorizeFileDTO.class));
            result.add(dto);
        }
        fillEnterpriseName(result);
        return Result.ok(result);
    }

    private SysUserRespDTO getUserInfo(String userId) {
        Result<SysUserRespDTO> currentUser = sysUserFeign.getUserByUserId(userId);
        if (currentUser == null || currentUser.getData() == null) {
            throw new BizException(PartnerResultCode.CONTACTOR_IN_VALID.getCode(), PartnerResultCode.CONTACTOR_IN_VALID.getMessage());
        }
        return currentUser.getData();
    }

    public Result<EnterpriseBizAuthorizeDTO> queryEnterpriseAuthorizeDetail(Long id) {
        EnterpriseBizAuthorizePO authorizePO = enterpriseBizAuthorizeMapperService.lambdaQuery()
                .eq(EnterpriseBizAuthorizePO::getId, id)
                .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).one();
        if (authorizePO == null) {
            return null;
        }
        List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                .in(EnterpriseBizAuthorizeFileRelationPO::getTargetId, id)
                .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
        EnterpriseBizAuthorizeDTO dto = new EnterpriseBizAuthorizeDTO();
        BeanUtil.copyProperties(authorizePO, dto);
        dto.setAuthorizeDataList(BeanUtil.copyToList(authRelList, EnterpriseBizAuthorizeFileDTO.class));
        List<EnterpriseBizAuthorizeDTO> dtos = Lists.newArrayList(dto);
        fillEnterpriseName(dtos);
        return Result.ok(dtos.get(0));
    }

    public List<EnterpriseBizAuthorizePO> queryAuthListToCheck(String thisYear, String nextYear) {
        List<EnterpriseBizAuthorizePO> pos = enterpriseBizAuthorizeMapperService.lambdaQuery()
                .eq(EnterpriseBizAuthorizePO::getAuthStatus, EnterpriseInfoConstants.ENTERPRISE_AUTH_CONFIRM)
                .eq(EnterpriseBizAuthorizePO::getAuthYear, thisYear)
                .eq(EnterpriseBizAuthorizePO::getIsRemind, 0)
                .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).list();
        List<EnterpriseBizAuthorizePO> nextPos = enterpriseBizAuthorizeMapperService.lambdaQuery()
                .in(EnterpriseBizAuthorizePO::getAuthStatus, Lists.newArrayList(EnterpriseInfoConstants.ENTERPRISE_AUTH_CONFIRM, EnterpriseInfoConstants.ENTERPRISE_AUTH_PENDING))
                .eq(EnterpriseBizAuthorizePO::getAuthYear, nextYear)
                .eq(EnterpriseBizAuthorizePO::getIsDeleted, 0).list();
        Set<String> checkKeys = nextPos.stream().map(n -> String.join("_", n.getEnterpriseId(), n.getAuthUserId())).collect(Collectors.toSet());
        pos = pos.stream().filter(p -> {
            String key = String.join("_", p.getEnterpriseId(), p.getAuthUserId());
            return !checkKeys.contains(key);
        }).toList();
        return pos;
    }

    public PageResponse<EnterpriseBizAuthorizeDTO> queryAuthorizeByPage(PageRequest<EnterpriseBizAuthorizeQueryDTO> queryDTO) {
        IPage<EnterpriseBizAuthorizeDTO> ipage = enterpriseMapper.queryAuthorizeByPage(new Page<>(queryDTO.getIndex(), queryDTO.getSize()), queryDTO.getQuery());
        List<EnterpriseBizAuthorizeDTO> records = ipage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> targetIds = records.stream().map(EnterpriseBizAuthorizeDTO::getId).toList();
            List<EnterpriseBizAuthorizeFileRelationPO> authRelList = enterpriseBizAuthorizeFileRelationMapperService.lambdaQuery()
                    .in(EnterpriseBizAuthorizeFileRelationPO::getTargetId, targetIds)
                    .eq(EnterpriseBizAuthorizeFileRelationPO::getIsDeleted, 0).list();
            Map<Long, List<EnterpriseBizAuthorizeFileRelationPO>> relMap = authRelList.stream().collect(Collectors.groupingBy(EnterpriseBizAuthorizeFileRelationPO::getTargetId));
            for (EnterpriseBizAuthorizeDTO dto : records) {
                List<EnterpriseBizAuthorizeFileRelationPO> relPOs = relMap.get(dto.getId());
                dto.setAuthorizeDataList(BeanUtil.copyToList(relPOs, EnterpriseBizAuthorizeFileDTO.class));
            }
        }
        fillEnterpriseName(records);
        return PageResponse.toResult(
                queryDTO,
                (int) ipage.getTotal(),
                records);
    }

    private void fillEnterpriseName(List<EnterpriseBizAuthorizeDTO> dtos) {
        List<String> coIds = dtos.stream().map(EnterpriseBizAuthorizeDTO::getRelateCoId).filter(StringUtils::isNotBlank).toList();
        List<String> enIds = dtos.stream().map(EnterpriseBizAuthorizeDTO::getEnterpriseId).filter(StringUtils::isNotBlank).toList();
        enIds.addAll(coIds);
        List<EnterpriseDTO> enterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(enIds).build());
        Map<String, EnterpriseDTO> enterMap = enterpriseDTOS.stream().collect(Collectors.toMap(EnterpriseDTO::getEnterpriseId, Function.identity(), (v1,v2)->v2));
        for (EnterpriseBizAuthorizeDTO dto : dtos) {
            if (enterMap.containsKey(dto.getEnterpriseId())) {
                dto.setEnterpriseName(enterMap.get(dto.getEnterpriseId()).getName());
            }
            if (enterMap.containsKey(dto.getRelateCoId())) {
                dto.setRelateCoName(enterMap.get(dto.getRelateCoId()).getName());
            }
        }
    }

    /**
     * 是否可以快速下单
     * @param userId
     * @return
     */
    public QuickOrderAuthDTO canQuickOrder(String userId) {
        Result<SysUseAndRoleRespDTO> userRespResult = sysUserFeign.getUserAndRoleByUserId(userId);
        Assert.isTrue(userRespResult.getSuccess(), "获取用户信息失败");

        SysUseAndRoleRespDTO sysUserRespDTO=userRespResult.getData();
        Assert.notNull(sysUserRespDTO, "获取用户信息失败");

        if(sysUserRespDTO.getUserType().equals("INTERNAL")){
            //如果是内部销售下单，优先判断是否为大区总
            List<RoleResDTO>  roleResDTOS=sysUserRespDTO.getRoleList();
            List<String> roleIds = roleResDTOS.stream().map(RoleResDTO::getRoleId).toList();
            if (roleIds.contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
                // 销售区域总能查看到自己所在战区的所有客户
                List<EnterpriseBizRelationResDTO> enterpriseBizRelationResDTOS = enterpriseBizRelationService.listByBizOrganizationCode(sysUserRespDTO.getOrganizationCode());
                if (ObjectUtil.isNotEmpty(enterpriseBizRelationResDTOS)&&checkOrgCode.contains(sysUserRespDTO.getOrganizationCode())) {
                    List<String> enterpriseIds = enterpriseBizRelationResDTOS.stream().map(EnterpriseBizRelationResDTO::getEnterpriseId).toList();
                    List<EnterpriseDTO> enterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(enterpriseIds).build());
                    QuickOrderAuthDTO quickOrderAuthDTO =queryQuickOrderAuth(enterpriseDTOS,null);
                    quickOrderAuthDTO.setUserType("INTERNAL");
//                    List<EnterpriseDTO> partnerEnterpriseDTOS=enterpriseDTOS.stream().filter(item->item.getCustomerCategory().equals(EnterpriseTypeEnum.PARTNER.getCode())).toList();
//                    List<QuickOrderEnterpriseDTO> quickOrderEnterpriseDTOS=partnerEnterpriseDTOS.stream().map(item->
//                        QuickOrderEnterpriseDTO.builder().enterpriseId(item.getEnterpriseId()).isManager(true).build()
//                    ).toList();
//                    quickOrderAuthDTO.setQuickOrderCompanyIdList(quickOrderEnterpriseDTOS);
                    return quickOrderAuthDTO;
                }else{
                    return QuickOrderAuthDTO.builder().canQuickOrder(false)
                            .userType("INTERNAL").quickOrderCompanyIdList(new ArrayList<>()).build();
                }
            }else{
                QuickOrderAuthDTO quickOrderAuthDTO=QuickOrderAuthDTO.builder().canQuickOrder(false)
                        .userType("INTERNAL").quickOrderCompanyIdList(new ArrayList<>()).build();
                //如果非大区总，则需要查询作为主销售直接管理的企业
                List<String> companyIds= enterpriseService.listByMainSalesId(userId);
                if(CollectionUtils.isNotEmpty(companyIds)){
                    //基于companyIds检索企业
                    List<EnterpriseDTO> enterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(companyIds).build());
                    QuickOrderAuthDTO tmpQuickOrderAuthDTO=queryQuickOrderAuth(enterpriseDTOS,null);
                    if(tmpQuickOrderAuthDTO.getCanQuickOrder()){
                        quickOrderAuthDTO.setCanQuickOrder(true);
                        quickOrderAuthDTO.getQuickOrderCompanyIdList().addAll(tmpQuickOrderAuthDTO.getQuickOrderCompanyIdList());
                    }
                }

                // 3、查询作为专属销售，能看到的自己所负责的所有客户和联系人，这些都属于间接管理的企业
                Result<List<SysDealerSalesRelationRespDTO>> companyIdsResult = sysDealerSalesRelationFeign.listSysDealerSalesRelationBySalesIds(Arrays.asList(userId));
                Assert.isTrue(companyIdsResult.getSuccess(), "获取专属销售失败");
                List<SysDealerSalesRelationRespDTO> sysDealerSalesRelationRespDTOS = companyIdsResult.getData();
                if (CollectionUtils.isEmpty(sysDealerSalesRelationRespDTOS)) {
                    return quickOrderAuthDTO;
                }

                Map<String,List<String>> enterpriseDealerUserMap=sysDealerSalesRelationRespDTOS.stream().collect(Collectors.groupingBy(SysDealerSalesRelationRespDTO::getEnterpriseId,Collectors.mapping(SysDealerSalesRelationRespDTO::getDealerUserId,Collectors.toList())));
                companyIds=companyIdsResult.getData().stream().map(SysDealerSalesRelationRespDTO::getEnterpriseId).toList();
                if(CollectionUtils.isNotEmpty(companyIds)){
                    //基于companyIds检索企业
                    List<EnterpriseDTO> enterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(companyIds).build());
                    QuickOrderAuthDTO tmpQuickOrderAuthDTO=queryQuickOrderAuth(enterpriseDTOS,enterpriseDealerUserMap);
                    if(tmpQuickOrderAuthDTO.getCanQuickOrder()){
                        quickOrderAuthDTO.setCanQuickOrder(true);
                        quickOrderAuthDTO.getQuickOrderCompanyIdList().addAll(tmpQuickOrderAuthDTO.getQuickOrderCompanyIdList());
                    }
                }

                //最后再做一次去重
                if(quickOrderAuthDTO.getCanQuickOrder()){
                    Map<String,QuickOrderEnterpriseDTO> partnerEnterpriseMap=quickOrderAuthDTO.getQuickOrderCompanyIdList().stream().collect(Collectors.toMap(QuickOrderEnterpriseDTO::getEnterpriseId, Function.identity(),(a,b)->a.getIsManager()?a:b));
                    quickOrderAuthDTO.setQuickOrderCompanyIdList(partnerEnterpriseMap.values().stream().toList());
                }
                return quickOrderAuthDTO;
            }
        }else{
            //作为外部用户，查询该用户所有关联的企业
            PageExternalEnterpriseQueryDTO pageExternalEnterpriseQueryDTO=new PageExternalEnterpriseQueryDTO();
            pageExternalEnterpriseQueryDTO.setDealerUserId(userId);
            Result<List<ExternalEnterpriseRespDTO>> result=sysDealerSalesRelationFeign.listExternalEnterprise(pageExternalEnterpriseQueryDTO);
            Assert.isTrue(result.getSuccess(),"获取用户关联企业失败");
            if(CollectionUtils.isNotEmpty(result.getData())){
                List<String> companyIds=result.getData().stream().map(ExternalEnterpriseRespDTO::getEnterpriseId).toList();
                Map<String,List<String>> enterpriseDealerUserMap=new HashMap<>();
                companyIds.forEach(item->{
                    enterpriseDealerUserMap.put(item,Arrays.asList(userId));
                });
                //基于companyIds检索企业
                List<EnterpriseDTO> enterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(companyIds).build());
                if(CollectionUtils.isNotEmpty(enterpriseDTOS)){
                    QuickOrderAuthDTO quickOrderAuthDTO=queryQuickOrderAuth(enterpriseDTOS,enterpriseDealerUserMap);
                    quickOrderAuthDTO.setUserType("EXTERNAL");
                    return quickOrderAuthDTO;
                }
            }
        }

        return QuickOrderAuthDTO.builder().canQuickOrder(false).build();
    }


    /**
     * 基于名下企业和可见的用户，检测是否可下快速订单
     * @param enterpriseDTOS
     * @return
     */
    private QuickOrderAuthDTO queryQuickOrderAuth(List<EnterpriseDTO> enterpriseDTOS,Map<String,List<String>> enterpriseDealerUserMap){
        Boolean partnerCanQuickOrder=false;
        Boolean businessCanQuickOrder=false;

        List<QuickOrderEnterpriseDTO> partnerEnterpriseIds=new ArrayList<>();

        //分别过滤出生态伙伴和工商业客户
        List<EnterpriseDTO> partnerEnterpriseDTOS=enterpriseDTOS.stream().filter(item->item.getCustomerCategory().equals(EnterpriseTypeEnum.PARTNER.getCode())).toList();
//        List<EnterpriseDTO> businessEnterpriseDTOS=enterpriseDTOS.stream().filter(item->item.getCustomerCategory().equals(EnterpriseTypeEnum.BUSINESS.getCode())).toList();
        List<EnterpriseDTO> businessEnterpriseDTOS=enterpriseDTOS;


        //1、首先判断是否存在生态伙伴，如果存在其生态伙伴、则查询该生态伙伴签署过有效的授权委托书
        if(CollectionUtils.isNotEmpty(partnerEnterpriseDTOS)){
            List<String> partnerCompanyIds=partnerEnterpriseDTOS.stream().map(EnterpriseDTO::getEnterpriseId).toList();
            List<EnterpriseBizAuthorizePO> enterpriseBizAuthorizePOS=listValidEnterpriseBizAuthorize(partnerCompanyIds, DateUtils.format(new Date(),"yyyy"));
            if(CollectionUtils.isNotEmpty(enterpriseBizAuthorizePOS)){
                if(enterpriseDealerUserMap==null){
                    //1.1、如果签署过有效的授权委托书，且不用进行人员的判断，则表明可以进行生态伙伴下渠道订单
                    partnerEnterpriseIds.addAll(enterpriseBizAuthorizePOS.stream().map(item-> QuickOrderEnterpriseDTO.builder().enterpriseId(item.getEnterpriseId()).isManager(true).build()).toList());
                    partnerCanQuickOrder=true;
                }else{
                    //1.2、否则判断该企业下可见的的用户，是否存在签署了授权委托书的，如果存在，则表明可以进行生态伙伴下渠道订单
                    for (EnterpriseBizAuthorizePO enterpriseBizAuthorizePO : enterpriseBizAuthorizePOS) {
                        if(enterpriseDealerUserMap.containsKey(enterpriseBizAuthorizePO.getEnterpriseId())&&
                                enterpriseDealerUserMap.get(enterpriseBizAuthorizePO.getEnterpriseId()).contains(enterpriseBizAuthorizePO.getAuthUserId())){
                            partnerEnterpriseIds.add(QuickOrderEnterpriseDTO.builder().enterpriseId(enterpriseBizAuthorizePO.getEnterpriseId()).isManager(true).build());
                            partnerCanQuickOrder=true;
                        }
                    }
                }
            }
        }

        //2、再判断工商业客户
        if(CollectionUtils.isNotEmpty(businessEnterpriseDTOS)){
            List<String> businessCompanyIds=businessEnterpriseDTOS.stream().map(EnterpriseDTO::getEnterpriseId).toList();
            List<EnterpriseBizAuthorizePO> enterpriseBizAuthorizePOS=listValidEnterpriseBizAuthorize(businessCompanyIds, DateUtils.format(new Date(),"yyyy"));
            if(CollectionUtils.isNotEmpty(enterpriseBizAuthorizePOS)) {
                if (enterpriseDealerUserMap == null) {
                    //2.1、如果工商业客户已经签署授权委托，且不用进行人员的判断，则初步校验通过
                    businessCanQuickOrder = true;
                    businessCompanyIds=enterpriseBizAuthorizePOS.stream().map(EnterpriseBizAuthorizePO::getEnterpriseId).toList();
                } else {
                    //2.2、否则判断该企业下有权限的的用户，是否存在签署了授权委托书的，如果存在，则初步校验通过
                    businessCompanyIds.clear();
                    for (EnterpriseBizAuthorizePO enterpriseBizAuthorizePO : enterpriseBizAuthorizePOS) {
                        if (enterpriseDealerUserMap.containsKey(enterpriseBizAuthorizePO.getEnterpriseId())&&
                                enterpriseDealerUserMap.get(enterpriseBizAuthorizePO.getEnterpriseId()).contains(enterpriseBizAuthorizePO.getAuthUserId())) {
                            businessCanQuickOrder = true;
                            businessCompanyIds.add(enterpriseBizAuthorizePO.getEnterpriseId());
                        }
                    }
                }

                //2.3、如果初步判断通过，还需要校验此工商业客户已经关联了生态伙伴
                if (businessCanQuickOrder) {
                    //首先反向查询关联企业
                    List<EnterpriseRelationPO> enterpriseRelationPOS = enterpriserelationMapperService.list(new LambdaQueryWrapper<EnterpriseRelationPO>()
                                    .in(EnterpriseRelationPO::getLinkEnterpriseId, businessCompanyIds)
                                    .eq(EnterpriseRelationPO::getIsDeleted, 0)
                                    .eq(EnterpriseRelationPO::getLinkEnterpriseType, EnterpriseTypeEnum.CAPITAL.getCode()))
                            .stream().toList();

                    //如果反向关联企业不会空，则判断是否存在生态伙伴
                    if (CollectionUtils.isNotEmpty(enterpriseRelationPOS)) {
                        List<String> linkedCompanyIds = enterpriseRelationPOS.stream().map(EnterpriseRelationPO::getEnterpriseId).toList();
                        List<EnterpriseDTO> linkedEnterpriseDTOS = enterpriseMapper.listByQuery(EnterpriseQueryDTO.builder().enterpriseIds(linkedCompanyIds).build());
                        partnerEnterpriseDTOS = linkedEnterpriseDTOS.stream().filter(item -> item.getCustomerCategory().equals(EnterpriseTypeEnum.PARTNER.getCode())).toList();
                        if (CollectionUtils.isEmpty(partnerEnterpriseDTOS)) {
                            //如果反向关联企业不存在生态伙伴，则终极校验失败
                            businessCanQuickOrder = false;
                        }else{
                            partnerEnterpriseIds.addAll(partnerEnterpriseDTOS.stream().map(item->QuickOrderEnterpriseDTO.builder().enterpriseId(item.getEnterpriseId()).isManager(false).build()).toList());
                        }
                    }
                }
            }
        }
        Map<String,QuickOrderEnterpriseDTO> partnerEnterpriseMap=partnerEnterpriseIds.stream().collect(Collectors.toMap(QuickOrderEnterpriseDTO::getEnterpriseId, Function.identity(),(a,b)->a.getIsManager()?a:b));
        partnerEnterpriseIds=partnerEnterpriseMap.values().stream().toList();

        //增加区域权限控制
        List<EnterpriseBizRelationResDTO> enterpriseBizRelationResDTOS=enterpriseBizRelationService.listByEnterpriseIds(partnerEnterpriseIds.stream().map(QuickOrderEnterpriseDTO::getEnterpriseId).toList());
        Map<String,EnterpriseBizRelationResDTO> hasPermissionEnterprisMap=enterpriseBizRelationResDTOS.stream().filter(item->checkOrgCode.contains(item.getBizOrganizationCode())).collect(Collectors.toMap(EnterpriseBizRelationResDTO::getEnterpriseId, Function.identity(),(a,b)->a));
        partnerEnterpriseIds=partnerEnterpriseMap.values().stream().filter(item->hasPermissionEnterprisMap.containsKey(item.getEnterpriseId())).toList();

        if(CollectionUtils.isNotEmpty(partnerEnterpriseIds)){
            if(businessCanQuickOrder&&partnerCanQuickOrder){
                return QuickOrderAuthDTO.builder().canQuickOrder(true).quickOrderType(QuickOrderTypeEnum.ALL.getCode()).quickOrderCompanyIdList(partnerEnterpriseIds).build();
            }else if(!businessCanQuickOrder&&partnerCanQuickOrder) {
                return QuickOrderAuthDTO.builder().canQuickOrder(true).quickOrderType(QuickOrderTypeEnum.PARTNER.getCode()).quickOrderCompanyIdList(partnerEnterpriseIds).build();
            }else if(businessCanQuickOrder&&!partnerCanQuickOrder) {
                return QuickOrderAuthDTO.builder().canQuickOrder(true).quickOrderType(QuickOrderTypeEnum.BUSINESS.getCode()).quickOrderCompanyIdList(partnerEnterpriseIds).build();
            }else{
                return QuickOrderAuthDTO.builder().canQuickOrder(false).build();
            }
        }else{
            //直接返回无权限
            return QuickOrderAuthDTO.builder().canQuickOrder(false).quickOrderCompanyIdList(Collections.emptyList()).build();
        }
    }

    private List<EnterpriseBizAuthorizePO> listValidEnterpriseBizAuthorize(List<String> partnerCompanyIds, String authYear) {
        return enterpriseBizAuthorizeMapperService.list(new LambdaQueryWrapper<EnterpriseBizAuthorizePO>().in(EnterpriseBizAuthorizePO::getEnterpriseId,partnerCompanyIds)
                .eq(EnterpriseBizAuthorizePO::getAuthYear,authYear)
                .eq(EnterpriseBizAuthorizePO::getAuthStatus, EnterpriseInfoConstants.ENTERPRISE_AUTH_CONFIRM)
                .eq(EnterpriseBizAuthorizePO::getIsDeleted,0));
    }
}
