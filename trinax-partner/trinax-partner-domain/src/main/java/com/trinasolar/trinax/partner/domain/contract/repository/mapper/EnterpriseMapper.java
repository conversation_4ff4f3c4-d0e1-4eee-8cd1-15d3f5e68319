package com.trinasolar.trinax.partner.domain.contract.repository.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.partner.domain.contract.repository.po.EnterprisePO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseBizAuthorizeQueryDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseCapitalQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryDTO;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryReqDTO;
import com.trinasolar.trinax.partner.dto.input.statistic.StatisticEnterpriseNumReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizAuthorizeDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseCapitalResPcDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseResPcDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationPcDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Mapper
public interface EnterpriseMapper extends BaseMapper<EnterprisePO> {

    default EnterprisePO selectByEnterpriseId(String enterpriseId) {
        LambdaQueryWrapper<EnterprisePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(EnterprisePO::getEnterpriseId, enterpriseId);
        return selectOne(queryWrapper);
    }

    default List<EnterprisePO> getEnterpriseByEnterpriseIds(List<String> ids) {
        if (ObjectUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EnterprisePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(EnterprisePO::getEnterpriseId, ids);
        return selectList(queryWrapper);
    }

    IPage<EnterpriseResPcDTO> pageEnterpriseResPC(IPage<?> page, @Param("query") EnterpriseQueryReqDTO query);

    List<EnterpriseResPcDTO> listEnterpriseResPc(@Param("query") EnterpriseQueryReqDTO reqDTO);

    List<EnterpriseDTO> listByQuery(@Param("query") EnterpriseQueryDTO queryDTO);

    IPage<EnterpriseDTO> pageByQuery(IPage<?> page, @Param("query") EnterpriseQueryDTO query);

    IPage<EnterpriseCapitalResPcDTO> pageEnterpriseCapitalResPC(IPage<?> page, @Param("query") EnterpriseCapitalQueryReqDTO query);

    IPage<EnterpriseRelationPcDTO> pageEnterpriseRelationPcDTO(IPage<?> page, @Param("enterpriseIds") List<String> enterpriseIds);

    int countStatisticEnterpriseNum(@Param("query") StatisticEnterpriseNumReqDTO reqDTO);

    IPage<EnterpriseBizAuthorizeDTO> queryAuthorizeByPage(IPage<?> page, @Param("query") EnterpriseBizAuthorizeQueryDTO query);
}
