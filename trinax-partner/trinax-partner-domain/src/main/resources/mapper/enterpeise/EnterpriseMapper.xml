<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.trinax.partner.domain.contract.repository.mapper.EnterpriseMapper">
    <sql id="resPcSql">
        select ep.enterprise_id,
        ep.name,
        ep.tax_number,
        ep.detail_region,
        ep.reg_status,
        ep.legal_person_name,
        ep.reg_number,
        ep.org_number,
        ep.company_org_type,
        ep.industry,
        ep.credit_code,
        ep.reg_capital,
        ep.created_by,
        ep.created_name,
        ep.created_time,
        ep.updated_by,
        ep.updated_name,
        ep.updated_time,
        eb.sf_id,
        eb.customer_status,
        eb.customer_category,
        eb.customer_org_nature,
        eb.business_line,
        eb.customer_role,
        eb.is_region_ka,
        eb.customer_level,
        eb.intercompany_order_type,
        eb.main_sales_user_id,
        eb.main_sales_user_name,
        eb.erp_business_entity,
        eb.erp_tax_rate,
        eb.currency,
        eb.mdm_id,
        eb.currency,
        eb.erp_business_entity,
        eb.erp_tax_rate,
        ebr.biz_organization_code,
        ea.country_name,
        ea.province_name,
        ea.city_name,
        ea.district_name,
        ea.address_detail regLocation
        from enterprise ep
        join enterprise_biz eb on ep.enterprise_id = eb.enterprise_id
        left join enterprise_biz_relation ebr on ep.enterprise_id = ebr.enterprise_id
        left join enterprise_address ea on ep.enterprise_id = ea.enterprise_id and ea.address_type = 'REGIST'
        where 1 = 1
        <if test="query.enterpriseId != null and query.enterpriseId != ''">
            and ep.enterprise_id like concat('%',#{query.enterpriseId},'%')
        </if>
        <if test="query.name != null and query.name != ''">
            and ep.name like concat('%',#{query.name},'%')
        </if>
        <if test="query.taxNumber != null and query.taxNumber != ''">
            and ep.tax_number like concat('%',#{query.taxNumber},'%')
        </if>
        <if test="query.legalPersonName != null and query.legalPersonName != ''">
            and ep.legal_person_name like concat('%',#{query.legalPersonName},'%')
        </if>
        <if test="query.regNumber != null and query.regNumber != ''">
            and ep.reg_number like concat('%',#{query.regNumber},'%')
        </if>
        <if test="query.creditCode != null and query.creditCode != ''">
            and ep.credit_code like concat('%',#{query.creditCode},'%')
        </if>
        <if test="query.mainSalesUserName != null and query.mainSalesUserName != ''">
            and eb.main_sales_user_name like concat('%',#{query.mainSalesUserName},'%')
        </if>

        <if test="query.customerStatus != null and query.customerStatus != ''">
            and eb.customer_status like concat('%',#{query.customerStatus},'%')
        </if>
        <if test="query.customerCategory != null and query.customerCategory != ''">
            and eb.customer_category like concat('%',#{query.customerCategory},'%')
        </if>
        <if test="query.customerOrgNature != null and query.customerOrgNature != ''">
            and eb.customer_org_nature like concat('%',#{query.customerOrgNature},'%')
        </if>
        <if test="query.businessLine != null and query.businessLine != ''">
            and eb.business_line like concat('%',#{query.businessLine},'%')
        </if>
        <if test="query.customerRole != null and query.customerRole != ''">
            and eb.customer_role like concat('%',#{query.customerRole},'%')
        </if>
        <if test="query.mdmId != null and query.mdmId != ''">
            and eb.mdm_id like concat('%',#{query.mdmId},'%')
        </if>
        <if test="query.bizOrganizationCode != null and query.bizOrganizationCode != ''">
            and ebr.biz_organization_code = #{query.bizOrganizationCode}
        </if>
        <choose>
            <when test="query.salesUserIds != null and query.salesUserIds.size() > 0 and query.enterpriseIds != null and query.enterpriseIds.size() > 0">
                and (eb.main_sales_user_id in
                <foreach item="item" collection="query.salesUserIds" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or ep.enterprise_id in
                <foreach item="item" collection="query.enterpriseIds" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </when>
            <otherwise>
                <if test="query.salesUserIds != null and query.salesUserIds.size() > 0">
                    and eb.main_sales_user_id in
                    <foreach item="item" collection="query.salesUserIds" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.enterpriseIds != null and query.enterpriseIds.size() > 0">
                    and ep.enterprise_id in
                    <foreach item="item" collection="query.enterpriseIds" index="index" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
    </sql>
    <select id="pageEnterpriseResPC" resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseResPcDTO">
        <include refid="resPcSql"/>
    </select>

    <select id="listEnterpriseResPc" resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseResPcDTO">
        <include refid="resPcSql"/>
    </select>


    <sql id="querySql">
        select ep.enterprise_id,
        ep.name,
        ep.tax_number,
        ep.detail_region,
        ep.reg_status,
        ep.legal_person_name,
        ep.reg_number,
        ep.org_number,
        ep.company_org_type,
        ep.industry,
        ep.credit_code,
        ep.reg_capital,
        ep.registed_time,
        ep.created_by,
        ep.created_name,
        ep.created_time,
        ep.updated_by,
        ep.updated_name,
        ep.updated_time,
        eb.sf_id,
        eb.customer_status,
        eb.customer_category,
        eb.customer_org_nature,
        eb.business_line,
        eb.customer_role,
        eb.is_region_ka,
        eb.customer_level,
        eb.intercompany_order_type,
        eb.main_sales_user_id,
        eb.main_sales_user_name,
        eb.erp_business_entity,
        eb.erp_tax_rate,
        eb.currency,
        eb.mdm_id,
        eb.status,
        eb.subordinate_group,
        eb.source_system,
        eb.transaction_scale,
        eb.main_segment,
        eb.main_market_segment,
        eb.main_channel_type,
        eb.legal_person_mobile,
        eb.contact_name,
        eb.contact_mobile,
        eb.total_annual_sales,
        eb.annual_turnover,
        eb.percentage,
        eb.main_business,
        eb.main_brand,
        eb.bank_name,
        eb.bank_id,
        eb.apply_time,
        eb.industry bizIndustry,
        eb. admission_user_id,
        eb. admission_user_name,
        eb. admission_time,
        eb. admission_status,
        eb. admission_desc
        from enterprise ep
        join enterprise_biz eb on ep.enterprise_id = eb.enterprise_id
        where 1 = 1
        <if test="query.enterpriseId != null and query.enterpriseId != ''">
            and ep.enterprise_id = #{query.enterpriseId}
        </if>
        <if test="query.enterpriseIds != null and query.enterpriseIds.size() > 0">
            and ep.enterprise_id in
            <foreach item="item" collection="query.enterpriseIds" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.taxNumber != null and query.taxNumber != ''">
            and ep.tax_number = #{query.taxNumber}
        </if>
        <if test="query.sfId != null and query.sfId != ''">
            and eb.sf_id = #{query.sfId}
        </if>
        <if test="query.customerCategory != null and query.customerCategory != ''">
            and eb.customer_category like concat('%',#{query.customerCategory},'%')
        </if>
    </sql>

    <select id="listByQuery" resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseDTO">
        <include refid="querySql"/>
    </select>

    <select id="pageByQuery" resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseDTO">
        <include refid="querySql"/>
    </select>

    <select id="pageEnterpriseCapitalResPC"
            resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseCapitalResPcDTO">
        select ep.enterprise_id,
        ep.name,
        ep.reg_status,
        ep.reg_number,
        ep.credit_code,
        ea.province_name
        from enterprise ep
        join enterprise_biz eb on ep.enterprise_id = eb.enterprise_id
        left join enterprise_address ea on ep.enterprise_id = ea.enterprise_id and ea.address_type = 'REGIST'
        where ep.enterprise_id not in
        <foreach item="item" collection="query.ninEnterpriseIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="query.queryP != null and query.queryP != ''">
            and (ep.name like concat('%',#{query.queryP},'%') or ep.credit_code like concat('%',#{query.queryP},'%'))
        </if>
    </select>

    <select id="pageEnterpriseRelationPcDTO"
            resultType="com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseRelationPcDTO">
        select ep.enterprise_id,
        ep.name enterpriseName,
        ep.legal_person_name,
        ep.tax_number,
        eb.customer_role,
        eb.currency,
        ep.reg_status,
        ep.reg_number,
        ep.credit_code,
        ea.province_name
        from enterprise ep
        join enterprise_biz eb on ep.enterprise_id = eb.enterprise_id
        left join enterprise_address ea on ep.enterprise_id = ea.enterprise_id and ea.address_type = 'REGIST'
        where ep.enterprise_id in
        <foreach item="item" collection="enterpriseIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="countStatisticEnterpriseNum" resultType="int">
        select
        count(1)
        from
        enterprise e
        join enterprise_biz eb on
        e.enterprise_id = eb.enterprise_id
        join enterprise_biz_relation ebr on
        e.enterprise_id = ebr.enterprise_id
        where
        eb.customer_category like concat('%',#{query.customerCategory},'%')
        <if test="query.bizOrganizationCode != null and query.bizOrganizationCode != ''">
            and ebr.biz_organization_code = #{query.bizOrganizationCode}
        </if>
        <if test="query.bizStatus != null and query.bizStatus != ''">
            and eb.status = #{query.bizStatus}
        </if>
        <if test="query.createTimeStart != null">
            and e.created_time &gt;= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            and e.created_time &lt;= #{query.createTimeEnd}
        </if>
    </select>

    <select id="queryAuthorizeByPage" resultType="com.trinasolar.trinax.partner.dto.output.EnterpriseBizAuthorizeDTO">
        select * from enterprise_biz_authorize
        where is_deleted = 0
        <if test="query.authStatus != null and query.authStatus != ''">
            and auth_status = #{query.authStatus}
        </if>
        <if test="query.authYear != null and query.authYear != ''">
            and auth_year = #{query.authYear}
        </if>
        <if test="query.enterpriseId != null and query.enterpriseId != ''">
            and enterprise_id = #{query.enterpriseId}
        </if>
    </select>
</mapper>
