package com.trinasolar.trinax.user.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.dto.mq.EnterpriseBindMqDTO;
import com.trinasolar.trinax.user.manager.CoBindMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class CoBindAuthFailedReAuthMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private CoBindMessageManager coBindMessageManager;

    @Value("${coBind.auth.failed.reAuthTodo}")
    private String todoTemplateCode;


    public void execute(EnterpriseBindMqDTO reqDTO) {
        log.info("外部用户对认证失败的企业绑定重启认证，给专属销售用户发待办消息,入参：{}", reqDTO);
        Map<String, String> content = new HashMap<>();
        content.put("externalUserName", reqDTO.getExternalUserName());
        content.put("enterpriseName", reqDTO.getEnterpriseName());

        //相同的人只发一个待办消息
//        Set<String> userIdSet = new HashSet<>();
//        userIdSet.add(reqDTO.getMainSaleUserId());
//        userIdSet.addAll(reqDTO.getSaleUserIdList());
//        List<String> userIdList = new ArrayList<>(userIdSet);

        MessageSendCommonReqDTO todoMessageReqDTO = new MessageSendCommonReqDTO();
        todoMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());
        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();
        String bizNo = reqDTO.getExternalUserId() + "|" + reqDTO.getEnterpriseId();
        MessageSendTodoReqDTO todoReqDTO = coBindMessageManager.generateTodo(reqDTO,
                todoTemplateCode, TodoBizCodeEnum.CO_BIND_AUTH_FAILED_RE_AUTH.getCode(),bizNo,
                reqDTO.getSaleUserIdList(),content, TodoOrganizationTypeEnum.SALES.getCode());

        todoList.add(todoReqDTO);
        todoMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(todoMessageReqDTO));
        log.info("外部用户对认证失败的企业绑定重启认证，给专属销售用户发待办消息完成，param:{}", JacksonUtil.bean2Json(todoMessageReqDTO));
    }
}
