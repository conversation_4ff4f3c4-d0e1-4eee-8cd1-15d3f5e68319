package com.trinasolar.trinax.user.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeSituationEnum;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SysSalesOperationRelationReqDTO;
import com.trinasolar.trinax.user.dto.input.changeowner.UserChangeOwnerReqDTO;
import com.trinasolar.trinax.user.dto.output.SysEnterpriseUserRelationResDTO;
import com.trinasolar.trinax.user.dto.output.SysOrganizationSalesRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import com.trinasolar.trinax.user.manager.UserRelationManager;
import com.trinasolar.trinax.user.repository.mapper.SysSalesOperationRelationMapper;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysSalesOperationRelationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import com.trinasolar.trinax.user.service.SysSalesOperationRelationService;
import com.trinasolar.trinax.user.service.SysSalesOrganizationRelationService;
import com.trinasolar.trinax.user.service.biz.changeowner.ManagerRelationChangeOwnerBiz;
import com.trinasolar.trinax.user.service.biz.changeowner.ManagerSubChangeOwnerBiz;
import com.trinasolar.trinax.user.service.biz.changeowner.OperationChangeOwnerBiz;
import com.trinasolar.trinax.user.service.biz.changeowner.SalesExternalChangeOwnerBiz;
import dtt.cache.redisclient.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SysSalesOperationRelationServiceImpl extends ServiceImpl<SysSalesOperationRelationMapper, SysSalesOperationRelationPO> implements SysSalesOperationRelationService {

    private final UserRelationManager userRelationManager;
    private final SysSalesOrganizationRelationService sysSalesOrganizationRelationService;
    private final RedisUtil redisUtil;
    private final EnterpriseFeign enterpriseFeign;
    private final SysUserMapper sysUserMapper;
    private final SalesExternalChangeOwnerBiz salesExternalChangeOwnerBiz;
    private final OperationChangeOwnerBiz operationChangeOwnerBiz;
    private final ManagerRelationChangeOwnerBiz managerRelationChangeOwnerBiz;
    private final ManagerSubChangeOwnerBiz managerSubChangeOwnerBiz;

    @Override
    public List<SysEnterpriseUserRelationResDTO> listEnterpriseUserRelation(String userId, String enterpriseUserType) {
        return userRelationManager.listEnterpriseUserRelation(userId, enterpriseUserType);
    }

    @Override
    public Result<SysSalesOperationRelationRespDTO> getOperationBySalesUserId(SysSalesOperationRelationReqDTO req) {

        String redisKey = UserConstant.DATA_OPERATION_BIZ_CACHE_PREFIX + req.getBizOrganizationCode() + req.getSalesUserId();
        String cache = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(cache)) {
            return Result.ok(JacksonUtil.json2Bean(cache, SysSalesOperationRelationRespDTO.class));
        }
        SysSalesOperationRelationPO relation = this.lambdaQuery()
                .eq(SysSalesOperationRelationPO::getSalesUserId, req.getSalesUserId())
                .eq(SysSalesOperationRelationPO::getBizOrganizationCode, req.getBizOrganizationCode())
                .one();
        if (relation == null) {
            return Result.fail("当前销售运营关系不存在");
        }

        SysSalesOperationRelationRespDTO resp = BeanUtil.copyProperties(relation, SysSalesOperationRelationRespDTO.class);
        //运营人员添加名字返回
        List<SysUserPO> userPOList = sysUserMapper.getUsersByUserIds(Collections.singletonList(resp.getOperationUserId()));
        if (!CollectionUtils.isEmpty(userPOList)) {
            resp.setOperationUserName(userPOList.get(0).getUserName());
        }

        cache = JacksonUtil.bean2Json(resp);
        redisUtil.set(redisKey, cache, UserConstant.USER_CACHE_TIME);
        return Result.ok(resp);
    }

    /**
     * 根据当前运营的ID
     * 查询自己和所有下级对应的所有的销售
     *
     * @param operationId 当前运营
     */
    @Override
    public Result<List<SysSalesOperationRelationRespDTO>> getSysSalesListByOps(String operationId) {
        // 检查缓存
        String cache = redisUtil.get(UserConstant.DATA_PERMISSION_OPERATION_CACHE_PREFIX + operationId);
        if (StringUtils.isNotBlank(cache)) {
            return Result.ok(JacksonUtil.json2List(cache, SysSalesOperationRelationRespDTO.class));
        }
        // 查到所有的下级(包含自己)
        List<String> followerIdList = sysSalesOrganizationRelationService.getSubordinateSalesUserId(operationId);
        // 查出所有运营对应的所有销售
        List<SysSalesOperationRelationPO> saleOpsList = this.lambdaQuery().in(SysSalesOperationRelationPO::getOperationUserId, followerIdList).list();
        // 类型转换
        List<SysSalesOperationRelationRespDTO> result = BeanUtil.copyToList(saleOpsList, SysSalesOperationRelationRespDTO.class);
        // 放入缓存
        redisUtil.set(UserConstant.DATA_PERMISSION_OPERATION_CACHE_PREFIX + operationId, JacksonUtil.bean2Json(result), UserConstant.DATA_PERMISSION_CACHE_TIME);
        return Result.ok(result);
    }

    /**
     * 根据当前运营的ID
     * 查询自己对应的所有的销售(及服务组织)
     *
     * @param operationId 当前运营
     */
    @Override
    public List<SysOrganizationSalesRespDTO> getSysSalesOrganizationByOps(String operationId) {
        return this.baseMapper.getSysSalesOrganizationByOps(operationId);
    }

    /**
     * 根据enterpriseId
     * 查询对应战区的所有的销售
     *
     * @param enterpriseId
     */
    @Override
    public List<SysOrganizationSalesRespDTO> getEnterpiseRegionSalesmen(String enterpriseId) {
        List<EnterpriseBizRelationResDTO> enterpriseBizRelationByEnterpriseId = enterpriseFeign.
                getEnterpriseBizRelationByEnterpriseId(enterpriseId).getData();
        List<String> bizOrganizationCodes = enterpriseBizRelationByEnterpriseId.stream().
                map(EnterpriseBizRelationResDTO::getBizOrganizationCode).toList();

        return sysUserMapper.getSalesmen(bizOrganizationCodes);
    }

    @Override
    public List<SysSalesOperationRelationRespDTO> listByOperationUserIds(List<String> operationUserIds) {
        if (ObjectUtil.isEmpty(operationUserIds)) {
            return Collections.emptyList();
        }
        List<SysSalesOperationRelationPO> sysSalesOperationRelationPOS = lambdaQuery().in(SysSalesOperationRelationPO::getOperationUserId, operationUserIds).list();
        return BeanUtil.copyToList(sysSalesOperationRelationPOS, SysSalesOperationRelationRespDTO.class);
    }

    @Override
    public Result<String> changeOwner(UserChangeOwnerReqDTO reqDTO) {
        if(ChangeSituationEnum.OPERATION_RELATION_SALES.getCode().equals(reqDTO.getSituationCode())){
            return operationChangeOwnerBiz.changeOwner(reqDTO);
        }else if(ChangeSituationEnum.AREA_MANAGER_SUB_REPORT.getCode().equals(reqDTO.getSituationCode())){
            return managerSubChangeOwnerBiz.changeOwner(reqDTO);
        }else if(ChangeSituationEnum.AREA_MANAGER_RELATION_REPORT.getCode().equals(reqDTO.getSituationCode())){
            return managerRelationChangeOwnerBiz.changeOwner(reqDTO);
        }else if(ChangeSituationEnum.EXTERNAL_RELATION_SALES.getCode().equals(reqDTO.getSituationCode()) ||
                ChangeSituationEnum.SALES_RELATION_SALES.getCode().equals(reqDTO.getSituationCode())){
            return salesExternalChangeOwnerBiz.changeOwner(reqDTO);
        }else{
            return Result.ok();
        }
    }

}