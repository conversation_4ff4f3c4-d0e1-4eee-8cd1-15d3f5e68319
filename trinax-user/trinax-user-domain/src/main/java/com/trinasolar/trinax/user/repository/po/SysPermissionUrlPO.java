package com.trinasolar.trinax.user.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "sys_permission_url")
public class SysPermissionUrlPO {

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "自增主键")
    private Long id;

    @Schema(description = "系统权限ID")
    private String permissionId;

    @Schema(description = "系统权限编码")
    private String permissionCode;

    @Schema(description = "系统权限路径")
    private String permissionUrl;

    @Schema(description = "系统权限描述")
    private String permissionUrlDesc;

    @Schema(description = "是否已删除;1：已删除 0：未删除")
    private Integer isDeleted;

    private String createdBy;

    private String createdName;

    private LocalDateTime createdTime;

    private String updatedBy;

    private String updatedName;

    private LocalDateTime updatedTime;
}
