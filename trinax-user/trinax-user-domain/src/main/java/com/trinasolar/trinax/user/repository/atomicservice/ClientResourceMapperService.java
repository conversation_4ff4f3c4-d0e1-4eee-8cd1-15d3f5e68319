package com.trinasolar.trinax.user.repository.atomicservice;

import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.trinax.user.repository.po.ClientResourcePO;
import com.trinasolar.trinax.user.repository.po.ResourcePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <pre>
 * Title: 角色资源dao
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
public interface ClientResourceMapperService extends IService<ClientResourcePO> {

    /**
     * 客户端id查询资源
     *
     * @param clientId
     * @return
     */
    List<ResourcePO> findResourcesByClientId(String clientId);

    /**
     * 获取可分配资源信息
     * @param clientId clientId
     * @return List<ResourceModel>
     */
    List<ResourcePO> findResourcesEnableByClientId(String clientId);

    List<ResourcePO> findResourcesEnableByClientIdAndRoleCodes(@Param("clientId") String clientId,@Param("roleCodes")  List<String> roleCodes);
}
