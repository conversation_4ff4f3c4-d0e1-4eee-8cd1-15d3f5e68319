package com.trinasolar.trinax.user.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.AllocateMainSalesDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import com.trinasolar.trinax.user.constants.CoBindMessageSituationEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.constants.UserResultCode;
import com.trinasolar.trinax.user.dto.input.relation.EnterpriseBindReqDTO;
import com.trinasolar.trinax.user.dto.mq.EnterpriseBindMqDTO;
import com.trinasolar.trinax.user.dto.output.authentication.AuthenticationResDTO;
import com.trinasolar.trinax.user.manager.EnterpriseRemoteManager;
import com.trinasolar.trinax.user.repository.atomicservice.SysDealerSalesRelationMapperService;
import com.trinasolar.trinax.user.repository.mapper.SysUserMapper;
import com.trinasolar.trinax.user.repository.po.SysDealerSalesRelationPO;
import com.trinasolar.trinax.user.repository.po.SysUserPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class ExternalBindEnterpriseBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private EnterpriseFeign enterpriseFeign;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysDealerSalesRelationMapperService sysDealerSalesRelationMapperService;

    @Autowired
    private EnterpriseRemoteManager enterpriseRemoteManager;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private TransactionTemplate transactionTemplate;

    public Result<AuthenticationResDTO> bindEnterprise(EnterpriseBindReqDTO reqDTO) {
        //查询企业主销售：不存在主销售，需要给销售区域管理发送消息；存在主销售，需要给主销售发送消息
        long start = System.currentTimeMillis();
        EnterpriseDTO enterpriseDTO = enterpriseRemoteManager.findByEnterpriseId(reqDTO.getEnterpriseId());
        Result<List<EnterpriseBizRelationResDTO>> enterpriseIdBizResult=enterpriseFeign.getEnterpriseBizRelationByEnterpriseId(reqDTO.getEnterpriseId());
        boolean success = enterpriseIdBizResult.getSuccess();
        if(!success ||CollUtil.isEmpty(enterpriseIdBizResult.getData())){
            throw new BizException(UserResultCode.ENTERPRISE_BIZ_NOT_EXIST.getCode(), UserResultCode.ENTERPRISE_BIZ_NOT_EXIST.getMessage());
        }
        EnterpriseBizRelationResDTO enterpriseBizRelationResDTO=enterpriseIdBizResult.getData().get(0);
        long end = System.currentTimeMillis();
        String mainSaleUserId = enterpriseDTO.getMainSalesUserId();
        String enterpriseId = reqDTO.getEnterpriseId();
        log.error("bindEnterprise-redis耗时：{}", end - start);

        SysUserPO currentSysUserPO=sysUserMapper.selectByUserId(reqDTO.getCurrentUserId());
        boolean newLogicFlag;
        if(StringUtils.isNotBlank(currentSysUserPO.getExclusiveSale())&&enterpriseBizRelationResDTO.getBizOrganizationCode().equals(currentSysUserPO.getOrganizationCode())){
            //如果该用户已经分配了专属销售,且该用户的所属区域和要绑定企业的所属区域一致，则需要走新的逻辑
            newLogicFlag=true;
        } else {
            newLogicFlag = false;
        }
        if (StringUtils.isNotEmpty(mainSaleUserId)) {
            long start3 = System.currentTimeMillis();
            Map<String, Long> timeMap = new HashMap<>();
            try {
                String handleSaleUserId;
                if(newLogicFlag){
                    handleSaleUserId=currentSysUserPO.getExclusiveSale();
                }else{
                    handleSaleUserId = mainSaleUserId;
                }
                //新增企业，销售，经销商 关联关系
                Result<AuthenticationResDTO> result = transactionTemplate.execute(ts -> {
                    timeMap.put("start2", System.currentTimeMillis());
                    saleDealerRelation(enterpriseDTO, handleSaleUserId,reqDTO);
                    timeMap.put("end2", System.currentTimeMillis());
                    sendExistSaleMessage(handleSaleUserId, reqDTO.getEnterpriseName(), reqDTO);
                    timeMap.put("end3", System.currentTimeMillis());
                    return Result.ok(AuthenticationResDTO.builder().setSupplementCoInfo(false).setEnterpriseId(enterpriseId));
                });
                long end4 = System.currentTimeMillis();
                log.error("bindEnterprise总耗时-{}，{}，{}", end4 - start3, timeMap.get("end2") - timeMap.get("start2"), timeMap.get("end3") - timeMap.get("end2"));
                return result;
            } catch (Exception e) {
                return Result.fail(e.toString());
            }
        } else {
            log.error("bindEnterprise2耗时：{},{}", end - start, mainSaleUserId);
            //查询企业关联信息，获取战区
            String bizOrgCode = getBizOrgCode(enterpriseId);
            // 查询sys_user和sys_user_role获取销售总监
            SysUserPO sysUserPO = getSaleDirector(bizOrgCode);
            if(newLogicFlag){
                //全都分配给专属销售来处理
                sysUserPO = sysUserMapper.selectByUserId(currentSysUserPO.getExclusiveSale());
            }
            SysUserPO finalSysUserPO = sysUserPO;
            return transactionTemplate.execute(ts -> {
                try {
                    //设置企业主销售信息
                    updateMainSale(enterpriseId, finalSysUserPO, reqDTO);
                    //新增企业，销售，经销商 关联关系
                    saleDealerRelation(enterpriseDTO, finalSysUserPO.getUserId(), reqDTO);
                    //发送通知消息
                    if(newLogicFlag){
                        sendExistSaleMessage(currentSysUserPO.getExclusiveSale(), reqDTO.getEnterpriseName(), reqDTO);
                    }else{
                        executorService.execute(() -> sendNoSaleMessage(finalSysUserPO.getUserId(), reqDTO.getEnterpriseName(), reqDTO));
                    }
                    return Result.ok(AuthenticationResDTO.builder().setSupplementCoInfo(false).setEnterpriseId(enterpriseId));
                } catch (Exception e) {
                    ts.setRollbackOnly();
                    return Result.fail(e.toString());
                }
            });
        }
    }

    /**
     * 场景：
     * 1、新用户注册时，会直接登录，此时认证信息中没有带用户名，后台获取登录人用户名为空；
     * 2、针对该情况，需要重新去查询用户名，进行填充；
     * 3、该用户名在发消息的场景会使用到。
     *
     * @param reqDTO
     */
    @Deprecated(since = "0630")
    private void fillLoginName(EnterpriseBindReqDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getCurrentUserName())) {
            SysUserPO sysUserPO = sysUserMapper.selectByUserId(reqDTO.getCurrentUserId());
            if (ObjectUtil.isNotEmpty(sysUserPO)) {
                reqDTO.setCurrentUserName(sysUserPO.getUserName());
            }
        }
    }

    /**
     * 查询企业对应战区
     *
     * @param enterpriseId
     * @return
     */
    private String getBizOrgCode(String enterpriseId) {
        String redisKey = "ENTERPRISE:BIZ_ORG_CODE:" + enterpriseId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            return redisVal;
        }else {
            Result<List<EnterpriseBizRelationResDTO>> result = enterpriseFeign.getEnterpriseBizRelationByEnterpriseId(enterpriseId);
            if (Boolean.TRUE.equals(!result.getSuccess()) || CollUtil.isEmpty(result.getData())) {
                throw new BizException(ResultCode.FAIL.getCode(), "查询企业战区关联信息异常");
            }
            String bizOrgCode = result.getData().get(0).getBizOrganizationCode();
            if (StringUtils.isEmpty(bizOrgCode)) {
                throw new BizException(ResultCode.FAIL.getCode(), "企业信息异常，企业战区信息为空");
            }
            redisUtil.set(redisKey, bizOrgCode, 30L);
            return bizOrgCode;
        }
    }

    /**
     * 查找销售总监
     *
     * @param bizOrgCode
     * @return
     */
    public SysUserPO getSaleDirector(String bizOrgCode) {
        String redisKey = "DIRECTOR:INFO:BIZ_ORG_CODE:" + bizOrgCode;
        String redisVal = redisUtil.get(redisKey);
        SysUserPO sysUserPO;
        if (StringUtils.isNotBlank(redisVal)) {
            sysUserPO = JacksonUtil.json2Bean(redisVal, SysUserPO.class);
        }else {
            List<SysUserPO> sysUserPOList = sysUserMapper.findUserByOrgAndRole(bizOrgCode, UserConstant.SALES_AREA_MANAGER_ROLE_ID);
            if (CollUtil.isEmpty(sysUserPOList)) {
                throw new BizException(ResultCode.FAIL.getCode(), "用户信息异常，根据战区编码没有找到销售区域管理");
            }
            sysUserPO = sysUserPOList.get(0);
            redisUtil.set(redisKey, JacksonUtil.bean2Json(sysUserPO), 30L);
        }
        return sysUserPO;
    }

    /**
     * 更新主销售信息
     *
     * @param enterpriseId
     * @param sysUserPO
     * @param reqDTO
     */
    private void updateMainSale(String enterpriseId, SysUserPO sysUserPO, EnterpriseBindReqDTO reqDTO) {
        //设置企业主销售信息
        AllocateMainSalesDTO allocateMainSalesDTO = new AllocateMainSalesDTO();
        allocateMainSalesDTO.setEnterpriseId(enterpriseId);
        allocateMainSalesDTO.setMainSalesUserId(sysUserPO.getUserId());
        allocateMainSalesDTO.setMainSalesUserName(sysUserPO.getUserName());
        allocateMainSalesDTO.setUpdatedBy(reqDTO.getCurrentUserId());
        allocateMainSalesDTO.setUpdatedName(reqDTO.getCurrentUserName());
        Result<Void> allocateResult = enterpriseFeign.updateMainSales(allocateMainSalesDTO);
        if (Boolean.FALSE.equals(allocateResult.getSuccess())) {
            throw new BizException(ResultCode.FAIL.getCode(), "绑定主销售信息失败");
        }
    }

    /**
     * 保存企业，经销商用户，销售管理信息
     *
     * @param saleUserId
     * @param reqDTO
     * @return
     */
    private boolean saleDealerRelation(EnterpriseDTO enterpriseDTO,String saleUserId, EnterpriseBindReqDTO reqDTO) {
        //新增企业，销售，经销商 关联关系
        SysDealerSalesRelationPO relationPO = new SysDealerSalesRelationPO();
        relationPO.setEnterpriseId(enterpriseDTO.getEnterpriseId());
        relationPO.setEnterpriseCustomerCategory(enterpriseDTO.getCustomerCategory());
        relationPO.setCreatedBy(reqDTO.getCurrentUserId());
        relationPO.setCreatedName(reqDTO.getCurrentUserName());
        relationPO.setDealerUserId(reqDTO.getCurrentUserId());
        relationPO.setOriginDealerUserId(reqDTO.getCurrentUserId());
        relationPO.setCreatedTime(LocalDateTime.now());
        relationPO.setEnabled(1);
        relationPO.setCreatedTime(LocalDateTime.now());
        relationPO.setSalesUserId(saleUserId);
        relationPO.setOriginSalesUserId(saleUserId);
        relationPO.setAuthStatus(2);

        LambdaUpdateWrapper<SysDealerSalesRelationPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysDealerSalesRelationPO::getDealerUserId, reqDTO.getCurrentUserId());
        updateWrapper.eq(SysDealerSalesRelationPO::getEnterpriseId, reqDTO.getEnterpriseId());
        updateWrapper.eq(SysDealerSalesRelationPO::getSalesUserId, saleUserId);
        return sysDealerSalesRelationMapperService.saveOrUpdate(relationPO, updateWrapper);
    }

    /**
     * 外部用户绑定公司，不存在主销售时，发送消息通知
     *
     * @param saleDirectorUserId
     * @param enterpriseName
     * @param reqDTO
     */
    private void sendNoSaleMessage(String saleDirectorUserId, String enterpriseName, EnterpriseBindReqDTO reqDTO) {
        log.info("sendNoSaleMessage：绑定的企业没有主销售时，给销售区域经理发消息");
        EnterpriseBindMqDTO messageMqDTO = EnterpriseBindMqDTO
                .builder()
                .setSaleDirectorUserId(saleDirectorUserId)
                .setExternalUserName(reqDTO.getCurrentUserName())
                .setEnterpriseName(enterpriseName)
                .setCurrentUserId(reqDTO.getCurrentUserId())
                .setCurrentUserName(reqDTO.getCurrentUserName())
                .setExternalUserId(reqDTO.getCurrentUserId())
                .setEnterpriseId(reqDTO.getEnterpriseId())
                .setSituationType(CoBindMessageSituationEnum.CO_BIND_EXTERNAL_BIND_NEW.getCode());
        mqManager.sendTopic(UserConstant.CO_BIND_TOPIC, JSONUtil.toJsonStr(messageMqDTO), true);
    }

    /**
     * 外部用户绑定公司，已经存在主销售时，发送消息通知
     *
     * @param mainSaleUserId
     * @param enterpriseName
     * @param reqDTO
     */
    private void sendExistSaleMessage(String mainSaleUserId, String enterpriseName, EnterpriseBindReqDTO reqDTO) {
        log.info("sendExistSaleMessage：绑定的企业存在主销售时,给主销售发消息");
        EnterpriseBindMqDTO messageMqDTO = EnterpriseBindMqDTO.builder()
                .setExternalUserName(reqDTO.getCurrentUserName())
                .setEnterpriseName(enterpriseName)
                .setCurrentUserId(reqDTO.getCurrentUserId())
                .setCurrentUserName(reqDTO.getCurrentUserName())
                .setMainSaleUserId(mainSaleUserId)
                .setExternalUserId(reqDTO.getCurrentUserId())
                .setEnterpriseId(reqDTO.getEnterpriseId())
                .setSituationType(CoBindMessageSituationEnum.CO_BIND_EXTERNAL_BIND_EXIST.getCode());
        mqManager.sendTopic(UserConstant.CO_BIND_TOPIC, JSONUtil.toJsonStr(messageMqDTO), true);
    }
}
