package com.trinasolar.trinax.user.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SalesEnterpriseRespDTO {

    @Schema(description = "企业id")
    private String enterpriseId;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "企业状态")
    private String regStatus;

    @Schema(description = "法人代表")
    private String legalPersonName;

    @Schema(description = "税号")
    private String taxNumber;

    @Schema(description = "注册地址")
    private String regLocation;

    @Schema(description = "是否存续 true:存续 false:非存续状态（包含多个状态）")
    private boolean subsisting;

    @Schema(description = "客户角色str")
    private String customerRoleStr;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "客户建商状态")
    private String customerStatus;

    @Schema(description = "客户建商状态str")
    private String customerStatusStr;

    @Schema(description = "客户类别, CAPITAL: 资方，PARTNER：合作伙伴，BUSINESS：工商业")
    private String customerCategory;

    @Schema(description = "客户类别str")
    private String customerCategoryStr;

    @Schema(description = "true: 生态伙伴， false：非生态伙伴")
    private boolean partner;

    @Schema(description = "true: 工商业， false：非工商业")
    private boolean business;

    @Schema(description = "准入状态：Access 准入， Applying 申请中， Pending admittance 待准入")
    private String admissionStatus;

    @Schema(description = "准入状态str")
    private String admissionStatusStr;

    @Schema(description = "当前登录人是否客户所有人 true:是 false:否")
    private boolean customerOwner;

    @Schema(description = "当前登录人是否用户所有人 true:是 false:否")
    private boolean userOwner;

    @Schema(description = "true:有框架合同 false:无框架合同")
    private boolean hasContractFrame;

    @Schema(description = "审批意见")
    private String admissionDesc;

    @Schema(description = "关联合作伙伴数")
    private int linkPartners;

    @Schema(description = "是否为该企业销售员")
    private Boolean isManager;
}
