package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.enterprise.ContactorSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.enterprise.EnterpriseDataSyncReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.ContactSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CreateBusinessResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.EnterpriseDataSyncResDTO;

import java.util.List;

public interface EnterpriseSyncService {
    Result<EnterpriseDataSyncResDTO> enterpriseDataSyncSF(EnterpriseDataSyncReqDTO req);

    Result<CreateBusinessResDTO> createBusiness(String sfId);

    Result<ContactSyncResDTO> contactorSyncSF(List<ContactorSyncReqDTO> req);
}
