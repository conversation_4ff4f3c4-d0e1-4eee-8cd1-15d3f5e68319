package com.trinasolar.trinax.integration.manager.remote.contract;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Created on 2024/12/16, 15:48
 * author: z<PERSON><PERSON>
 */
@Data
@Schema(description = "")
public class DetailDocumentParam {

    String name;

    String value;

    Boolean required;

    Integer page;

    Number offsetX;

    Number offsetY;

    Number signatory;

    Boolean filled;

}
