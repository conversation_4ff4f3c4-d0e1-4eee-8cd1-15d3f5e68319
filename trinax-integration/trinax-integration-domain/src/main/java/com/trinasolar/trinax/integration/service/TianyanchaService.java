package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.enterprise.CompanySearchReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CompanyBaseInfoResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CompanySearchResDTO;

public interface TianyanchaService {
    Result<PageResponse<CompanySearchResDTO>> searchCompany(CompanySearchReqDTO req);

    Result<CompanyBaseInfoResDTO> qryCompanyInfo(String keyword);
}
