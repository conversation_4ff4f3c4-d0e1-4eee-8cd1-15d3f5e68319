package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.contract.SfSyncContractReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleContractToOaReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfContractItemRemainingDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfSyncContractDTO;

import java.util.List;

/**
 * <AUTHOR> Yang
 */
public interface IntegrationContractService {
    List<SfContractItemRemainingDTO> getSfContractItemRemainingDTOS(List<String> sfContractIds);

    Result<SfSyncContractDTO> syncContractStatusToSFDC(SfSyncContractReqDTO sfSyncContractReqDTO);

    Result<String> sendContractToOa(SaleContractToOaReqDTO saleContractToOaReqDTO);
}
