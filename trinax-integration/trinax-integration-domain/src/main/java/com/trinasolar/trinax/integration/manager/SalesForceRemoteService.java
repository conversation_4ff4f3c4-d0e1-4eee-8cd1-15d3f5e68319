package com.trinasolar.trinax.integration.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.common.utils.httpclient.OkHttpUtil;
import com.trinasolar.trinax.integration.constants.IntegrationResultCode;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationResponseStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.constants.enums.intentOrder.CancelStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.intentOrder.ConfirmHeaderStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.intentOrder.ConfirmItemStatusEnum;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.integration.dto.input.contract.ContractChangeReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.SfSyncContractReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.DeliverSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.NoReleaseQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderItemSyncRowDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderSyncHeadDTO;
import com.trinasolar.trinax.integration.dto.input.enterprise.ContactorSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.enterprise.EnterpriseDataSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.IntendOrderConfirmReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.ReceiveOpportunityLostReqDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderConfirmResDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderFailResDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderItemFailResDTO;
import com.trinasolar.trinax.integration.dto.output.ReceiveOpportunityLostRespDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfSyncContractDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.DeliverSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.NoReleaseResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.OrderSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.ContactSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CreateBusinessResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.EnterpriseDataSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.so.RecordOccupiedQty;
import com.trinasolar.trinax.integration.dto.output.so.SaleOrderOccupiedResDTO;
import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class SalesForceRemoteService {
    //调用SF接口需要的认证信息
    @Value("${ipass.clientId}")
    private String salesforceClientId;

    @Value("${ipass.clientSecret}")
    private String salesforceClientSecret;

    @Value("${ipass.qryNoReleaseUrl}")
    private String qryNoReleaseUrl;


    @Value("${ipass.qryEnterpriseBalanceUrl}")
    private String qryEnterpriseBalanceUrl;

    @Value("${ipass.editEnterpriseBalanceUrl}")
    private String editEnterpriseBalanceUrl;

    @Value("${ipass.syncContractUrl}")
    private String syncContractUrl;


    @Value("${salesforce.intentOrder.confirmUrl}")
    private String salesforceConfirmUrl;

    @Value("${salesforce.intentOrder.cancelUrl}")
    private String salesforceCancelUrl;

    @Value("${salesforce.orderSync.orderSyncUrl}")
    private String sfOrderSyncUrl;

    @Value("${salesforce.orderSync.orderItemSyncUrl}")
    private String sfOrderItemSyncUrl;
    @Value("${salesforce.dr.deliverSyncUrl}")
    private String deliverSyncUrl;
    @Value("${salesforce.enterprise.createBusinessUrl}")
    private String createBusinessUrl;
    @Value("${salesforce.enterprise.contactorSyncUrl}")
    private String contactorSyncUrl;
    @Value("${salesforce.enterprise.enterpriseSyncUrl}")
    private String enterpriseSyncUrl;
    @Value("${salesforce.contract.itemChangeUrl}")
    private String contractItemChangeUrl;
    @Value("${trina.sf.editBalaceFail:false}")
    private Boolean editBalaceFail;

    @Value("${trina.sf.deliverSyncFail:false}")
    private Boolean deliverSyncFail;

    @Value("${salesforce.so.occupiedQtyUrl}")
    private String occupiedQtyUrl;

    @Autowired
    IntegrationLogFeign integrationLogFeign;
    @Autowired
    IntegrationLogManager integrationLogManager;

    /**
     * ns
     * Trina SF意向单确认接口
     *
     * @param req
     * @return
     */
    public Result<IntendOrderConfirmResDTO> intentOrderConfirm(IntendOrderConfirmReqDTO req) {
        String jsonString = JacksonUtil.bean2Json(req);
        log.info("salesforce request param jsonString:{}", jsonString);
        String response = OkHttpUtil.postJsonParams(salesforceConfirmUrl, jsonString, headerMapInit());
        LocalDateTime start = LocalDateTime.now();
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        long milliSeconds = Duration.between(start, LocalDateTime.now()).toMillis();
        log.info("同步意向单{},请求耗时{}毫秒", req.getIntentOrderNo(), milliSeconds);
        log.info("salesforce request response:{}", response);
        if (StringUtils.isNotBlank(response)) {
            String code = JacksonUtils.toObj(response).get("code").asText();
            String data = JacksonUtils.toObj(response).get("data").toString();
            if (ConfirmHeaderStatusEnum.SUCCESS.getCode().equalsIgnoreCase(code)) {
                IntendOrderConfirmResDTO resDTO = JacksonUtil.json2Bean(data, IntendOrderConfirmResDTO.class);
                log.info("convert result resDTO:{}", resDTO);
                integrationLogManager.sendIntegrationLogMq(req.getIntentOrderNo(), BizOperationEnum.SYNC_INTENT_ORDER_INFO.getCode()
                        , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                        , jsonString, response, start, "", responseStatus);
                return Result.ok(resDTO);
            } else {
                responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
                String oriHeadMsg = JacksonUtils.toObj(response).get("msg").toString();
                String success = JacksonUtils.toObj(response).get("success").toString();
                IntendOrderConfirmResDTO resDTO = new IntendOrderConfirmResDTO();
                IntendOrderFailResDTO head = new IntendOrderFailResDTO();
                head.setCode(code);
                String newHeadMsg = ConfirmHeaderStatusEnum.getDescByCode(code);
                head.setMsg(StringUtils.isNotBlank(newHeadMsg) ? newHeadMsg : oriHeadMsg);
                head.setSuccess(success);
                String content = JacksonUtils.toObj(data).get("opportunityItems").toString();
                if (StringUtils.isNotBlank(content)) {
                    List<IntendOrderItemFailResDTO> itemList = JacksonUtil.json2List(content, IntendOrderItemFailResDTO.class);
                    if (CollUtil.isNotEmpty(itemList)) {
                        List<IntendOrderItemFailResDTO> failItemList = itemList.stream().filter(e -> !"SUCCESS".equalsIgnoreCase(e.getCode())).toList();
                        //返回文案替换,当替换出来为空时，原样返回
                        failItemList.forEach(e -> {
                            String oriItemMsg = e.getMsg();
                            String newItemMsg = ConfirmItemStatusEnum.getDescByCode(e.getCode());
                            e.setMsg(StringUtils.isNotBlank(newItemMsg) ? newItemMsg : oriItemMsg);
                        });
                        head.setFailItemList(failItemList);
                    }
                }
                resDTO.setSalesforceFailInfo(head);
                integrationLogManager.sendIntegrationLogMq(req.getIntentOrderNo(), BizOperationEnum.SYNC_INTENT_ORDER_INFO.getCode()
                        , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                        , jsonString, response, start, "", responseStatus);
                return Result.result(IntegrationResultCode.SALESFORCE_BACK_FAIL.getCode(), "Salesforce校验失败", resDTO);
            }
        } else {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            integrationLogManager.sendIntegrationLogMq(req.getIntentOrderNo(), BizOperationEnum.SYNC_INTENT_ORDER_INFO.getCode()
                    , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , jsonString, response, start, "", responseStatus);
            return Result.fail("请求返回结果response为空");
        }
    }


    public Result<Boolean> cancelIntentOrder(ReceiveOpportunityLostReqDTO lost) {
        String payload = JSONUtil.toJsonStr(lost);
        log.info("cancelIntentOrder request payload:{}", payload);
        String response = null;
        LocalDateTime start = LocalDateTime.now();
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            response = OkHttpUtil.postJsonParams(salesforceCancelUrl, payload, headerMapInit());
        } catch (Exception e) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            integrationLogManager.sendIntegrationLogMq(lost.getIntentOrderNo(), BizOperationEnum.CANCEL_INTENT_ORDER.getCode()
                    , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , payload, response, start, "", responseStatus);
            log.error(e.getMessage());
            return Result.fail("请求SF取消意向单接口异常");
        }
        long milliSeconds = Duration.between(start, LocalDateTime.now()).toMillis();
        log.info("取消意向单{},请求耗时{}毫秒", lost.getIntentOrderNo(), milliSeconds);
        log.info("cancelIntentOrder response:{}", response);
        if (StringUtils.isBlank(response)) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            integrationLogManager.sendIntegrationLogMq(lost.getIntentOrderNo(), BizOperationEnum.CANCEL_INTENT_ORDER.getCode()
                    , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , payload, response, start, "", responseStatus);
            return Result.fail("取消意向单请求返回结果response为空");
        }
        ReceiveOpportunityLostRespDTO result = JSONUtil.toBean(response, ReceiveOpportunityLostRespDTO.class);
        if (CancelStatusEnum.SUCCESS.getCode().equalsIgnoreCase(result.getCode())) {
            integrationLogManager.sendIntegrationLogMq(lost.getIntentOrderNo(), BizOperationEnum.CANCEL_INTENT_ORDER.getCode()
                    , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , payload, response, start, "", responseStatus);
            return Result.ok();
        } else {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            integrationLogManager.sendIntegrationLogMq(lost.getIntentOrderNo(), BizOperationEnum.CANCEL_INTENT_ORDER.getCode()
                    , BizTypeEnum.INTENT_ORDER.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , payload, response, start, "", responseStatus);
            String oriMsg = result.getMsg();
            String newMsg = CancelStatusEnum.getDescByCode(result.getCode());
            return Result.result(ResultCode.FAIL.getCode(), StringUtils.isNotBlank(newMsg) ? newMsg : oriMsg, result.getSuccess());
        }
    }

    public Result<OrderSyncResDTO> orderSync(List<OrderSyncHeadDTO> req) {

        Map<String, List> map = new HashMap<>();
        map.put("records", req);
        String reqStr = JSONUtil.toJsonStr(map);
        log.info("orderSync request order:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(sfOrderSyncUrl, reqStr, headerMapInit());
        log.info("orderSync response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        OrderSyncResDTO result = JSONUtil.toBean(response, OrderSyncResDTO.class);
        if (result.isHasErrors()) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.get(0).getOpportunityId(), BizOperationEnum.CREATE_ORDER_INFO.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);
        return Result.ok(result);

    }

    public Result<OrderSyncResDTO> orderProductSync(List<OrderItemSyncRowDTO> req) {

        Map<String, List> map = new HashMap<>();
        map.put("records", req);
        String reqStr = JSONUtil.toJsonStr(map);
        log.info("orderProductSync request reqStr:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(sfOrderItemSyncUrl, reqStr, headerMapInit());
        log.info("orderProductSync response:{}", response);
        OrderSyncResDTO result = JSONUtil.toBean(response, OrderSyncResDTO.class);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if (result.isHasErrors()) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.get(0).getOrderId(), BizOperationEnum.CREATE_ORDER_ITEM_INFO.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);
        return Result.ok(result);

    }

    public Result<EnterpriseDataSyncResDTO> enterpriseDataSyncSF(EnterpriseDataSyncReqDTO req) {

        String reqStr = JSONUtil.toJsonStr(req);
        log.info("enterpriseDataSyncSF request reqStr:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = null;
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            response = OkHttpUtil.postJsonParams(enterpriseSyncUrl, reqStr, headerMapInit());
            log.info("enterpriseDataSyncSF response:{}", response);
        } catch (Exception e) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            log.error("enterpriseDataSyncSF异常", e);
        } finally {
            integrationLogManager.sendIntegrationLogMq(req.getEnterpriseId(), BizOperationEnum.SYNC_ENTERPRISE_INFO.getCode()
                    , BizTypeEnum.ENTERPRISE.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , reqStr, response, requestTime, "", responseStatus);
        }
        EnterpriseDataSyncResDTO result = JSONUtil.toBean(response, EnterpriseDataSyncResDTO.class);
        return Result.ok(result);
    }

    public Result<CreateBusinessResDTO> createBusiness(String sfId) {
        Map<String, Object> map = new HashMap<>();
        map.put("sfId", sfId);
        map.put("isCreateOpp", true);
        String reqStr = JSONUtil.toJsonStr(map);

        LocalDateTime requestTime = LocalDateTime.now();
        log.info("createBusiness request reqStr:{}", reqStr);
        String response = OkHttpUtil.postJsonParams(createBusinessUrl, reqStr, headerMapInit());
        log.info("createBusiness response:{}", response);
        CreateBusinessResDTO result = JSONUtil.toBean(response, CreateBusinessResDTO.class);

        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if (!result.isSuccess()) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(sfId, BizOperationEnum.SYNC_ENTERPRISE_BUILD.getCode()
                , BizTypeEnum.ENTERPRISE.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);
        return Result.ok(result);

    }

    public Result<ContactSyncResDTO> contactorSyncSF(List<ContactorSyncReqDTO> req) {

        String reqStr = JSONUtil.toJsonStr(req);
        log.info("contactorSyncSF request reqStr:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(contactorSyncUrl, reqStr, headerMapInit());
        ContactSyncResDTO result = JSONUtil.toBean(response, ContactSyncResDTO.class);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if (!result.getStatus().equals("Success")) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.get(0).getAccountId(), BizOperationEnum.SYNC_ENTERPRISE_CONTACT.getCode()
                , BizTypeEnum.ENTERPRISE.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);
        return Result.ok(result);
    }

    public Result<DeliverSyncResDTO> deliverSync(List<DeliverSyncReqDTO> req) {
        if(deliverSyncFail){
            return Result.fail("快速失败！");
        }
        Map<String, List> map = new HashMap<>();
        map.put("drList", req);
        String reqStr = JSONUtil.toJsonStr(map);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(deliverSyncUrl, reqStr, headerMapInit());
        DeliverSyncResDTO result = JSONUtil.toBean(response, DeliverSyncResDTO.class);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if (!result.getCode().equals("200")) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.get(0).getDrNo(), BizOperationEnum.SYNC_DR_INFO.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);
        return Result.ok(result);
    }

    private Map<String, String> headerMapInit() {
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tsl-clientid", salesforceClientId);
        headerMap.put("tsl-clientsecret", salesforceClientSecret);

        return headerMap;
    }

    public Result<List<ContractChangeItemResDTO>> contractItemChange(ContractChangeReqDTO req) {
        String reqStr = JSONUtil.toJsonStr(req);
        log.info("contractItemChange request param:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(contractItemChangeUrl, reqStr, headerMapInit());
        log.info("contractItemChange response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        ContractChangeResDTO result = JSONUtil.toBean(response, ContractChangeResDTO.class);
        boolean ifFail = false;
        if (!"Success".equals(result.getStatus())) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            ifFail = true;
        }
        integrationLogManager.sendIntegrationLogMq(req.getRecordId(), BizOperationEnum.CONTRACT_CHANGE_ITEM.getCode()
                , BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);

        if (ifFail) {
            return Result.fail("修改合同产品行失败");
        } else {
            return Result.ok(result.getReturnItems());
        }
    }

    public Result<NoReleaseResDTO> qryNoRelease(NoReleaseQryReqDTO req) {
        String reqStr = JSONUtil.toJsonStr(req);
        log.info("qryNoRelease request param:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(qryNoReleaseUrl, reqStr, headerMapInit());
        log.info("qryNoRelease response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        boolean success = JacksonUtils.toObj(response).get("isSuccess").asBoolean();
        NoReleaseResDTO noReleaseResDTO = NoReleaseResDTO.builder();
        String responseCode = ResultCode.OK.getCode();
        if (!success) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            responseCode = ResultCode.FAIL.getCode();
        } else {
            String noRelease = JacksonUtils.toObj(response).findValue("noRelease").toString();
            noReleaseResDTO = JacksonUtil.json2Bean(noRelease, NoReleaseResDTO.class);
        }
        integrationLogManager.sendIntegrationLogMq(req.getDrNo(), BizOperationEnum.QRY_NO_RELEASE.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);

        return Result.result(responseCode, "", noReleaseResDTO);

    }

    public Result<BigDecimal> queryBalance(String enterpriseMdmId) {
//        String reqStr = JSONUtil.toJsonStr(req);
        String reqStr = "{\"mdmCustomerCode\":\"" + enterpriseMdmId + "\"}";
        log.info("queryBalance request param:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(qryEnterpriseBalanceUrl, reqStr, headerMapInit());
        log.info("queryBalance response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        boolean success = JacksonUtils.toObj(response).get("success").asBoolean();
//        NoReleaseResDTO noReleaseResDTO = NoReleaseResDTO.builder();
        String responseCode = ResultCode.OK.getCode();
        BigDecimal balance =null;
        if (!success) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            responseCode = ResultCode.FAIL.getCode();
        } else {
            String data = JacksonUtils.toObj(response).findValue("data").asText();
            Map<String,Object> map = JacksonUtil.json2Map(data, String.class, Object.class);
            balance = new BigDecimal(map.get("distributionAccountBalance").toString());
        }
        integrationLogManager.sendIntegrationLogMq(enterpriseMdmId, BizOperationEnum.QRY_BALANCE_RELEASE.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);

        return Result.result(responseCode, "", balance);
    }

    public Result<Object> editBalance(SfAccountBalanceEditReq sfAccountBalanceEditReq) {
        if(editBalaceFail){
            return Result.fail("快速失败！");
        }
        String reqStr = JSONUtil.toJsonStr(sfAccountBalanceEditReq);
        log.info("queryBalance request param:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(editEnterpriseBalanceUrl, reqStr, headerMapInit());
        log.info("queryBalance response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        boolean success = JacksonUtils.toObj(response).get("success").asBoolean();
        String responseCode = ResultCode.OK.getCode();
        BigDecimal balance =null;
        if (!success) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            responseCode = ResultCode.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(sfAccountBalanceEditReq.getMdmCode(), BizOperationEnum.EDIT_BALANCE_RELEASE.getCode()
                , BizTypeEnum.DR.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);

        return Result.result(responseCode, "", balance);
    }

    public Result<SfSyncContractDTO> syncContract(SfSyncContractReqDTO sfSyncContractReqDTO) {
        String reqStr = JSONUtil.toJsonStr(sfSyncContractReqDTO);
        log.info("syncContract request param:{}", reqStr);
        LocalDateTime requestTime = LocalDateTime.now();
        String response = OkHttpUtil.postJsonParams(syncContractUrl, reqStr, headerMapInit());
        log.info("syncContract response:{}", response);
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        JsonNode jsonNode=JacksonUtils.toObj(response);
        boolean success = jsonNode.get("success").asBoolean();
        String msg = jsonNode.get("msg").toString();
        SfSyncContractDTO sfSyncContractDTO = JSON.parseObject(jsonNode.get("data").toString(), SfSyncContractDTO.class);
        String responseCode = ResultCode.OK.getCode();
        if (!success) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            responseCode = ResultCode.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(sfSyncContractReqDTO.getIntentOrderNo(), BizOperationEnum.SYNC_CONTRACT_TO_SF.getCode()
                , BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                , reqStr, response, requestTime, "", responseStatus);

        return Result.result(responseCode, msg, sfSyncContractDTO);
    }

    public Result<List<RecordOccupiedQty>> queryOccupiedQty(List<String> sfRddIds) {
        if (CollUtil.isEmpty(sfRddIds)) {
            return Result.fail("查询RDD ID的SO占用数量的接口ID参数不能为空");
        }

        String inClause = sfRddIds.stream().map(id -> "'" + id + "'").collect(Collectors.joining(","));
        String whereCondition = sfRddIds.size() == 1 ? "Id = " + inClause : "Id IN (" + inClause + ")";
        String reqStr = String.format("SELECT Id, TS_SO_Occupied_QTY__c FROM TS_RDD__c WHERE %s", whereCondition);

        LocalDateTime requestTime = LocalDateTime.now();
        String response = "";

        try {
            log.info("查询RDD ID的SO占用数量的接口 request param:{}", reqStr);
            response = OkHttpUtil.get(occupiedQtyUrl, Collections.singletonMap("q", reqStr), headerMapInit());
            log.info("查询RDD ID的SO占用数量的接口 response:{}", response);

            if (CharSequenceUtil.isBlank(response)) {
                return Result.fail("查询RDD ID的SO占用数量的接口返回为空");
            }

            // 判断响应类型（数组或对象）
            if (response.trim().startsWith("[")) {
                // 处理错误响应（数组格式）
                List<SaleOrderOccupiedResDTO.ErrorResponse> errorResponses = JSONUtil.toList(response, SaleOrderOccupiedResDTO.ErrorResponse.class);

                if (CollUtil.isNotEmpty(errorResponses)) {
                    String errorMessage = errorResponses.stream() .map(SaleOrderOccupiedResDTO.ErrorResponse::getMessage)
                            .collect(Collectors.joining("; "));
                    sendIntegrationLog(sfRddIds, reqStr, response, requestTime, IntegrationResponseStatusEnum.FAIL.getCode(), errorMessage);
                    return Result.fail("查询RDD ID的SO占用数量的接口失败: " + errorMessage);
                }
            } else {
                // 处理成功响应（对象格式）
                SaleOrderOccupiedResDTO dtoResponse = JSONUtil.toBean(response, SaleOrderOccupiedResDTO.class);

                if (Objects.nonNull(dtoResponse) && Boolean.TRUE.equals(dtoResponse.getDone()) && CollUtil.isNotEmpty(dtoResponse.getRecords())) {

                    List<RecordOccupiedQty> result = dtoResponse.getRecords().stream()
                            .map(record -> new RecordOccupiedQty(record.getId(), record.getTS_SO_Occupied_QTY__c()))
                            .toList();

                    sendIntegrationLog(sfRddIds, reqStr, response, requestTime, IntegrationResponseStatusEnum.SUCCESS.getCode(), null);
                    return Result.ok(result);
                } else {
                    String errorMsg = dtoResponse == null ? "响应解析为空" : dtoResponse.toString();
                    sendIntegrationLog(sfRddIds, reqStr, response, requestTime, IntegrationResponseStatusEnum.FAIL.getCode(), errorMsg);
                    return Result.fail("查询RDD ID的SO占用数量的接口失败: " + errorMsg);
                }
            }
            return Result.fail("查询RDD ID的SO占用数量的接口返回未知响应格式");
        } catch (Exception e) {
            log.error("查询RDD ID的SO占用数量的接口异常: {}", e.getMessage(), e);
            sendIntegrationLog(sfRddIds, reqStr, response, requestTime, IntegrationResponseStatusEnum.FAIL.getCode(), e.getMessage());
            return Result.fail("查询RDD ID的SO占用数量的接口异常: " + e.getMessage());
        }
    }

    private void sendIntegrationLog(List<String> sfRddIds, String reqStr, String response, LocalDateTime requestTime, String statusCode, String errorMsg) {
        integrationLogManager.sendIntegrationLogMq(
                sfRddIds.get(0),
                BizOperationEnum.QRY_SO_OCCUPIED.getCode(),
                BizTypeEnum.SO.getCode(),
                IntegrationForwardEnum.OUT.getCode(),
                IntegrationSystemEnum.SALES_FORCE.getCode(),
                reqStr,
                response,
                requestTime,
                errorMsg != null ? errorMsg : "",
                statusCode
        );
    }
}
