package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.enterprise.ContactorSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.enterprise.EnterpriseDataSyncReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.ContactSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CreateBusinessResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.EnterpriseDataSyncResDTO;
import com.trinasolar.trinax.integration.manager.SalesForceRemoteService;
import com.trinasolar.trinax.integration.service.EnterpriseSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnterpriseSyncServiceImpl implements EnterpriseSyncService {
    @Autowired
    SalesForceRemoteService salesForceRemoteService;

    @Override
    public Result<EnterpriseDataSyncResDTO> enterpriseDataSyncSF(EnterpriseDataSyncReqDTO req) {
        return salesForceRemoteService.enterpriseDataSyncSF(req);
    }

    @Override
    public Result<CreateBusinessResDTO> createBusiness(String sfId) {
        return salesForceRemoteService.createBusiness(sfId);
    }

    @Override
    public Result<ContactSyncResDTO> contactorSyncSF(List<ContactorSyncReqDTO> req) {
        return salesForceRemoteService.contactorSyncSF(req);
    }


}
