package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.delivery.DeliverSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderItemSyncRowDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderSyncHeadDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.DeliverSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.LogisticsResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.OrderSyncResDTO;

import java.util.List;

public interface LogisticsService {
    Result<LogisticsResDTO> queryLogistics(String  wayBillNo);

    Result<OrderSyncResDTO> orderSync(List<OrderSyncHeadDTO> req);

    Result<OrderSyncResDTO> orderProductSync(List<OrderItemSyncRowDTO> req);

    Result<DeliverSyncResDTO> deliverSync(List<DeliverSyncReqDTO> req);
}
