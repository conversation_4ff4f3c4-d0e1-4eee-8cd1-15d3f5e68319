package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.config.tos.TosProperties;
import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import com.trinasolar.trinax.integration.service.TosService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Date;

@RequiredArgsConstructor
@Service
@Slf4j
public class TosServiceImpl implements TosService {

    private final static String ORIGINAL_FILE_NAME = "originalFileName";
    private final AmazonS3 amazonS3;
    private final TosProperties properties;
    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public TosFileDTO upload(MultipartFile file) {
        if (null == file) {
            throw new RuntimeException("上传文件不能为空!");
        } else if (!amazonS3.doesBucketExistV2(properties.getBucketName())) {
            throw new RuntimeException("桶不存在");
        }

        String filePath = getFilePath();
        String fileBucketName = properties.getBucketName() + filePath;
        String fileName = IdUtil.fastSimpleUUID();
        try {
            String originalFilename = URLEncoder.encode(file.getOriginalFilename(), StandardCharsets.UTF_8);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(file.getContentType());
            objectMetadata.setContentLength(file.getSize());
            objectMetadata.addUserMetadata(ORIGINAL_FILE_NAME, originalFilename);
            PutObjectResult putObjectResult = amazonS3.putObject(fileBucketName, fileName, file.getInputStream(), objectMetadata);
            log.info("上传文件至TOS，tosResult={}", JacksonUtil.bean2Json(putObjectResult));

            TosFileDTO tosFile = new TosFileDTO();
            tosFile.setFilePath(filePath);
            tosFile.setFileName(fileName);
            tosFile.setUrl(filePath + "/" + fileName);
            tosFile.setOriginalFileName(file.getOriginalFilename());
            tosFile.setFileSize(objectMetadata.getContentLength());
            tosFile.setFileType(objectMetadata.getContentType());
            log.info("上传文件至TOS成功，tosFile={}", JacksonUtil.bean2Json(tosFile));
            return tosFile;
        } catch (Exception e) {
            log.error("上传文件至TOS失败", e);
            throw new RuntimeException(e);
        }
    }

    private String getFilePath() {
        String dateDir = DateUtil.format(new Date(), "/yyyy/MM/dd");
        return "/" + env + dateDir;
    }

    /**
     * 删除文件
     *
     * @param filePath 文件保存路径 格式：/文件名/
     * @param fileName 文件名称
     */
    @Override
    public void delete(String filePath, String fileName) {
        amazonS3.deleteObject(properties.getBucketName() + filePath, fileName);
    }

    /**
     * 下载文件
     *
     * @param filePath 文件保存路径 格式：/文件名/
     * @param fileName 文件名称
     * @param response
     */
    @Override
    public void download(String filePath, String fileName, HttpServletResponse response) {
        S3ObjectInputStream input = null;
        OutputStream output = null;
        try {
            S3Object object = amazonS3.getObject(new GetObjectRequest(properties.getBucketName() + filePath, fileName));
            response.setCharacterEncoding("UTF-8");
            response.setContentType(object.getObjectMetadata().getContentType());
            response.setHeader("Content-Disposition", genContentDisposition(object));
            response.setHeader("Pragma", "no-cache");

            log.info("查看文件下载名字 {}", JacksonUtil.bean2Json(object));

            output = response.getOutputStream();
            input = object.getObjectContent();

            IOUtils.copy(input, output);
            output.flush();
        } catch (AmazonS3Exception e) {
            if (e.getStatusCode() == HttpStatus.HTTP_NOT_FOUND) {
                response.setStatus(HttpStatus.HTTP_NOT_FOUND);
                return;
            }

            log.error("从TOS下载文件失败，filePath={}，fileName={}", filePath, fileName, e);
            throw new RuntimeException("从TOS下载文件失败");
        } catch (Exception e) {
            log.error("从TOS下载文件异常，filePath={}，fileName={}", filePath, fileName, e);
            throw new RuntimeException("从TOS下载文件异常");
        } finally {
            IOUtils.closeQuietly(input);
            IOUtils.closeQuietly(output);
        }
    }

    private String genContentDisposition(S3Object object) throws UnsupportedEncodingException {
        String fileName = URLDecoder.decode(object.getObjectMetadata().getUserMetaDataOf(ORIGINAL_FILE_NAME), "UTF-8");
        if (CharSequenceUtil.isEmpty(fileName)) {
            fileName = object.getKey();
        }
        byte[] fileNameBytes = fileName.getBytes(StandardCharsets.UTF_8);

        fileName = new String(fileNameBytes, 0, fileNameBytes.length, StandardCharsets.ISO_8859_1);
        log.info("文件名：{}", fileName);

        return MessageFormat.format("form-data; name=\"file\"; filename=\"{0}\"", fileName);
    }

}
