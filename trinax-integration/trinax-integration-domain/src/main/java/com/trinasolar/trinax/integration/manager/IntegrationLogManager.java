package com.trinasolar.trinax.integration.manager;

import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.dto.mq.InterfaceLogMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;


@Service
@Slf4j
public class IntegrationLogManager {

    @Autowired
    IntegrationLogFeign integrationLogFeign;

    /**
     * 集成日志消息写MQ
     */
    public void sendIntegrationLogMq(String bizNo, String bizOperation, String bizType, String integrationForward,
                                     String integrationSystem, String request, String response,
                                     LocalDateTime requestTime, String userId,String responseStatus) {
        InterfaceLogMqDTO messageReq = InterfaceLogMqDTO.builder()
                .setBizNo(bizNo)
                .setBizOperation(bizOperation)
                .setBizType(bizType)
                .setIntegrationForward(integrationForward)
                .setIntegrationSystem(integrationSystem)
                .setRequestData(request)
                .setRequestTime(requestTime)
                .setResponseTime(LocalDateTime.now())
                .setResponseStatus(responseStatus)
                .setResponseData(response)
                .setUserId(userId);
        integrationLogFeign.sendIntegrationLogMq(messageReq);
    }

}
