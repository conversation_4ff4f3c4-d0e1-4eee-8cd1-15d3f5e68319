package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.dto.input.documentPreview.GetUrlReqDTO;
import com.trinasolar.trinax.integration.dto.output.documentPreview.GetUrlResDTO;
import com.trinasolar.trinax.integration.manager.remote.collaborate.*;
import com.trinasolar.trinax.integration.service.DocumentPreviewService;
import dtt.cache.redisclient.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import static com.trinasolar.trinax.integration.manager.remote.collaborate.ThirdCollaborateGetUrlReqDTO.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentPreviewServiceImpl implements DocumentPreviewService {

    private final static String SERVICE_TYPE = "collaborate";
    private final static String GET_URL_TYPE = "preview";
    private final static String GET_TOKEN_KEY = "COLLABORATE_TOKEN_KEY";
    private final static String GET_TOKEN_LOCK_KEY = "COLLABORATE_GET_TOKEN_LOCK";

    @Value("${collaborate.appKey}")
    private String appKey;
    @Value("${collaborate.appSecret}")
    private String appSecret;

    private final ThirdCollaborateFeign thirdCollaborateFeign;
    private final RedisUtil redisUtil;
    private final RedissonClient redissonClient;

    @Override
    public GetUrlResDTO getUrl(GetUrlReqDTO req) {
        ThirdCollaborateFileUserDTO user = new ThirdCollaborateFileUserDTO();
        user.setId(req.getUserId());
        user.setName(req.getUserName());

        ThirdCollaborateGetUrlReqDTO getUrlReq = new ThirdCollaborateGetUrlReqDTO();
        getUrlReq.setAppKey(appKey);
        getUrlReq.setType(GET_URL_TYPE);
        getUrlReq.setUser(user);
        getUrlReq.setFile(buildFile(req));
        log.info("获取在线预览地址开始，req={}", JacksonUtil.bean2Json(getUrlReq));
        ThirdCollaborateResult<ThirdCollaborateGetUrlResDTO> getUrlRes = thirdCollaborateFeign.getUrl(getUrlReq, getToken().getAccessToken());
        log.info("获取在线预览地址结果，result={}", JacksonUtil.bean2Json(getUrlRes));
        if (!CharSequenceUtil.equals(getUrlRes.getBizcode(), "10000") || !CharSequenceUtil.equals(getUrlRes.getData().getBizCode(), "10000")) {
            throw new BizException(ResultCode.FAIL.getCode(), "获取在线预览地址失败，" + (getUrlRes.getData()!=null?getUrlRes.getData().getBizMsg():""));
        }

        GetUrlResDTO res = new GetUrlResDTO();
        res.setUrl(getUrlRes.getData().getUrl());
        return res;
    }

    private ThirdCollaborateFileDTO buildFile(GetUrlReqDTO req) {
        ThirdCollaborateFileUserDTO creator = new ThirdCollaborateFileUserDTO();
        creator.setId(req.getCreatorId());
        creator.setName(req.getCreatorName());

        ThirdCollaborateFileDTO file = new ThirdCollaborateFileDTO();
        file.setId(req.getId());
        file.setName(req.getName());
        file.setSize(req.getSize());
        file.setDownloadUrl(req.getDownloadUrl());
        file.setCreateTime(System.currentTimeMillis()/1000);
        file.setCreator(creator);
        file.setWatermark(new ThirdCollaborateWatermarkDTO());
        return file;
    }

    private String getTokenCache() {
        String token = redisUtil.get(GET_TOKEN_KEY);
        if (CharSequenceUtil.isNotEmpty(token)) {
            return token;
        }

        RLock lock = redissonClient.getLock(GET_TOKEN_LOCK_KEY);
        try {
            lock.lock();

            token = redisUtil.get(GET_TOKEN_KEY);
            if (CharSequenceUtil.isNotEmpty(token)) {
                return token;
            }

            ThirdCollaborateGetTokenResDTO tokenRes = getToken();
            if (tokenRes.getExpireIn() != null && tokenRes.getExpireIn() > 120) {
                redisUtil.set(GET_TOKEN_KEY, tokenRes.getAccessToken(), tokenRes.getExpireIn() - 60);
            }
        } finally {
            lock.unlock();
        }
        return token;
    }

    private ThirdCollaborateGetTokenResDTO getToken() {
        ThirdCollaborateGetTokenReqDTO req = new ThirdCollaborateGetTokenReqDTO();
        req.setAppKey(appKey);
        req.setAppSecret(appSecret);
        req.setServiceType(SERVICE_TYPE);
        ThirdCollaborateResult<ThirdCollaborateGetTokenResDTO> res = thirdCollaborateFeign.getToken(req);
        if (CharSequenceUtil.equals(res.getBizcode(), "10000")) {
            log.info("获取在线文档服务认证Token成功");
            return res.getData();
        }
        throw new BizException(ResultCode.FAIL.getCode(), "获取在线文档服务认证Token失败");
    }

}
