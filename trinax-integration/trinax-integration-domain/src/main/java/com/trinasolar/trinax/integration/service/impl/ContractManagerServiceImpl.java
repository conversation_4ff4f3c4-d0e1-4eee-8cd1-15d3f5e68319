package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.contract.BrowseReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.PreSignReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.SignFileReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.AddressUrlDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SignFileResDTO;
import com.trinasolar.trinax.integration.manager.ContractRemoteService;
import com.trinasolar.trinax.integration.service.ContractManagerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Service
@Slf4j
public class ContractManagerServiceImpl implements ContractManagerService {

    @Autowired
    ContractRemoteService manageService;


    @Override
    public Result<AddressUrlDTO> getPreSignUrl(PreSignReqDTO req) {
        return manageService.getPreSignUrl(req);
    }

    @Override
    public Result<AddressUrlDTO> getBrowseUrl(BrowseReqDTO req) {
        return manageService.getBrowseUrl(req);
    }

    @Override
    public Result<List<SignFileResDTO>> signFileUrl(SignFileReqDTO req) {
        return manageService.signFileUrl(req);
    }

    @Override
    public void signFileDown(String documentId, String fileName, HttpServletResponse response, String contentType, String fileType) {
        manageService.signFileDown(documentId, fileName, response, contentType, fileType);
    }
}
