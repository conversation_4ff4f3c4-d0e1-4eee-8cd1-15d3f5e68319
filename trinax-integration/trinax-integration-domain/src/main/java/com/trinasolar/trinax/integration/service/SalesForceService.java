package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.integration.dto.input.contract.ContractChangeReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.NoReleaseQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.IntendOrderConfirmReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.ReceiveOpportunityLostReqDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderConfirmResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.NoReleaseResDTO;
import com.trinasolar.trinax.integration.dto.output.so.RecordOccupiedQty;

import java.math.BigDecimal;
import java.util.List;

public interface SalesForceService {

    Result<Boolean> cancelIntentOrder(ReceiveOpportunityLostReqDTO lost);

    Result<IntendOrderConfirmResDTO> intentOrderConfirm(IntendOrderConfirmReqDTO req);

    Result<List<ContractChangeItemResDTO>> contractItemChange(ContractChangeReqDTO lost);

    Result<NoReleaseResDTO> qryNoRelease(NoReleaseQryReqDTO req);

    Result<BigDecimal> queryBalance(String enterpriseMdmId);

    Result editBalance(SfAccountBalanceEditReq sfAccountBalanceEditReq);

    Result<List<RecordOccupiedQty>> queryOccupiedQty(List<String> sfRddIds);
}
