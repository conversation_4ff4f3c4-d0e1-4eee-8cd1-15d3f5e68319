package com.trinasolar.trinax.integration.service.impl;


import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.enterprise.CompanySearchReqDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CompanyBaseInfoResDTO;
import com.trinasolar.trinax.integration.dto.output.enterprise.CompanySearchResDTO;
import com.trinasolar.trinax.integration.manager.TianyanchaRemoteService;
import com.trinasolar.trinax.integration.service.TianyanchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
public class TianyanchaServiceImpl implements TianyanchaService {

    @Autowired
    TianyanchaRemoteService tianyanchaRemoteService;

    @Override
    public Result<PageResponse<CompanySearchResDTO>> searchCompany(CompanySearchReqDTO req) {
        return tianyanchaRemoteService.searchCompany(req);
    }

    @Override
    public Result<CompanyBaseInfoResDTO> qryCompanyInfo(String keyword) {
        return tianyanchaRemoteService.qryCompanyInfo(keyword);
    }
}
