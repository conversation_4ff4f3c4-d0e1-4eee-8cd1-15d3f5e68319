package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.oa.InvoiceQuantityQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleInvoiceReqDTO;
import com.trinasolar.trinax.integration.dto.output.oa.InvoiceQuantityQryResDTO;
import com.trinasolar.trinax.integration.dto.output.oa.SaleInvoiceResDTO;
import com.trinasolar.trinax.integration.manager.InvoiceRemoteService;
import com.trinasolar.trinax.integration.service.OAInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class OAInvoiceServiceImpl implements OAInvoiceService {

    @Value("${oaSystem.invoice.qryInvoiceQuantityUrl}")
    private String qryInvoiceQuantityUrl;

    @Autowired
    private InvoiceRemoteService invoiceRemoteService;

    @Override
    public Result<List<InvoiceQuantityQryResDTO>> qryInvoiceQuantity(List<InvoiceQuantityQryReqDTO> req) {
        return null;
    }

    @Override
    public Result<SaleInvoiceResDTO> initiateSaleInvoice(SaleInvoiceReqDTO req) {
        return invoiceRemoteService.initiateSaleInvoice(req);
    }
}
