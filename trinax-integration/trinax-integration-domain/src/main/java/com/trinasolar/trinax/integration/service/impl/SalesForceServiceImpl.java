package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.integration.dto.input.contract.ContractChangeReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.NoReleaseQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.IntendOrderConfirmReqDTO;
import com.trinasolar.trinax.integration.dto.input.intentOrder.ReceiveOpportunityLostReqDTO;
import com.trinasolar.trinax.integration.dto.output.IntendOrderConfirmResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.NoReleaseResDTO;
import com.trinasolar.trinax.integration.dto.output.so.RecordOccupiedQty;
import com.trinasolar.trinax.integration.manager.SalesForceRemoteService;
import com.trinasolar.trinax.integration.service.SalesForceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;


@Service
@Slf4j
public class SalesForceServiceImpl implements SalesForceService {
    @Autowired
    private SalesForceRemoteService salesForceRemoteService;

    @Override
    public Result<Boolean> cancelIntentOrder(ReceiveOpportunityLostReqDTO lost) {
        return salesForceRemoteService.cancelIntentOrder(lost);
    }

    @Override
    public Result<IntendOrderConfirmResDTO> intentOrderConfirm(IntendOrderConfirmReqDTO req) {
        return salesForceRemoteService.intentOrderConfirm(req);
    }

    @Override
    public Result<List<ContractChangeItemResDTO>> contractItemChange(ContractChangeReqDTO req) {
        return salesForceRemoteService.contractItemChange(req);
    }

    @Override
    public Result<NoReleaseResDTO> qryNoRelease(NoReleaseQryReqDTO req) {
        return salesForceRemoteService.qryNoRelease(req);
    }

    @Override
    public Result<BigDecimal> queryBalance(String enterpriseMdmId) {
        return salesForceRemoteService.queryBalance(enterpriseMdmId);
    }

    @Override
    public Result<Object> editBalance(SfAccountBalanceEditReq sfAccountBalanceEditReq) {
        return salesForceRemoteService.editBalance(sfAccountBalanceEditReq);
    }

    @Override
    public Result<List<RecordOccupiedQty>> queryOccupiedQty(List<String> sfRddIds) {
        return salesForceRemoteService.queryOccupiedQty(sfRddIds);
    }
}
