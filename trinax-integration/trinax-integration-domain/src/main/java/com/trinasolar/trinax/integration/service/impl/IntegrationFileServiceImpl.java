package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.integration.constants.enums.EncodeTypeEnum;
import com.trinasolar.trinax.integration.service.IntegrationFileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Base64;

@Slf4j
@Service
public class IntegrationFileServiceImpl implements IntegrationFileService {

    @Override
    public void downloadDoc(String urlStr, HttpServletResponse response, String contentType, String fileType, String encodeType) {
        HttpURLConnection conn = null;
        int byteread = 0;
        if (EncodeTypeEnum.BASE64.getCode().equals(encodeType)) {
            //base64 0brRtNG+0bU= GET传输变成0brRtNG 0bU=
            urlStr = urlStr.replace(" ", "+");
            byte[] decodeUrlByte = Base64.getDecoder().decode(urlStr);
            log.info("三方文件下载链接 base64 url: {}", urlStr);
            try {
                urlStr = new String(decodeUrlByte, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("三方文件下载,base64解密url失败：", e);
            }
        } else {
            try {
                urlStr = URLDecoder.decode(urlStr, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                log.error("三方文件下载，UrlDecoder解密url失败", e);
            }
        }

        try {
            log.info("三方文件下载链接 url: {}", urlStr);
            String[] fileArray = urlStr.split("/");
            String fileName = fileArray[fileArray.length - 1];
            if (StringUtils.isBlank(fileName)) {
                fileName = "签约合同";
            }

            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            //设置超时间为5秒
            conn.setConnectTimeout(10 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

            String fileNameURL = URLEncoder.encode(fileName, "UTF-8");

            // 设置response的Header post 文件名方式
            response.addHeader("Content-Disposition", "attachment;filename=" + fileNameURL);
            log.info("当前浏览器：{}", contentType);

            if (StringUtils.isNotBlank(contentType)) {
                //chrome头也包含safari,需要排除chrome
                if (contentType.contains("safari") && !contentType.contains("chrome")) {
                    response.setContentType("application/pdf");
                } else {
                    response.setContentType("application/octet-stream");
                }
            } else {
                response.setContentType("application/octet-stream");
            }


            InputStream inStream = conn.getInputStream();

            //支持 60m 下载
            byte[] buffer = new byte[122880];
            while ((byteread = inStream.read(buffer)) != -1) {
                //本地下载放开
                //fs.write(buffer, 0, byteread);
                //写入输出流
                response.getOutputStream().write(buffer, 0, byteread);
            }
        } catch (Exception e) {
            log.error("三方文件下载失败：", e);

        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }

    }
}
