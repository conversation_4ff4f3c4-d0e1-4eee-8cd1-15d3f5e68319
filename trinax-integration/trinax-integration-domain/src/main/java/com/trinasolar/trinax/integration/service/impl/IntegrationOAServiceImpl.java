package com.trinasolar.trinax.integration.service.impl;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.oa.DeliverApprovalReqDTO;
import com.trinasolar.trinax.integration.manager.TrinaOAService;
import com.trinasolar.trinax.integration.service.IntegrationOAService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IntegrationOAServiceImpl implements IntegrationOAService {
    @Autowired
    TrinaOAService trinaOAService;

    @Override
    public Result<String> submitApplication(DeliverApprovalReqDTO req) {
        return trinaOAService.submitApplication(req);
    }
}
