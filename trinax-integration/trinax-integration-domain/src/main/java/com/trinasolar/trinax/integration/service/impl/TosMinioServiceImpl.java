package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.config.tos.TosProperties;
import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import com.trinasolar.trinax.integration.service.TosService;
import io.minio.*;
import io.minio.errors.ErrorResponseException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.Consts;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@RequiredArgsConstructor
@Slf4j
@Service
public class TosMinioServiceImpl implements TosService {

    private final static String ORIGINAL_FILE_NAME = "originalfilename";
    private final MinioClient minioClient;
    private final TosProperties properties;
    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public TosFileDTO upload(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BizException("上传文件不能为空!", "");
        }

        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(properties.getBucketName()).build())) {
                throw new BizException("桶不存在", "");
            }
        } catch (Exception e) {
            throw new BizException("minio error", e.getMessage());
        }

        String filePath = getFilePath();
        String fileName = IdUtil.fastSimpleUUID();
        String objectKey = filePath + "/" + fileName;

        try (InputStream inputStream = file.getInputStream()) {
            String originalFilename = URLEncoder.encode(Objects.requireNonNull(file.getOriginalFilename()), StandardCharsets.UTF_8);
            Map<String, String> metadata = new HashMap<>();
            metadata.put(ORIGINAL_FILE_NAME, originalFilename);
            minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(properties.getBucketName())
                    .object(objectKey)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .userMetadata(metadata)
                    .build()
            );

            TosFileDTO tosFile = new TosFileDTO();
            tosFile.setFilePath(filePath);
            tosFile.setFileName(fileName);
            tosFile.setUrl(filePath + "/" + fileName);
            tosFile.setOriginalFileName(file.getOriginalFilename());
            tosFile.setFileSize(file.getSize());
            tosFile.setFileType(file.getContentType());
            log.info("上传文件至TOS成功，tosFile={}", JacksonUtil.bean2Json(tosFile));
            return tosFile;
        } catch (Exception e) {
            log.error("上传文件至TOS失败", e);
            throw new BizException("上传文件至TOS失败", e.getMessage());
        }
    }

    private String getFilePath() {
        String dateDir = DateUtil.format(new Date(), "yyyy-MM-dd");
        return "/" + env + "/"+dateDir;
    }

    @Override
    public void delete(String filePath, String fileName) {
        try {
            minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(properties.getBucketName())
                        .object(filePath + "/" + fileName)
                    .build()
            );
        } catch (Exception e) {
            throw new BizException("删除文件失败", e.getMessage());
        }
    }

    @Override
    public void download(String filePath, String fileName, HttpServletResponse response) {
        String bucketName = properties.getBucketName() ;
        try (InputStream input = minioClient.getObject(
            GetObjectArgs.builder()
                .bucket(bucketName)
                .object(filePath + "/" + fileName)
                .build()
        )) {
            StatObjectResponse object = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filePath + "/" + fileName)
                            .build()
            );
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setContentType(object.contentType());
            response.setHeader("Content-Disposition", genContentDisposition(object));
            response.setHeader("Pragma", "no-cache");


            OutputStream output = response.getOutputStream();
            IOUtils.copy(input, output);
            output.flush();
        } catch (ErrorResponseException e) {
            if (e.errorResponse().code().equals("NoSuchKey")) {
                response.setStatus(HttpStatus.HTTP_NOT_FOUND);
                return;
            }
            log.error("从TOS下载文件失败，filePath={}，fileName={}", filePath, fileName, e);
            throw new BizException("从TOS下载文件失败", e.getMessage());
        } catch (Exception e) {
            log.error("从TOS下载文件失败，filePath={}，fileName={}", filePath, fileName, e);
            throw new BizException("从TOS下载文件失败", e.getMessage());
        }
    }

    private String genContentDisposition(StatObjectResponse object) {
        String fileName = URLDecoder.decode(object.userMetadata().get(ORIGINAL_FILE_NAME), StandardCharsets.UTF_8);
        if (CharSequenceUtil.isEmpty(fileName)) {
            fileName = object.object();
        }

        byte[] fileNameBytes = fileName.getBytes(StandardCharsets.UTF_8);
        fileName = new String(fileNameBytes, StandardCharsets.ISO_8859_1);
        return MessageFormat.format("form-data; name=\"file\"; filename=\"{0}\"", fileName);
    }
}