package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.dt.dataphin.client.ApiClient;
import com.alibaba.dt.dataphin.client.ApiClientBuilderParams;
import com.alibaba.dt.dataphin.schema.OrderBy;
import com.alibaba.dt.dataphin.schema.QueryParamRequest;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.trinasolar.trinax.integration.dto.output.contract.*;
import com.trinasolar.trinax.integration.service.ContractShipmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.function.Function;

import static com.alibaba.cloudapi.sdk.constant.SdkConstant.CLOUDAPI_LF;


@Service
@Slf4j
public class ContractShipmentServiceImpl implements ContractShipmentService {
    @Value("${trina.shipment.host}")
    private String host;
    @Value("${trina.shipment.appKey}")
    private String appKey;
    @Value("${trina.shipment.appSecret}")
    private String appSecret;

    @Value("${trina.shipment.outBoundApiId}")
    private String outBoundApiId;

    @Value("${trina.shipment.signApiId}")
    private String signApiId;

    @Value("${trina.shipment.releaseApproveApiId}")
    private String releaseApproveApiId;

    @Value("${trina.shipment.transportApiId}")
    private String transportApiId;

    @Value("${trina.shipment.releaseApiId}")
    private String releaseApiId;

    @Value("${trina.shipment.fullOrderProcessApiId}")
    private String fullOrderProcessApiId;

    private static final int CODE = 200;

    @Override
    public List<ShipmentSignItemsDTO> shipmentSignItemsDTO(String pi, String erpSo) {
//        String apiReturnFields = "id,pi,project_name,erp_so,whse,delivery_time,outbound_time,shipment_xid,pod_no,sign_time,destination,product,required_date,capacity,cabinet_number,product_item_number,item_number,container_number,seal_number,product_power,quantity,total_power,current_grading,pallets_number,license_plate_number,transports,signer,etl_time";
        String apiReturnFields = "pi,erp_so,shipment_xid,pod_no,sign_time,product,quantity,total_power,transports,signer,license_plate_number";
        return getResults(signApiId, apiReturnFields, pi, erpSo, item -> ShipmentSignItemsDTO.builder()
                .pi(item.getStr("PI"))
                .so(item.getStr("ERP_SO"))
                .shipmentNo(item.getStr("SHIPMENT_XID"))
                .product(item.getStr("PRODUCT"))
                .quantity(getQuantity(item.getStr("QUANTITY")))
                .totalPower(item.getStr("TOTAL_POWER"))
                .transports(item.getStr("TRANSPORTS"))
                .signer(item.getStr("SIGNER"))
                .licensePlateNumber(item.getStr("LICENSE_PLATE_NUMBER"))
                .podNo(item.getStr("POD_NO"))
                .signTime(DateUtil.parseLocalDateTime(item.getStr("SIGN_TIME")))
                .build());
    }

    @Override
    public List<ShipmentOutboundItemsDTO> shipmentOutboundItemsDTO(String pi) {
//        String apiReturnFields = "id,pi,project_name,erp_so,whse_id,whse,order_key,cabinet_number,quantity,pallets_number,delivery_time,product,required_date,destination,product_item_number,item_number,product_power,total_power,current_grading,outbound_person,trade_terms,license_plate_number,etl_time";
        String apiReturnFields = "pi,erp_so,product,order_key,quantity,delivery_time,item_number,product_power";
        return getResults(outBoundApiId, apiReturnFields, pi, null, item -> ShipmentOutboundItemsDTO.builder()
                .pi(item.getStr("PI"))
                .deliveryTime(DateUtil.parseLocalDateTime(item.getStr("DELIVERY_TIME"), "yyyy-MM-dd HH:mm:ss"))
                .productId(item.getStr("ITEM_NUMBER"))
                .product(item.getStr("PRODUCT"))
                .so(item.getStr("ERP_SO"))
                .quantity(getQuantity(item.getStr("QUANTITY")))
                .orderKey(item.getStr("ORDER_KEY"))
                .productPower(item.getInt("PRODUCT_POWER"))
                .build());
    }

    @Override
    public List<ShipmentReleaseApproveItemsDTO> shipmentReleaseApproveItemsDTO(String pi) {
//        String apiReturnFields = "id,pi,project_name,erp_so,start_time,delivery_time,delivery_approval_time,approval_duration,destination,product,required_date,quantity,total_power,approval_status,approval_person,finance_approval_time,finance_approval_person,etl_time";
        String apiReturnFields = "pi,erp_so,product,quantity,delivery_approval_time,approval_status,finance_approval_time";
        return getResults(releaseApproveApiId, apiReturnFields, pi, null, item -> ShipmentReleaseApproveItemsDTO.builder()
                .pi(item.getStr("PI"))
                .product(item.getStr("PRODUCT"))
                .so(item.getStr("ERP_SO"))
                .quantity(getQuantity(item.getStr("QUANTITY")))
                .deliveryApprovalTime(DateUtil.parseLocalDateTime(item.getStr("DELIVERY_APPROVAL_TIME")))
                .approvalStatus(item.getStr("APPROVAL_STATUS"))
                .financeApprovalTime(DateUtil.parseLocalDateTime(item.getStr("FINANCE_APPROVAL_TIME")))
                .build());
    }

    @Override
    public List<ShipmentTransportItemsDTO> shipmentTransportItemsDTO(String pi, String erpSo) {
//        String apiReturnFields = ""id,pi,project_name,erp_so,order_key,shipment_xid,destination,product,required_date,capacity,cabinet_number,item_number,container_number,license_plate_number,transports,oversea_origin_port,oversea_destination_port,oversea_arrival,transport_person,transport_time,etl_time";
        String apiReturnFields = "pi,erp_so,shipment_xid,product,license_plate_number,transport_person";
        return getResults(transportApiId, apiReturnFields, pi, erpSo, item -> ShipmentTransportItemsDTO.builder()
                .pi(item.getStr("PI"))
                .product(item.getStr("PRODUCT"))
                .so(item.getStr("ERP_SO"))
                .shipmentNo(item.getStr("SHIPMENT_XID"))
                .licensePlateNumber(item.getStr("LICENSE_PLATE_NUMBER"))
                .transportPerson(item.getStr("TRANSPORT_PERSON"))
                .build());
    }

    @Override
    public List<ShipmentReleaseItemsDTO> shipmentReleaseItemsDTO(String pi) {
//        String apiReturnFields = "id,pi,project_name,invoicing_amount_ratio,collection_amount_ratio,erp_so,whse,delivery_time,destination,product,required_date,capacity,cabinet_number,product_item_number,item_number,container_number,product_power,quantity,total_power,current_grading,pallets_number,delivery_owner,etl_time";
        String apiReturnFields = "pi,erp_so,product,quantity,total_power,product_power,delivery_time";
        return getResults(releaseApiId, apiReturnFields, pi, null, item -> ShipmentReleaseItemsDTO.builder()
                .pi(item.getStr("PI"))
                .deliveryTime(DateUtil.parseLocalDateTime(item.getStr("DELIVERY_TIME")))
                .productPower(item.getInt("PRODUCT_POWER"))
                .totalPower(item.getStr("TOTAL_POWER"))
                .product(item.getStr("PRODUCT"))
                .so(item.getStr("ERP_SO"))
                .quantity(getQuantity(item.getStr("QUANTITY")))
                .build());
    }

    @Override
    public List<FullOrderProcessDTO> customerSigningStatus(List<String> contractNos) {
//        String apiReturnFields = "id,contract_no,customer_name,opportunity_generation_status,opportunity_generation_time,quotation_provision_status,quotation_provision_time,contract_confirmation_status,contract_confirmation_time,contract_double_sign_status,contract_double_sign_time,production_scheduling_application_status,production_scheduling_application_time,schedule_confirmation_status,schedule_confirmation_time,starting_production_status,production_start_time,completion_status,completion_time,cabinet_reporting_status,container_reporting_time,release_status,delivery_time,release_approval_status,release_approval_time,shipment_status,shipment_time,starting_transportation_status,start_transportation_time,origin_port_loading_status,origin_port_loading_time,destination_port_arrival_status,destination_port_arrival_time,destination_warehouse_bottom_status,destination_warehouse_bottom_time,customer_signing_status,customer_signature_time,invoice_status,invoice_time,collection_customs_declaration_status,collection_customs_clearance_time,etl_time,sort_col,ts_sales_team_c,ts_account_mdm_id_formula_c";
        String apiReturnFields = "contract_no,customer_signing_status";
        return getResultsByContractNo(fullOrderProcessApiId, apiReturnFields, contractNos, item -> FullOrderProcessDTO.builder()
                .contractNo(item.getStr("CONTRACT_NO"))
                .customerSigningStatus(item.getStr("CUSTOMER_SIGNING_STATUS"))
                .build());
    }

    private Integer getQuantity(String quantityStr) {
        if (ObjectUtil.isNotEmpty(quantityStr)) {
            return Double.valueOf(quantityStr).intValue();
        }
        return null;
    }

    private <T> List<T> getResultsByContractNo(String apiId, String apiReturnFields, List<String> contractNos, Function<JSONObject, T> func) {
        String apiType = "LIST";
        String listType = "LIST";
        HashMap<String, Object> condition = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(contractNos)) {
            condition.put("contract_no", contractNos);
        }
        return getResults(apiId, apiType, apiReturnFields, listType, condition, func);

    }

    private <T> List<T> getResults(String apiId, String apiReturnFields, String pi, String erpSo, Function<JSONObject, T> func) {
        String apiType = "LIST";
        String listType = "LIST";
        HashMap<String, Object> condition = Maps.newHashMap();
        if (ObjectUtil.isNotEmpty(pi)) {
            condition.put("pi", Lists.newArrayList(pi));
        }
        if (ObjectUtil.isNotEmpty(erpSo)) {
            condition.put("erp_so", Lists.newArrayList(erpSo));
        }
        return getResults(apiId, apiType, apiReturnFields, listType, condition, func);
    }

    /**
     * 调用接口并解析成对应的返回数据
     */
    private <T> List<T> getResults(String apiId, String apiType, String apiReturnFields, String listType, HashMap<String, Object> condition, Function<JSONObject, T> func) {
        List<T> datas = new ArrayList<>();
        Integer pageIndex = 1;
        Integer pageSize = 5000;
        while (true) {
            log.info("pageIndex: {}， pageSize: {}", pageIndex, pageSize);
            Integer pageStart = (pageIndex - 1) * pageSize;
            JSONObject result = callApi(pageStart, pageSize, apiId, apiType, apiReturnFields, listType, condition);
            if (ObjectUtil.isNotNull(result)) {
                JSONArray items = result.getJSONArray("results");
                // 添加空指针保护
                if (items == null) {
                    return datas;
                }
                if (ObjectUtil.isNotEmpty(items)) {
                    List<T> dataItems = items.stream().map(e -> func.apply((JSONObject) e)).toList();
                    datas.addAll(dataItems);
                    pageIndex++;
                }
                int returnSize = items.size();
                // 如果返回的数量和分页大小不一致，说明分页结束了
                if (returnSize != pageSize) {
                    return datas;
                }
            } else {
                return datas;
            }
        }
    }

    @SuppressWarnings("all")
    private JSONObject callApi(Integer pageStart, Integer pageSize, String apiId, String apiType, String apiReturnFields, String listType, HashMap<String, Object> condition) {

        //创建请求参数 ---------------------------------------
        QueryParamRequest queryParamRequest = new QueryParamRequest();
        //构造请求参数对象
        //---条件参数
        //添加查询条件,其中key为对应的查询字段,value为查询字段对应的值, 例如这里的id为请求字段名,1为id对应的值,可以设置多个查询参数
//        HashMap<String, Object> condition = Maps.newHashMap();
        //注意：如果是 IN 类型的参数，使用 list 包装参数值。
        queryParamRequest.setConditions(condition);

        //-- 排序(可选设置)
        // 注意oracle和sqlServer使用分页需要同时使用排序
        // 排序字段,根据返回参数指定升序或者降序, 例如返回结果按id进行升序, 可设置多个字段进行升序或者降序
        // 使用分页则必须指定排序字段，并且要使用排序稳定的字段（例如主键、联合主键）保证每次排序结果相同，避免分页不准确
        ArrayList<OrderBy> orderList = Lists.newArrayList();
        //OrderBy.Order asc = OrderBy.Order.ASC;
        //OrderBy orderByColumn1 = new OrderBy("your order column", asc);
        //OrderBy orderByColumn2 = new OrderBy("your order column", asc);
        //orderList.add(orderByColumn1);
        //orderList.add(orderByColumn2);
        queryParamRequest.setOrderBys(orderList);

        //指定返回有权限的参数
        List<String> returnFiles = Lists.newArrayList(Splitter.on(",").split(apiReturnFields));
        queryParamRequest.setReturnFields(returnFiles);

        //进行分页(可选).不设置，默认取1~1000条数据
        queryParamRequest.setPageStart(pageStart);
        queryParamRequest.setPageSize(pageSize);

        // 是否缓存查询结果，开启则会缓存同一个API相同条件、想通返回字段的查询结果
        // 适用于数据不变化的查询
        // 缓存时长默认30分钟, 3.5.6 版本后，在开发API时可设置缓存时长
        queryParamRequest.setUseResultCache(true);
        //结束创建请求参数 ---------------------------------------

        ApiClient apiClient = createHttpClient(appKey, appSecret);
        try {
            ApiResponse response = listType.equalsIgnoreCase(apiType) ? apiClient.listSync(apiId, queryParamRequest)
                    : apiClient.getSync(apiId, queryParamRequest);
            if (CODE != response.getCode()) {
                log.info("请求天机接口失败，请求code: {}", CODE);
                String result = new String(response.getBody());
                log.info("返回的body: {}", result);
                return null;
            }
            String result = new String(response.getBody());
            log.info(getResultString(response));
            return JSONUtil.parseObj(result);
        } catch (Exception e) {
            log.error("请求天机接口失败,异常信息: {}", e.getMessage());
            log.error("",e.getMessage());
        }
        return null;
    }

    private ApiClient createHttpClient(String appKey, String appSecret) {
        ApiClientBuilderParams params = new ApiClientBuilderParams();
        params.setAppKey(appKey);
        params.setAppSecret(appSecret);
        params.setHost(host);
        //默认为http协议, 如果API 支持 HTTPS, 这里也可以设置HTTPS
        params.setScheme(Scheme.HTTP);
        params.setStage("RELEASE");
        params.setEnv("PROD");
        return new ApiClient(params);
    }

    private static String getResultString(ApiResponse response) {
        StringBuilder result = new StringBuilder();
        result.append("ResultCode:").append(CLOUDAPI_LF).append(response.getCode()).append(CLOUDAPI_LF);
        result.append("RequestId:").append(response.getHeaders().get("x-ca-request-id")).append(CLOUDAPI_LF);
        result.append("ErrorCode:").append(response.getHeaders().get("x-ca-error-code")).append(CLOUDAPI_LF);
        if (CODE != response.getCode()) {
            result.append("Error:").append(response.getHeaders().get("x-ca-error-message")).append(CLOUDAPI_LF);
        }

        result.append("ResultBody:").append(CLOUDAPI_LF).append(
                new String(response.getBody(), SdkConstant.CLOUDAPI_ENCODING));
        return result.toString();
    }
}
