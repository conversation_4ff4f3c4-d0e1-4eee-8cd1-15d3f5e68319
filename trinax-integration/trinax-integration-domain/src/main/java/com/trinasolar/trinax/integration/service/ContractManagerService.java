package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.dto.input.contract.BrowseReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.PreSignReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.SignFileReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.AddressUrlDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SignFileResDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ContractManagerService {


    Result<AddressUrlDTO> getPreSignUrl(PreSignReqDTO req);

    Result<AddressUrlDTO> getBrowseUrl(BrowseReqDTO req);

    Result<List<SignFileResDTO>> signFileUrl(SignFileReqDTO req);

    void signFileDown(String documentId, String fileName, HttpServletResponse response, String contentType, String fileType);
}
