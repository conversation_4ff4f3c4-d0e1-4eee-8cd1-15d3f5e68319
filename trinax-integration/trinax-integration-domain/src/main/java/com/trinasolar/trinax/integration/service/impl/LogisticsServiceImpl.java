package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.json.JSONUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.httpclient.OkHttpUtil;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationResponseStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.dto.input.delivery.DeliverSyncReqDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderItemSyncRowDTO;
import com.trinasolar.trinax.integration.dto.input.delivery.OrderSyncHeadDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.DeliverSyncResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.LogisticsResDTO;
import com.trinasolar.trinax.integration.dto.output.delivery.OrderSyncResDTO;
import com.trinasolar.trinax.integration.manager.IntegrationLogManager;
import com.trinasolar.trinax.integration.manager.SalesForceRemoteService;
import com.trinasolar.trinax.integration.service.LogisticsService;
import com.trinasolar.trinax.integration.utils.CommonHelper;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LogisticsServiceImpl implements LogisticsService {
    @Autowired
    SalesForceRemoteService salesForceRemoteService;
    @Autowired
    IntegrationLogManager integrationLogManager;

    @Value("${trina.logistics.logisticsUrl}")
    private String logisticsUrl;

    @Value("${trina.logistics.tslClientId}")
    private String tslClientId;

    @Value("${trina.logistics.tslClientSecret}")
    private String tslClientSecret;

    @Override
    public Result<LogisticsResDTO> queryLogistics(String waybillNo) {

        String response = "";
        Map bodyMap = new HashMap<>();
        bodyMap.put("waybillNo", waybillNo);
        log.info("queryLogistics request:{}", bodyMap);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("tsl-clientid", tslClientId);
        headerMap.put("tsl-clientsecret", tslClientSecret);
        LocalDateTime start = LocalDateTime.now();
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            response = OkHttpUtil.postJsonParams(logisticsUrl, JSONUtil.toJsonStr(bodyMap), headerMap);
        } catch (Exception e) {
            log.error(waybillNo, e.getMessage());
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            return Result.fail("获取物流信息失败");
        } finally {
            integrationLogManager.sendIntegrationLogMq(waybillNo, BizOperationEnum.GET_CONTRACT_DELIVER_INFO.getCode(),
                    BizTypeEnum.DELIVER.getCode(), IntegrationForwardEnum.OUT.getCode(),
                    IntegrationSystemEnum.TMS.getCode(), waybillNo, response,
                    start, "", responseStatus);
        }

        return CommonHelper.analyzeLogisticsRsp(response, LogisticsResDTO.class);

    }

    @Override
    public Result<OrderSyncResDTO> orderSync(List<OrderSyncHeadDTO> req) {
        return salesForceRemoteService.orderSync(req);
    }

    @Override
    public Result<OrderSyncResDTO> orderProductSync(List<OrderItemSyncRowDTO> req) {
        return salesForceRemoteService.orderProductSync(req);
    }

    @Override
    public Result<DeliverSyncResDTO> deliverSync(List<DeliverSyncReqDTO> req) {
        return salesForceRemoteService.deliverSync(req);
    }
}
