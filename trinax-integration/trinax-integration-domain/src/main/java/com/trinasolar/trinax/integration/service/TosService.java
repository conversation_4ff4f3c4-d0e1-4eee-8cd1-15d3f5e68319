package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

public interface TosService {

    /**
     * 上传文件
     */
    TosFileDTO upload(MultipartFile file);

    /**
     * 下载文件
     */
    void download(String filePath, String fileName, HttpServletResponse response);

    /**
     * 删除文件
     *
     * @param filePath 文件保存路径 格式：/文件名/
     * @param fileName 文件名称
     */
    void delete(String filePath, String fileName);

}
