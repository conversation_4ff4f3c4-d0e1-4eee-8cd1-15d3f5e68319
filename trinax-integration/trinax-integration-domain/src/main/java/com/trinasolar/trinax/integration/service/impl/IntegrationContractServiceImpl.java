package com.trinasolar.trinax.integration.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationResponseStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.dto.input.contract.SfSyncContractReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleContractToOaReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfContractItemRemainingDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfSyncContractDTO;
import com.trinasolar.trinax.integration.manager.IntegrationLogManager;
import com.trinasolar.trinax.integration.manager.SalesForceRemoteService;
import com.trinasolar.trinax.integration.manager.TrinaOAService;
import com.trinasolar.trinax.integration.service.IntegrationContractService;
import com.trinasolar.trinax.integration.service.biz.RemainingNumberBiz;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Yang
 */
@Slf4j
@Service
@RefreshScope
public class IntegrationContractServiceImpl implements IntegrationContractService {

    @Autowired
    private RemainingNumberBiz remainingNumberBiz;

    @Value("${remainingPiecesDrC}")
    private boolean remainingPiecesDrC;
    @Autowired
    IntegrationLogManager integrationLogManager;
    @Autowired
    private SalesForceRemoteService salesforceRemoteService;
    @Autowired
    private TrinaOAService trinaOaService;


    @Override
    public List<SfContractItemRemainingDTO> getSfContractItemRemainingDTOS(List<String> sfRecordIds) {
        JSONObject body = JSONUtil.createObj()
                .set("contractIdList", sfRecordIds);
        String dataStr = null;
        LocalDateTime requestTime =LocalDateTime.now();

        try {
            dataStr = remainingNumberBiz.getSfContractItemRemainingDTOS(body);

            integrationLogManager.sendIntegrationLogMq("", BizOperationEnum.GET_CONTRACT_REMAIN_QTY.getCode()
                    , BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(), IntegrationSystemEnum.SALES_FORCE.getCode()
                    , JacksonUtil.bean2Json(sfRecordIds), dataStr, requestTime, "", IntegrationResponseStatusEnum.SUCCESS.getCode());
            log.info("从SF获取合同剩余数量获取结果：{}", dataStr);

            JSONObject result = JSONUtil.parseObj(dataStr);
            List<SfContractItemRemainingDTO> sfContractItemRemainingDTOS = result.getJSONArray("records").stream().map(obj -> {
                JSONObject record = (JSONObject) obj;
                return SfContractItemRemainingDTO.builder()
                        .sfContractItemId(record.getStr("Id"))
                        .sfRecordId(record.getStr("TS_Contract__c"))
                        .remainingQuantityP(remainingPiecesDrC ? record.getInt("TS_Remaining_Pieces_DR__c") : record.getInt("TS_Remaining_Pieces__c"))
                        .soRemainingQuantity(record.getInt("TS_Remaining_Pieces__c"))
                        .deliveredPieces(record.getInt("TS_Delivered_Pieces__c"))
                        .drOccupied(record.getInt("TS_DR_Occupied_QTY__c"))
                        .build();
            }).toList();
//            log.info("从SF获取合同剩余数量获取结果：{}", sfContractItemRemainingDTOS);
            return sfContractItemRemainingDTOS;
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            log.error("调用接口失败，从SF获取合同剩余数量获取结果：{}, 请求数据:{}，异常信息：{}", dataStr, body, ex.getMessage());
            throw new BizException(ResultCode.FAIL.getCode(), "从SF获取合同剩余数量失败");
        }
    }

    @Override
    public Result<SfSyncContractDTO> syncContractStatusToSFDC(SfSyncContractReqDTO sfSyncContractReqDTO) {
        return salesforceRemoteService.syncContract(sfSyncContractReqDTO);
    }

    @Override
    public Result<String> sendContractToOa(SaleContractToOaReqDTO saleContractToOaReqDTO) {
        return trinaOaService.sendContractToOa(saleContractToOaReqDTO);
    }

}
