package com.trinasolar.trinax.integration.service;

import com.trinasolar.trinax.integration.dto.output.contract.*;

import java.util.List;

public interface ContractShipmentService {

    List<ShipmentSignItemsDTO> shipmentSignItemsDTO(String pi, String erpSo);

    List<ShipmentOutboundItemsDTO> shipmentOutboundItemsDTO(String pi);

    List<ShipmentReleaseApproveItemsDTO> shipmentReleaseApproveItemsDTO(String pi);

    List<ShipmentTransportItemsDTO> shipmentTransportItemsDTO(String pi, String erpSo);

    List<ShipmentReleaseItemsDTO> shipmentReleaseItemsDTO(String pi);

    List<FullOrderProcessDTO> customerSigningStatus(List<String> contractNos);
}
