package com.trinasolar.trinax.integration.service.biz;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;

@Slf4j
@Service
public class RemainingNumberBiz {

    @Value("${trina.remain.url02}")
    private String remainUrl02;

    @Value("${ipass.clientId}")
    private String tslClientId;

    @Value("${ipass.clientSecret}")
    private String tslClientSecret;

    @Value("${spring.profiles.active}")
    private String active;



    /**
     * 超时时间
     */
    private int TIMEOUT_MILLISECONDS = 20000;

    @Retryable(value = Exception.class, maxAttempts = 2)
    public String getSfContractItemRemainingDTOS(JSONObject body) {
        log.info("active: {}, remainUrl02: {}， tslClientId：{}, tslClientSecret: {}", active, remainUrl02, tslClientId, tslClientSecret);
        LocalDateTime start = LocalDateTime.now();
        try {
            String dataStr = HttpRequest.post(remainUrl02)
                    .body(body.toString())
                    .header("tsl-clientid", tslClientId)
                    .header("tsl-clientsecret", tslClientSecret)
                    .timeout(TIMEOUT_MILLISECONDS)
                    .execute().body();
            return dataStr;
        } finally {
            log.info("调用剩余数量耗时{}毫秒", Duration.between(start, LocalDateTime.now()).toMillis());
        }

    }
}
