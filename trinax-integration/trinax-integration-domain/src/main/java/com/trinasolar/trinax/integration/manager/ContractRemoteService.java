package com.trinasolar.trinax.integration.manager;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.api.ContractFrameFeign;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.dto.output.ContractResPcDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.ContractFrameResDTO;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationResponseStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.dto.input.contract.BrowseReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.PreSignReqDTO;
import com.trinasolar.trinax.integration.dto.input.contract.SignFileReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.AddressUrlDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SignFileResDTO;
import com.trinasolar.trinax.integration.manager.remote.contract.*;
import com.trinasolar.trinax.integration.utils.TokenHelper;
import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class ContractRemoteService {
    @Autowired
    ContractFeign contractFeign;
    @Autowired
    TokenHelper tokenHelper;
    @Autowired
    ThirdContractFeign thirdFeign;

    @Autowired
    private ContractFrameFeign contractFrameFeign;
    @Autowired
    IntegrationLogFeign integrationLogFeign;

    //合同查看方式进入详情页
    private static final String CONTENT = "CONTENT";

    //页面是否显示下载按钮
    private static final String ALLOW_DOWNLOAD = "true";

    //签署方类型：COMPANY（外部企业）
    private static final String COMPANY = "COMPANY";
    //签署顺序（从1开始）默认塞2，单签动作已做过
    private static final Integer SERIAL_NO = 2;
    //CORPORATE（企业签章）
    private static final String CORPORATE = "CORPORATE";

    private static final String signFileUrlPath = "/document/download";

    @Autowired
    IntegrationLogManager integrationLogManager;
    //流速
    @Value("${trina.contract.url}")
    String signFileDownUrl;

    /**
     * 获取签署地址接口
     * 调用契约锁
     */
    public Result<AddressUrlDTO> getPreSignUrl(PreSignReqDTO req) {
        String tenantName;
        String agreementId;
        String contractStatus;
        if (req.isFrame()) {
            ContractFrameResDTO contractFrameResDTO = contractFrameFeign.getByContractId(req.getTrinaContractId());
            tenantName = contractFrameResDTO.getEnterpriseName();
            agreementId = contractFrameResDTO.getAgreementId();
            contractStatus = contractFrameResDTO.getContractStatus();
        } else {
            ContractResPcDTO contractInfo = contractFeign.queryContactById(req.getTrinaContractId()).getData();
            tenantName = StringUtils.isBlank(contractInfo.getCapitalEnterpriseName()) ?
                    contractInfo.getEnterpriseName() : contractInfo.getCapitalEnterpriseName();
            agreementId = contractInfo.getAgreementId();
            contractStatus = contractInfo.getContractStatus();

        }
        if (!ContractStatusEnum.SINGLE_SIGNED.getCode().equals(contractStatus)) {
            return Result.fail("当前合同状态已发生变更，您无法进行在线签约哦～");
        }
        ThirdContractSignReqDTO request = getRequestBody(req, agreementId, tenantName);
        LocalDateTime start = LocalDateTime.now();
        ThirdContractPreSignResDTO urlResult = null;
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            urlResult = thirdFeign.signurl(request, tokenHelper.contractHeaderInit());
            log.info("获取签署地址结果，result={}", JacksonUtil.bean2Json(urlResult));
            checkResponse(urlResult.getCode(), "获取签署地址异常");
        } catch (Exception e) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            log.error("获取签署地址失败", e);
            throw new BizException(ResultCode.FAIL.getCode(), "获取签署地址失败");
        } finally {
            integrationLogManager.sendIntegrationLogMq(req.getTrinaContractId(), BizOperationEnum.GET_CONTRACT_SIGN_URL.getCode(),
                    BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(),
                    IntegrationSystemEnum.QYS.getCode(), JacksonUtil.bean2Json(request), JacksonUtil.bean2Json(urlResult),
                    start, "", responseStatus);
        }
        AddressUrlDTO addressUrlDTO = new AddressUrlDTO();
        addressUrlDTO.setPresignUrl(urlResult.getSignUrl());
        return Result.ok(addressUrlDTO);

    }

    /**
     * 获取请求body
     */
    private ThirdContractSignReqDTO getRequestBody(PreSignReqDTO req, String agreementId, String tenantName) {
        Long contractId = Long.parseLong(agreementId);
        ThirdContractSignReqDTO signReq = new ThirdContractSignReqDTO();
        signReq.setContractId(contractId);
        signReq.setTenantName(tenantName);
        signReq.setTenantType(COMPANY);
        signReq.setContact(req.getMobile());
        Action action = new Action();
        action.setName(tenantName + "签约请求");
        action.setType(CORPORATE);
        action.setSerialNo(SERIAL_NO);
        signReq.setAction(action);
        return signReq;

    }

    /**
     * 获取 合同浏览页面地址
     * 调用契约锁
     */
    public Result<AddressUrlDTO> getBrowseUrl(BrowseReqDTO req) {
        String agreementId;
        if (req.isFrame()) {
            // 框架合同
            ContractFrameResDTO contractFrameResDTO = contractFrameFeign.getByContractId(req.getTrinaContractId());
            agreementId = contractFrameResDTO.getAgreementId();
        } else {
            ContractResPcDTO contactInfo = contractFeign.queryContactById(req.getTrinaContractId()).getData();
            agreementId = contactInfo.getAgreementId();
        }
        Long contractId = Long.parseLong(agreementId);
        LocalDateTime start = LocalDateTime.now();
        ThirdContractViewResDTO urlResult = null;

        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            urlResult = thirdFeign.viewurl(contractId, CONTENT, ALLOW_DOWNLOAD, tokenHelper.contractHeaderInit());
            checkResponse(urlResult.getCode(), "查看合同文件详情异常");

        } catch (Exception e) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            log.error("获取查看合同地址失败", e);
            throw new BizException(ResultCode.FAIL.getCode(), "获取查看合同地址失败");
        } finally {
            integrationLogManager.sendIntegrationLogMq(req.getTrinaContractId(), BizOperationEnum.GET_CONTRACT_VIEW_URL.getCode(),
                    BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(),
                    IntegrationSystemEnum.QYS.getCode(), agreementId, JacksonUtil.bean2Json(urlResult),
                    start, "", responseStatus);
        }

        AddressUrlDTO addressUrlDTO = new AddressUrlDTO();
        addressUrlDTO.setViewUrl(urlResult.getViewUrl());
        return Result.ok(addressUrlDTO);
    }

    public Result<List<SignFileResDTO>> signFileUrl(SignFileReqDTO req) {
        ContractSignFileResDTO response = null;
        LocalDateTime start = LocalDateTime.now();
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        try {
            response = thirdFeign.detail(Long.parseLong(req.getAgreementId()), "", false, false, tokenHelper.contractHeaderInit());
            checkResponse(response.getCode(), "查看合同文件详情异常");
        } catch (Exception e) {
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
            log.error("查看合同文件详情异常捕获", e);
            throw new BizException(ResultCode.FAIL.getCode(), "查看合同文件详情异常");
        } finally {
            integrationLogManager.sendIntegrationLogMq(req.getAgreementId(), BizOperationEnum.GET_QYS_CONTRACT_DETAIL.getCode(),
                    BizTypeEnum.CONTRACT.getCode(), IntegrationForwardEnum.OUT.getCode(),
                    IntegrationSystemEnum.QYS.getCode(), req.getAgreementId(), JacksonUtil.bean2Json(response),
                    start, req.getCurrentUserId(), responseStatus);
        }

        return Result.ok(signFileInit(response));
    }

    private void checkResponse(Integer code, String methodName) {
        if (0 != code) {
            throw new BizException(ResultCode.FAIL.getCode(), methodName);
        }
    }

    private List<SignFileResDTO> signFileInit(ContractSignFileResDTO response) {
        if (0 != response.getCode()) {
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(response.getContract().getDocuments(), SignFileResDTO.class);
    }

    public void signFileDown(String documentId, String fileName, HttpServletResponse response, String contentType, String fileType) {
        //base64 0brRtNG+0bU= GET传输变成0brRtNG 0bU=
//        documentId = documentId.replace(" ", "+");
//        fileName = fileName.replace(" ", "+");
//        byte[] decodeUrlByte = Base64.getDecoder().decode(documentId);
//        byte[] fileNameDecodeByte = Base64.getDecoder().decode(fileName);
        int byteread = 0;
        HttpURLConnection conn = null;
        try {
            byte[] fileNameDecodeByte = URLDecoder.decode(fileName,"utf-8").getBytes();
            documentId = new String(URLDecoder.decode(documentId,"utf-8").getBytes(), "GBK");
            fileName = new String(fileNameDecodeByte, "GBK");

            if (StringUtils.isBlank(fileName)) {
                fileName = "签约合同";
            }
            StringBuilder urlStr = new StringBuilder();

            urlStr.append(signFileDownUrl)
                    .append(signFileUrlPath)
                    .append("?")
                    .append("documentId")
                    .append("=")
                    .append(documentId);
            HashMap<String, String> headers = tokenHelper.qysHeaderInit();
            URL url = new URL(urlStr.toString());
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Host", url.getHost());
            conn.setRequestProperty("Accept", "text/plain,application/json");
            conn.setRequestProperty("User-Agent", "privateapp-java-api-client");
            conn.setRequestProperty("x-qys-accesstoken", headers.get("x-qys-accesstoken"));
            conn.setRequestProperty("x-qys-timestamp", headers.get("x-qys-timestamp"));
            conn.setRequestProperty("x-qys-signature", headers.get("x-qys-signature"));
            conn.setRequestProperty("Content-Type", headers.get("Content-Type"));
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);

            String fileNameURL = URLEncoder.encode(fileName, "GBK");
            //get 文件名请求方式
//            response.setHeader("Content-disposition", "attachment;filename=" + fileNameURL + ";" + "filename*=utf-8''" + fileNameURL);
            // 设置response的Header post 文件名方式

            log.info("当前浏览器：{}", contentType);

            if (org.apache.commons.lang3.StringUtils.isNotBlank(contentType)) {
                //chrome头也包含safari,需要排除chrome
                if (contentType.contains("safari") && !contentType.contains("chrome")) {
                    response.setContentType("application/pdf");
                    fileName = new String(fileNameDecodeByte, "utf-8");
                    fileNameURL = URLEncoder.encode(fileName, "utf-8");
                    //get 文件名请求方式
                    response.setHeader("content-disposition","attachment;filename*=UTF-8''" + fileNameURL);                    // 设置response的Header post 文件名方式
                } else {
                    response.setContentType("application/octet-stream");
                    response.setHeader("Content-Disposition", "attachment;filename=" + fileNameURL);
                }
            } else {
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileNameURL);
            }

            InputStream inStream = conn.getInputStream();

            //支持 60m 下载
            byte[] buffer = new byte[122880];
            while ((byteread = inStream.read(buffer)) != -1) {
                //本地下载放开
                //fs.write(buffer, 0, byteread);
                //写入输出流
                response.getOutputStream().write(buffer, 0, byteread);
            }
        } catch (Exception e) {
            log.error("三方文件下载失败：", e);

        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }
}
