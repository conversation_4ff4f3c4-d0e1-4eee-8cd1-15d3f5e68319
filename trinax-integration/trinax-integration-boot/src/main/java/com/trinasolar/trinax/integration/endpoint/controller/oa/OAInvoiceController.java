package com.trinasolar.trinax.integration.endpoint.controller.oa;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.api.oa.OAInvoiceFeign;
import com.trinasolar.trinax.integration.dto.input.oa.InvoiceQuantityQryReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleInvoiceReqDTO;
import com.trinasolar.trinax.integration.dto.output.oa.InvoiceQuantityQryResDTO;
import com.trinasolar.trinax.integration.dto.output.oa.SaleInvoiceResDTO;
import com.trinasolar.trinax.integration.service.OAInvoiceService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "OA系统对接-开票")
@RequiredArgsConstructor
public class OAInvoiceController implements OAInvoiceFeign {
    final OAInvoiceService oaInvoiceService;

    @Override
    public Result<List<InvoiceQuantityQryResDTO>> qryInvoiceQuantity(List<InvoiceQuantityQryReqDTO> req) {
        return oaInvoiceService.qryInvoiceQuantity(req);
    }

    @Override
    public Result<SaleInvoiceResDTO> initiateSaleInvoice(SaleInvoiceReqDTO req) {
        return oaInvoiceService.initiateSaleInvoice(req);
    }
}
