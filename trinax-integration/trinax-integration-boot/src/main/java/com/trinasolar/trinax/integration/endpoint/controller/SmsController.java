package com.trinasolar.trinax.integration.endpoint.controller;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.api.SmsFeign;
import com.trinasolar.trinax.integration.dto.input.TrinaSmsReqDTO;
import com.trinasolar.trinax.integration.service.SmsService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * created on 2023/10/09
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "Trina发送短信")
public class SmsController implements SmsFeign {

    @Autowired
    private SmsService smsService;

    @Override
    public Result<String> trinaSmsSend(TrinaSmsReqDTO req) {
        return smsService.trinaSmsSend(req);
    }
}
