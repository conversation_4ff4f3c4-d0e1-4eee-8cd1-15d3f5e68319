package com.trinasolar.trinax.config;

import com.trinasolar.trinax.utils.QiyuesuoCallbackUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CallbackConfiguration {
	
	@Value("${qiyuesuo.callback.aesKey:KaLRxXpHVK}")
	private String aesKey;
	@Value("${qiyuesuo.callback.token:yLsJilaO1Cx10CNihBYU9UkWCHvJrM}")
	private String token;
	
	@Bean
	public QiyuesuoCallbackUtils callbackV2DecryptUtil() {
		return new QiyuesuoCallbackUtils(aesKey, token);
	}
}
