package com.trinasolar.trinax.integration.endpoint.controller.third;

import cn.hutool.core.date.DateUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.config.TokenVerification;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.dto.input.BPMContractReqDTO;
import com.trinasolar.trinax.delivery.api.OpsDeliverFeign;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.ApprovalStatusReqDTO;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.dto.output.oa.InvoiceStatusChangedResDTO;
import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import com.trinasolar.trinax.log.dto.mq.InterfaceLogMqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "oa/bpm For Third")
public class ThirdOaController {

    private final OpsDeliverFeign opsDeliverFeign;
    private final IntegrationLogFeign integrationLogFeign;

    private final ContractFeign contractFeign;


    @Operation(summary = "bpm审批状态通知接口")
    @PostMapping("/external/drRelease/approvalStatusAccess")
    @TokenVerification
    Result<InvoiceStatusChangedResDTO> approvalStatusAccess(@RequestBody ApprovalStatusReqDTO  req) {
        log.info("ThirdOaController.approvalStatusAccess req={}", JacksonUtil.bean2Json(req));
        LocalDateTime requestTime = LocalDateTime.now();
        Result<String> resp = opsDeliverFeign.approvalStatusAccess(req);
        log.info("ThirdOaController.approvalStatusAccess resp={}", JacksonUtil.bean2Json(resp));
        InterfaceLogMqDTO messageReq = InterfaceLogMqDTO.builder()
                .setBizNo(req.getZzbId())
                .setBizOperation(BizOperationEnum.BPM_STATUS_ACCESS.getCode())
                .setBizType(BizTypeEnum.RELEASE_DR.getCode())
                .setIntegrationForward(IntegrationForwardEnum.IN.getCode())
                .setIntegrationSystem(IntegrationSystemEnum.OA.getCode())
                .setRequestData(JacksonUtil.bean2Json(req))
                .setResponseStatus("SUCCESS")
                .setRequestTime(requestTime)
                .setResponseTime(LocalDateTime.now())
                .setResponseData(JacksonUtil.bean2Json(resp));
        integrationLogFeign.sendIntegrationLogMq(messageReq);
        return Result.ok();
    }

    @Operation(summary = "合同的bpm审批状态通知接口")
    @PostMapping("/external/contract/trinaxBPMCallBack")
    @TokenVerification
    Result<String> trinaxBPMCallBack(@RequestBody Map<String, String> req) {
        log.info("ThirdOaController.trinaxBPMCallBack req={}", JacksonUtil.bean2Json(req));
        LocalDateTime requestTime = LocalDateTime.now();
        BPMContractReqDTO bpmContractReqDTO = build(req);
        Result<String> resp = contractFeign.trinaxBPMCallBack(bpmContractReqDTO);
        log.info("ThirdOaController.trinaxBPMCallBack resp={}", JacksonUtil.bean2Json(resp));
        InterfaceLogMqDTO messageReq = InterfaceLogMqDTO.builder()
                .setBizNo(bpmContractReqDTO.getBpmTaskID())
                .setBizOperation(BizOperationEnum.BPM_STATUS_ACCESS.getCode())
                .setBizType(BizTypeEnum.RELEASE_DR.getCode())
                .setIntegrationForward(IntegrationForwardEnum.IN.getCode())
                .setIntegrationSystem(IntegrationSystemEnum.OA.getCode())
                .setRequestData(JacksonUtil.bean2Json(req))
                .setResponseStatus("SUCCESS")
                .setRequestTime(requestTime)
                .setResponseTime(LocalDateTime.now())
                .setResponseData(JacksonUtil.bean2Json(resp));
        integrationLogFeign.sendIntegrationLogMq(messageReq);
        return Result.ok();
    }

    private BPMContractReqDTO build(Map<String, String> req) {
        BPMContractReqDTO bpmContractReqDTO = new BPMContractReqDTO();
        bpmContractReqDTO.setApprovalCompletionTime(DateUtil.parseLocalDateTime(req.get("approvalCompletionTime")));
        bpmContractReqDTO.setApprovalNodeDescription(req.get("approvalNodeDescription"));
        bpmContractReqDTO.setApprovalNodeID(req.get("approvalNodeID"));
        bpmContractReqDTO.setApprovalResult(req.get("approvalResult"));
        bpmContractReqDTO.setApprover(req.get("approver"));
        bpmContractReqDTO.setSourceOperator(req.get("sourceOperator"));
        bpmContractReqDTO.setBpmTaskID(req.get("BPMTaskID"));
        bpmContractReqDTO.setComments(req.get("Comments"));
        bpmContractReqDTO.setTransferType(req.get("TransferType"));
        bpmContractReqDTO.setFinalApprovalResult(req.get("finalApprovalResult"));
        return bpmContractReqDTO;
    }
}
