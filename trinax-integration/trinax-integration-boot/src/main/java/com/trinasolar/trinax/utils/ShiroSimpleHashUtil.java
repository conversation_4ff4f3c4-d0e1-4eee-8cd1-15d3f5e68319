package com.trinasolar.trinax.utils;

import org.apache.shiro.crypto.hash.SimpleHash;
import org.apache.shiro.util.ByteSource;

public class ShiroSimpleHashUtil {

	/**
	 * 加密算法
	 */
	public final static String HASH_ALGORITHM_NAME = "SHA-256";
	/**
	 * 循环次数
	 */
	public final static int HASH_ITERATIONS = 16;

	/**
	 * shiro simple hash
	 *
	 * @param source
	 * @param salt
	 * @return java.lang.String
	 * @create 2019/9/25
	 */
	public static String simpleHash(String source, String salt) {
		return new SimpleHash(HASH_ALGORITHM_NAME, source, ByteSource.Util.bytes(salt), HASH_ITERATIONS).toString();
	}


	/**
	 * shiro simple hash
	 *
	 * @param source
	 * @return java.lang.String
	 * @create 2019/9/25
	 */
	public static String simpleHash(String source) {
		return simpleHash(source, "YzcmCZNvbXocrsz9dm8e");
	}
}
