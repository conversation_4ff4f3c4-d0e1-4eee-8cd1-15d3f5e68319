package com.trinasolar.trinax.integration.endpoint.controller;

import cn.hutool.core.util.StrUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.api.TosFeign;
import com.trinasolar.trinax.integration.dto.input.DeleteTosFileReqDTO;
import com.trinasolar.trinax.integration.dto.input.DownloadTosFileReqDTO;
import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import com.trinasolar.trinax.integration.service.TosService;
import com.trinasolar.trinax.integration.service.impl.TosMinioServiceImpl;
import feign.Response;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> Hongyi
 * created on 2023/10/23
 */
@Slf4j
@RestController
@Tag(name = "访问TOS")
public class TosController implements TosFeign {

    @Resource(type = TosMinioServiceImpl.class)
    private TosService tosService;

    /**
     * 上传文件
     */
    @Override
    @Operation(summary = "上传文件")
    public Result<TosFileDTO> upload(MultipartFile file) {
        return Result.ok(tosService.upload(file));
    }

    @Override
    @Operation(summary = "下载文件")
    public Response download(DownloadTosFileReqDTO req) {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        tosService.download(req.getFilePath(), req.getFileName(), response);
        return null;
    }

    @Override
    @Operation(summary = "下载文件（URL）")
    public Response download(String url) {
        int index = url.lastIndexOf("/");
        String filePath = StrUtil.sub(url, 0, index);
        String fileName = StrUtil.sub(url, index + 1, url.length());
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        tosService.download(filePath, fileName, response);
        return null;
    }

    @Override
    @Operation(summary = "删除文件")
    public Result<Void> delete(DeleteTosFileReqDTO req) {
        tosService.delete(req.getFilePath(), req.getFileName());
        return Result.ok();
    }

}