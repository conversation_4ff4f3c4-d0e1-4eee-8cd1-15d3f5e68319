package com.trinasolar.trinax.integration.endpoint.controller.third;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.config.TokenVerification;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationResponseStatusEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.manager.IntegrationLogManager;
import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.dto.input.SyncProductReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.SyncStorageProductReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Hongyi
 * created on 2023/10/12
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "产品管理 For Third")
public class ThirdPbiController {

    private final ProductFeign productFeign;
    private final IntegrationLogManager integrationLogManager;

    @Operation(summary = "同步产品族信息")
    @PostMapping("/products/sync")
    @TokenVerification
    public Result<Void> sync(@RequestBody SyncProductReqDTO req) {
        log.info("ThirdPbiController.sync req={}", JacksonUtil.bean2Json(req));
        LocalDateTime requestTime = LocalDateTime.now();
        Result<Void> resp = productFeign.sync(req);
        log.info("ThirdContractController.sync resp={}", JacksonUtil.bean2Json(resp));
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if(Boolean.FALSE.equals(resp.getSuccess())){
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.getProductId(),BizOperationEnum.SYNC_PRODUCT_INFO.getCode()
                ,BizTypeEnum.PRODUCT.getCode(),IntegrationForwardEnum.IN.getCode(),IntegrationSystemEnum.PBI.getCode()
                ,JacksonUtil.bean2Json(req),JacksonUtil.bean2Json(resp),requestTime,"", responseStatus);
        return resp;
    }

    @Operation(summary = "同步工商储产品族信息")
    @PostMapping("/products/storage/sync")
    @TokenVerification
    public Result<Void> sync(@RequestBody SyncStorageProductReqDTO req) {
        log.info("ThirdPbiController.storage.sync req={}", JacksonUtil.bean2Json(req));
        LocalDateTime requestTime = LocalDateTime.now();
        Result<Void> resp = productFeign.storageSync(req);
        log.info("ThirdContractController.storage.sync resp={}", JacksonUtil.bean2Json(resp));
        String responseStatus = IntegrationResponseStatusEnum.SUCCESS.getCode();
        if(Boolean.FALSE.equals(resp.getSuccess())){
            responseStatus = IntegrationResponseStatusEnum.FAIL.getCode();
        }
        integrationLogManager.sendIntegrationLogMq(req.getProductName(),BizOperationEnum.SYNC_STORAGE_PRODUCT_INFO.getCode()
                ,BizTypeEnum.PRODUCT.getCode(),IntegrationForwardEnum.IN.getCode(),IntegrationSystemEnum.PBI.getCode()
                ,JacksonUtil.bean2Json(req),JacksonUtil.bean2Json(resp),requestTime,"", responseStatus);
        return resp;
    }

}