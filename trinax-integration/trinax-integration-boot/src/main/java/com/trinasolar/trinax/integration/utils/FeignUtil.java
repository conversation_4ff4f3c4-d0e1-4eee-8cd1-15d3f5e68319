package com.trinasolar.trinax.integration.utils;

import cn.hutool.core.collection.CollectionUtil;
import feign.Response;

import java.util.Collection;

public class FeignUtil {

    public static String getHeader(Response response, String headerKey) {
        if (response == null) {
            return null;
        }

        Collection<String> list = response.headers().get(headerKey);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.toString().replace("[", "").replace("]", "");
    }

}
