package com.trinasolar.trinax.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SentinelServersConfig;
import org.redisson.config.SingleServerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Configuration
//@ConditionalOnExpression("#{!'local'.equals(environment['spring.profiles.active'])}")
public class RedissonConfig {

    @Autowired
    private RedisProperties redisProperties;

    @Bean
    public RedissonClient getRedissonClient() {
        Config config = new Config();
        if (redisProperties.getSentinel() != null) {
            // 集群模式配置
            List<String> nodes = redisProperties.getSentinel().getNodes();

            List<String> sentinelAddresses = new ArrayList<>();
            for (int i = 0; i < nodes.size(); i++) {
                if (redisProperties.isSsl()) {
                    sentinelAddresses.add("rediss://" + nodes.get(i));
                } else {
                    sentinelAddresses.add("redis://" + nodes.get(i));
                }
            }
            SentinelServersConfig sentinelServersConfig = config.useSentinelServers();
            sentinelServersConfig.setSentinelAddresses(sentinelAddresses);

            if (!StrUtil.isEmpty(redisProperties.getPassword())) {
                sentinelServersConfig.setPassword(redisProperties.getPassword());
            }
            sentinelServersConfig.setMasterName(redisProperties.getSentinel().getMaster());
            sentinelServersConfig.setMasterConnectionMinimumIdleSize(5);
            sentinelServersConfig.setSlaveConnectionMinimumIdleSize(5);
            sentinelServersConfig.setMasterConnectionPoolSize(10);
            sentinelServersConfig.setSlaveConnectionPoolSize(10);
            sentinelServersConfig.setConnectTimeout(5000);
            sentinelServersConfig.setTimeout(5000);
        } else {
            // 单节点配置
            String address = "redis://" + redisProperties.getHost() + ":" + redisProperties.getPort();
            SingleServerConfig serverConfig = config.useSingleServer();
            serverConfig.setAddress(address);
            if (!StrUtil.isEmpty(redisProperties.getPassword())) {
                serverConfig.setPassword(redisProperties.getPassword());
            }
            serverConfig.setDatabase(redisProperties.getDatabase());
        }
        return Redisson.create(config);
    }

}