package com.trinasolar.trinax.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.parameters.Parameter;
import org.springdoc.core.customizers.OperationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Knife4jConfiguration {

    @Bean
    public OpenAPI api() {
        return new OpenAPI()
                .info(new Info().title("第三放系统对接-API文档")
                        .description("第三放系统对接")
                        .version("1.0"));
    }

    /**
     * 添加认证header
     * @return
     */
    @Bean
    public OperationCustomizer operationCustomizer() {
        return (operation, handlerMethod) -> operation
                .addParametersItem(
                    new Parameter()
                        .in("header")
                        .required(false)
                        .description("token验证")
                        .name("zzb-appid"))
                .addParametersItem(
                    new Parameter()
                        .in("header")
                        .required(false)
                        .description("token验证")
                        .name("zzb-token"))
                .addParametersItem(
                        new Parameter()
                                .in("header")
                                .required(false)
                                .description("token验证")
                                .name("Authorization"));
    }

}
