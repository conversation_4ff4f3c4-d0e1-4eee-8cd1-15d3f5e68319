package com.trinasolar.trinax.dto;

import java.util.HashMap;

public class AccessResult extends HashMap<String, Object> {
	private static final String DEF_CODE_VALUE = "code";
	private static final String DEF_STATUS = "status";
	private static final String DEF_MESSAGE_VALUE = "message";
	
	public AccessResult(Integer code, String message, String status) {
		super();
		if (code != null) {
			super.put(DEF_CODE_VALUE, code);
		}
		if (message != null) {
			super.put(DEF_MESSAGE_VALUE, message);
		}
		if (status != null) {
			super.put(DEF_STATUS, status);
		}
	}
	
	public static AccessResult newSuccessMessage(String message) {
		return new AccessResult(0, message, "success");
	}
	
	public static AccessResult newErrorMessage(Integer code, String message) {
		return new AccessResult(code, message, "error");
	}
}
