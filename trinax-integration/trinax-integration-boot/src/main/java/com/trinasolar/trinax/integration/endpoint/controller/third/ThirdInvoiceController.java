package com.trinasolar.trinax.integration.endpoint.controller.third;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.config.TokenVerification;
import com.trinasolar.trinax.integration.constants.enums.BizOperationEnum;
import com.trinasolar.trinax.integration.constants.enums.BizTypeEnum;
import com.trinasolar.trinax.integration.constants.enums.IntegrationSystemEnum;
import com.trinasolar.trinax.integration.dto.input.oa.InvoiceStatusChangedReqDTO;
import com.trinasolar.trinax.integration.dto.output.oa.InvoiceStatusChangedResDTO;
import com.trinasolar.trinax.log.api.IntegrationLogFeign;
import com.trinasolar.trinax.log.constants.enums.IntegrationForwardEnum;
import com.trinasolar.trinax.log.dto.mq.InterfaceLogMqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

/**
 * <AUTHOR> Yin
 * created on 2023/11/29
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@Tag(name = "开票 For Third")
public class ThirdInvoiceController {

    @Autowired
    private IntegrationLogFeign integrationLogFeign;


    @Operation(summary = "开票申请状态变化时")
    @PostMapping("/contract/invoiceStatusChanged")
    @TokenVerification
    Result<InvoiceStatusChangedResDTO> invoiceStatusChanged(@RequestBody InvoiceStatusChangedReqDTO req) {
        log.info("ThirdInvoiceController.invoiceStatusChanged req={}", JacksonUtil.bean2Json(req));
        LocalDateTime requestTime=LocalDateTime.now();
        //TODO 1、contract需要提供修改开票信息接口；2、日志枚举修改
        Result<InvoiceStatusChangedResDTO> resp = null;
        log.info("ThirdInvoiceController.invoiceStatusChanged resp={}", JacksonUtil.bean2Json(resp));
        InterfaceLogMqDTO messageReq = InterfaceLogMqDTO.builder()
                .setBizNo(req.getInvoiceApplicationId())
                .setBizOperation(BizOperationEnum.CONTRACT_SINGLE_SIGN_NOTIFY.getCode())
                .setBizType(BizTypeEnum.CONTRACT.getCode())
                .setIntegrationForward(IntegrationForwardEnum.IN.getCode())
                .setIntegrationSystem(IntegrationSystemEnum.QYS.getCode())
                .setRequestData(JacksonUtil.bean2Json(req))
                .setResponseStatus("SUCCESS")
                .setRequestTime(requestTime)
                .setResponseTime(LocalDateTime.now())
                .setResponseData(JacksonUtil.bean2Json(resp));
        integrationLogFeign.sendIntegrationLogMq(messageReq);
        return Result.ok();

    }
}
