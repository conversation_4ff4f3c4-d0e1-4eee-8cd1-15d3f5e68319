package com.trinasolar.trinax.integration.api;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.integration.constants.ServiceIds;
import com.trinasolar.trinax.integration.dto.input.DeleteTosFileReqDTO;
import com.trinasolar.trinax.integration.dto.input.DownloadTosFileReqDTO;
import com.trinasolar.trinax.integration.dto.output.tos.TosFileDTO;
import feign.Response;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = ServiceIds.INTEGRATION_SERVICE)
public interface TosFeign {

    @PostMapping(value= "/tos/upload" , consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<TosFileDTO> upload(@RequestPart("file") MultipartFile file);

    @Parameters({
            @Parameter(name = "filePath", required = true, description = "文件路径", example = "/2023/10/25", schema = @Schema(type = "string")),
            @Parameter(name = "fileName", required = true, description = "文件名称", example = "7fcee8966b744f27a346abe234b36319", schema = @Schema(example = "string"))
    })
    @GetMapping(value= "/tos/download")
    Response download(@Parameter(hidden = true) @SpringQueryMap DownloadTosFileReqDTO req);

    @GetMapping(value= "/tos/downloadByUrl")
    Response download(@RequestParam String url);

    @Parameters({
            @Parameter(name = "filePath", required = true, description = "文件路径", example = "/2023/10/25", schema = @Schema(type = "string")),
            @Parameter(name = "fileName", required = true, description = "文件名称", example = "7fcee8966b744f27a346abe234b36319", schema = @Schema(example = "string"))
    })
    @GetMapping(value= "/tos/delete")
    Result<Void> delete(@Parameter(hidden = true) @SpringQueryMap DeleteTosFileReqDTO req);

}
