package com.trinasolar.trinax.integration.dto.input.delivery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
public class OrderItemSyncRowDTO {
    @Schema(description = "订单产品")
    private String Product2Id;

    @Schema(description = "订单")
    private String OrderId;

    @Schema(description = "价格条目")
    private String PricebookEntryId;

    @Schema(description = "端子")
    private String TS_Plug_Connector__c;

    @Schema(description = "安装方式")
    private String TS_Installation__c;

    @Schema(description = "合同产品")
    private String TS_Contract_Item__c;

    @Schema(description = "订单类型")
    private String TS_OrderType__c;

    @Schema(description = "线缆长度")
    private String TS_Cable_Length__c;

    @Schema(description = "正极电缆长度/米")
    private BigDecimal TS_Cable_Length_MM__c;
    @Schema(description = "负极电缆长度/米")
    private BigDecimal TS_Cable_Length_M__c;

    @Schema(description = "功率")
    private BigDecimal TS_Power__c;

    @Schema(description = "期望发货日期")
    private LocalDate TS_Expected_Delivery_Date__c;

    @Schema(description = "单价/瓦")
    private BigDecimal TS_Unit_Price__c;

    @Schema(description = "notes")
    private String TS_Notes__c;

    @Schema(description = "行合同号-柜号")
    private String TS_Contract_No__c;

    @Schema(description = "FromProductEditor")
    private Boolean TS_FromProductEditor__c;

    @Schema(description = "容量")
    private BigDecimal TS_Volume_MW__c;

    @Schema(description = "数量")
    private Integer Quantity;

    @Schema(description = "单价")
    private BigDecimal UnitPrice;

    @Schema(description = "片数")
    private Integer TS_Pieces__c;

    @Schema(description = "对象属性")
    private OrderSyncAttributes attributes;

}
