package com.trinasolar.trinax.integration.dto.output.so;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Schema()
@RequiredArgsConstructor(staticName = "builder")
@AllArgsConstructor
@Accessors(chain = true)
public class RecordOccupiedQty {
    @Schema(description = "RDD")
    private String Id;

    @Schema(description = "占用数量")
    private Integer TS_SO_Occupied_QTY__c;
}