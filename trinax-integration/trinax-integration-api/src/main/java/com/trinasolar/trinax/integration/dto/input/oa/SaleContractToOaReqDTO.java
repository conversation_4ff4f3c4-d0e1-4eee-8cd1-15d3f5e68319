package com.trinasolar.trinax.integration.dto.input.oa;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(description = "销售合同请求DTO")
public class SaleContractToOaReqDTO {
    @Schema(description = "货物到达区域")
    private String arrivalRegion;

    @Schema(description = "货物到达子区域")
    private String arrivalSubRegion;

    @Schema(description = "平均价差")
    private String aspDifAVG;

    @Schema(description = "开票国家")
    private String billToCountry;

    @Schema(description = "气候类型")
    private String climateType;

    @Schema(description = "合同类型")
    private String contract;

    @Schema(description = "合同号")
    private String contractBarcode;

    @Schema(description = "企业类型")
    private String creditTerms;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "客户ID")
    private String customerId;

    @Schema(description = "客户名")
    private String customerName;

    @Schema(description = "发货日期")
    private String deliveryDate;

    @Schema(description = "附件列表")
    private List<Attachment> fileList;

    @Schema(description = "框架合同确认")
    private String frameContractConfirm;

    @Schema(description = "")
    private String frameNo;

    @Schema(description = "是否投标")
    private String isBiddingContract;

    @Schema(description = "是否有咨询费")
    private String isFinderFee;

    @Schema(description = "")
    private String isFrame;

    @Schema(description = "是否框架合同")
    private String isFrameContract;

    @Schema(description = "")
    private String kfContractType;

    @Schema(description = "主产品列表")
    private List<MainProduct> mainProducts;

    @Schema(description = "最长交期")
    private String maxDeliveryTime;

    @Schema(description = "其他列表")
    private List<OtherProduct> otherProduct;

    @Schema(description = "有无其他信控条款风险")
    private String otherTerms;

    @Schema(description = "原因")
    private String otherTermsReason;

    @Schema(description = "业务实体Code")
    private String ouCode;

    @Schema(description = "业务实体Name")
    private String ouName;

    @Schema(description = "付款条款")
    private String paymentTerms;

    @Schema(description = "担保付款")
    private String prePayment;

    @Schema(description = "总额度差")
    private String priceDif;

    @Schema(description = "项目地")
    private String projectSite;

    @Schema(description = "申请人邮箱")
    private String proposer;

    @Schema(description = "季度")
    private String quarter;

    @Schema(description = "USD转RMB汇率")
    private String rateUSDToRMB;

    @Schema(description = "区域")
    private String region;

    @Schema(description = "是否关联方")
    private String relatedParty;

    @Schema(description = "销售团队类型")
    private String salesTeamType;

    @Schema(description = "销售人员")
    private String salesperson;

    @Schema(description = "")
    private String sfComment;

    @Schema(description = "SF标识")
    private String sfdcId;

    @Schema(description = "发运到国家")
    private String shipToCountry;

    @Schema(description = "备品备件列表")
    private List<SpareProduct> spareProducts;

    @Schema(description = "子区域")
    private String subRegion;

    @Schema(description = "产品合同总额")
    private String sumAmountM;

    @Schema(description = "产品合同总额(RMB)")
    private String sumAmountMRMB;

    @Schema(description = "产品合同总额(USD)")
    private String sumAmountMUSD;

    @Schema(description = "合同总额")
    private String totalAmount;

    @Schema(description = "合同总额(RMB)")
    private String totalAmountRMB;

    @Schema(description = "合同总额(USD)")
    private String totalAmountUSD;

    @Schema(description = "贸易条款")
    private String tradeTerms;

    @Schema(description = "")
    private String utilityText;

    @Schema(description = "年")
    private String year;

    @Schema(description = "")
    private String frameInstanceId;

    @Schema(description = "销售折让金额")
    private String saleAmount;

    @Schema(description = "付款日期")
    private String payTime;

    @Schema(description = "发货时间")
    private String deliveryTime;

    @Schema(description = "付款期间天数")
    private String payDueDays;

    @Schema(description = "发货期限天数")
    private String deliveryDueDays;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "是否存在销售折让")
    private Integer sfczzr;

    @Schema(description = "是否标准合同")
    private Integer sfbzcgmb;


    @Schema(description = "客户信息")
    private List<SaleContractOaCustomer> customerList;

}
