package com.trinasolar.trinax.integration.dto.output.enterprise;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class Address {
    @Schema(description = "sf 客户地址 id")
    private String sfAddressId;

    @Schema(description = "地址类型")
    private String addressType;

    @Schema(description = "收件人姓名")
    private String recipientName;

    @Schema(description = "收件人联系方式")
    private String recipientPhone;

    @Schema(description = "地址名称")
    private String addressName;

    @Schema(description = "国家编码")
    private String countryCode;

    @Schema(description = "省份编码")
    private String provinceCode;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String districtCode;

    @Schema(description = "街道编码")
    private String streetCode;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "详细地址")
    private String addressDetail;

    @Schema(description = "地址状态")
    private String addressStatus;

    @Schema(description = "ouList")
    private List<OrganizationalUnit> ouList;
}
