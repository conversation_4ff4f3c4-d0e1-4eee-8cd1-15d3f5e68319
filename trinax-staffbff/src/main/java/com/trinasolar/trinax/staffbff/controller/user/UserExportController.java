package com.trinasolar.trinax.staffbff.controller.user;


import com.alibaba.excel.support.ExcelTypeEnum;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.excel.ExcelDownloadUtil;
import com.trinasolar.trinax.user.api.ReportFormsFeign;
import com.trinasolar.trinax.user.dto.input.report.UserTrendReqDTO;
import com.trinasolar.trinax.user.dto.output.report.UserTrendResDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.List;

@RequestMapping("/user")
@Tag(name = "用户报表")
@RestController
@RequiredArgsConstructor
public class UserExportController {
    private final ReportFormsFeign reportFormsFeign;

    @Operation(summary = "用户趋势图")
    @PostMapping("/userTrend")
    Result<List<UserTrendResDTO>> userTrend(@Valid @RequestBody UserTrendReqDTO req) {
        return reportFormsFeign.userTrend(req);
    }

    @Operation(summary = "用户趋势列表导出")
    @PostMapping({"userTrendExport"})
    void trendListExport(@RequestBody UserTrendReqDTO reqDTO, HttpServletResponse response) {
        Result<List<UserTrendResDTO>> result = reportFormsFeign.userTrend(reqDTO);
        String filename = "用户_" + DateFormatUtils.format(new Date(), "yyyyMMdd_HH_mm_ss");
        ExcelDownloadUtil.download(response, UserTrendResDTO.class, result.getData(), filename, ExcelTypeEnum.XLSX);
    }
}
