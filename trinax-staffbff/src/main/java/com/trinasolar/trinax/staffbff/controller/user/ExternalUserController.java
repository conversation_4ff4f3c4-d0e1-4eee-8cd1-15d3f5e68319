package com.trinasolar.trinax.staffbff.controller.user;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.excel.ExcelDownloadUtil;
import com.trinasolar.trinax.log.behavior.support.BehaviorLog;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBriefDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.input.*;
import com.trinasolar.trinax.user.dto.output.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "外部员工用户管理")
public class ExternalUserController {

    private final SysUserFeign sysUserFeign;
    private final EnterpriseFeign enterpriseFeign;

    @PostMapping(value = "/user/external-users-page")
    public Result<PageResponse<SysExternalUserRespDTO>> getExternalUserPage(@RequestBody PageRequest<UsersQueryDTO> request) {
        request.getQuery().setLoginUserId(AuthUserHelper.getAuthUser().getUserIdStr());
        return sysUserFeign.getExternalUserPage(request);
    }
    @Operation(summary = "外部用户更新")
    @BehaviorLog(ignore = false,ignoreReq = false,bizNo = "#reqDTO.userId")
    @PutMapping("/user/external-users")
    public Result<Boolean> updateExternalUser(@Valid @RequestBody SysExternalUserUpdateReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        if (null != authUser) {
            reqDTO.setUpdatedBy(authUser.getUserIdStr());
            reqDTO.setUpdatedName(authUser.getName());
        }
        Result<Boolean> isMobileChanged = sysUserFeign.updateExternalUser(reqDTO);
        return isMobileChanged;
    }

    @Operation(summary = "添加外部用户")
    @PostMapping("/user/externalAdd")
    public Result<Void> externalAdd(@Valid @RequestBody SysExternalUserAddReqDTO reqDTO) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        reqDTO.setCreatedBy(authUser.getUserIdStr());
        reqDTO.setCreatedName(authUser.getName());
        return sysUserFeign.externalAdd(reqDTO);
    }

    @Operation(summary = "pc端外部用户查询详情")
    @GetMapping("/user/external/{userId}")
    public Result<SysExternalUserDetailRespDTO> getExternalUserByUserId(@PathVariable @NotBlank(message = "必须存在userId") String userId) {
        return sysUserFeign.getExternalUserByUserId(userId);
    }

    @GetMapping("/user/external/enterprises")
    public Result<List<EnterpriseBriefDTO>> getAllEnterpriseBriefs() {
        return enterpriseFeign.getAllEnterpriseBriefs();
    }

    @GetMapping(value = "/user/external/superiors")
    public Result<List<SysUserRespDTO>> getSuperiors(@RequestParam("enterpriseId") String enterpriseId) {
        return sysUserFeign.getExternalSuperiors(enterpriseId);
    }

    @GetMapping(value = "/user/external/salesmen")
    public Result<List<SysOrganizationSalesRespDTO>> getSalesmen(@RequestParam("enterpriseId") String enterpriseId) {
        return sysUserFeign.getSalesmen(enterpriseId);
    }

    @BehaviorLog(ignore = false,ignoreReq = false)
    @PostMapping("/user/external/export")
    public void exportExternalUsers(@Validated @RequestBody UsersQueryDTO req,
                                    HttpServletResponse response) {
        List<SysExternalUserRespDTO> sysExternalUserRespDTOS = sysUserFeign.exportExternalUsers(req);
        ExcelDownloadUtil.download(response, SysExternalUserRespDTO.class, sysExternalUserRespDTOS, "外部用户数据", ExcelTypeEnum.XLSX);
    }

    @Operation(summary = "问卷-选择外部用户-分页 ")
    @PostMapping("/user/pageQuestionnaireExternalUser")
    Result<PageResponse<QuestionnaireExternalUserRespDTO>> pageQuestionnaireExternalUser(@RequestBody PageRequest<QuestionnaireExternalUserQueryDTO> pageReqDTO) {
        return sysUserFeign.pageQuestionnaireExternalUser(pageReqDTO);
    }
}
