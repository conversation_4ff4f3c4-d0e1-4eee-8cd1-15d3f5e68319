package com.trinasolar.trinax.staffbff.controller.user;

import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysUserStatusEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SysUserStatusUpdateReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserUpdateReqDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import com.trinasolar.trinax.user.dto.output.UserDetailRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.trinasolar.trinax.common.dto.output.Result.ok;
import static com.trinasolar.trinax.user.constants.UserResultCode.USER_STATUS_EXCEPTION;

@RequestMapping("/user")
@Tag(name = "用户管理")
@RestController
@RequiredArgsConstructor
public class CommonUserController {
    private final SysUserFeign sysUserFeign;

    private final TokenStore tokenStore;

    @Value("${pcClientId:AdnbImRHCsJPyTJs6d7OUnWl}")
    private String pcClientId;

    @PutMapping("/user-status")
    public Result<List<String>> updateUserStatusBatch(@Validated @RequestBody SysUserStatusUpdateReqDTO reqDTO) {
        Result<List<String>> repeatUser = sysUserFeign.updateUserStatusBatch(reqDTO);
        if (StringUtils.equals(repeatUser.getCode(), USER_STATUS_EXCEPTION.getCode())) {
            return repeatUser;
        }
        if (SysUserStatusEnum.ENABLE.getCode().equals(reqDTO.getStatus())) {
            return ok();
        }
        //禁用后清空token
        for (String userId : reqDTO.getUserIds()) {
            Result<SysUserRespDTO> userDto = sysUserFeign.getUserByUserId(userId);
            //如果userId不存在，就不用走下线
            if (!userDto.getSuccess()) {
                continue;
            }
            SysUserRespDTO user = userDto.getData();
            String userType = user.getUserType();
            if (SysUserTypeEnum.INTERNAL.getType().equals(userType)) {
                //内部是邮箱或者工号
//                AuthUserHelper.offlineUser(tokenStore,appClientId,user.getUserEmail());//TODO 只考虑了email
                AuthUserHelper.offlineUser(tokenStore, pcClientId, user.getUserEmail());
            }
            if (SysUserTypeEnum.EXTERNAL.getType().equals(userType)) {
                //外部手机号
//                AuthUserHelper.offlineUser(tokenStore,appClientId,user.getMobile());
            }
        }

        return ok();
    }

    @Operation(summary = "获取用户详情-pc端")
    @GetMapping("/pcUserDetail")
    Result<UserDetailRespDTO> pcUserDetail(@RequestParam String userId) {
        return sysUserFeign.pcUserDetail(userId);
    }

    /**
     * 邮箱登录
     *
     * @param reqDTO
     * @return
     */
    @Operation(summary = "邮箱登录")
    @PostMapping("/validateEmail")
    Result<Boolean> validateEmail(@RequestBody SysUserUpdateReqDTO reqDTO){
        return sysUserFeign.validateEmail(reqDTO);
    }

    /**
     * 修改用户邮箱密码
     *
     * @param reqDTO
     * @return
     */
    @PutMapping("/updateEmail")
    Result<Boolean> updateEmail(@RequestBody SysUserUpdateReqDTO reqDTO){
        return sysUserFeign.updateEmail(reqDTO);
    }


}
