spring:
  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis连接超时时间（单位为毫秒）
    timeout: 30000
    password: ${REDIS_PASSWORD}
    sentinel:
      master: redis-master
      nodes:
        - ${REDIS_NODES_1}
        - ${REDIS_NODES_2}
        - ${REDIS_NODES_3}

rocketmq:
  name-server: ${ROCKETMQ_HOST}
  producer:
    # 发送同一类消息的设置为同一个group，保证唯一
    group: staffbff

#日志上报环境地址
logging:
  collector:
    address: ${LOGGING_COLLECTOR_ADDRESS}

appClientId: ${APP_CLIENT_ID}