package com.trinasolar.trinax.basic.controller;

import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.domain.option.service.OptionGroupService;
import com.trinasolar.trinax.basic.domain.option.service.OptionItemLanguageService;
import com.trinasolar.trinax.basic.domain.option.service.OptionItemService;
import com.trinasolar.trinax.basic.dto.input.OptionGroupPageReqDTO;
import com.trinasolar.trinax.basic.dto.input.OptionItemBatchReqDTO;
import com.trinasolar.trinax.basic.dto.input.option.*;
import com.trinasolar.trinax.basic.dto.output.*;
import com.trinasolar.trinax.basic.dto.output.option.*;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "字典查询接口")
@Slf4j
@Validated
@RestController
public class OptionController implements OptionFeign {
    @Autowired
    OptionGroupService optionGroupService;

    @Autowired
    private OptionItemService optionItemService;

    @Autowired
    private OptionItemLanguageService optionItemLanguageService;



    @Override
    public Result<List<OptionGroupDTO>> getGroupList() {
        return Result.ok(optionGroupService.getGroupList());
    }

    @Override
    public Result<List<OptionItemDTO>> getOptionItemList(String language, String optionGroup) {
        return Result.ok(optionGroupService.getOptionItemList(optionGroup, language));
    }

    @Override
    public Result<List<OptionItemLanguageResDTO>> getOptionItemListBatch(OptionItemBatchReqDTO reqDTO) {
        return Result.ok(optionGroupService.getOptionItemListBatch(reqDTO));
    }


    @Override
    public Result<List<IndustryOptionItemDTO>> getIndustryOptionItemList(String language) {
        return Result.ok(optionGroupService.getIndustryOptionItemList(language));

    }

    @Override
    public Result<List<OptionItemResDTO>> getOptionItemByValue(String optionValue) {
        return Result.ok(optionItemService.getOptionItemByValue(optionValue));
    }

    @Override
    public Result<List<OptionItemResDTO>> getBatchOptionItemByValue(List<String> optionValueList) {
        return Result.ok(optionItemService.getBatchOptionItemByValue(optionValueList));
    }

    @Override
    public Result<PageResponse<OptionGroupResDTO>> findOptionGroupPage(PageRequest<OptionGroupPageReqDTO> request) {
        return optionGroupService.findOptionGroupPage(request);
    }

    @Override
    public Result<PageResponse<OptionItemLanguagePageResDTO>> findOptionItemLanguagePage(PageRequest<OptionItemLanguagePageReqDTO> request) {
        return optionItemLanguageService.findOptionItemLanguagePage(request);
    }

    @Override
    public Result<List<OptionItemLanguageExcelDTO>> findOptionItemLanguageList(OptionItemLanguagePageReqDTO request) {
        return optionItemLanguageService.findOptionItemLanguageList(request);
    }

    @Override
    public Result<String> enableOrDisableItemLanguage(OptionItemLanguageEnableReqDTO reqDTO) {
        return optionItemLanguageService.enableOrDisable(reqDTO);
    }

    @Override
    public Result<String> optionItemLanguageSave(OptionItemLanguageSaveReqDTO reqDTO) {
        return optionItemLanguageService.optionItemLanguageSave(reqDTO);
    }

    @Override
    public Result<String> optionItemLanguageEdit(OptionItemLanguageUpdateReqDTO reqDTO) {
        return optionItemLanguageService.optionItemLanguageEdit(reqDTO);
    }

    @Override
    public Result<List<OptionInfoDownloadRTO>> qryOptionGroup(OptionGroupPageReqDTO request) {
        return optionGroupService.qryOptionGroup(request);
    }

    @Override
    public Result<OptionItemResDTO> getOptionItemLevelOneByOptionGroup(String optionGroup) {
        return null;
    }

    @Override
    public Result<List<OptionItemResDTO>> qryOptionItemList(OptionItemQueryReqDTO reqDTO) {
        return Result.ok(optionItemService.qryOptionItemList(reqDTO));
    }

    @Override
    public Result<String> saveItem(OptionItemSaveReqDTO reqDTO) {
        return optionItemService.saveItem(reqDTO);
    }

    @Override
    public Result<String> modifyItem(OptionItemModifyReqDTO reqDTO) {
        return optionItemService.modifyItem(reqDTO);
    }

    @Override
    public Result<List<OptionItemResDTO>> qryOptionItemNoUseList(OptionItemQueryReqDTO reqDTO) {
        return optionItemService.qryOptionItemNoUseList(reqDTO);
    }

    @Override
    public Result<PageResponse<OptionItemResDTO>> qryOptionItemListPage(PageRequest<OptionItemQueryReqDTO> reqDTO) {
        return optionItemService.qryOptionItemListPage(reqDTO);
    }

    @Override
    public Result<OptionItemDetailResDTO> optionItemDetail(Long id) {
        return optionItemService.optionItemDetail(id);
    }
}
