# Tomcat
server:
  port: 7021
#日志组件
trinasolar:
  team: dtt-team
  project:
    name: trinax-basic-service

# Spring
spring:
  task:
    execution:
      pool:
        core-size: 4
        max-size: 10
        queue-capacity: 50
        keep-alive: 60
 
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  application:
    # 应用名称
    name: trinax-basic-service
    version: 1.0
  profiles:
    # 环境配置
    active: ${ACTIVE_PROFILE:local}
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
#      username: trinasolarx
#      password: ${NACOS_PASSWORD}
      discovery:
        # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
        # 服务注册地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
      config:
       # 使用德勤分配的 namespace 代替，如没有则注释掉这一样
        namespace: trinax
         # 使用德勤分配的 group 代替，如没有则注释掉这一样
#        group: trinax-public
        # 配置中心地址
        server-addr: ${NACOS:localhost}:8848
#        server-addr: mynacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-dataids: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
    sentinel:
      # 取消控制台懒加载
      eager: true
      transport:
        # 控制台地址
        dashboard: mysentinel:8718
      # nacos配置持久化
      datasource:
        ds1:
          nacos:
            server-addr: ${NACOS:localhost}:8848
#            server-addr: mynacos:8848
            dataId: sentinel-basic-service
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: flow
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  global-config:
    db-config:
      id-type: none

management:
  health.defaults.enabled: false

  #SPLTIJVCNPCCCPFU

# id生成配置
id-generator.segments:
  basic-todo-id:
    step: 3
    limitId: 999999
    preloadNext: false
  feedback:
    step: 3
    limitId: 999999
    preloadNext: false
  feedback-reply:
    step: 3
    limitId: 999999
    preloadNext: false
  changeOwner-task-no:
    step: 3
    limitId: 999999
    preloadNext: false
  changeOwner-sub-task-no:
    step: 10
    limitId: 999999
    preloadNext: false

message:
  template:
    feedback: FEEDBACK_SUBMIT
    feedbackReply: FEEDBACK_REPLY
    questionnaire: QUESTIONNAIRE_NOTICE
    noticeOriginSaleTemplate: CHANGE_OWNER_ORIGIN_SALES_NOTICE
    noticeNewSaleTemplate: CHANGE_OWNER_NEW_SALES_NOTICE
    noticeOriginOperationTemplate: CHANGE_OWNER_ORIGIN_OPERATION_NOTICE
    noticeNewOperationTemplate: CHANGE_OWNER_NEW_OPERATION_NOTICE
    noticeOriginManagerTemplate: CHANGE_OWNER_ORIGIN_MANAGER_NOTICE
    noticeNewManagerTemplate: CHANGE_OWNER_NEW_MANAGER_NOTICE
    noticeOriginExternalTemplate: CHANGE_OWNER_ORIGIN_EXTERNAL_NOTICE
    noticeNewExternalTemplate: CHANGE_OWNER_NEW_EXTERNAL_NOTICE


thymeleaf:
  prefix: classpath:/templates/
  suffix: .html
  encoding: UTF-8
  mode: HTML
  servlet:
    content-type: text/html