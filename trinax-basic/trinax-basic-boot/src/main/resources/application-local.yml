
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************************************************************************************************
    username: root
    password: 123456@2025
    hikari:
      minimum-idle: 10
      maximum-pool-size: 20
      idle-timeout: 10000
      max-lifetime: 1800000
      connection-timeout: 30000

  redis:
    # redis数据库索引（默认为0），我们使用索引为3的数据库，避免和其他数据库冲突
    database: 0
    # redis服务器地址（默认为localhost）
    host: ${REDIS_HOST:*************}
    # redis端口（默认为6379）
    port: ${REDIS_PORT:6379}
    # redis访问密码（默认为空）
#    password: ${REDIS_PASSWORD:Trina@123}
    # redis连接超时时间（单位为毫秒）
    timeout: 30000

  mail:
    default-encoding: UTF-8
    host: mail.trinasolar.com
    port: 25
    username: <EMAIL>
    password: ZHIZUNBAO1234.
    properties:
      mail:
        smtp:
          auth: false
          ssl:
            enable: false
          starttls:
            enable: false
            required: false

#日志上报环境地址
logging:
  collector:
    #dev
    address: localhost:8088

integration:
  log:
    on-off: false

xxl:
  job:
    adminAddress: ${XXLJOB_ADMINADDRESS:http://localhost:9997/xxl-job-admin}
    executorName: trinax-basic-service
    ip:
    port: 12000
    accessToken:
    logPath: /apps/logs/${spring.application.name}/xxl-job
    logRetentionDays: -1

rocketmq:
  name-server: ${ROCKETMQ_HOST:*************:9876}
  producer:
    group: basic
  consumer:
    group:
    topic:

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl #开启sql日志
application:
  website: https://trinax-dev.trinasolar.com/choose
  email:
    switch: false


filter:
  sender:
    email: <EMAIL>