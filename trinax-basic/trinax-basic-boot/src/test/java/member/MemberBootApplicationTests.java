package member;

import com.trinasolar.trinax.basic.BasicBootApplication;
import com.trinasolar.trinax.basic.constants.enums.EmailTemplateEnum;
import com.trinasolar.trinax.basic.dto.input.email.NewUserFollowSendEmailReqDTO;
import com.trinasolar.trinax.basic.email.EmailSendService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

@SpringBootTest(classes = BasicBootApplication.class)
class MemberBootApplicationTests {

	@Autowired
	EmailSendService emailSendService;

	@Test
	void testSendEmail() {
		NewUserFollowSendEmailReqDTO newUserFollowSendEmailReqDTO=new NewUserFollowSendEmailReqDTO();
		newUserFollowSendEmailReqDTO.setCcEmails(Arrays.asList("<EMAIL>"));
		newUserFollowSendEmailReqDTO.setToEmails(Arrays.asList("<EMAIL>"));
		newUserFollowSendEmailReqDTO.setCurrentUserId("");
		newUserFollowSendEmailReqDTO.setCurrentUserName("xxljob");
		newUserFollowSendEmailReqDTO.setEmailTemplateEnum(EmailTemplateEnum.NEW_USER_OPERATOR_EMAIL);
		newUserFollowSendEmailReqDTO.setSubject("用户运营跟进提醒邮件");
		newUserFollowSendEmailReqDTO.setFileId("6425f3fda611452a8c63277169c5cf7c");

		emailSendService.sendNewUserFollowHtmlMail(newUserFollowSendEmailReqDTO);
	}

}
