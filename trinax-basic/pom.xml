<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trinax-shared</artifactId>
        <version>0.0.2.RELEASE</version>
        <relativePath>../trinax-shared</relativePath>
    </parent>


    <packaging>pom</packaging>
    <modules>
        <module>trinax-basic-api</module>
        <module>trinax-basic-boot</module>
        <module>trinax-basic-domain</module>
    </modules>
    <groupId>com.trinasolar</groupId>
    <artifactId>trinax-basic-root</artifactId>
    <version>0.0.4-SNAPSHOT</version>
    <name>trinax-basic</name>
    <description>trina基础服务</description>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-basic-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-basic-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-common</artifactId>
                <version>0.0.2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-integration-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-user-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-partner-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-contract-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-delivery-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>