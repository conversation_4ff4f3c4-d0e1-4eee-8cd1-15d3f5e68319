package com.trinasolar.trinax.basic.changeowner.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * change owner变更记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("change_owner_record")
public class ChangeOwnerRecordPO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主任务编号
     */
    private String taskNo;

    /**
     * 子任务编号
     */
    private String subTaskNo;

    /**
     * 场景编码：意向单，合同,发货申请...
     */
    private String situationCode;

    /**
     * 变更的业务表
     */
    private String changeTable;

    /**
     * 变更的业务表字段
     */
    private String changeField;

    /**
     * 旧值
     */
    private String oldValue;

    /**
     * 新值
     */
    private String newValue;

    /**
     * 创建人id:操作人id
     */
    private String createdBy;

    /**
     * 创建人姓名；操作人名称
     */
    private String createdName;

    /**
     * 创建时间：操作时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人id
     */
    private String updatedBy;

    /**
     * 更新人姓名
     */
    private String updatedName;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


}
