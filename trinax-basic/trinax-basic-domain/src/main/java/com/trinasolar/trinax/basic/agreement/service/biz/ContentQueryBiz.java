package com.trinasolar.trinax.basic.agreement.service.biz;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.agreement.repository.atomicservice.ContentFileRelationMapperService;
import com.trinasolar.trinax.basic.agreement.repository.atomicservice.ContentInfoMapperService;
import com.trinasolar.trinax.basic.agreement.repository.mapper.ContentFileRelationMapper;
import com.trinasolar.trinax.basic.agreement.repository.mapper.ContentInfoMapper;
import com.trinasolar.trinax.basic.agreement.repository.po.ContentInfoPO;
import com.trinasolar.trinax.basic.constants.enums.AgreementTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.content.ContentEffectEnum;
import com.trinasolar.trinax.basic.domain.file.repository.po.UploadFilePO;
import com.trinasolar.trinax.basic.dto.input.content.AgreementAdminPageReqDTO;
import com.trinasolar.trinax.basic.dto.output.GetUploadFileResDTO;
import com.trinasolar.trinax.basic.dto.output.content.AgreementAdminPageRespDTO;
import com.trinasolar.trinax.basic.dto.output.content.AgreementAdminViewRespDTO;
import com.trinasolar.trinax.basic.dto.output.content.ContentFileInfo;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ContentQueryBiz {
    @Autowired
    ContentInfoMapper contentInfoMapper;
    @Autowired
    ContentInfoMapperService contentInfoMapperService;
    @Autowired
    ContentFileRelationMapper contentFileRelationMapper;
    @Autowired
    ContentFileRelationMapperService contentFileRelationMapperService;


    public PageResponse<AgreementAdminPageRespDTO> getAgreementAdminPage(PageRequest<AgreementAdminPageReqDTO> reqDTO) {

        Page<ContentInfoPO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        LambdaQueryWrapper<ContentInfoPO> queryWrapper = queryConditionInit(reqDTO.getQuery());

        Page<ContentInfoPO> rowPage = contentInfoMapper.selectPage(page, queryWrapper);
        List<ContentInfoPO> records = rowPage.getRecords();
        List<AgreementAdminPageRespDTO> agreementAdminPageRespDtos = BeanUtil.copyToList(records, AgreementAdminPageRespDTO.class);
        List<String> contentIds = agreementAdminPageRespDtos.stream().map(AgreementAdminPageRespDTO::getContentId).collect(Collectors.toList());

        List<ContentFileInfo> contentFileInfos =null;
        if (ObjectUtils.isEmpty(contentIds)) {
            contentFileInfos = new ArrayList<>();
        }else {
            contentFileInfos = contentFileRelationMapper.selFileInfoByFileId(contentIds);
        }

        Map<String, List<ContentFileInfo>> map = contentFileInfos.stream().collect(Collectors.groupingBy(ContentFileInfo::getContentId));
        agreementAdminPageRespDtos.forEach(agreement -> {
                    agreement.setTypeText(AgreementTypeEnum.getDescByCode(agreement.getType()));
                    agreement.setIsEffectName(ContentEffectEnum.finContentEffDesc(agreement.getIsEffect()));
                    if (ObjectUtils.isEmpty(map.get(agreement.getContentId()))) {
                        agreement.setAnnouncementType(0);
                    } else {
                        agreement.setAnnouncementType(1);
                        agreement.setFileInfos(map.get(agreement.getContentId()));
                    }
                }
        );

        return PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) rowPage.getTotal(),
                agreementAdminPageRespDtos);

    }

    private LambdaQueryWrapper<ContentInfoPO> queryConditionInit(AgreementAdminPageReqDTO reqDTO) {

        LambdaQueryWrapper<ContentInfoPO> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(reqDTO.getType())) {
            queryWrapper.eq(ContentInfoPO::getType, reqDTO.getType());
        }
        if (StringUtils.isNotBlank(reqDTO.getVersion())) {
            queryWrapper.eq(ContentInfoPO::getVersion, reqDTO.getVersion());
        }
        if (ObjectUtils.isNotEmpty(reqDTO.getIsEffect())) {
            queryWrapper.eq(ContentInfoPO::getIsEffect, reqDTO.getIsEffect());
        }
        queryWrapper.eq(ContentInfoPO::getIsDeleted, 0);
        queryWrapper.orderByDesc(ContentInfoPO::getType, ContentInfoPO::getIsEffect, ContentInfoPO::getCreatedTime);

        return queryWrapper;
    }

    public AgreementAdminViewRespDTO getAgreementAdmin(Long id) {
        ContentInfoPO contentinfoPo = contentInfoMapperService.getById(id);
        List<GetUploadFileResDTO> fileList = new ArrayList<>();

        if (ObjectUtils.isEmpty(contentinfoPo)) {
            return null;
        }

        List<UploadFilePO> file = contentFileRelationMapper.selFileInfo(contentinfoPo.getContentId());
        if (ObjectUtils.isNotEmpty(file)) {
            fileList = BeanUtil.copyToList(file, GetUploadFileResDTO.class);
        }
        AgreementAdminViewRespDTO result = BeanUtil.copyProperties(contentinfoPo, AgreementAdminViewRespDTO.class);
        result.setIsEffectName(ContentEffectEnum.finContentEffDesc(result.getIsEffect()));
        result.setFileInfo(fileList);
        return result;
    }

    public Result<List<AgreementAdminPageRespDTO>> getContentList(AgreementAdminPageReqDTO queryDto) {

        LambdaQueryWrapper<ContentInfoPO> queryWrapper = queryConditionInit(queryDto);

        List<ContentInfoPO> records = contentInfoMapperService.list(queryWrapper);
        if (ObjectUtils.isEmpty(records)) {
            return Result.ok();
        }
        List<AgreementAdminPageRespDTO> agreementAdminPageRespDtos = BeanUtil.copyToList(records, AgreementAdminPageRespDTO.class);
        List<String> contentIds = agreementAdminPageRespDtos.stream().map(AgreementAdminPageRespDTO::getContentId).collect(Collectors.toList());

        List<ContentFileInfo> contentFileInfos = contentFileRelationMapper.selFileInfoByFileId(contentIds);

        if (ObjectUtils.isEmpty(contentFileInfos)) {
            contentFileInfos = new ArrayList<>();
        }

        Map<String, List<ContentFileInfo>> map = contentFileInfos.stream().collect(Collectors.groupingBy(ContentFileInfo::getContentId));
        agreementAdminPageRespDtos.forEach(agreement -> {
                    agreement.setTypeText(AgreementTypeEnum.getDescByCode(agreement.getType()));
                    agreement.setIsEffectName(ContentEffectEnum.finContentEffDesc(agreement.getIsEffect()));
                    if (ObjectUtils.isEmpty(map.get(agreement.getContentId()))) {
                        agreement.setAnnouncementType(0);
                    } else {
                        agreement.setAnnouncementType(1);
                        agreement.setFileInfos(map.get(agreement.getContentId()));
                    }
                }
        );

        return Result.ok(agreementAdminPageRespDtos);
    }
}
