package com.trinasolar.trinax.basic.email.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
public class StaticThreadPoolHolder implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        StaticThreadPoolHolder.applicationContext = applicationContext;
    }

    public static ThreadPoolTaskExecutor getTaskExecutor() {
        return applicationContext.getBean("taskExecutor",ThreadPoolTaskExecutor.class);
    }
}