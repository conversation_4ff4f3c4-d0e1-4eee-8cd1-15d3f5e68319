package com.trinasolar.trinax.basic.todolist.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoPopupStatusEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoStatusEnum;
import com.trinasolar.trinax.basic.dto.TodoPopupReqDTO;
import com.trinasolar.trinax.basic.dto.input.*;
import com.trinasolar.trinax.basic.dto.output.TodoListNumResDTO;
import com.trinasolar.trinax.basic.dto.output.TodoListResDTO;
import com.trinasolar.trinax.basic.todolist.repository.atomicservice.TodoListMapperService;
import com.trinasolar.trinax.basic.todolist.repository.mapper.TodoListMapper;
import com.trinasolar.trinax.basic.todolist.repository.po.TodoListPO;
import com.trinasolar.trinax.basic.todolist.service.TodoListService;
import com.trinasolar.trinax.basic.todolist.service.biz.TodoListChangeOwnerBiz;
import com.trinasolar.trinax.basic.todolist.utils.TodoIdGenerator;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class TodoListServiceImpl implements TodoListService {

    @Autowired
    private TodoListMapper todoListMapper;

    @Autowired
    private TodoListMapperService todoListMapperService;

    @Autowired
    private TodoIdGenerator todoIdGenerator;

    @Autowired
    private TodoListChangeOwnerBiz todoListChangeOwnerBiz;


    @Override
    public Result<String> save(TodoListSaveReqDTO reqDTO) {
        TodoListPO todoListPO = BeanUtil.toBean(reqDTO, TodoListPO.class);
        todoListPO.setTodoId(todoIdGenerator.generateTodoId());
        todoListPO.setOriginTodoUserId(todoListPO.getTodoUserId());
        todoListPO.setCreatedTime(LocalDateTime.now());
        todoListPO.setPopupStatus(TodoPopupStatusEnum.NOT_POPUP.getCode());
        boolean bool = todoListMapperService.save(todoListPO);
        if (bool) {
            return Result.ok("添加待办成功");
        } else {
            return Result.fail("添加待办失败");
        }

    }

    @Override
    public Result<String> saveBatch(List<TodoListSaveReqDTO> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return Result.fail("批量添加待办失败,数据不为空");
        }
        List<TodoListPO> poList = BeanUtil.copyToList(reqList, TodoListPO.class);
        poList.forEach(e -> {
            e.setOriginTodoUserId(e.getTodoUserId());
            e.setCreatedTime(LocalDateTime.now());
            e.setTodoId(todoIdGenerator.generateTodoId());
            e.setPopupStatus(TodoPopupStatusEnum.NOT_POPUP.getCode());
        });
        boolean bool = todoListMapperService.saveBatch(poList);
        if (bool) {
            return Result.ok("批量添加待办成功");
        } else {
            return Result.fail("批量添加待办失败");
        }
    }

    @Override
    public Result<PageResponse<TodoListResDTO>> findPage(PageRequest<TodoListPageReqDTO> reqDTO) {
        LambdaQueryWrapper<TodoListPO> queryWrapper = new LambdaQueryWrapper<>();
        TodoListPageReqDTO query = reqDTO.getQuery();
        queryWrapper.ge(ObjectUtils.isNotEmpty(query.getCreatedTimeStart()), TodoListPO::getCreatedTime, query.getCreatedTimeStart())
                .le(ObjectUtils.isNotEmpty(query.getCreatedTimeEnd()), TodoListPO::getCreatedTime, query.getCreatedTimeEnd())
                .like(ObjectUtils.isNotEmpty(query.getTodoTitle()), TodoListPO::getTodoTitle, query.getTodoTitle())
                .eq(ObjectUtils.isNotEmpty(query.getTodoStatus()), TodoListPO::getTodoStatus, query.getTodoStatus())
                .like(ObjectUtils.isNotEmpty(query.getTodoContent()), TodoListPO::getTodoContent, query.getTodoContent())
                .likeRight(ObjectUtils.isNotEmpty(query.getBizCodeStart()), TodoListPO::getBizCode, query.getBizCodeStart())
                .eq(TodoListPO::getTodoUserId, query.getCurrentUserId())
                .eq(TodoListPO::getIsDeleted, 0)
                .orderByDesc(TodoListPO::getCreatedTime);

        Page<TodoListPO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        IPage<TodoListPO> todoPage = todoListMapper.selectPage(page, queryWrapper);

        List<TodoListPO> pageList = todoPage.getRecords();
        List<TodoListResDTO> resultList = new ArrayList<>();
        pageList.forEach(e -> {
            TodoListResDTO resDTO = BeanUtil.toBean(e, TodoListResDTO.class);
            resDTO.setTodoStatusText(TodoStatusEnum.getDescByCode(e.getTodoStatus()));
            resDTO.setBizCodeText(TodoBizCodeEnum.getDescByCode(e.getBizCode()));
            resDTO.setOrganizationTypeText(TodoOrganizationTypeEnum.getDescByCode(e.getOrganizationType()));
            resultList.add(resDTO);
        });
        PageResponse<TodoListResDTO> result = PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) todoPage.getTotal(),
                resultList);

        return Result.ok(result);
    }

    @Override
    public Long getNums(String userId, TodoStatusEnum todoStatusEnum) {
        LambdaQueryWrapper<TodoListPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TodoListPO::getTodoStatus, todoStatusEnum.getCode())
                .eq(TodoListPO::getIsDeleted, 0)
                .eq(TodoListPO::getTodoUserId, userId);
        return Long.valueOf(todoListMapper.selectCount(queryWrapper));
    }

    @Override
    public Result<String> updateStatus(TodoListUpdateReqDTO updateReqDTO) {
        boolean bool = todoListMapperService.lambdaUpdate()
                .eq(TodoListPO::getBizNo, updateReqDTO.getBizNo())
                .eq(TodoListPO::getBizCode, updateReqDTO.getBizCode())
                .set(TodoListPO::getTodoStatus, updateReqDTO.getTodoStatus())
                .set(TodoListPO::getUpdatedBy, updateReqDTO.getUpdatedBy())
                .set(TodoListPO::getUpdatedName, updateReqDTO.getUpdatedName())
                .set(TodoListPO::getDoneTime, LocalDateTime.now())
                .set(TodoListPO::getUpdatedTime, LocalDateTime.now())
                .update();
        if (bool) {
            return Result.ok("更新成功");
        } else {
            return Result.fail("更新失败");
        }
    }

    @Override
    public Result<String> batchUpdateStatus(List<TodoListUpdateReqDTO> updateReqDTOList) {
        updateReqDTOList.forEach(this::updateStatus);
        return Result.ok("更新成功");
    }

    @Override
    public TodoListNumResDTO getTodoListNumResDTO(String userId) {
        return new TodoListNumResDTO(getNums(userId, TodoStatusEnum.DONE), getNums(userId, TodoStatusEnum.TODO));
    }

    @Override
    public List<TodoListResDTO> listTodoByBizCodeAndLikeBizNo(String bizCode, String likeBizNo) {
        List<TodoListPO> todoListPOS = todoListMapperService.listTodoByBizCodeAndLikeBizNo(bizCode, likeBizNo);
        return BeanUtil.copyToList(todoListPOS, TodoListResDTO.class);
    }

    @Override
    public Result<Void> completeByBizNo(TodoListCompleteByBizNoReqDTO reqDTO) {
        todoListMapperService.lambdaUpdate()
                .eq(TodoListPO::getTodoStatus, TodoStatusEnum.TODO.getCode())
                .eq(TodoListPO::getBizNo, reqDTO.getBizNo())
                .set(TodoListPO::getTodoStatus, TodoStatusEnum.DONE.getCode())
                .set(TodoListPO::getDoneTime, LocalDateTime.now())
                .set(TodoListPO::getUpdatedBy, reqDTO.getUpdatedBy())
                .set(TodoListPO::getUpdatedName, reqDTO.getUpdatedName())
                .set(TodoListPO::getUpdatedTime, LocalDateTime.now())
                .update();
        return Result.ok();
    }

    @Override
    public Result<String> changeOwner(TodoListChangeOwnerReqDTO reqDTO) {
        return todoListChangeOwnerBiz.changeOwner(reqDTO);
    }

    @Override
    public Result<List<TodoListResDTO>> showPopupList(String userId) {
        LambdaQueryWrapper<TodoListPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TodoListPO::getTodoUserId,userId);
        queryWrapper.eq(TodoListPO::getPopupStatus, TodoPopupStatusEnum.NOT_POPUP.getCode());
        queryWrapper.eq(TodoListPO::getTodoStatus,TodoStatusEnum.TODO.getCode());
        queryWrapper.eq(TodoListPO::getIsDeleted,0);
        queryWrapper.orderByDesc(TodoListPO::getCreatedTime);
        List<TodoListPO> todoListPO= todoListMapper.selectList(queryWrapper);
        List<TodoListResDTO> resultList = new ArrayList<>();
        todoListPO.forEach(e -> {
            TodoListResDTO resDTO = BeanUtil.toBean(e, TodoListResDTO.class);
            resDTO.setTodoStatusText(TodoStatusEnum.getDescByCode(e.getTodoStatus()));
            resDTO.setBizCodeText(TodoBizCodeEnum.getDescByCode(e.getBizCode()));
            resDTO.setOrganizationTypeText(TodoOrganizationTypeEnum.getDescByCode(e.getOrganizationType()));
            resultList.add(resDTO);
        });
        return Result.ok(resultList);
    }

    @Override
    public Result<String> updatePopup(TodoPopupReqDTO reqDTO) {
        boolean bool = todoListMapperService.lambdaUpdate()
                .eq(TodoListPO::getTodoUserId, reqDTO.getUserId())
                .eq(TodoListPO::getTodoId, reqDTO.getTodoId())
                .set(TodoListPO::getPopupStatus,TodoPopupStatusEnum.HAS_POPUP.getCode())
                .update();
        if (bool) {
            return Result.ok("更新成功");
        } else {
            return Result.fail("更新失败");
        }
    }


}
