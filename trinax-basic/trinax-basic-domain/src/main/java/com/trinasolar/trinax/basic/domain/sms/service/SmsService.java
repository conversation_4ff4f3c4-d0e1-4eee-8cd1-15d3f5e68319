package com.trinasolar.trinax.basic.domain.sms.service;

import com.trinasolar.trinax.basic.domain.sms.manager.SmsSendManager;
import com.trinasolar.trinax.basic.dto.input.TrinaSolarSmsReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 13/09/2023
 * @description
 */
@Service
@Slf4j
public class SmsService {

    @Autowired
    private SmsSendManager smsSendManager;

    /***
     * TrinaSolar发送短信
     * @param reqDTO
     */
    public Result<Boolean> trinaSolarSend(TrinaSolarSmsReqDTO reqDTO) {
        return smsSendManager.trinaSmsSend(reqDTO);
    }

}