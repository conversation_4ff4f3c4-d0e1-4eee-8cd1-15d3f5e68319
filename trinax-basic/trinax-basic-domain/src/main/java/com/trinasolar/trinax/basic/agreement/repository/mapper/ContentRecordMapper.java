package com.trinasolar.trinax.basic.agreement.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.basic.agreement.repository.po.ContentRecordPO;
import com.trinasolar.trinax.basic.dto.output.ContentUserDto;
import com.trinasolar.trinax.basic.dto.output.content.AgreementAdminPageRespDTO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Mapper
public interface ContentRecordMapper extends BaseMapper<ContentRecordPO> {

    List<ContentUserDto> getContentIds(@Param("userId") Long userId);

    List<AgreementAdminPageRespDTO> popNotification(@Param("contentIds") List<Integer> contentIds);

    @Delete("delete from trinax_basic.content_record where type='BULLETIN'")

    void contentRecordDeal();

}
