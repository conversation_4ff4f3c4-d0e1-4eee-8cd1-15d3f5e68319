package com.trinasolar.trinax.basic.email.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.basic.email.repository.atomicservice.EmailRecordMapperService;
import com.trinasolar.trinax.basic.email.repository.mapper.EmailRecordMapper;
import com.trinasolar.trinax.basic.email.repository.po.EmailRecordPO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邮件发送记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-09
 */
@Service
public class EmailRecordMapperServiceImpl extends ServiceImpl<EmailRecordMapper, EmailRecordPO> implements EmailRecordMapperService {

}
