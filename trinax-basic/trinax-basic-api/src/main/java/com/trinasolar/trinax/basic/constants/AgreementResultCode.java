package com.trinasolar.trinax.basic.constants;

import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.dto.output.ResultCodeArtist;

public class AgreementResultCode extends ResultCode {
    public static ResultCodeArtist AGREEMENT_ALREADY_EXIST = new ResultCodeArtist(false, "AGREEMENT_ALREADY_EXIST", "相同协议已经存在");

    public static ResultCodeArtist EXPIRED_TIME_ERROR = new ResultCodeArtist(false, "EXPIRED_TIME_ERROR", "失效时间必须大于当前时间");

    public static ResultCodeArtist EXPIRED_TIME_BLANK = new ResultCodeArtist(false, "EXPIRED_TIME_BLANK", "生效时间和失效时间不为空");

    public static ResultCodeArtist VALID_TIME_ERROR = new ResultCodeArtist(false, "VALID_TIME_ERROR", "失效时间必须大于生效时间");

    public static ResultCodeArtist AGREEMENT_IS_EXIST = new ResultCodeArtist(false, "AGREEMENT_IS_EXIST", "公告已读");

    @Override
    protected void setValueMap() {
        valueMap.put(AGREEMENT_ALREADY_EXIST.getCode(), AGREEMENT_ALREADY_EXIST);
    }
}
