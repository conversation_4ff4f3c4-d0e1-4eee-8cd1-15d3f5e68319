package com.trinasolar.trinax.basic.event;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * opType枚举，用于控制消息的显示图标，目前按（意向单，发货申请，合同，产品）区分图标；该字段未使用，暂留
 */
@Getter
public enum MessageOpTypeEnum {

//    MSG_OP_TYPE_CANCEL("CANCEL", "取消"),
//    MSG_OP_TYPE_CREATE("CREATE", "创建"),
//    MSG_OP_TYPE_CONFIRM("CONFIRM", "确认"),
//    MSG_OP_TYPE_UPDATE("UPDATE", "更新"),

    ;

    private String value;
    private String displayName;

    MessageOpTypeEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private static Map<String, MessageOpTypeEnum> valueMap = new HashMap<String, MessageOpTypeEnum>();

    static {
        for (MessageOpTypeEnum e : MessageOpTypeEnum.values()) {
            valueMap.put(e.value, e);
        }
    }

    public static MessageOpTypeEnum get(String value) {
        return valueMap.get(value);
    }
}
