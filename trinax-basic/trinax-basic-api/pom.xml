<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trinax-basic-root</artifactId>
        <version>0.0.4-SNAPSHOT</version>
    </parent>

    <packaging>jar</packaging>
    <artifactId>trinax-basic-api</artifactId>
    <name>trinax-basic-api</name>
    <properties>
        <java.version>17</java.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.trinasolar</groupId>
            <artifactId>trinax-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>atb-ecp-nexus</id>
            <name>atb-ecp-nexus</name>
            <url>http://10.173.61.218:8081/repository/ecp-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>atb-ecp-release</id>
            <name>Releases</name>
            <url>http://10.173.61.218:8081/repository/ecp-releases/</url>
        </repository>
        <snapshotRepository>
            <id>atb-ecp-snapshot</id>
            <name>Snapshot</name>
            <url>http://10.173.61.218:8081/repository/ecp-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
