/*
 Navicat Premium Dump SQL

 Source Server         : *************_3306
 Source Server Type    : MySQL
 Source Server Version : 80027 (8.0.27)
 Source Host           : *************:3306
 Source Schema         : trinax_report

 Target Server Type    : MySQL
 Target Server Version : 80027 (8.0.27)
 File Encoding         : 65001

 Date: 25/06/2025 11:05:54
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ads_zzb_outbound_df
-- ----------------------------
DROP TABLE IF EXISTS `ads_zzb_outbound_df`;
CREATE TABLE `ads_zzb_outbound_df`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '主键',
  `pi` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '合同号',
  `customer_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户姓名',
  `double_sign_date` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '双签时间',
  `sales_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '销售人员工号',
  `sales_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '销售人员',
  `operations_number` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运营人员工号',
  `operations_person` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '运营人员',
  `pi_total_power` bigint NULL DEFAULT NULL COMMENT '合同总量(W)',
  `outbound_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '出库时间（天维度）',
  `outbound_power` bigint NULL DEFAULT NULL COMMENT '已发量(W)',
  `department` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门',
  `etl_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'etl时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '至尊宝出库明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ads_zzb_outbound_df
-- ----------------------------
INSERT INTO `ads_zzb_outbound_df` VALUES ('OB001', 'test_sf_contract_no_1', '常州丽曜光电科技有限公司', '2025-06-19', '700001', '刘佳', '700010', '李庆', 595, '2025-06-19', 595, 'TRINA_CN_SALES_HUABEI', '2025-06-19 10:00:00');

-- ----------------------------
-- Table structure for behavior_log
-- ----------------------------
DROP TABLE IF EXISTS `behavior_log`;
CREATE TABLE `behavior_log`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `behavior` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为',
  `behavior_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为编码',
  `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '日志来源（APP、PC）',
  `biz_no` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据单号',
  `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据类型',
  `biz_type_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型编码',
  `biz_req` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '业务数据',
  `op_time` datetime NOT NULL COMMENT '操作时间',
  `op_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作URL',
  `op_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作Method',
  `op_result` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志状态（成功、失败、异常）',
  `op_exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '日志异常',
  `op_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人ID',
  `op_user_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `op_user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人类型',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 350 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of behavior_log
-- ----------------------------
INSERT INTO `behavior_log` VALUES (1, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506130001\",\"roleIds\":[\"R010002\",\"R001001\",\"R000501\",\"reportRole\",\"R000002\",\"R001100\",\"R001600\",\"R001500\",\"R001400\",\"R001300\",\"R001200\",\"R000001\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-13 14:18:22', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (2, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506130002\",\"roleIds\":[\"R010002\",\"R001001\",\"R000501\",\"reportRole\",\"R000002\",\"R001100\",\"R001600\",\"R001500\",\"R001400\",\"R001300\",\"R001200\",\"R000001\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-13 14:18:07', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (3, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer c8454f5c-8f37-420d-9cb5-eeb1dfe69645\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1749795716887\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:58530\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-13 14:21:57', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (4, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506130001\",\"roleIds\":[\"R010002\",\"R001001\",\"R000501\",\"reportRole\",\"R000002\",\"R001100\",\"R001600\",\"R001500\",\"R001400\",\"R001300\",\"R001200\",\"R000001\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-13 14:16:48', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (5, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 4e0f2d98-1b9d-4706-b4dc-07ac2e8875e5\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1749787390589\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:63469\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-13 12:03:10', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (6, NULL, NULL, 'PC', 'IU202506130002', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506130002\",\"parentUserId\":\"\",\"originParentUserId\":null,\"userName\":\"LL\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"\",\"mobile\":\"***********\",\"salesRelations\":[],\"organizationCode\":\"TRINA\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"LL\"}', '2025-06-13 14:57:54', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (7, NULL, NULL, 'PC', 'TRINA', NULL, NULL, '{\"organizationCode\":\"TRINA\",\"organizationName\":\"Deloitte\",\"organizationType\":\"COMMON\",\"organizationLevel\":1,\"description\":null,\"parentOrganizationCode\":null,\"areaCodes\":[],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-13 14:21:26', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-13 14:59:02', NULL, NULL, '2025-06-13 14:59:02');
INSERT INTO `behavior_log` VALUES (8, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer ac51c2bf-8c79-443e-8833-e180cd4f7606\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750056411040\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:59006\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 14:46:52', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 14:46:55', NULL, NULL, '2025-06-16 14:46:55');
INSERT INTO `behavior_log` VALUES (9, NULL, NULL, 'APP', '18758588073', NULL, NULL, '{\"phone\":\"18758588073\",\"userType\":\"EXTERNAL\",\"smsCode\":\"964728\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750056882772\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:60578\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 14:54:42', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160002', NULL, 'EXTERNAL', 0, NULL, NULL, '2025-06-16 14:54:47', NULL, NULL, '2025-06-16 14:54:47');
INSERT INTO `behavior_log` VALUES (10, NULL, NULL, 'PC', 'huang', NULL, NULL, '{\"userName\":\"huang\",\"userProvinceCode\":\"50\",\"userProvinceName\":\"重庆\",\"userCityCode\":\"500100\",\"userCityName\":\"重庆市\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-16 14:58:31', '/user/updatePartnerUserInfo', 'POST', 'SUCCESS', NULL, 'EU202506160002', 'huang', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 14:58:34', NULL, NULL, '2025-06-16 14:58:34');
INSERT INTO `behavior_log` VALUES (11, NULL, NULL, 'PC', '18758588073', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 679ccafe-b96d-420b-bf45-01d9f3c436a6\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057143501\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:61591\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 14:59:03', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160002', 'huang', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 14:59:04', NULL, NULL, '2025-06-16 14:59:04');
INSERT INTO `behavior_log` VALUES (12, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"468043\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057181757\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:61591\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 14:59:41', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 14:59:43', NULL, NULL, '2025-06-16 14:59:43');
INSERT INTO `behavior_log` VALUES (13, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 3d505111-3217-475d-9a6a-cc896ba765e2\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057420946\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62360\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:03:41', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:03:42', NULL, NULL, '2025-06-16 15:03:42');
INSERT INTO `behavior_log` VALUES (14, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160001\",\"roleIds\":[\"R001501\",\"R002200\",\"R002100\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-16 15:04:09', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:04:10', NULL, NULL, '2025-06-16 15:04:10');
INSERT INTO `behavior_log` VALUES (15, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160002\",\"roleIds\":[\"R001501\",\"R002200\",\"R002100\",\"externalRole\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-16 15:04:17', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:04:19', NULL, NULL, '2025-06-16 15:04:19');
INSERT INTO `behavior_log` VALUES (16, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 53d7ca26-f9d6-407a-9b2d-4a108998f83f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057496803\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62412\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:04:57', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:04:58', NULL, NULL, '2025-06-16 15:04:58');
INSERT INTO `behavior_log` VALUES (17, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"468043\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057503471\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62412\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 15:05:03', '/login/sms/token', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-16 15:05:04', NULL, NULL, '2025-06-16 15:05:04');
INSERT INTO `behavior_log` VALUES (18, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"604061\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057550476\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62360\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 15:05:50', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:05:52', NULL, NULL, '2025-06-16 15:05:52');
INSERT INTO `behavior_log` VALUES (19, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 26c2d0a8-611d-4afe-be95-c27c3c9addf0\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057676508\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62813\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:07:56', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:07:57', NULL, NULL, '2025-06-16 15:07:57');
INSERT INTO `behavior_log` VALUES (20, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 45d3737b-028b-4342-aef8-5e4159865f37\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057741354\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:59388\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:09:02', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:09:03', NULL, NULL, '2025-06-16 15:09:03');
INSERT INTO `behavior_log` VALUES (21, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 03579e57-e46f-40ac-a0a5-80924d3ad7da\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057742896\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62815\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:09:03', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:09:05', NULL, NULL, '2025-06-16 15:09:05');
INSERT INTO `behavior_log` VALUES (22, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer e82433fc-0eb3-4b5c-b8b5-3e30834a7f93\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057832022\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:59388\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:10:32', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:10:33', NULL, NULL, '2025-06-16 15:10:33');
INSERT INTO `behavior_log` VALUES (23, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"158699\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057914434\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:59388\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 15:11:54', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:11:55', NULL, NULL, '2025-06-16 15:11:55');
INSERT INTO `behavior_log` VALUES (24, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 3303b1b2-4032-4f16-bc0a-0f98d0da8387\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750057954275\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:59380\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:12:34', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:12:35', NULL, NULL, '2025-06-16 15:12:35');
INSERT INTO `behavior_log` VALUES (25, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160003\",\"roleIds\":[\"R001501\",\"R002200\",\"R002100\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-16 15:18:07', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:18:09', NULL, NULL, '2025-06-16 15:18:09');
INSERT INTO `behavior_log` VALUES (26, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 2656e1fa-3da8-48e0-81c9-9948e78e998b\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750058293873\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:51240\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:18:14', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:18:15', NULL, NULL, '2025-06-16 15:18:15');
INSERT INTO `behavior_log` VALUES (27, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer c2e7871b-5488-410c-a12d-83ef7e424d33\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750058468522\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:51508\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 15:21:08', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-16 15:21:09', NULL, NULL, '2025-06-16 15:21:09');
INSERT INTO `behavior_log` VALUES (28, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750058476079\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:51508\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 15:21:16', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-16 15:21:16', NULL, NULL, '2025-06-16 15:21:16');
INSERT INTO `behavior_log` VALUES (29, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"986872\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750058985598\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:52090\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 15:29:45', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:29:47', NULL, NULL, '2025-06-16 15:29:47');
INSERT INTO `behavior_log` VALUES (30, NULL, NULL, 'PC', 'OPP-20250616-000001', NULL, NULL, '{\"itemList\":[],\"enterpriseId\":\"1\",\"enterpriseName\":\"客户1\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"Residential\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Automobile|New energy vehicle\",\"industryAttributesText\":\"汽车|新能源汽车\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"luolan\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-16 15:30:33', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 15:30:36', NULL, NULL, '2025-06-16 15:30:36');
INSERT INTO `behavior_log` VALUES (31, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer a8ba972d-8fcf-4206-9bad-2c01a308059b\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750062404260\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:59750\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 16:26:46', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-16 16:26:48', NULL, NULL, '2025-06-16 16:26:48');
INSERT INTO `behavior_log` VALUES (32, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"900020\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750062521111\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:60210\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-16 16:28:41', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 16:28:43', NULL, NULL, '2025-06-16 16:28:43');
INSERT INTO `behavior_log` VALUES (33, NULL, NULL, 'PC', 'OPP-20250616-000002', NULL, NULL, '{\"itemList\":[],\"enterpriseId\":\"1\",\"enterpriseName\":\"客户1\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"Residential\",\"goodsArrivalRegion\":null,\"goodsArrivalSubRegion\":null,\"area\":null,\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":null,\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"\",\"industryAttributesText\":\"\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-16 16:34:10', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 16:34:11', NULL, NULL, '2025-06-16 16:34:11');
INSERT INTO `behavior_log` VALUES (34, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer b705aff6-5670-49f8-aa75-85db0a702a85\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750064901525\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:58054\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-16 17:08:21', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 17:08:22', NULL, NULL, '2025-06-16 17:08:22');
INSERT INTO `behavior_log` VALUES (35, NULL, NULL, 'PC', 'OPP-20250616-000002', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250616-000002\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-16 17:53:37', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-16 17:53:37', NULL, NULL, '2025-06-16 17:53:37');
INSERT INTO `behavior_log` VALUES (36, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"237543\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750126227838\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:57974\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 10:10:27', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 10:10:32', NULL, NULL, '2025-06-17 10:10:32');
INSERT INTO `behavior_log` VALUES (37, NULL, NULL, 'PC', 'AD20250617000002', NULL, NULL, '{\"enterpriseId\":\"1\",\"recipientName\":\"yoyo\",\"recipientPhone\":\"18976542345\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"江北区石马河街道江山云著\",\"addressType\":\"RECEIVE\",\"isDefault\":0,\"loginUserId\":\"EU202506160003\",\"loginUserName\":\"luolan\"}', '2025-06-17 14:40:34', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (38, NULL, NULL, 'PC', 'OPP-20250617-000002', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250617-000002\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-17 15:08:14', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (39, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"765837\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750138905883\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:50344\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 13:41:45', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (40, NULL, NULL, 'PC', 'AD20250617000004', NULL, NULL, '{\"enterpriseId\":\"2\",\"recipientName\":\"abc\",\"recipientPhone\":\"17693847654\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"重庆市渝中区化龙桥企业天地\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506160001\",\"loginUserName\":\"test20250616\"}', '2025-06-17 16:36:55', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (41, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 18621f4b-ffe9-4920-ad14-67a6acbfcf31\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750143861678\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:65073\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 15:04:22', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (42, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 4f7c86c8-8623-41de-add6-72ac7d276e92\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139247730\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:54982\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:47:27', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (43, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 1c56ff48-5704-4902-8d5e-cf8ceea16ccf\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139801325\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:55930\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:56:41', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (44, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7\"],\"requesttime\":[\"1750148588737\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:56947\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 16:23:08', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (45, NULL, NULL, 'PC', 'OPP-20250617-000002', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"220\",\"quantityP\":\"11\",\"quantityW\":0,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":null,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":null,\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":null,\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"Residential\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-21\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Mineral exploitation\",\"industryAttributesText\":\"能源/化工/环保|矿产开采\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-17 15:08:06', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (46, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer e887cca9-e214-407f-84c3-7718196837d9\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139921034\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:56631\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:58:41', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (47, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145191345\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:53518\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:26:31', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (48, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"11\",\"installProvinceName\":\"北京\",\"installCityCode\":\"110100\",\"installCityName\":\"北京市\",\"quantityP\":1,\"quantityW\":\"220\",\"requireOrderDescription\":\"啊啊啊啊啊啊否定的否定刚刚好\",\"opportunitySegment\":\"Residential\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-17 14:04:14', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (49, NULL, NULL, 'PC', 'CON-20250617-000004', NULL, NULL, '{\"id\":\"2\",\"version\":\"1.3\",\"type\":\"AGREEMENT\",\"title\":\"Deloitte服务用户协议\",\"isEffect\":2,\"content\":\"<p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">本版本发布日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">生效日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">一、 总则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.1 Deloitte服务（以下简称本平台）的所有权归德勤华庆商务服务有限公司（注册地址：重庆市渝中区瑞天路10号企业天地8号楼；以下简称“德勤服务”或“我们”)。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.2 用户在使用之前，应当仔细阅读本协议，充分理解协议条款的法律后果，用户应当受本协议的约束。用户在使用服务或产品时，应当同意接受本协议及其他政策后方能使用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.3 本协议将由Deloitte服务根据实际情况以公告方式随时更新，用户应当及时关注，如不同意变更后内容可书面提交申请，以与德勤服务解除协议关系，如在公告期内不予答复，即视为同意按照协议变更内容履行。用户同意本平台不承担一对一的通知义务。本平台的通知、公告、声明或其它类似内容是本协议的一部分。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">二、 用户帐号</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">2.1 用户有义务保证帐号的安全，用户利用该帐号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本平台不承担任何责任。如用户发现帐号遭到未授权的使用或发生其他任何安全问题，应立即修改帐号并妥善保管，如有必要，请通知本平台。因黑客行为或用户的保管疏忽导致帐号非法使用，本平台不承担任何责任，但是平台可协助用户处理。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">三、 使用规则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.1 遵守中华人民共和国相关法律法规，包括但不限于《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》、《最高人民法院关于审理涉及计算机网络著作权纠纷案件适用法律若干问题的解释(法释[2006]11号)》、《全国人大常委会关于维护互联网安全的决定》、《互联网新闻信息服务管理规定》、《互联网著作权行政保护办法》和《信息网络传播权保护条例》等有关计算机互联网规定和知识产权的法律和法规、实施办法。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.2 用户对其自行发表、上传或传送的内容负全部责任，所有用户不得在本平台任何页面发布、转载、传送含有下列内容之一的信息，否则本平台有权自行处理并不通知用户：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）违反宪法确定的基本原则的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）危害国家安全，泄漏国家机密，颠覆国家政权，破坏国家统一的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）损害国家荣誉和利益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）煽动民族仇恨、民族歧视，破坏民族团结的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（5）破坏国家宗教政策，宣扬邪教和封建迷信的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（6）散布谣言，扰乱社会秩序，破坏社会稳定的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（7）散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（8）侮辱或者诽谤他人，侵害他人合法权益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（9）煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（10）以非法民间组织名义活动的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（11）含有法律、行政法规禁止的其他内容的。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.3 用户承诺对其发表或者上传于本平台的所有信息(即包括但不限于《中华人民共和国著作权法》规定的作品，包括但不限于文字、图片、音乐、电影、表演和录音录像制品和电脑程序等；《中华人民共和国商标法》规定的商标、标识等；《中华人民共和国专利法》规定的专利等；《中华人民共和国反不正当竞争法》规定的商业秘密信息等)均享有完整的知识产权，或者已经得到相关权利人的合法授权；如用户违反本条规定造成本平台被第三人索赔的，用户应全额补偿本平台一切费用(包括但不限于各种赔偿费、诉讼代理费及为此支出的其它合理费用）；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.4 当第三方认为用户发表或者上传于本平台的信息侵犯其权利，并根据《信息网络传播权保护条例》或者相关法律规定向本平台发送权利通知书时，用户同意本平台可以自行判断决定删除涉嫌侵权信息，除非用户提交书面证据材料排除侵权的可能性，本平台将不会自动恢复上述删除的信息；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.5 如用户在使用网络服务时违反下列任何规定，本平台有权要求用户改正或直接采取一切必要的措施(包括但不限于删除用户张贴的内容、暂停或终止用户使用网络服务的权利）以减轻用户不当行为而造成的影响：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）不得为任何非法目的而使用网络服务系统；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）遵守所有与网络服务有关的网络协议、规定和程序；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）不得利用本平台进行任何可能对互联网的正常运转造成不利影响的行为；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）不得利用本平台进行任何不利于本平台的行为。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">四、隐私保护</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.1 本平台不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本平台的非公开内容，但下列情况除外：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）事先获得用户的明确授权；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）根据有关的法律法规要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）按照相关政府主管部门的要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）为维护社会公众的利益。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.2 在不透露单个用户隐私资料的前提下，本平台有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.3 在用户与本平台终止合作，解除协议关系后，或者用户提出删除需求时，在不影响本平台正常运作情况下，本平台将删除该用户的隐私资料。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.4 本平台具有全面的技术保护措施和安全维护机制来保证用户信息的内容安全，但由于不可抗力或者因计算机病毒感染、黑客攻击等特殊外力侵扰，导致用户信息破坏、泄密并受到损失的，本平台不承担任何法律责任，本平台将最大努力保护用户信息，以及出现风险后及时做补救措施。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">五、责任声明</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">5.1 用户明确同意其使用本平台网络服务所存在的风险及一切后果将完全由用户本人承担，Deloitte服务对此不承担任何责任，如客户需本平台可协助用户处理相关问题。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.2 本平台无法保证网络服务一定能满足用户的要求，也不保证网络服务的及时性、安全性、准确性，本平台将尽最大力量保障系统稳定和信息安全。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.3 本平台不保证为方便用户而设置的外部链接的准确性和完整性，同时，对于该等外部链接指向的不由本平台实际控制的任何网页上的内容，本平台不承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.4 对于因不可抗力或本平台不能控制的原因造成的网络服务中断或其它缺陷，本平台不承担任何责任，但将尽力减少因此而给用户造成的损失和影响。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.5 本平台有权于任何时间暂时或永久修改或终止本服务(或其任何部分)，而无论其通知与否，本平台对用户和任何第三人均无需承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;六、附则 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.2 如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.3 本协议解释权及修订权归德勤华庆商务服务有限公司所有。</span></p>\",\"updatedBy\":null,\"updatedName\":null,\"createdName\":\"184运营\",\"createdBy\":\"IU202401010002\",\"fileIdList\":null,\"effectSj\":null,\"outSj\":null,\"areaScopes\":null}', '2025-06-17 11:28:41', '/agreement/add', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (50, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145197880\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:53518\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:26:37', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (51, NULL, NULL, 'PC', 'CON-20250617-000002', NULL, NULL, '{\"id\":null,\"version\":\"1.2\",\"type\":\"AGREEMENT\",\"title\":\"Deloitte服务用户协议\",\"isEffect\":2,\"content\":\"<p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">本版本发布日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">生效日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">一、 总则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.1 Deloitte服务（以下简称本平台）的所有权归德勤华庆商务服务有限公司（注册地址：常州市新北区天合光伏产业园天合路2号；以下简称“天合光能”或“我们”)。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.2 用户在使用之前，应当仔细阅读本协议，充分理解协议条款的法律后果，用户应当受本协议的约束。用户在使用服务或产品时，应当同意接受本协议及其他政策后方能使用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.3 本协议将由至尊宝APP根据实际情况以公告方式随时更新，用户应当及时关注，如不同意变更后内容可书面提交申请，以与天合光能解除协议关系，如在公告期内不予答复，即视为同意按照协议变更内容履行。用户同意本平台不承担一对一的通知义务。本平台的通知、公告、声明或其它类似内容是本协议的一部分。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">二、 用户帐号</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">2.1 用户有义务保证帐号的安全，用户利用该帐号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本平台不承担任何责任。如用户发现帐号遭到未授权的使用或发生其他任何安全问题，应立即修改帐号并妥善保管，如有必要，请通知本平台。因黑客行为或用户的保管疏忽导致帐号非法使用，本平台不承担任何责任，但是平台可协助用户处理。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">三、 使用规则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.1 遵守中华人民共和国相关法律法规，包括但不限于《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》、《最高人民法院关于审理涉及计算机网络著作权纠纷案件适用法律若干问题的解释(法释[2006]11号)》、《全国人大常委会关于维护互联网安全的决定》、《互联网新闻信息服务管理规定》、《互联网著作权行政保护办法》和《信息网络传播权保护条例》等有关计算机互联网规定和知识产权的法律和法规、实施办法。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.2 用户对其自行发表、上传或传送的内容负全部责任，所有用户不得在本平台任何页面发布、转载、传送含有下列内容之一的信息，否则本平台有权自行处理并不通知用户：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）违反宪法确定的基本原则的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）危害国家安全，泄漏国家机密，颠覆国家政权，破坏国家统一的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）损害国家荣誉和利益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）煽动民族仇恨、民族歧视，破坏民族团结的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（5）破坏国家宗教政策，宣扬邪教和封建迷信的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（6）散布谣言，扰乱社会秩序，破坏社会稳定的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（7）散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（8）侮辱或者诽谤他人，侵害他人合法权益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（9）煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（10）以非法民间组织名义活动的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（11）含有法律、行政法规禁止的其他内容的。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.3 用户承诺对其发表或者上传于本平台的所有信息(即包括但不限于《中华人民共和国著作权法》规定的作品，包括但不限于文字、图片、音乐、电影、表演和录音录像制品和电脑程序等；《中华人民共和国商标法》规定的商标、标识等；《中华人民共和国专利法》规定的专利等；《中华人民共和国反不正当竞争法》规定的商业秘密信息等)均享有完整的知识产权，或者已经得到相关权利人的合法授权；如用户违反本条规定造成本平台被第三人索赔的，用户应全额补偿本平台一切费用(包括但不限于各种赔偿费、诉讼代理费及为此支出的其它合理费用）；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.4 当第三方认为用户发表或者上传于本平台的信息侵犯其权利，并根据《信息网络传播权保护条例》或者相关法律规定向本平台发送权利通知书时，用户同意本平台可以自行判断决定删除涉嫌侵权信息，除非用户提交书面证据材料排除侵权的可能性，本平台将不会自动恢复上述删除的信息；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.5 如用户在使用网络服务时违反下列任何规定，本平台有权要求用户改正或直接采取一切必要的措施(包括但不限于删除用户张贴的内容、暂停或终止用户使用网络服务的权利）以减轻用户不当行为而造成的影响：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）不得为任何非法目的而使用网络服务系统；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）遵守所有与网络服务有关的网络协议、规定和程序；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）不得利用本平台进行任何可能对互联网的正常运转造成不利影响的行为；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）不得利用本平台进行任何不利于本平台的行为。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">四、隐私保护</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.1 本平台不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本平台的非公开内容，但下列情况除外：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）事先获得用户的明确授权；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）根据有关的法律法规要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）按照相关政府主管部门的要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）为维护社会公众的利益。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.2 在不透露单个用户隐私资料的前提下，本平台有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.3 在用户与本平台终止合作，解除协议关系后，或者用户提出删除需求时，在不影响本平台正常运作情况下，本平台将删除该用户的隐私资料。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.4 本平台具有全面的技术保护措施和安全维护机制来保证用户信息的内容安全，但由于不可抗力或者因计算机病毒感染、黑客攻击等特殊外力侵扰，导致用户信息破坏、泄密并受到损失的，本平台不承担任何法律责任，本平台将最大努力保护用户信息，以及出现风险后及时做补救措施。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">五、责任声明</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">5.1 用户明确同意其使用本平台网络服务所存在的风险及一切后果将完全由用户本人承担，至尊宝APP对此不承担任何责任，如客户需本平台可协助用户处理相关问题。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.2 本平台无法保证网络服务一定能满足用户的要求，也不保证网络服务的及时性、安全性、准确性，本平台将尽最大力量保障系统稳定和信息安全。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.3 本平台不保证为方便用户而设置的外部链接的准确性和完整性，同时，对于该等外部链接指向的不由本平台实际控制的任何网页上的内容，本平台不承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.4 对于因不可抗力或本平台不能控制的原因造成的网络服务中断或其它缺陷，本平台不承担任何责任，但将尽力减少因此而给用户造成的损失和影响。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.5 本平台有权于任何时间暂时或永久修改或终止本服务(或其任何部分)，而无论其通知与否，本平台对用户和任何第三人均无需承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;六、附则 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.2 如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.3 本协议解释权及修订权归天合光能股份有限公司所有。</span></p>\",\"updatedBy\":null,\"updatedName\":null,\"createdName\":\"184运营\",\"createdBy\":\"IU202401010002\",\"fileIdList\":null,\"effectSj\":null,\"outSj\":null,\"areaScopes\":null}', '2025-06-17 11:23:22', '/agreement/add', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (52, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 3c14f824-b795-43d9-aae1-51589d98cee9\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139718812\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:55930\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:55:19', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (53, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer d8cc7fe5-1300-413a-ad89-8433ef474532\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139351463\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:55377\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:49:11', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (54, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"220\",\"requireOrderDescription\":\"啊啊啊啊啊啊否定的否定刚刚好\",\"opportunitySegment\":\"Residential\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-17 14:04:04', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (55, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"164104\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750138296281\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:64871\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 13:31:36', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (56, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"613732\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750143923321\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:65072\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:05:23', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:20', NULL, NULL, '2025-06-17 16:53:20');
INSERT INTO `behavior_log` VALUES (57, NULL, NULL, 'PC', 'OPP-20250617-000001', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"2\",\"productName\":\"组件-产品2\",\"power\":\"\",\"quantityP\":\"0\",\"quantityW\":0,\"guidePrice\":2,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":0,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":null,\"itemType\":\"PRODUCT\",\"productImgUrl\":null,\"productCategory\":null,\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":null,\"goodsArrivalRegion\":null,\"goodsArrivalSubRegion\":null,\"area\":null,\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":null,\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":null,\"industryAttributes\":\"\",\"industryAttributesText\":\"\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-17 10:58:39', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (58, NULL, NULL, 'PC', 'AD20250617000001', NULL, NULL, '{\"enterpriseId\":\"1\",\"recipientName\":\"yoyo\",\"recipientPhone\":\"18976542345\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"渝中区瑞天路企业天地\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506160003\",\"loginUserName\":\"luolan\"}', '2025-06-17 14:39:23', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (59, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"220\",\"requireOrderDescription\":\"啊啊啊啊啊啊否定的否定刚刚好\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-17 14:01:54', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (60, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer d306e0fe-3b79-4f6f-98e2-e856229f9599\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750139203888\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:54982\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:46:44', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (61, NULL, NULL, 'PC', 'CON-20250617-000001', NULL, NULL, '{\"id\":null,\"version\":\"1.1\",\"type\":\"POLICY\",\"title\":\"Deloitte服务系统用户隐私政策\",\"isEffect\":2,\"content\":\"<p style=\\\"text-align: center;\\\"><strong>Deloitte服务系统用户隐私政策</strong></p><p style=\\\"text-align: right;\\\"><span style=\\\"font-family: 宋体;\\\">本版本发布日期：2024年11月12日</span></p><p style=\\\"text-align: right;\\\"><span style=\\\"font-family: 宋体;\\\">生效日期：2024年11月12日</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">Deloitte服务系统是一款面向分销商下单的系统，分销商可以在线下单、合同在线签约、订单物流跟踪，及时了解最新产品咨询及活动公告，旨在帮助用户更快速便捷地获取订单进度，查询并推动业务进程，把握行业动向，实现全链路线上数字管理。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">尊敬的Deloitte服务系统用户（以下简称“您”），德勤华庆商务服务有限公司（注册地址：重庆市渝中区瑞天路10号企业天地八号楼；以下简称“德勤服务”或“我们”)非常尊重并保护您的隐私，也感谢您对我们的信任。您在使用德勤服务的运营和控制的网站或应用(以下简称 \\\"服务\\\")时，德勤服务将按照《Deloitte系统用户隐私政策》（以下简称“本政策”）收集、使用和披露您的个人信息。德勤服务将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。请您仔细阅读本政策并确认了解我行对您个人信息的处理规则。阅读过程中，如您有任何疑问，可联系我们 <EMAIL> 咨询。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在使用德勤服务的服务前，请您务必仔细阅读并透彻理解本政策，确认了解德勤服务对您个人信息的处理规则，在确认充分理解并同意后使用德勤服务产品或服务。在您使用德勤服务产品或服务之前，您就本政策点击或勾选“同意”并确认提交，即为您已充分理解并同意本政策。</span></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本政策将帮助您了解以下内容：</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>一、我们如何收集您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>二、我们如何使用Cookie和同类技术</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>三、我们如何使用您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>四、我们如何存储和保护您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>五、您的权利</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>六、第三方收集和使用您的个人信息情况</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>七、未成年人保护</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>八、本政策的修订</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>九、如何与德勤服务联系</strong></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本政策具体内容如下：</span></p><p style=\\\"text-indent: 20pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>一、我们如何收集您的个人信息</strong></span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为了实现特定业务功能，我们需要收集和使用您的部分个人信息。您有权拒绝我们收集和使用您的个人信息，法律、行政法规另有规定的除外。但请您理解，如您拒绝我们收集和使用业务功能所必须的个人信息，则可能无法使用对应的业务功能。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">我们的部分功能需要调用您的设备权限，我们会采用系统弹窗或应用弹窗的方式询问您是否授权。您可以随时在您设备的设置功能中选择开关相关权限。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在您使用德勤服务的产品和服务过程中，我们会在向您提供服务过程中使用您主动提供的，以及经过您的授权通过自动化手段收集的下列信息：</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>1、注册登录</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>当您使用Deloitte系统进行注册、登录时，我们需要收集您的个人信息，包括但不限于您的身份基本信息，包括姓名、手机号码、邮箱，以用于验证您的身份，和创建至尊宝账号。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>2、在线签约服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>在您使用Deloitte系统过程中，可能涉及到以电子方式签署合同性质的文件资料，电子签署服务由第三方服务商提供。为满足您合同在线签约的服务所需，届时您需要提供在线签约服务所需要的个人信息，基于您的同意，我们会将您的手机号，您所属的企业名称共享给第三方服务商。签署电子协议时，您需要提供姓名，手机号，有效证件号码，人脸生物识别信息(如面部识别特征等)，银行账号(部分场景使用)，以便于第三方服务商验证您的身份并签署电子协议。这些信息的收集和使用遵循第三方服务商的隐私协议， 具体内容请访问 https://www.qiyuesuo.com/appagreement。如果您与我们发生合同纠纷，我们会向第三方服务商申请调取您签约过程的相关数据。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>3、创建意向单服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您可以通过Deloitte系统下单相关产品。在下意向单过程中，我们会收集您的姓名、所在公司、关联方、联系电话。同时我们会通过系统为您生成采购产品的意向单，该意向单中载明您所下单的产品信息、意向单编号、意向单创建时间、购买方式、意向单金额。您可以通过Deloitte服务为其他人下意向单，您需要提供该实际下单人的前述个人信息，并确保已取得该实际下单人的同意。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>4、发货申请服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您可以通过Deloitte系统对已双签的合同进行发货申请，在创建发货申请过程中，我们会收集您进行发货申请的公司名称、关联方、收货人姓名、收货地址、收货人联系方式。我们会通过系统为您生成发货申请单，该发货申请单中会载明您进行申请的发货产品信息，发货申请编号，发货申请创建时间。您可以通过Deloitte系统为其他人创建发货申请，您需要提供该实际下单人的前述个人信息，并确保已取得该实际申请人的同意。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>5、</strong><span style=\\\"font-family: 宋体;\\\">当您使用我们的服务时，在某些特定使用场景下，可能会使用具有相应业务资质及能力的第三方服务商提供的软件服务工具包（简称“SDK”）来为您提供服务，此时第三方服务商需要收集您的必要信息。具体包括以下内容：</span></p><p style=\\\"text-align: left;\\\"><br></p><table style=\\\"width: auto; text-align: start;\\\"><tbody><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"58\\\">SDK 名称</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"73\\\">收集目的</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"115\\\">SDK供应商名称</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">涉及收集的个人信息字段</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">隐私政策/官网链接</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"58\\\">极光推送</td><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"73\\\">消息推送</td><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"115\\\">深圳市和讯华谷信息技术有限公司</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">联网信息，设备信息，手机号</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://www.jiguang.cn/license/privacy</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">安卓 ID</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://docs.jiguang.cn/jpush/client/Android/android_jghgzy</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"58\\\">微信分享</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"73\\\">支持微信分享</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"115\\\">深圳市腾讯计算机系统有限公司</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">设备标识符（Android如IMEI、Android ID、Serial）、MAC地址、WLAN接入点、分享图片或内容</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</td></tr></tbody></table><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">上述相关第三方服务商收集前述信息发生信息泄露的，由相关第三方服务商承担相应的法律责任。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如您不同意上述第三方服务商收集前述信息，可能无法获得相应服务，但不影响您正常使用Deloitte系统的其他功能或服务。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>6、</strong><span style=\\\"font-family: 宋体;\\\">当您使用Deloitte系统服务时，为了维护服务的安全稳定运行，我司会收集您的设备型号、操作系统、唯一设备标识符、软件版本号、登录IP地址、接入网络的方式、类型和状态、网络质量数据、与Deloitte系统操作日志及服务日志相关的信息、显示系统窗口、地理位置信息、设备名称、Android ID、BSSID、MAC、SSID、运营商信息。如您不同意收集前述信息，可能无法获得相应服务，但不影响您正常使用Deloitte服务的其他功能或服务。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>7、</strong><span style=\\\"font-family: 宋体;\\\">为实现向您提供我们产品的功能，您可选择是否授权我们收集、使用的信息。如果您拒绝提供，您将无法正常使用相关附加功能或无法达到我们拟达到的功能效果。另，为确保相关业务功能的正常实现，我们需要根据具体的使用场景调用对应的使用权限，并在调用前向您弹窗询问；以下情形中，您可选择是否授权我们收集、使用您的个人信息。如您拒绝授权部分功能或服务所需信息，您将无法使用相关功能或服务，但这不影响您正常使用Deloitte系统的其他功能或服务：</span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（1）存储权限，用于缓存您在使用Deloitte服务过程中产生的文本、图像、视频内容。如您拒绝授权后，上述功能可能无法正常使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（2）摄像头，用于设置头像、录像、拍照、上传图片、上传视频，用于签署合同时进行契约锁人脸识别。拒绝授权后，上述功能将无法使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（3）麦克风，用于签署合同时进行契约锁人脸识别，如果您拒绝授权后，将可能无法使用契约锁人脸识别功能。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（4）相册，用于上传照片设置您的头像、发送图片内容、发送视频内容、分享功能。我们获得的图片信息，加密后存储于数据库中。拒绝授权后，上述功能将无法使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（5）手机短信，用于短信验证，系统后台不保存短信内容。如您拒绝通讯信息（短信）授权，将无法获取短信验证码，则您无法享受上述服务。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（6）网络通讯，用于与服务端进行通讯。拒绝授权后，Deloitte服务所有功能无法使用。我司系统后台保存客户访问时所使用设备的网络信息，包括IP、端口信息。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（7）获取已安装列表，用于使用微信分享时判断是否安装了微信，如果您拒绝授权后，将可能无法使用微信分享。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>8、 消息推送</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>为保证消息通知信息及时有效的推送给您，使用您的IP地址、手机IMEI码、MAC地址、版本号信息。当我们向您推送消息时，需要获取您消息通知的权限以及消息震动并唤醒屏幕的权限，同时为了检查Deloitte服务消息接收服务状态是否正常，确保消息能够通知到您，还需要获取检索当前运行的应用程序的权限。我们申请此权限仅是为了判断至Deloitte服务自身的服务状态，不会收集、存储、利用您手机中其他应用程序的信息。您可以在手机系统设置中打开或者关闭上述权限。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>9、第三方分享服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">当您希望能够将APP内的内容分享给他人时，我们需要获取您第三方登录和分享的权限。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>10、自启动相关说明</strong></p><p style=\\\"text-indent: 20pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为确保Deloitte服务处于关闭或后台运行状态下可正常接收到客户端推送的信息，厂商推送须使用自启动能力，将存在一定频率通过系统发送广播唤醒Deloitte服务自启动或关联启动行为，是因实现功能及服务所必要。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>11、我们如何使用Cookie和同类技术</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为使您获得更轻松的访问体验，您访问本服务时，我们可能会通过小型数据文件识别您的身份，这么做可帮您省去重复输入用户信息的步骤，或者帮助判断您的账户状态。这些数据文件可能是 Cookie、Flash Cookie、您的浏览器或关联应用程序提供的其他本地存储（以下简称“Cookie”） 。请您理解，我们的某些服务只能通过使用Cookie才可得到实现。如您的浏览器或浏览器附加服务允许，您可以修改对Cookie的接受程度或者拒绝至尊宝的Cookie。多数浏览器工具条中的“帮助”部分会告诉您怎样防止您的浏览器接受新的Cookie，怎样让您的浏览器在您收到一条新Cookie时通知您或者怎样彻底关闭Cookie。此外，您可以通过改变浏览器附加程序的设置，或通过访问提供商的网页，来关闭或删除浏览器附加程序使用的Flash Cookie及类似数据。但这一举动在某些情况下可能会影响您安全访问至尊宝应用。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>12、 我们如何使用您的个人信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为了遵守国家法律法规及监管要求，以及向您提供服务及提升服务质量，或保障您的账户安全，我们会在以下情形中使用您的信息：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">1、我们会根据本政策的约定并为实现服务或功能对所收集的您的个人信息进行使用。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">2、为了提升您的服务体验，我们可能会根据您的信息，向您发送与您相关的信息提醒。如您不希望接收此类内容，您可通过回复短信退订以拒绝该类短信。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">3、为了保障服务的稳定性与安全性，我们会将您的信息用于身份验证、安全防范、诈骗监测、预防或禁止非法活动、降低风险、存档和备份用途。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">4、根据法律法规或监管要求向相关部门进行报告。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">5、我们会采取脱敏、去标识化方式对您的信息进行综合统计、分析加工，以便为您提供更加准确、个性、流畅及便捷的服务，或帮助我们评估、改善或设计服务及运营活动等。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">6、我们会对我们的服务或功能使用情况进行统计，并可能会与公众或第三方共享这些统计信息，以展示我们的服务或功能的整体使用趋势。但这些统计信息不包含您的任何身份识别信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">当我们要将信息用于本政策未载明的其他用途时,会按照法律法规及国家标准的要求以确认协议、具体场景下的文案确认动作等形式再次征得您的同意。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>13、我们如何存储和保护您的个人信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">德勤服务重视个人信息安全，并采取一切合理可行的措施，保护您的个人信息：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">1、我们采取符合业界标准的安全措施和技术手段存储和保护您的个人信息，以防止其遭到未经授权访问、披露、篡改、丢失或毁坏。例如，我们会使用加密技术确保数据的保密性；我们会使用受信赖的保护机制防止数据遭到恶意攻击；我们会部署访问控制机制，确保只有授权人员才可访问个人信息；以及我们会举办安全和隐私保护培训课程，加强员工对于保护个人信息重要性的认识。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">2、我们会采取一切合理可行的措施，确保未收集无关的个人信息。我们仅在本政策所述目的所必需期间，且在法律法规及监管规定允许的时限内保存您的个人信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">3、互联网并非绝对安全的环境，而且电子邮件、即时通讯、社交软件等与其他用户的交流方式无法确定是否完全加密，我们建议您使用此类工具时请使用复杂密码，并注意保护您的个人信息安全。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">4、互联网环境并非百分之百安全，我们将尽力确保或担保您发送给我们的任何信息的安全性。如果我们的物理、技术、或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改、或毁坏，导致您的合法权益受损，我们将承担相应的法律责任。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">5、在不幸发生个人信息安全事件后，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。我们将及时将事件相关情况以电子邮件、短信、电话、推送、公告等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。同时，我们还将按照监管部门要求，主动上报个人信息安全事件的处置情况。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>14、您的权利</strong></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">按照中国相关的法律法规和监管规定，我们保障您对自己的用户信息行使以下权利：</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>a) &nbsp; 访问、更正及更新你的用户信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">您有权通过Deloitte服务访问及更正、更新您的信息、更新后立即生效。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（1）账户信息：如果您希望访问您的帐户中的个人资料信息：具体路径为：首页—“我的”进入我的—点击设置进入个人信息设置（姓名，手机号码）。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果您无法通过上述方式访问您的个人信息，您可以按照《隐私声明》中列明的联系方式与我们联系。我们将在30天内回复您的访问请求。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您有责任及时更新您的用户信息。</strong></span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>b) &nbsp; 关闭终端申请的权限</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在使用Deloitte服务的过程中，有些功能需要申请权限（存储、摄像头、相册、麦克风、通信录等），在使用相关功能后，可以在系统设置中关闭相关权限，并不影响后续的正常使用。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>c) &nbsp; 删除您的用户信息</strong></p><p style=\\\"text-align: left;\\\"><strong>在以下情形中，您可以向我们提出删除用户信息的请求：</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; &nbsp; i. &nbsp; &nbsp; &nbsp;如果我们收集、使用用户信息的行为违反法律规定；</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; &nbsp;ii. &nbsp; &nbsp; &nbsp;如果我们收集、使用您的用户信息，却未征得您的同意；</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; iii. &nbsp; &nbsp; &nbsp;如果我们处理用户信息的行为违反了与您的约定。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>d) &nbsp; 注销账户</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">随时可注销此前注册的账户，在注销账户之后，我们将停止为您提供产品或服务，并依据您的要求，删除您的个人信息，法律法规另有规定的除外。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>e) &nbsp; 响应您的请求</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果您无法通过上述方式实现以上权利，您可以随时通过***********************邮件方式与我们联系。为了保障安全，我们可能需要您提供书面请求，或以其他方式证明您的身份，我们将在收到您的反馈并验证您的身份后三十（30）日内答复您的请求。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情收取一定成本费用。对于那些无端重复、需要过多技术手段（例如，需要开发新系统或从根本上改变现行惯例）、给他人合法权益带来风险或者非常不切实际（例如，涉及备份磁带上存放的信息）的请求，我们可能会予以拒绝。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在以下情形中，按照法律法规要求，我们将无法响应您的请求：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（1）与国家安全、国防安全有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（2）与公共安全、公共卫生、重大公共利益有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（3）与犯罪侦查、起诉和审判等有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（4）有充分证据表明您存在主观恶意或滥用权利的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（5）响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>15、第三方收集和使用您的个人信息情况</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本服务可能包含指向第三方网站的链接。本隐私政策不涉及任何第三方（包括运营通过本服务提供的任何网站或在线服务（包括但不限于任何应用程序）或包含链接）的任何第三方的隐私、信息或行为，并且我们对这些行为不承担任何责任。在本服务上提供或包含任何该等网站或资源的链接并不意味着我们或我们的分支机构认可该等网站或资源。</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>当您查看第三方创建的网页或使用第三方开发的应用程序时，这些第三方可能会放置他们自己的Cookie或网络Beacon，这些Cookie或网络Beacon不受我司的控制，且它们的使用不受本政策的约束。建议您与他们联系以获得关于他们的隐私政策的详细情况。如您发现这些第三方创建的网页或第三方开发的应用程序存在风险时，建议您终止相关操作以保护您的合法权益。如因这些第三方行为导致您的信息发生泄露的，由相关第三方承担相应的法律责任。</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>为了提升为您提供服务的质量，我们会接入优质第三方服务商一起为您提供服务，在提供服务的过程中，我们将基于为您提供服务的目的向第三方服务商披露您必要的个人信息，也会要求第三方服务商向我们回传您相关的个人信息。如您拒绝授权提供，将可能导致您无法正常使用我们的服务的某些功能。我们会要求第三方遵守同等的保密义务和采取保密措施。</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"color: black; font-family: 宋体;\\\">以下是我们使用的第三方 SDK 的情况，我们将不时地更新此清单，以更清晰地展示我们与第三方合作或使用第三方功能以完善产品和服务体验的情况。我们会对 SDK 的接入和使用进行符合互联网行业通常能力的审核，以保障你的个人信息安全。也请你理解，即便我们尽可能地及时更新此清单，但在一定情况下仍可能会滞后于app实际使用的情况。同时，为了对我们的商业信息和技术手段保密，部分用于平台风控或特殊合作的 SDK 可能不在此清单中，但该等 SDK 如需获取你的重要隐私信息或设备权限时，我们会通过合理方式取得你的授权同意。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"color: black; font-family: 宋体;\\\">SDK 全称 Software Development Kit ，通常翻译为软件开发工具包。是指用于为特定的软件包、软件框架、硬件平台、操作系统等创建应用软件的开发工具的集合。</span></p><p style=\\\"text-align: left;\\\"><strong>微信 SDK</strong></p><p style=\\\"text-align: left;\\\"><strong>功能类型：</strong><span style=\\\"color: black; font-family: 宋体;\\\">微信分享</span></p><p style=\\\"text-align: left;\\\"><strong>使用目的：</strong><span style=\\\"color: black; font-family: 宋体;\\\">支持微信分享</span></p><p style=\\\"text-align: left;\\\"><strong>数据类型：</strong><span style=\\\"color: black; font-family: 宋体;\\\">设备标识符（Android如IMEI、Android ID、Serial）、MAC地址、WLAN接入点、分享图片或内容</span></p><p style=\\\"text-align: left;\\\"><strong>官网链接：</strong><span style=\\\"color: black; font-family: 宋体;\\\">https://open.weixin.qq.com/</span></p><p style=\\\"text-align: left;\\\"><strong>隐私政策：https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</strong></p><p style=\\\"text-align: left;\\\"><strong>极光开发者服务SDK</strong></p><p style=\\\"text-align: left;\\\"><strong>功能类型：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">推送</span></p><p style=\\\"text-align: left;\\\"><strong>使用目的：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">通过识别设备信息为App赋予推送能力，用于消息推送</span></p><p style=\\\"text-align: left;\\\"><strong>数据类型：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">设备信息[ 设备信息包括：设备标识符（IMEI、IDFA、Android ID、MAC、OAID、IMSI等相关信息）、应用信息（通知开关状态、软件列表等相关信息）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息）]、网络信息[ 网络信息包括：IP地址，WiFi信息，基站信息等相关信息。 ]。</span></p><p style=\\\"text-align: left;\\\"><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.jiguang.cn/license/privacy</span></p><p style=\\\"text-align: left;\\\"><strong>小米推送 SDK涉及的个人信息类型：设备标识符（如 Android ID、OAID、GAID）、设备信息使用目的：推送消息使用场景：在小米手机终端推送消息时使用第三方主体：北京小米移动软件有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.mi.com/console/appservice/push.html</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.mi.com/console/doc/detail?pId=1822</span></p><p style=\\\"text-align: left;\\\"><strong>华为 HMS SDK涉及的个人信息类型：应用基本信息、应用内设备标识符、设备的硬件信息、系统基本信息和系统设置信息使用目的：推送消息使用场景：在华为手机终端推送消息时使用第三方主体：华为软件技术有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.huawei.com/consumer/cn/</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/sdk-data-security-0000001050042177</span></p><p style=\\\"text-align: left;\\\"><strong>荣耀推送 SDK涉及的个人信息类型：应用匿名标识 (AAID)，应用 Token使用目的：推送消息使用场景：在荣耀手机终端推送消息时使用第三方主体：荣耀终端有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.hihonor.com/cn/promoteService</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.hihonor.com/cn/privacy/privacy-policy/</span></p><p style=\\\"text-align: left;\\\"><strong>OPPO 推送 SDK涉及的个人信息类型：设备标识符（如 IMEI、ICCID、IMSI、Android ID、GAID）、应用信息（如应用包名、版本号和运行状态）、网络信息（如 IP 或域名连接结果，当前网络类型）使用目的：推送消息使用场景：在 OPPO 手机终端推送消息时使用第三方主体：广东欢太科技有限公司数据处理方式：通过加密传输和处理的安全处理方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://open.oppomobile.com/new/introduction?page_name=oppopush</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://open.oppomobile.com/wiki/doc#id=10288</span></p><p style=\\\"text-align: left;\\\"><strong>vivo 推送 SDK涉及的个人信息类型：设备信息使用目的：推送消息使用场景：在 vivo 手机终端推送消息时使用第三方主体：广东天宸网络科技有限公司及将来受让运营 vivo 开放平台的公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.vivo.com.cn/promote/pushNews</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.vivo.com.cn/about-vivo/privacy-policy</span></p><p style=\\\"text-align: left;\\\"><strong>魅族推送 SDK涉及的个人信息类型：设备标识信息、位置信息、网络状态信息、运营商信息使用目的：推送消息使用场景：在魅族手机终端推送消息时使用第三方主体：珠海市魅族通讯设备有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://open.flyme.cn/service?type=push</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.meizu.com/legal.html</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>16、未成年人保护</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">我们非常重视对未成年人个人信息的保护。为安全起见，我们将拒绝18周岁以下的未成年人认证及使用本服务。本服务并非面向未成年人，我们也请求未成年人不要通过本服务提供个人信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果你的监护人不同意您按照本政策使用我们的服务或向我们提供信息，请立即终止使用我们的服务并及时通知我们，以便我们采取相应的措施。我们将根据国家相关法律法规的规定保障未成年人的个人信息的保密性和安全性。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，请通过第九节的联系方式与我们联系。我们会设法尽快删除相关数据。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>17、本政策的修订</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>根据国家法律法规、监管政策变化及服务运营需要，我们将对本政策及相关规则不时地进行修改，修改后的内容会通过Deloitte服务公布，公布后即生效，并取代此前相关内容。建议您不时关注相关公告、提示信息及协议、规则等相关内容的变动。您知悉并确认，如您不同意更新后的内容，应立即停止使用相应服务，并注销相关的用户，我行将停止收集您的相关个人信息；如您继续使用服务，即视为同意接受更新内容。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>18、如何与德勤服务联系</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">德勤服务注册地址为：重庆市渝中区瑞天路10号企业天地八号楼。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果你对本政策或个人信息保护相关事宜，有任何疑问或投诉、建议时，您可以通过以下任一方式与我们联系：</span></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">a) &nbsp; </span><u>将您的问题发送至*************************；</u></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">b) &nbsp; 寄到如下地址：重庆市渝中区瑞天路10号企业天地八号楼。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">一般情况下，收到你的投诉反馈后的十五天内予以回复。</span></p><p><br></p><p><br></p>\",\"updatedBy\":null,\"updatedName\":null,\"createdName\":\"184运营\",\"createdBy\":\"IU202401010002\",\"fileIdList\":null,\"effectSj\":null,\"outSj\":null,\"areaScopes\":null}', '2025-06-17 11:20:58', '/agreement/add', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (62, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 6f61a93c-3395-404a-ba03-3645e7cb396f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750142873606\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:63323\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 14:47:53', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (63, NULL, NULL, 'PC', 'OPP-20250616-000002', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250616-000002\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-17 15:07:43', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (64, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"007019\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145176614\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:54108\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:26:16', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (65, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145392218\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:54233\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:29:52', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (66, NULL, NULL, 'PC', 'AD20250617000003', NULL, NULL, '{\"enterpriseId\":\"1\",\"recipientName\":\"1223\",\"recipientPhone\":\"13312345678\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"1231231\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506160001\",\"loginUserName\":\"test20250616\"}', '2025-06-17 16:26:39', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (67, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750150159239\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:62307\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 16:49:19', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (68, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 88cad1cd-8eac-4f20-b3a3-bcd819c005df\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750138984273\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:54689\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 13:43:04', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (69, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750149166374\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:50422\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 16:32:46', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (70, NULL, NULL, 'PC', 'CON-20250617-000003', NULL, NULL, '{\"id\":\"1\",\"version\":\"1.2\",\"type\":\"POLICY\",\"title\":\"Deloitte服务系统用户隐私政策\",\"isEffect\":2,\"content\":\"<p style=\\\"text-align: center;\\\"><strong>Deloitte服务系统用户隐私政策</strong></p><p style=\\\"text-align: right;\\\"><span style=\\\"font-family: 宋体;\\\">本版本发布日期：2024年11月12日</span></p><p style=\\\"text-align: right;\\\"><span style=\\\"font-family: 宋体;\\\">生效日期：2024年11月12日</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">Deloitte服务系统是一款面向分销商下单的系统，分销商可以在线下单、合同在线签约、订单物流跟踪，及时了解最新产品咨询及活动公告，旨在帮助用户更快速便捷地获取订单进度，查询并推动业务进程，把握行业动向，实现全链路线上数字管理。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">尊敬的Deloitte服务系统用户（以下简称“您”），德勤华庆商务服务有限公司（注册地址：重庆市渝中区瑞天路10号企业天地八号楼；以下简称“德勤服务”或“我们”)非常尊重并保护您的隐私，也感谢您对我们的信任。您在使用德勤服务的运营和控制的网站或应用(以下简称 \\\"服务\\\")时，德勤服务将按照《Deloitte系统用户隐私政策》（以下简称“本政策”）收集、使用和披露您的个人信息。德勤服务将按法律法规要求，采取相应安全保护措施，尽力保护您的个人信息安全可控。请您仔细阅读本政策并确认了解我行对您个人信息的处理规则。阅读过程中，如您有任何疑问，可联系我们 <EMAIL> 咨询。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在使用德勤服务的服务前，请您务必仔细阅读并透彻理解本政策，确认了解德勤服务对您个人信息的处理规则，在确认充分理解并同意后使用德勤服务产品或服务。在您使用德勤服务产品或服务之前，您就本政策点击或勾选“同意”并确认提交，即为您已充分理解并同意本政策。</span></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本政策将帮助您了解以下内容：</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>一、我们如何收集您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>二、我们如何使用Cookie和同类技术</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>三、我们如何使用您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>四、我们如何存储和保护您的个人信息</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>五、您的权利</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>六、第三方收集和使用您的个人信息情况</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>七、未成年人保护</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>八、本政策的修订</strong></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>九、如何与德勤服务联系</strong></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本政策具体内容如下：</span></p><p style=\\\"text-indent: 20pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>一、我们如何收集您的个人信息</strong></span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为了实现特定业务功能，我们需要收集和使用您的部分个人信息。您有权拒绝我们收集和使用您的个人信息，法律、行政法规另有规定的除外。但请您理解，如您拒绝我们收集和使用业务功能所必须的个人信息，则可能无法使用对应的业务功能。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">我们的部分功能需要调用您的设备权限，我们会采用系统弹窗或应用弹窗的方式询问您是否授权。您可以随时在您设备的设置功能中选择开关相关权限。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在您使用德勤服务的产品和服务过程中，我们会在向您提供服务过程中使用您主动提供的，以及经过您的授权通过自动化手段收集的下列信息：</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>1、注册登录</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>当您使用Deloitte系统进行注册、登录时，我们需要收集您的个人信息，包括但不限于您的身份基本信息，包括姓名、手机号码、邮箱，以用于验证您的身份，和创建至尊宝账号。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>2、在线签约服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>在您使用Deloitte系统过程中，可能涉及到以电子方式签署合同性质的文件资料，电子签署服务由第三方服务商提供。为满足您合同在线签约的服务所需，届时您需要提供在线签约服务所需要的个人信息，基于您的同意，我们会将您的手机号，您所属的企业名称共享给第三方服务商。签署电子协议时，您需要提供姓名，手机号，有效证件号码，人脸生物识别信息(如面部识别特征等)，银行账号(部分场景使用)，以便于第三方服务商验证您的身份并签署电子协议。这些信息的收集和使用遵循第三方服务商的隐私协议， 具体内容请访问 https://www.qiyuesuo.com/appagreement。如果您与我们发生合同纠纷，我们会向第三方服务商申请调取您签约过程的相关数据。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>3、创建意向单服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您可以通过Deloitte系统下单相关产品。在下意向单过程中，我们会收集您的姓名、所在公司、关联方、联系电话。同时我们会通过系统为您生成采购产品的意向单，该意向单中载明您所下单的产品信息、意向单编号、意向单创建时间、购买方式、意向单金额。您可以通过Deloitte服务为其他人下意向单，您需要提供该实际下单人的前述个人信息，并确保已取得该实际下单人的同意。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>4、发货申请服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您可以通过Deloitte系统对已双签的合同进行发货申请，在创建发货申请过程中，我们会收集您进行发货申请的公司名称、关联方、收货人姓名、收货地址、收货人联系方式。我们会通过系统为您生成发货申请单，该发货申请单中会载明您进行申请的发货产品信息，发货申请编号，发货申请创建时间。您可以通过Deloitte系统为其他人创建发货申请，您需要提供该实际下单人的前述个人信息，并确保已取得该实际申请人的同意。</strong></span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>5、</strong><span style=\\\"font-family: 宋体;\\\">当您使用我们的服务时，在某些特定使用场景下，可能会使用具有相应业务资质及能力的第三方服务商提供的软件服务工具包（简称“SDK”）来为您提供服务，此时第三方服务商需要收集您的必要信息。具体包括以下内容：</span></p><p style=\\\"text-align: left;\\\"><br></p><table style=\\\"width: auto; text-align: start;\\\"><tbody><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"58\\\">SDK 名称</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"73\\\">收集目的</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"115\\\">SDK供应商名称</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">涉及收集的个人信息字段</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">隐私政策/官网链接</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"58\\\">极光推送</td><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"73\\\">消息推送</td><td colspan=\\\"1\\\" rowspan=\\\"2\\\" width=\\\"115\\\">深圳市和讯华谷信息技术有限公司</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">联网信息，设备信息，手机号</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://www.jiguang.cn/license/privacy</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">安卓 ID</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://docs.jiguang.cn/jpush/client/Android/android_jghgzy</td></tr><tr><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"58\\\">微信分享</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"73\\\">支持微信分享</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"115\\\">深圳市腾讯计算机系统有限公司</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"189\\\">设备标识符（Android如IMEI、Android ID、Serial）、MAC地址、WLAN接入点、分享图片或内容</td><td colspan=\\\"1\\\" rowspan=\\\"1\\\" width=\\\"170\\\">https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</td></tr></tbody></table><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">上述相关第三方服务商收集前述信息发生信息泄露的，由相关第三方服务商承担相应的法律责任。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如您不同意上述第三方服务商收集前述信息，可能无法获得相应服务，但不影响您正常使用Deloitte系统的其他功能或服务。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>6、</strong><span style=\\\"font-family: 宋体;\\\">当您使用Deloitte系统服务时，为了维护服务的安全稳定运行，我司会收集您的设备型号、操作系统、唯一设备标识符、软件版本号、登录IP地址、接入网络的方式、类型和状态、网络质量数据、与Deloitte系统操作日志及服务日志相关的信息、显示系统窗口、地理位置信息、设备名称、Android ID、BSSID、MAC、SSID、运营商信息。如您不同意收集前述信息，可能无法获得相应服务，但不影响您正常使用Deloitte服务的其他功能或服务。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>7、</strong><span style=\\\"font-family: 宋体;\\\">为实现向您提供我们产品的功能，您可选择是否授权我们收集、使用的信息。如果您拒绝提供，您将无法正常使用相关附加功能或无法达到我们拟达到的功能效果。另，为确保相关业务功能的正常实现，我们需要根据具体的使用场景调用对应的使用权限，并在调用前向您弹窗询问；以下情形中，您可选择是否授权我们收集、使用您的个人信息。如您拒绝授权部分功能或服务所需信息，您将无法使用相关功能或服务，但这不影响您正常使用Deloitte系统的其他功能或服务：</span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（1）存储权限，用于缓存您在使用Deloitte服务过程中产生的文本、图像、视频内容。如您拒绝授权后，上述功能可能无法正常使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（2）摄像头，用于设置头像、录像、拍照、上传图片、上传视频，用于签署合同时进行契约锁人脸识别。拒绝授权后，上述功能将无法使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（3）麦克风，用于签署合同时进行契约锁人脸识别，如果您拒绝授权后，将可能无法使用契约锁人脸识别功能。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（4）相册，用于上传照片设置您的头像、发送图片内容、发送视频内容、分享功能。我们获得的图片信息，加密后存储于数据库中。拒绝授权后，上述功能将无法使用。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（5）手机短信，用于短信验证，系统后台不保存短信内容。如您拒绝通讯信息（短信）授权，将无法获取短信验证码，则您无法享受上述服务。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>（6）网络通讯，用于与服务端进行通讯。拒绝授权后，Deloitte服务所有功能无法使用。我司系统后台保存客户访问时所使用设备的网络信息，包括IP、端口信息。</strong></span></p><p style=\\\"text-indent: 24.25pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（7）获取已安装列表，用于使用微信分享时判断是否安装了微信，如果您拒绝授权后，将可能无法使用微信分享。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>8、 消息推送</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>为保证消息通知信息及时有效的推送给您，使用您的IP地址、手机IMEI码、MAC地址、版本号信息。当我们向您推送消息时，需要获取您消息通知的权限以及消息震动并唤醒屏幕的权限，同时为了检查Deloitte服务消息接收服务状态是否正常，确保消息能够通知到您，还需要获取检索当前运行的应用程序的权限。我们申请此权限仅是为了判断至Deloitte服务自身的服务状态，不会收集、存储、利用您手机中其他应用程序的信息。您可以在手机系统设置中打开或者关闭上述权限。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>9、第三方分享服务</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">当您希望能够将APP内的内容分享给他人时，我们需要获取您第三方登录和分享的权限。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>10、自启动相关说明</strong></p><p style=\\\"text-indent: 20pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为确保Deloitte服务处于关闭或后台运行状态下可正常接收到客户端推送的信息，厂商推送须使用自启动能力，将存在一定频率通过系统发送广播唤醒Deloitte服务自启动或关联启动行为，是因实现功能及服务所必要。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>11、我们如何使用Cookie和同类技术</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为使您获得更轻松的访问体验，您访问本服务时，我们可能会通过小型数据文件识别您的身份，这么做可帮您省去重复输入用户信息的步骤，或者帮助判断您的账户状态。这些数据文件可能是 Cookie、Flash Cookie、您的浏览器或关联应用程序提供的其他本地存储（以下简称“Cookie”） 。请您理解，我们的某些服务只能通过使用Cookie才可得到实现。如您的浏览器或浏览器附加服务允许，您可以修改对Cookie的接受程度或者拒绝至尊宝的Cookie。多数浏览器工具条中的“帮助”部分会告诉您怎样防止您的浏览器接受新的Cookie，怎样让您的浏览器在您收到一条新Cookie时通知您或者怎样彻底关闭Cookie。此外，您可以通过改变浏览器附加程序的设置，或通过访问提供商的网页，来关闭或删除浏览器附加程序使用的Flash Cookie及类似数据。但这一举动在某些情况下可能会影响您安全访问至尊宝应用。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>12、 我们如何使用您的个人信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">为了遵守国家法律法规及监管要求，以及向您提供服务及提升服务质量，或保障您的账户安全，我们会在以下情形中使用您的信息：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">1、我们会根据本政策的约定并为实现服务或功能对所收集的您的个人信息进行使用。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">2、为了提升您的服务体验，我们可能会根据您的信息，向您发送与您相关的信息提醒。如您不希望接收此类内容，您可通过回复短信退订以拒绝该类短信。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">3、为了保障服务的稳定性与安全性，我们会将您的信息用于身份验证、安全防范、诈骗监测、预防或禁止非法活动、降低风险、存档和备份用途。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">4、根据法律法规或监管要求向相关部门进行报告。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">5、我们会采取脱敏、去标识化方式对您的信息进行综合统计、分析加工，以便为您提供更加准确、个性、流畅及便捷的服务，或帮助我们评估、改善或设计服务及运营活动等。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">6、我们会对我们的服务或功能使用情况进行统计，并可能会与公众或第三方共享这些统计信息，以展示我们的服务或功能的整体使用趋势。但这些统计信息不包含您的任何身份识别信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">当我们要将信息用于本政策未载明的其他用途时,会按照法律法规及国家标准的要求以确认协议、具体场景下的文案确认动作等形式再次征得您的同意。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>13、我们如何存储和保护您的个人信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">德勤服务重视个人信息安全，并采取一切合理可行的措施，保护您的个人信息：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">1、我们采取符合业界标准的安全措施和技术手段存储和保护您的个人信息，以防止其遭到未经授权访问、披露、篡改、丢失或毁坏。例如，我们会使用加密技术确保数据的保密性；我们会使用受信赖的保护机制防止数据遭到恶意攻击；我们会部署访问控制机制，确保只有授权人员才可访问个人信息；以及我们会举办安全和隐私保护培训课程，加强员工对于保护个人信息重要性的认识。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">2、我们会采取一切合理可行的措施，确保未收集无关的个人信息。我们仅在本政策所述目的所必需期间，且在法律法规及监管规定允许的时限内保存您的个人信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">3、互联网并非绝对安全的环境，而且电子邮件、即时通讯、社交软件等与其他用户的交流方式无法确定是否完全加密，我们建议您使用此类工具时请使用复杂密码，并注意保护您的个人信息安全。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">4、互联网环境并非百分之百安全，我们将尽力确保或担保您发送给我们的任何信息的安全性。如果我们的物理、技术、或管理防护设施遭到破坏，导致信息被非授权访问、公开披露、篡改、或毁坏，导致您的合法权益受损，我们将承担相应的法律责任。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">5、在不幸发生个人信息安全事件后，我们将按照法律法规的要求，及时向您告知：安全事件的基本情况和可能的影响、我们已采取或将要采取的处置措施、您可自主防范和降低风险的建议、对您的补救措施等。我们将及时将事件相关情况以电子邮件、短信、电话、推送、公告等方式告知您，难以逐一告知个人信息主体时，我们会采取合理、有效的方式发布公告。同时，我们还将按照监管部门要求，主动上报个人信息安全事件的处置情况。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>14、您的权利</strong></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">按照中国相关的法律法规和监管规定，我们保障您对自己的用户信息行使以下权利：</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>a) &nbsp; 访问、更正及更新你的用户信息</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">您有权通过Deloitte服务访问及更正、更新您的信息、更新后立即生效。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（1）账户信息：如果您希望访问您的帐户中的个人资料信息：具体路径为：首页—“我的”进入我的—点击设置进入个人信息设置（姓名，手机号码）。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果您无法通过上述方式访问您的个人信息，您可以按照《隐私声明》中列明的联系方式与我们联系。我们将在30天内回复您的访问请求。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\"><strong>您有责任及时更新您的用户信息。</strong></span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>b) &nbsp; 关闭终端申请的权限</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在使用Deloitte服务的过程中，有些功能需要申请权限（存储、摄像头、相册、麦克风、通信录等），在使用相关功能后，可以在系统设置中关闭相关权限，并不影响后续的正常使用。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>c) &nbsp; 删除您的用户信息</strong></p><p style=\\\"text-align: left;\\\"><strong>在以下情形中，您可以向我们提出删除用户信息的请求：</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; &nbsp; i. &nbsp; &nbsp; &nbsp;如果我们收集、使用用户信息的行为违反法律规定；</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; &nbsp;ii. &nbsp; &nbsp; &nbsp;如果我们收集、使用您的用户信息，却未征得您的同意；</strong></p><p style=\\\"text-align: left;\\\"><strong> &nbsp; iii. &nbsp; &nbsp; &nbsp;如果我们处理用户信息的行为违反了与您的约定。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>d) &nbsp; 注销账户</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">随时可注销此前注册的账户，在注销账户之后，我们将停止为您提供产品或服务，并依据您的要求，删除您的个人信息，法律法规另有规定的除外。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>e) &nbsp; 响应您的请求</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果您无法通过上述方式实现以上权利，您可以随时通过***********************邮件方式与我们联系。为了保障安全，我们可能需要您提供书面请求，或以其他方式证明您的身份，我们将在收到您的反馈并验证您的身份后三十（30）日内答复您的请求。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">对于您合理的请求，我们原则上不收取费用，但对多次重复、超出合理限度的请求，我们将视情收取一定成本费用。对于那些无端重复、需要过多技术手段（例如，需要开发新系统或从根本上改变现行惯例）、给他人合法权益带来风险或者非常不切实际（例如，涉及备份磁带上存放的信息）的请求，我们可能会予以拒绝。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">在以下情形中，按照法律法规要求，我们将无法响应您的请求：</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（1）与国家安全、国防安全有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（2）与公共安全、公共卫生、重大公共利益有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（3）与犯罪侦查、起诉和审判等有关的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（4）有充分证据表明您存在主观恶意或滥用权利的；</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">（5）响应您的请求将导致您或其他个人、组织的合法权益受到严重损害的。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>15、第三方收集和使用您的个人信息情况</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">本服务可能包含指向第三方网站的链接。本隐私政策不涉及任何第三方（包括运营通过本服务提供的任何网站或在线服务（包括但不限于任何应用程序）或包含链接）的任何第三方的隐私、信息或行为，并且我们对这些行为不承担任何责任。在本服务上提供或包含任何该等网站或资源的链接并不意味着我们或我们的分支机构认可该等网站或资源。</span></p><p style=\\\"text-indent: 21.1pt; text-align: left;\\\"><strong>当您查看第三方创建的网页或使用第三方开发的应用程序时，这些第三方可能会放置他们自己的Cookie或网络Beacon，这些Cookie或网络Beacon不受我司的控制，且它们的使用不受本政策的约束。建议您与他们联系以获得关于他们的隐私政策的详细情况。如您发现这些第三方创建的网页或第三方开发的应用程序存在风险时，建议您终止相关操作以保护您的合法权益。如因这些第三方行为导致您的信息发生泄露的，由相关第三方承担相应的法律责任。</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>为了提升为您提供服务的质量，我们会接入优质第三方服务商一起为您提供服务，在提供服务的过程中，我们将基于为您提供服务的目的向第三方服务商披露您必要的个人信息，也会要求第三方服务商向我们回传您相关的个人信息。如您拒绝授权提供，将可能导致您无法正常使用我们的服务的某些功能。我们会要求第三方遵守同等的保密义务和采取保密措施。</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"color: black; font-family: 宋体;\\\">以下是我们使用的第三方 SDK 的情况，我们将不时地更新此清单，以更清晰地展示我们与第三方合作或使用第三方功能以完善产品和服务体验的情况。我们会对 SDK 的接入和使用进行符合互联网行业通常能力的审核，以保障你的个人信息安全。也请你理解，即便我们尽可能地及时更新此清单，但在一定情况下仍可能会滞后于app实际使用的情况。同时，为了对我们的商业信息和技术手段保密，部分用于平台风控或特殊合作的 SDK 可能不在此清单中，但该等 SDK 如需获取你的重要隐私信息或设备权限时，我们会通过合理方式取得你的授权同意。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"color: black; font-family: 宋体;\\\">SDK 全称 Software Development Kit ，通常翻译为软件开发工具包。是指用于为特定的软件包、软件框架、硬件平台、操作系统等创建应用软件的开发工具的集合。</span></p><p style=\\\"text-align: left;\\\"><strong>微信 SDK</strong></p><p style=\\\"text-align: left;\\\"><strong>功能类型：</strong><span style=\\\"color: black; font-family: 宋体;\\\">微信分享</span></p><p style=\\\"text-align: left;\\\"><strong>使用目的：</strong><span style=\\\"color: black; font-family: 宋体;\\\">支持微信分享</span></p><p style=\\\"text-align: left;\\\"><strong>数据类型：</strong><span style=\\\"color: black; font-family: 宋体;\\\">设备标识符（Android如IMEI、Android ID、Serial）、MAC地址、WLAN接入点、分享图片或内容</span></p><p style=\\\"text-align: left;\\\"><strong>官网链接：</strong><span style=\\\"color: black; font-family: 宋体;\\\">https://open.weixin.qq.com/</span></p><p style=\\\"text-align: left;\\\"><strong>隐私政策：https://support.weixin.qq.com/cgi-bin/mmsupportacctnodeweb-bin/pages/RYiYJkLOrQwu0nb8</strong></p><p style=\\\"text-align: left;\\\"><strong>极光开发者服务SDK</strong></p><p style=\\\"text-align: left;\\\"><strong>功能类型：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">推送</span></p><p style=\\\"text-align: left;\\\"><strong>使用目的：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">通过识别设备信息为App赋予推送能力，用于消息推送</span></p><p style=\\\"text-align: left;\\\"><strong>数据类型：</strong><span style=\\\"color: rgb(18, 18, 18); background-color: white; font-family: 宋体;\\\">设备信息[ 设备信息包括：设备标识符（IMEI、IDFA、Android ID、MAC、OAID、IMSI等相关信息）、应用信息（通知开关状态、软件列表等相关信息）、设备参数及系统信息（设备类型、设备型号、操作系统及硬件相关信息）]、网络信息[ 网络信息包括：IP地址，WiFi信息，基站信息等相关信息。 ]。</span></p><p style=\\\"text-align: left;\\\"><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.jiguang.cn/license/privacy</span></p><p style=\\\"text-align: left;\\\"><strong>小米推送 SDK涉及的个人信息类型：设备标识符（如 Android ID、OAID、GAID）、设备信息使用目的：推送消息使用场景：在小米手机终端推送消息时使用第三方主体：北京小米移动软件有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.mi.com/console/appservice/push.html</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.mi.com/console/doc/detail?pId=1822</span></p><p style=\\\"text-align: left;\\\"><strong>华为 HMS SDK涉及的个人信息类型：应用基本信息、应用内设备标识符、设备的硬件信息、系统基本信息和系统设置信息使用目的：推送消息使用场景：在华为手机终端推送消息时使用第三方主体：华为软件技术有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.huawei.com/consumer/cn/</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/sdk-data-security-0000001050042177</span></p><p style=\\\"text-align: left;\\\"><strong>荣耀推送 SDK涉及的个人信息类型：应用匿名标识 (AAID)，应用 Token使用目的：推送消息使用场景：在荣耀手机终端推送消息时使用第三方主体：荣耀终端有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://developer.hihonor.com/cn/promoteService</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.hihonor.com/cn/privacy/privacy-policy/</span></p><p style=\\\"text-align: left;\\\"><strong>OPPO 推送 SDK涉及的个人信息类型：设备标识符（如 IMEI、ICCID、IMSI、Android ID、GAID）、应用信息（如应用包名、版本号和运行状态）、网络信息（如 IP 或域名连接结果，当前网络类型）使用目的：推送消息使用场景：在 OPPO 手机终端推送消息时使用第三方主体：广东欢太科技有限公司数据处理方式：通过加密传输和处理的安全处理方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://open.oppomobile.com/new/introduction?page_name=oppopush</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://open.oppomobile.com/wiki/doc#id=10288</span></p><p style=\\\"text-align: left;\\\"><strong>vivo 推送 SDK涉及的个人信息类型：设备信息使用目的：推送消息使用场景：在 vivo 手机终端推送消息时使用第三方主体：广东天宸网络科技有限公司及将来受让运营 vivo 开放平台的公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://dev.vivo.com.cn/promote/pushNews</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.vivo.com.cn/about-vivo/privacy-policy</span></p><p style=\\\"text-align: left;\\\"><strong>魅族推送 SDK涉及的个人信息类型：设备标识信息、位置信息、网络状态信息、运营商信息使用目的：推送消息使用场景：在魅族手机终端推送消息时使用第三方主体：珠海市魅族通讯设备有限公司数据处理方式：通过去标识化、加密传输及其他安全方式官网链接：</strong><span style=\\\"font-family: 宋体;\\\">https://open.flyme.cn/service?type=push</span><strong>隐私政策：</strong><span style=\\\"font-family: 宋体;\\\">https://www.meizu.com/legal.html</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>16、未成年人保护</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">我们非常重视对未成年人个人信息的保护。为安全起见，我们将拒绝18周岁以下的未成年人认证及使用本服务。本服务并非面向未成年人，我们也请求未成年人不要通过本服务提供个人信息。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果你的监护人不同意您按照本政策使用我们的服务或向我们提供信息，请立即终止使用我们的服务并及时通知我们，以便我们采取相应的措施。我们将根据国家相关法律法规的规定保障未成年人的个人信息的保密性和安全性。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如您是未成年人的监护人，当您对您所监护的未成年人的个人信息有相关疑问时，请通过第九节的联系方式与我们联系。我们会设法尽快删除相关数据。</span></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>17、本政策的修订</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><strong>根据国家法律法规、监管政策变化及服务运营需要，我们将对本政策及相关规则不时地进行修改，修改后的内容会通过Deloitte服务公布，公布后即生效，并取代此前相关内容。建议您不时关注相关公告、提示信息及协议、规则等相关内容的变动。您知悉并确认，如您不同意更新后的内容，应立即停止使用相应服务，并注销相关的用户，我行将停止收集您的相关个人信息；如您继续使用服务，即视为同意接受更新内容。</strong></p><p style=\\\"text-indent: 24pt; text-align: left;\\\"><strong>18、如何与德勤服务联系</strong></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">德勤服务注册地址为：重庆市渝中区瑞天路10号企业天地八号楼。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">如果你对本政策或个人信息保护相关事宜，有任何疑问或投诉、建议时，您可以通过以下任一方式与我们联系：</span></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">a) &nbsp; </span><u>将您的问题发送至*************************；</u></p><p style=\\\"text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">b) &nbsp; 寄到如下地址：重庆市渝中区瑞天路10号企业天地八号楼。</span></p><p style=\\\"text-indent: 21pt; text-align: left;\\\"><span style=\\\"font-family: 宋体;\\\">一般情况下，收到你的投诉反馈后的十五天内予以回复。</span></p><p><br></p><p><br></p>\",\"updatedBy\":null,\"updatedName\":null,\"createdName\":\"184运营\",\"createdBy\":\"IU202401010002\",\"fileIdList\":null,\"effectSj\":null,\"outSj\":null,\"areaScopes\":null}', '2025-06-17 11:23:49', '/agreement/add', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (71, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer a29546ae-e8b2-4dd6-a29c-609b09c4e013\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750140049426\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:55930\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 14:00:50', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (72, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"220\",\"requireOrderDescription\":\"啊啊啊啊啊啊否定的否定刚刚好\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-17 14:01:41', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (73, NULL, NULL, 'PC', 'REQ-20250617-000005', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"11\",\"installProvinceName\":\"北京\",\"installCityCode\":\"110100\",\"installCityName\":\"北京市\",\"quantityP\":1,\"quantityW\":\"220\",\"requireOrderDescription\":\"111\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-17 14:12:26', '/pc-customer/requireOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (74, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer b0844caa-9867-4500-9a6b-0acb1cb1f2b0\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750140664753\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:58268\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 14:11:05', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (75, NULL, NULL, 'APP', '***********', NULL, NULL, '{\"phone\":\"***********\",\"userType\":\"EXTERNAL\",\"smsCode\":\"377974\"}{\"content-length\":[\"64\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_APP\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750140699311\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:58267\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 14:11:39', '/login/sms/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (76, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145207800\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:53518\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 15:26:47', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (77, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer fde69b7a-1fc0-48c8-9cf3-0c4f72c5fc7c\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750145153765\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:54092\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 15:25:54', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 16:53:23', NULL, NULL, '2025-06-17 16:53:23');
INSERT INTO `behavior_log` VALUES (78, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7\"],\"requesttime\":[\"1750150564224\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"*************:58972\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 16:56:04', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 16:56:09', NULL, NULL, '2025-06-17 16:56:09');
INSERT INTO `behavior_log` VALUES (79, NULL, NULL, 'PC', 'CON-20250617-000005', NULL, NULL, '{\"id\":\"4\",\"version\":\"1.4\",\"type\":\"AGREEMENT\",\"title\":\"Deloitte服务用户协议\",\"isEffect\":2,\"content\":\"<p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">本版本发布日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">生效日期：2024年11月12日</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">一、 总则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.1 Deloitte服务（以下简称本平台）的所有权归德勤华庆商务服务有限公司（注册地址：重庆市渝中区瑞天路10号企业天地8号楼；以下简称“德勤服务”或“我们”)。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.2 用户在使用之前，应当仔细阅读本协议，充分理解协议条款的法律后果，用户应当受本协议的约束。用户在使用服务或产品时，应当同意接受本协议及其他政策后方能使用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">1.3 本协议将由Deloitte服务根据实际情况以公告方式随时更新，用户应当及时关注，如不同意变更后内容可书面提交申请，以与德勤服务解除协议关系，如在公告期内不予答复，即视为同意按照协议变更内容履行。用户同意本平台不承担一对一的通知义务。本平台的通知、公告、声明或其它类似内容是本协议的一部分。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">二、 用户帐号</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">2.1 用户有义务保证帐号的安全，用户利用该帐号所进行的一切活动引起的任何损失或损害，由用户自行承担全部责任，本平台不承担任何责任。如用户发现帐号遭到未授权的使用或发生其他任何安全问题，应立即修改帐号并妥善保管，如有必要，请通知本平台。因黑客行为或用户的保管疏忽导致帐号非法使用，本平台不承担任何责任，但是平台可协助用户处理。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">三、 使用规则</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.1 遵守中华人民共和国相关法律法规，包括但不限于《中华人民共和国计算机信息系统安全保护条例》、《计算机软件保护条例》、《最高人民法院关于审理涉及计算机网络著作权纠纷案件适用法律若干问题的解释(法释[2006]11号)》、《全国人大常委会关于维护互联网安全的决定》、《互联网新闻信息服务管理规定》、《互联网著作权行政保护办法》和《信息网络传播权保护条例》等有关计算机互联网规定和知识产权的法律和法规、实施办法。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.2 用户对其自行发表、上传或传送的内容负全部责任，所有用户不得在本平台任何页面发布、转载、传送含有下列内容之一的信息，否则本平台有权自行处理并不通知用户：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）违反宪法确定的基本原则的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）危害国家安全，泄漏国家机密，颠覆国家政权，破坏国家统一的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）损害国家荣誉和利益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）煽动民族仇恨、民族歧视，破坏民族团结的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（5）破坏国家宗教政策，宣扬邪教和封建迷信的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（6）散布谣言，扰乱社会秩序，破坏社会稳定的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（7）散布淫秽、色情、赌博、暴力、恐怖或者教唆犯罪的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（8）侮辱或者诽谤他人，侵害他人合法权益的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（9）煽动非法集会、结社、游行、示威、聚众扰乱社会秩序的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（10）以非法民间组织名义活动的；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（11）含有法律、行政法规禁止的其他内容的。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.3 用户承诺对其发表或者上传于本平台的所有信息(即包括但不限于《中华人民共和国著作权法》规定的作品，包括但不限于文字、图片、音乐、电影、表演和录音录像制品和电脑程序等；《中华人民共和国商标法》规定的商标、标识等；《中华人民共和国专利法》规定的专利等；《中华人民共和国反不正当竞争法》规定的商业秘密信息等)均享有完整的知识产权，或者已经得到相关权利人的合法授权；如用户违反本条规定造成本平台被第三人索赔的，用户应全额补偿本平台一切费用(包括但不限于各种赔偿费、诉讼代理费及为此支出的其它合理费用）；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.4 当第三方认为用户发表或者上传于本平台的信息侵犯其权利，并根据《信息网络传播权保护条例》或者相关法律规定向本平台发送权利通知书时，用户同意本平台可以自行判断决定删除涉嫌侵权信息，除非用户提交书面证据材料排除侵权的可能性，本平台将不会自动恢复上述删除的信息；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">3.5 如用户在使用网络服务时违反下列任何规定，本平台有权要求用户改正或直接采取一切必要的措施(包括但不限于删除用户张贴的内容、暂停或终止用户使用网络服务的权利）以减轻用户不当行为而造成的影响：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）不得为任何非法目的而使用网络服务系统；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）遵守所有与网络服务有关的网络协议、规定和程序；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）不得利用本平台进行任何可能对互联网的正常运转造成不利影响的行为；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）不得利用本平台进行任何不利于本平台的行为。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">四、隐私保护</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.1 本平台不对外公开或向第三方提供单个用户的注册资料及用户在使用网络服务时存储在本平台的非公开内容，但下列情况除外：</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（1）事先获得用户的明确授权；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（2）根据有关的法律法规要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（3）按照相关政府主管部门的要求；</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">（4）为维护社会公众的利益。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.2 在不透露单个用户隐私资料的前提下，本平台有权对整个用户数据库进行分析并对用户数据库进行商业上的利用。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.3 在用户与本平台终止合作，解除协议关系后，或者用户提出删除需求时，在不影响本平台正常运作情况下，本平台将删除该用户的隐私资料。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">4.4 本平台具有全面的技术保护措施和安全维护机制来保证用户信息的内容安全，但由于不可抗力或者因计算机病毒感染、黑客攻击等特殊外力侵扰，导致用户信息破坏、泄密并受到损失的，本平台不承担任何法律责任，本平台将最大努力保护用户信息，以及出现风险后及时做补救措施。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">五、责任声明</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\">5.1 用户明确同意其使用本平台网络服务所存在的风险及一切后果将完全由用户本人承担，Deloitte服务对此不承担任何责任，如客户需本平台可协助用户处理相关问题。</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.2 本平台无法保证网络服务一定能满足用户的要求，也不保证网络服务的及时性、安全性、准确性，本平台将尽最大力量保障系统稳定和信息安全。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.3 本平台不保证为方便用户而设置的外部链接的准确性和完整性，同时，对于该等外部链接指向的不由本平台实际控制的任何网页上的内容，本平台不承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.4 对于因不可抗力或本平台不能控制的原因造成的网络服务中断或其它缺陷，本平台不承担任何责任，但将尽力减少因此而给用户造成的损失和影响。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;5.5 本平台有权于任何时间暂时或永久修改或终止本服务(或其任何部分)，而无论其通知与否，本平台对用户和任何第三人均无需承担任何责任。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;六、附则 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.1 本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.2 如本协议中的任何条款无论因何种原因完全或部分无效或不具有执行力，本协议的其余条款仍应有效并且有约束力。 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span></p><p style=\\\"text-align: start;\\\"><span style=\\\"color: rgb(38, 38, 38);\\\"> &nbsp; &nbsp; &nbsp; &nbsp;6.3 本协议解释权及修订权归德勤华庆商务服务有限公司所有。</span></p>\",\"updatedBy\":null,\"updatedName\":null,\"createdName\":\"184运营\",\"createdBy\":\"IU202401010002\",\"fileIdList\":null,\"effectSj\":null,\"outSj\":null,\"areaScopes\":null}', '2025-06-17 17:08:20', '/agreement/add', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:08:21', NULL, NULL, '2025-06-17 17:08:21');
INSERT INTO `behavior_log` VALUES (80, NULL, NULL, 'PC', 'R000001', NULL, NULL, '{\"roleId\":null,\"roleName\":\"test\",\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":\"INTERNAL\",\"roleStatus\":1,\"roleDesc\":\"rwew\",\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:09:39', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:09:40', NULL, NULL, '2025-06-17 17:09:40');
INSERT INTO `behavior_log` VALUES (81, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"roleId\":\"R000001\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:09:46', '/role', 'POST', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:09:46', NULL, NULL, '2025-06-17 17:09:46');
INSERT INTO `behavior_log` VALUES (82, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"roleId\":\"R000001\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:09:50', '/role', 'POST', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:09:51', NULL, NULL, '2025-06-17 17:09:51');
INSERT INTO `behavior_log` VALUES (83, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"roleId\":\"R000001\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:09:59', '/role', 'POST', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:10:00', NULL, NULL, '2025-06-17 17:10:00');
INSERT INTO `behavior_log` VALUES (84, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"roleId\":\"R000001\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:12:30', '/role', 'POST', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:12:30', NULL, NULL, '2025-06-17 17:12:30');
INSERT INTO `behavior_log` VALUES (85, NULL, NULL, 'PC', 'R000001', NULL, NULL, '{\"roleId\":\"R000001\",\"permissionIds\":[\"PC_ORGANIZATION_MANAGE_PAGE\",\"PC_USER_MANAGE_PAGE\",\"PC_INTENT_ORDER_MANAGE_PAGE\",\"PC_INTENT_ORDER_EXPORT_BTN\",\"PC_CONTRACT_MANAGE_PAGE\",\"PC_CONTRACT_EXPORT_BTN\",\"PC_DELIVER_CONFIRM_BTN\",\"PC_DELIVER_RETURN_BTN\",\"PC_DELIVER_EXPORT_BTN\",\"PC_PRODUCT_MANAGE_PAGE\",\"PC_PRODUCT_EXPORT_BTN\",\"PC_PRODUCT_EDIT_BTN\",\"PC_PRODUCT_RELEASE_POWER_BTN\",\"PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN\",\"PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN\",\"PC_GIVEN_MANAGE_PAGE\",\"PC_MASTER_MANAGE_PAGE\",\"PC_ACCESSORY_MANAGE_PAGE\",\"PC_CUSTOMER_MANAGE_PAGE\",\"PC_REQUIRE_ORDER_MANAGE_PAGE\",\"PC_QUESTIONNAIRE_MANAGE_PAGE\",\"PC_QUESTIONNAIRE_ADD_BTN\",\"PC_QUESTIONNAIRE_EDIT_BTN\",\"PC_QUESTIONNAIRE_RELEASE_BTN\",\"PC_QUESTIONNAIRE_DELETE_BTN\",\"REPORT_MANAGE_PAGE\",\"REPORT_INTENT_ORDER_AMOUNT_TOTAL\",\"REPORT_INTENT_ORDER_AMOUNT_NEW\",\"REPORT_INTENT_ORDER_POWER_TOTAL\",\"REPORT_INTENT_ORDER_POWER_NEW\",\"REPORT_INTENT_ORDER_PRODUCT\",\"REPORT_CONTRACT_PRODUCT\",\"REPORT_PARTNER_AMOUNT_TOTAL\",\"REPORT_PARTNER_AMOUNT_NEW\",\"REPORT_CONTRACT_POWER_TOTAL\",\"REPORT_CONTRACT_POWER_NEW\",\"REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM\",\"REPORT_INTENT_ORDER_POWER_TOP_BOTTOM\",\"REPORT_REQIORE_ORDER_AMOUNT_TOTAL\",\"REPORT_REQIORE_ORDER_AMOUNT_NEW\",\"REPORT_CONTRACT_NONEY_TOTAL\",\"REPORT_CONTRACT_NONEY_NEW\",\"REPORT_PRODUCT_FAVORITE_AMOUNT\",\"REPORT_DELIVERY_AMOUNT_TOTAL\",\"REPORT_DELIVERY_AMOUNT_NEW\",\"REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED\",\"REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED\",\"REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER\",\"PC_INVOICE_MANAGE_PAGE\",\"REPORT_DELIVERY_DATA_LIST\",\"REPORT_CONTRACT_ITEM_DATA_LIST\",\"PC_OPTION_MANAGE_PAGE\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-17 17:13:23', '/role/rolePerms', 'PUT', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:13:24', NULL, NULL, '2025-06-17 17:13:24');
INSERT INTO `behavior_log` VALUES (86, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"roleId\":\"R000001\",\"roleName\":\"test11\",\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":\"EXTERNAL\",\"roleStatus\":1,\"roleDesc\":\"rwew111\",\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:13:35', '/role', 'POST', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:13:36', NULL, NULL, '2025-06-17 17:13:36');
INSERT INTO `behavior_log` VALUES (87, NULL, NULL, 'PC', 'R002100', NULL, NULL, '{\"roleId\":\"R002100\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":1,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:15:56', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:15:57', NULL, NULL, '2025-06-17 17:15:57');
INSERT INTO `behavior_log` VALUES (88, NULL, NULL, 'PC', 'R002100', NULL, NULL, '{\"roleId\":\"R002100\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:16:01', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:16:02', NULL, NULL, '2025-06-17 17:16:02');
INSERT INTO `behavior_log` VALUES (89, NULL, NULL, 'PC', 'R001001', NULL, NULL, '{\"roleId\":\"R001001\",\"permissionIds\":[\"PC_USER_MANAGE_PAGE\",\"PC_LOG_MANAGE_PAGE\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-17 17:29:09', '/role/rolePerms', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:29:10', NULL, NULL, '2025-06-17 17:29:10');
INSERT INTO `behavior_log` VALUES (90, NULL, NULL, 'PC', 'R000002', NULL, NULL, '{\"roleId\":null,\"roleName\":\"新用户运营11\",\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":\"INTERNAL\",\"roleStatus\":0,\"roleDesc\":\"111\",\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:29:33', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:29:34', NULL, NULL, '2025-06-17 17:29:34');
INSERT INTO `behavior_log` VALUES (91, NULL, NULL, 'PC', 'R000002', NULL, NULL, '{\"roleId\":\"R000002\",\"permissionIds\":[\"PC_FOLLOW_UP_WITH_NEW_USERS\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-17 17:29:36', '/role/rolePerms', 'PUT', 'EXCEPTION', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:29:37', NULL, NULL, '2025-06-17 17:29:37');
INSERT INTO `behavior_log` VALUES (92, NULL, NULL, 'PC', 'R000003', NULL, NULL, '{\"roleId\":null,\"roleName\":\"新用户运营1111\",\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":\"INTERNAL\",\"roleStatus\":0,\"roleDesc\":\"ddd\",\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:29:50', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:29:51', NULL, NULL, '2025-06-17 17:29:51');
INSERT INTO `behavior_log` VALUES (93, NULL, NULL, 'PC', 'R000003', NULL, NULL, '{\"roleId\":\"R000003\",\"permissionIds\":[\"PC_FOLLOW_UP_WITH_NEW_USERS\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-17 17:29:52', '/role/rolePerms', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:29:53', NULL, NULL, '2025-06-17 17:29:53');
INSERT INTO `behavior_log` VALUES (94, NULL, NULL, 'PC', 'R000003', NULL, NULL, '{\"roleId\":\"R000003\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":1,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:30:04', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:30:05', NULL, NULL, '2025-06-17 17:30:05');
INSERT INTO `behavior_log` VALUES (95, NULL, NULL, 'PC', 'R000003', NULL, NULL, '{\"roleId\":\"R000003\",\"roleName\":null,\"userId\":\"IU202401010002\",\"userName\":\"184运营\",\"roleLabel\":null,\"roleStatus\":0,\"roleDesc\":null,\"delIds\":null,\"specialType\":\"COMMON\"}', '2025-06-17 17:30:10', '/role', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:30:12', NULL, NULL, '2025-06-17 17:30:12');
INSERT INTO `behavior_log` VALUES (96, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"delIds\":[\"R000003\"],\"userId\":\"IU202401010002\",\"userName\":\"184运营\"}', '2025-06-17 17:30:20', '/role/delete', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:30:21', NULL, NULL, '2025-06-17 17:30:21');
INSERT INTO `behavior_log` VALUES (97, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"delIds\":[\"R000002\"],\"userId\":\"IU202401010002\",\"userName\":\"184运营\"}', '2025-06-17 17:30:27', '/role/delete', 'POST', 'FAIL', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:30:28', NULL, NULL, '2025-06-17 17:30:28');
INSERT INTO `behavior_log` VALUES (98, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"delIds\":[\"R000002\"],\"userId\":\"IU202401010002\",\"userName\":\"184运营\"}', '2025-06-17 17:30:34', '/role/delete', 'POST', 'FAIL', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:30:35', NULL, NULL, '2025-06-17 17:30:35');
INSERT INTO `behavior_log` VALUES (99, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer f9f5c84f-870e-483c-9487-cf5da842fa57\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750153037472\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62809\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"],\"content-length\":[\"0\"]}', '2025-06-17 17:37:17', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-17 17:37:19', NULL, NULL, '2025-06-17 17:37:19');
INSERT INTO `behavior_log` VALUES (100, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750153043956\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=************;for=\\\"************:62810\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"************\"],\"host\":[\"************:78\"]}', '2025-06-17 17:37:23', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:37:25', NULL, NULL, '2025-06-17 17:37:25');
INSERT INTO `behavior_log` VALUES (101, NULL, NULL, 'PC', 'OPP-20250617-000002', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250617-000002\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-17 17:40:00', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:40:01', NULL, NULL, '2025-06-17 17:40:01');
INSERT INTO `behavior_log` VALUES (102, NULL, NULL, 'PC', 'OPP-20250617-000001', NULL, NULL, '\"OPP-20250617-000001\"', '2025-06-17 17:40:24', '/pc-customer/intentOrder/deleteIntentOrder', 'DELETE', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:40:25', NULL, NULL, '2025-06-17 17:40:25');
INSERT INTO `behavior_log` VALUES (103, NULL, NULL, 'PC', 'OPP-20250617-000004', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"220\",\"quantityP\":\"11\",\"quantityW\":0,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":null,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":null,\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":null,\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"2\",\"enterpriseName\":\"企业2\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"Residential\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":null,\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Mineral exploitation\",\"industryAttributesText\":\"能源/化工/环保|矿产开采\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-17 17:40:55', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:40:56', NULL, NULL, '2025-06-17 17:40:56');
INSERT INTO `behavior_log` VALUES (104, NULL, NULL, 'PC', 'OPP-20250617-000004', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250617-000004\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-17 17:42:16', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:42:17', NULL, NULL, '2025-06-17 17:42:17');
INSERT INTO `behavior_log` VALUES (105, NULL, NULL, 'PC', 'OPP-20250617-000005', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"\",\"quantityP\":\"1111\",\"quantityW\":0,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":0,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Module\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Module\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":null,\"enterpriseName\":\"\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":null,\"goodsArrivalRegion\":null,\"goodsArrivalSubRegion\":null,\"area\":null,\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":null,\"opportunityCurrency\":null,\"salesInternalUserId\":null,\"salesInternalUserName\":\"\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":null,\"industryAttributes\":\"\",\"industryAttributesText\":\"\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":null,\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-17 17:46:15', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:46:16', NULL, NULL, '2025-06-17 17:46:16');
INSERT INTO `behavior_log` VALUES (106, NULL, NULL, 'PC', 'OPP-20250617-000005', NULL, NULL, '\"OPP-20250617-000005\"', '2025-06-17 17:46:39', '/pc-customer/intentOrder/deleteIntentOrder', 'DELETE', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:46:40', NULL, NULL, '2025-06-17 17:46:40');
INSERT INTO `behavior_log` VALUES (107, NULL, NULL, 'PC', 'OPP-20250617-000006', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"\",\"quantityP\":\"0\",\"quantityW\":0,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":0,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Module\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Module\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":null,\"enterpriseName\":\"\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"Residential\",\"goodsArrivalRegion\":null,\"goodsArrivalSubRegion\":null,\"area\":null,\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":null,\"opportunityCurrency\":null,\"salesInternalUserId\":null,\"salesInternalUserName\":\"\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":null,\"industryAttributes\":\"\",\"industryAttributesText\":\"\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":null,\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-17 17:47:36', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-17 17:47:38', NULL, NULL, '2025-06-17 17:47:38');
INSERT INTO `behavior_log` VALUES (108, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"loginUserId\":null,\"userIds\":null,\"userId\":\"\",\"keyword\":\"\",\"status\":[],\"roleIds\":[],\"organizationCode\":\"\",\"organizationCodeList\":null,\"regions\":[],\"userProvinceCode\":null,\"userCityCode\":null,\"followStatus\":null,\"isBindEnterprise\":null,\"registrationTimeStart\":null,\"registrationTimeEnd\":null,\"isAssignManager\":null,\"saleFollowStatus\":null}', '2025-06-18 13:46:23', '/user/internal/export', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 13:53:28', NULL, NULL, '2025-06-18 13:53:28');
INSERT INTO `behavior_log` VALUES (109, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 86e36f9f-2931-4529-8a1a-0dd9132b9294\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750227450050\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54476\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-18 14:17:30', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 14:17:32', NULL, NULL, '2025-06-18 14:17:32');
INSERT INTO `behavior_log` VALUES (110, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750227456950\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54485\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-18 14:17:36', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 14:17:38', NULL, NULL, '2025-06-18 14:17:38');
INSERT INTO `behavior_log` VALUES (111, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 3f5b226e-8df7-4d51-86eb-a1234ce24d5b\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750228093607\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:55080\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-18 14:28:14', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 14:28:15', NULL, NULL, '2025-06-18 14:28:15');
INSERT INTO `behavior_log` VALUES (112, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750230564478\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"************:63313\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-18 15:09:24', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 15:09:26', NULL, NULL, '2025-06-18 15:09:26');
INSERT INTO `behavior_log` VALUES (113, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 9d15d4ff-ca7f-4d43-9e3f-b06cdc0caaf0\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750230630625\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"************:63313\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-18 15:10:31', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 15:10:32', NULL, NULL, '2025-06-18 15:10:32');
INSERT INTO `behavior_log` VALUES (114, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"渝中区华盛路\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202401010002\",\"currentUserName\":\"184运营\",\"currentUserCode\":null,\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:29:32', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:29:36', NULL, NULL, '2025-06-18 15:29:36');
INSERT INTO `behavior_log` VALUES (115, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"62\",\"provinceName\":\"甘肃\",\"cityCode\":\"620300\",\"cityName\":\"金昌市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"瑞天路10号\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202401010002\",\"currentUserName\":\"184运营\",\"currentUserCode\":null,\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:30:25', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:30:28', NULL, NULL, '2025-06-18 15:30:28');
INSERT INTO `behavior_log` VALUES (116, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":\"657219\",\"streetCode\":null,\"streetName\":\"重庆市渝中区企业天地8号楼\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130001\",\"currentUserName\":\"test2025\",\"currentUserCode\":\"173277\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:33:00', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506130001', 'test2025', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:33:07', NULL, NULL, '2025-06-18 15:33:07');
INSERT INTO `behavior_log` VALUES (117, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":\"657219\",\"streetCode\":null,\"streetName\":\"重庆市渝中区企业天地8号楼\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130001\",\"currentUserName\":\"test2025\",\"currentUserCode\":\"173277\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:33:37', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130001', 'test2025', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:33:40', NULL, NULL, '2025-06-18 15:33:40');
INSERT INTO `behavior_log` VALUES (118, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":\"657210\",\"streetCode\":null,\"streetName\":\"重庆市渝中区企业天地2号楼\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130001\",\"currentUserName\":\"test2025\",\"currentUserCode\":\"173277\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:36:06', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130001', 'test2025', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:36:09', NULL, NULL, '2025-06-18 15:36:09');
INSERT INTO `behavior_log` VALUES (119, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":\"657210\",\"streetCode\":null,\"streetName\":\"重庆市渝中区企业天地2号楼\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130001\",\"currentUserName\":\"test2025\",\"currentUserCode\":\"173277\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 15:39:37', '/enterprise/modifyEnterpriseBusiness', 'POST', 'EXCEPTION', 'feign.RetryableException: Read timed out executing POST http://trinax-partner-service/modifyEnterpriseBusiness\r\n	at feign.FeignException.errorExecuting(FeignException.java:268)\r\n	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:129)\r\n	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)\r\n	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)\r\n	at com.sun.proxy.$Proxy183.modifyEnterpriseBusiness(Unknown Source)\r\n	at com.trinasolar.trinax.staffbff.controller.partner.EnterpriseController.modifyEnterpriseBusiness(EnterpriseController.java:135)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\r\n	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n	at java.lang.reflect.Method.invoke(Method.java:498)\r\n	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:799)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)\r\n	at com.trinasolar.trinax.staffbff.controller.partner.EnterpriseController$$EnhancerBySpringCGLIB$$dd7e4c5f.modifyEnterpriseBusiness(<generated>)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\r\n	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n	at java.lang.reflect.Method.invoke(Method.java:498)\r\n	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\r\n	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\r\n	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\r\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\r\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\r\n	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\r\n	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\r\n	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\r\n	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\r\n	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\r\n	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)\r\n	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\r\n	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at com.github.xiaoymin.knife4j.extend.filter.basic.ServletSecurityBasicAuthFilter.doFilter(ServletSecurityBasicAuthFilter.java:56)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)\r\n	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)\r\n	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)\r\n	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)\r\n	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)\r\n	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)\r\n	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)\r\n	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)\r\n	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)\r\n	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)\r\n	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\r\n	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\r\n	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\r\n	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\r\n	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\r\n	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\r\n	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)\r\n	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)\r\n	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\r\n	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)\r\n	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)\r\n	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\r\n	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\r\n	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\r\n	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\r\n	at java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.net.SocketTimeoutException: Read timed out\r\n	at java.net.SocketInputStream.socketRead0(Native Method)\r\n	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)\r\n	at java.net.SocketInputStream.read(SocketInputStream.java:171)\r\n	at java.net.SocketInputStream.read(SocketInputStream.java:141)\r\n	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)\r\n	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)\r\n	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)\r\n	at sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:745)\r\n	at sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:680)\r\n	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1610)\r\n	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1515)\r\n	at java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:480)\r\n	at feign.Client$Default.convertResponse(Client.java:109)\r\n	at feign.Client$Default.execute(Client.java:105)\r\n	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:57)\r\n	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:95)\r\n	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:114)\r\n	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)\r\n	... 128 more\r\n', 'IU202506130001', 'test2025', 'INTERNAL', 0, NULL, NULL, '2025-06-18 15:40:38', NULL, NULL, '2025-06-18 15:40:38');
INSERT INTO `behavior_log` VALUES (120, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750234090885\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"************:53234\\\"\"],\"x-forwarded-for\":[\"************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-18 16:08:10', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:08:13', NULL, NULL, '2025-06-18 16:08:13');
INSERT INTO `behavior_log` VALUES (121, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750235199477\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54674\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-18 16:26:39', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:26:41', NULL, NULL, '2025-06-18 16:26:41');
INSERT INTO `behavior_log` VALUES (122, NULL, NULL, 'PC', 'REQ-********-000001', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"Residential\",\"currentUserId\":\"EU202506160001\",\"currentUserName\":\"test20250616\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-18 16:29:16', '/pc-customer/requireOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:29:24', NULL, NULL, '2025-06-18 16:29:24');
INSERT INTO `behavior_log` VALUES (123, NULL, NULL, 'PC', '2', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"2\",\"admissionApplying\":false,\"industry\":\"Apparel\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":\"重庆\",\"cityCode\":\"500100\",\"cityName\":\"重庆市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"瑞天路\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"\",\"contactName\":null,\"businessLine\":\"Module\",\"contactMobile\":null,\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202401010002\",\"currentUserName\":\"184运营\",\"currentUserCode\":null,\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 16:36:19', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 16:36:30', NULL, NULL, '2025-06-18 16:36:30');
INSERT INTO `behavior_log` VALUES (124, NULL, NULL, 'PC', 'OPP-********-000001', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"\",\"quantityP\":\"1\",\"quantityW\":0,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":0,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Module\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Module\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-19\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"EXW\",\"industryAttributes\":\"Machinery/Manufacturing|Electrical machinery/equipment\",\"industryAttributesText\":\"机械/制造|电气机械/器材\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160001\",\"createdName\":\"test20250616\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-18 16:47:26', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:47:29', NULL, NULL, '2025-06-18 16:47:29');
INSERT INTO `behavior_log` VALUES (125, NULL, NULL, 'PC', 'OPP-********-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-********-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-18 16:47:53', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:47:53', NULL, NULL, '2025-06-18 16:47:53');
INSERT INTO `behavior_log` VALUES (126, NULL, NULL, 'PC', 'OPP-********-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-********-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-18 16:50:50', '/pc-customer/intentOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 16:50:51', NULL, NULL, '2025-06-18 16:50:51');
INSERT INTO `behavior_log` VALUES (127, NULL, NULL, 'PC', 'OPP-********-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-********-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506160001\",\"userName\":\"test20250616\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-18 17:02:12', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 17:02:13', NULL, NULL, '2025-06-18 17:02:13');
INSERT INTO `behavior_log` VALUES (128, NULL, NULL, 'PC', 'AD********000032', NULL, NULL, '{\"enterpriseId\":\"1\",\"recipientName\":\"chenwenjian\",\"recipientPhone\":\"13312345678\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"重庆市渝中区企业天地8号楼\",\"addressType\":\"RECEIVE\",\"isDefault\":0,\"loginUserId\":\"EU202506160001\",\"loginUserName\":\"test20250616\"}', '2025-06-18 17:03:40', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 17:03:42', NULL, NULL, '2025-06-18 17:03:42');
INSERT INTO `behavior_log` VALUES (129, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD********000032\",\"comment\":\"测试\",\"contact\":null,\"contracts\":[],\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"orderType\":null,\"userId\":\"EU202506160001\",\"userName\":null}', '2025-06-18 17:04:01', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 17:04:03', NULL, NULL, '2025-06-18 17:04:03');
INSERT INTO `behavior_log` VALUES (130, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD********000032\",\"comment\":\"测试\",\"contact\":null,\"contracts\":[],\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"orderType\":null,\"userId\":\"EU202506160001\",\"userName\":null}', '2025-06-18 17:04:13', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 17:04:14', NULL, NULL, '2025-06-18 17:04:14');
INSERT INTO `behavior_log` VALUES (131, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"1\",\"enterpriseName\":\"企业1\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD********000032\",\"comment\":\"测试\",\"contact\":null,\"contracts\":[],\"salesInternalUserId\":\"IU202506130001\",\"salesInternalUserName\":\"test2025\",\"orderType\":null,\"userId\":\"EU202506160001\",\"userName\":null}', '2025-06-18 17:07:27', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506160001', 'test20250616', 'EXTERNAL', 0, NULL, NULL, '2025-06-18 17:07:28', NULL, NULL, '2025-06-18 17:07:28');
INSERT INTO `behavior_log` VALUES (132, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"34\",\"provinceName\":\"安徽\",\"cityCode\":\"340800\",\"cityName\":\"安庆市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰路\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 17:37:25', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 17:37:28', NULL, NULL, '2025-06-18 17:37:28');
INSERT INTO `behavior_log` VALUES (133, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"34\",\"provinceName\":\"安徽\",\"cityCode\":\"340800\",\"cityName\":\"安庆市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰路\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 17:39:02', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 17:39:07', NULL, NULL, '2025-06-18 17:39:07');
INSERT INTO `behavior_log` VALUES (134, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"34\",\"provinceName\":\"安徽\",\"cityCode\":\"340800\",\"cityName\":\"安庆市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰路\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 17:42:11', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 17:42:13', NULL, NULL, '2025-06-18 17:42:13');
INSERT INTO `behavior_log` VALUES (135, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 17:58:40', '/enterprise/modifyEnterpriseBusiness', 'POST', 'EXCEPTION', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 17:58:42', NULL, NULL, '2025-06-18 17:58:42');
INSERT INTO `behavior_log` VALUES (136, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 18:00:12', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 18:00:14', NULL, NULL, '2025-06-18 18:00:14');
INSERT INTO `behavior_log` VALUES (137, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 18:01:50', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 18:01:53', NULL, NULL, '2025-06-18 18:01:53');
INSERT INTO `behavior_log` VALUES (138, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 18:02:27', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-18 18:02:30', NULL, NULL, '2025-06-18 18:02:30');
INSERT INTO `behavior_log` VALUES (139, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文峰街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202401010002\",\"currentUserName\":\"184运营\",\"currentUserCode\":null,\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-18 18:05:21', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-18 18:05:25', NULL, NULL, '2025-06-18 18:05:25');
INSERT INTO `behavior_log` VALUES (140, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190001\",\"roleIds\":[\"R001100\"],\"updateBy\":\"IU202401010002\",\"updateName\":\"184运营\"}', '2025-06-19 10:23:19', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:23:23', NULL, NULL, '2025-06-19 10:23:23');
INSERT INTO `behavior_log` VALUES (141, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190002\",\"roleIds\":[\"R001500\"],\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-19 10:38:03', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:38:05', NULL, NULL, '2025-06-19 10:38:05');
INSERT INTO `behavior_log` VALUES (142, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190003\",\"roleIds\":[\"R001600\"],\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-19 10:40:23', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:40:24', NULL, NULL, '2025-06-19 10:40:24');
INSERT INTO `behavior_log` VALUES (143, NULL, NULL, 'PC', 'IU202506190003', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190003\",\"parentUserId\":\"IU202506190002\",\"originParentUserId\":null,\"userName\":\"任洋杉\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JCFBYA4\",\"mobile\":\"***********\",\"salesRelations\":[],\"organizationCode\":\"TRINA_CN_OPERATION\",\"updatedBy\":\"IU202506190001\",\"updatedName\":\"陈文见\"}', '2025-06-19 10:40:36', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:40:38', NULL, NULL, '2025-06-19 10:40:38');
INSERT INTO `behavior_log` VALUES (144, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文月街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-19 10:57:13', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:57:16', NULL, NULL, '2025-06-19 10:57:16');
INSERT INTO `behavior_log` VALUES (145, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190005\",\"roleIds\":[\"R001300\"],\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-19 10:58:02', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-19 10:58:02', NULL, NULL, '2025-06-19 10:58:02');
INSERT INTO `behavior_log` VALUES (146, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer a4c40b93-818a-4a3b-9bea-cd673442485f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750315331315\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:55997\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 14:42:11', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-19 14:42:13', NULL, NULL, '2025-06-19 14:42:13');
INSERT INTO `behavior_log` VALUES (147, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750315338031\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:55998\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-19 14:42:18', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 14:42:21', NULL, NULL, '2025-06-19 14:42:21');
INSERT INTO `behavior_log` VALUES (148, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 159eeec2-7d6e-420f-9ded-97abd246b71b\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750317201370\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:59356\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 15:13:21', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 15:13:22', NULL, NULL, '2025-06-19 15:13:22');
INSERT INTO `behavior_log` VALUES (149, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 0dffdc35-f4bd-450b-9468-6626e9bd7a19\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750317737940\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:60110\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 15:22:18', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-19 15:22:19', NULL, NULL, '2025-06-19 15:22:19');
INSERT INTO `behavior_log` VALUES (150, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750317916881\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:60508\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-19 15:25:16', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 15:25:18', NULL, NULL, '2025-06-19 15:25:18');
INSERT INTO `behavior_log` VALUES (151, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer d1508e2e-0fbd-4780-8475-d345af8fb156\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750318188435\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:61093\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 15:29:48', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 15:29:49', NULL, NULL, '2025-06-19 15:29:49');
INSERT INTO `behavior_log` VALUES (152, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750318312547\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:61397\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-19 15:31:52', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 15:31:55', NULL, NULL, '2025-06-19 15:31:55');
INSERT INTO `behavior_log` VALUES (153, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 82bec04b-b2b8-44ea-9070-5a1633cd4956\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750318326663\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:61397\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 15:32:07', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 15:32:09', NULL, NULL, '2025-06-19 15:32:09');
INSERT INTO `behavior_log` VALUES (154, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 8a2da257-5fcc-45e6-a61f-7896b43a3916\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750318502223\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:61716\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-19 15:35:02', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-19 15:35:03', NULL, NULL, '2025-06-19 15:35:03');
INSERT INTO `behavior_log` VALUES (155, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190009\",\"roleIds\":[\"R001200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-19 16:32:48', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:32:49', NULL, NULL, '2025-06-19 16:32:49');
INSERT INTO `behavior_log` VALUES (156, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190007\",\"roleIds\":[\"R001200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-19 16:33:09', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:33:10', NULL, NULL, '2025-06-19 16:33:10');
INSERT INTO `behavior_log` VALUES (157, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506190008\",\"roleIds\":[\"R001501\"],\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-19 16:37:19', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:37:20', NULL, NULL, '2025-06-19 16:37:20');
INSERT INTO `behavior_log` VALUES (158, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750322427548\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:59433\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-19 16:40:27', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:40:29', NULL, NULL, '2025-06-19 16:40:29');
INSERT INTO `behavior_log` VALUES (159, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:42:09', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:42:12', NULL, NULL, '2025-06-19 16:42:12');
INSERT INTO `behavior_log` VALUES (160, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"Residential\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:42:23', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:42:25', NULL, NULL, '2025-06-19 16:42:25');
INSERT INTO `behavior_log` VALUES (161, NULL, NULL, 'PC', 'OPP-20250619-000001', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"2\",\"productName\":\"组件-产品2\",\"power\":\"2\",\"quantityP\":\"1\",\"quantityW\":2,\"guidePrice\":2,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.000002,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":4,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-20\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"EXW\",\"industryAttributes\":\"Advertising/Media/Culture/Sports|Advertising/Public Relations/Exhibitions\",\"industryAttributesText\":\"广告/传媒/文化/体育|广告/公关/会展\",\"isSurrogateOrder\":\"False\",\"remark\":\"1\",\"createdBy\":\"EU202506190008\",\"createdName\":\"罗蓉\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"6\",\"capitalEnterpriseName\":\"厦门象屿新能源有限责任公司\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-19 16:43:20', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:43:24', NULL, NULL, '2025-06-19 16:43:24');
INSERT INTO `behavior_log` VALUES (162, NULL, NULL, 'PC', 'OPP-20250619-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250619-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506190008\",\"userName\":\"罗蓉\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-19 16:43:32', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:43:33', NULL, NULL, '2025-06-19 16:43:33');
INSERT INTO `behavior_log` VALUES (163, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:45:04', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:45:06', NULL, NULL, '2025-06-19 16:45:06');
INSERT INTO `behavior_log` VALUES (164, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"32\",\"installProvinceName\":\"江苏\",\"installCityCode\":\"320400\",\"installCityName\":\"常州市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:46:24', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:46:26', NULL, NULL, '2025-06-19 16:46:26');
INSERT INTO `behavior_log` VALUES (165, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"32\",\"installProvinceName\":\"江苏\",\"installCityCode\":\"320400\",\"installCityName\":\"常州市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:46:40', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:47:01', NULL, NULL, '2025-06-19 16:47:01');
INSERT INTO `behavior_log` VALUES (166, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":null,\"streetCode\":null,\"streetName\":\"312\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"\",\"contactName\":null,\"businessLine\":\"Tracker\",\"contactMobile\":null,\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-19 16:47:47', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:47:50', NULL, NULL, '2025-06-19 16:47:50');
INSERT INTO `behavior_log` VALUES (167, NULL, NULL, 'PC', 'IU202506190003', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190003\",\"parentUserId\":\"IU202506190002\",\"originParentUserId\":null,\"userName\":\"任洋杉\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JCFBYA4\",\"mobile\":\"***********\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190003\",\"bizOrganizationCode\":\"TRINA_CN_SALES_HUADONG\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_HUADONG\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"LL\"}', '2025-06-19 16:48:55', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:48:56', NULL, NULL, '2025-06-19 16:48:56');
INSERT INTO `behavior_log` VALUES (168, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"32\",\"installProvinceName\":\"江苏\",\"installCityCode\":\"320400\",\"installCityName\":\"常州市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:49:09', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:49:10', NULL, NULL, '2025-06-19 16:49:10');
INSERT INTO `behavior_log` VALUES (169, NULL, NULL, 'PC', 'IU202506190009', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190009\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"刘彦麟\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"\",\"mobile\":\"13262772720\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190009\",\"bizOrganizationCode\":\"TRINA_CN_SALES_LUEXIANG\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_HUANAN\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"LL\"}', '2025-06-19 16:55:55', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-19 16:55:56', NULL, NULL, '2025-06-19 16:55:56');
INSERT INTO `behavior_log` VALUES (170, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"32\",\"installProvinceName\":\"江苏\",\"installCityCode\":\"320400\",\"installCityName\":\"常州市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:56:10', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:56:12', NULL, NULL, '2025-06-19 16:56:12');
INSERT INTO `behavior_log` VALUES (171, NULL, NULL, 'PC', 'REQ-20250619-000008', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":null,\"enterpriseName\":null,\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":1,\"quantityW\":\"1\",\"requireOrderDescription\":\"1\",\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506190008\",\"currentUserName\":\"罗蓉\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-19 16:56:29', '/pc-customer/requireOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-19 16:56:31', NULL, NULL, '2025-06-19 16:56:31');
INSERT INTO `behavior_log` VALUES (172, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750384958205\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:60289\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 10:02:38', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:02:40', NULL, NULL, '2025-06-20 10:02:40');
INSERT INTO `behavior_log` VALUES (173, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750385962818\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62172\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 10:19:22', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:19:24', NULL, NULL, '2025-06-20 10:19:24');
INSERT INTO `behavior_log` VALUES (174, NULL, NULL, 'PC', 'AD20250620000061', NULL, NULL, '{\"enterpriseId\":\"5\",\"recipientName\":\"陈文见\",\"recipientPhone\":\"18215644525\",\"provinceCode\":\"50\",\"cityCode\":\"500100\",\"districtCode\":null,\"addressDetail\":\"重庆市渝中区企业天地8号楼\",\"addressType\":\"RECEIVE\",\"isDefault\":0,\"loginUserId\":\"EU202506190008\",\"loginUserName\":\"罗蓉\"}', '2025-06-20 10:25:59', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:26:01', NULL, NULL, '2025-06-20 10:26:01');
INSERT INTO `behavior_log` VALUES (175, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"NEG19RC.20_CHN_2023A\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:28:09', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:28:10', NULL, NULL, '2025-06-20 10:28:10');
INSERT INTO `behavior_log` VALUES (176, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"NEG19RC.20_CHN_2023A\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:36:16', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:36:16', NULL, NULL, '2025-06-20 10:36:16');
INSERT INTO `behavior_log` VALUES (177, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"NEG19RC.20_CHN_2023A\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:45:10', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:45:11', NULL, NULL, '2025-06-20 10:45:11');
INSERT INTO `behavior_log` VALUES (178, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"NEG19RC.20_CHN_2023A\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:45:21', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:45:22', NULL, NULL, '2025-06-20 10:45:22');
INSERT INTO `behavior_log` VALUES (179, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:50:56', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:50:56', NULL, NULL, '2025-06-20 10:50:56');
INSERT INTO `behavior_log` VALUES (180, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 10:51:29', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 10:51:30', NULL, NULL, '2025-06-20 10:51:30');
INSERT INTO `behavior_log` VALUES (181, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer ccfd592e-2d1b-4db4-a6f1-bb69d9b1ae7d\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750388503352\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63103\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 11:01:43', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 11:01:44', NULL, NULL, '2025-06-20 11:01:44');
INSERT INTO `behavior_log` VALUES (182, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750388546066\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63103\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 11:02:26', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:02:27', NULL, NULL, '2025-06-20 11:02:27');
INSERT INTO `behavior_log` VALUES (183, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:03:05', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:03:06', NULL, NULL, '2025-06-20 11:03:06');
INSERT INTO `behavior_log` VALUES (184, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:04:12', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:04:12', NULL, NULL, '2025-06-20 11:04:12');
INSERT INTO `behavior_log` VALUES (185, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:07:34', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:07:35', NULL, NULL, '2025-06-20 11:07:35');
INSERT INTO `behavior_log` VALUES (186, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:08:28', '/pc-customer/delivery/save', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:08:32', NULL, NULL, '2025-06-20 11:08:32');
INSERT INTO `behavior_log` VALUES (187, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:10:33', '/pc-customer/delivery/save', 'POST', 'EXCEPTION', 'feign.RetryableException: Read timed out executing POST http://trinax-delivery-service/delivery/save\r\n	at feign.FeignException.errorExecuting(FeignException.java:268)\r\n	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:129)\r\n	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)\r\n	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)\r\n	at com.sun.proxy.$Proxy161.saveDeliver(Unknown Source)\r\n	at com.trinasolar.trinax.partnerbff.controller.delivery.DeliveryController.pcCustomerSaveDeliver(DeliveryController.java:80)\r\n	at com.trinasolar.trinax.partnerbff.controller.delivery.DeliveryController$$FastClassBySpringCGLIB$$7612b9b6.invoke(<generated>)\r\n	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)\r\n	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)\r\n	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)\r\n	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)\r\n	at com.trinasolar.trinax.partnerbff.controller.delivery.DeliveryController$$EnhancerBySpringCGLIB$$2447e076.pcCustomerSaveDeliver(<generated>)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)\r\n	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n	at java.lang.reflect.Method.invoke(Method.java:498)\r\n	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)\r\n	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)\r\n	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)\r\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)\r\n	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)\r\n	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\r\n	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)\r\n	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)\r\n	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)\r\n	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)\r\n	at javax.servlet.http.HttpServlet.service(HttpServlet.java:665)\r\n	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)\r\n	at javax.servlet.http.HttpServlet.service(HttpServlet.java:750)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)\r\n	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)\r\n	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)\r\n	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)\r\n	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(OAuth2AuthenticationProcessingFilter.java:176)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)\r\n	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)\r\n	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112)\r\n	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)\r\n	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)\r\n	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)\r\n	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)\r\n	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:94)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)\r\n	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)\r\n	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)\r\n	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)\r\n	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)\r\n	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)\r\n	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)\r\n	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)\r\n	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)\r\n	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)\r\n	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)\r\n	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)\r\n	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)\r\n	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)\r\n	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)\r\n	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)\r\n	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\r\n	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)\r\n	at java.lang.Thread.run(Thread.java:750)\r\nCaused by: java.net.SocketTimeoutException: Read timed out\r\n	at java.net.SocketInputStream.socketRead0(Native Method)\r\n	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)\r\n	at java.net.SocketInputStream.read(SocketInputStream.java:171)\r\n	at java.net.SocketInputStream.read(SocketInputStream.java:141)\r\n	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)\r\n	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)\r\n	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)\r\n	at sun.net.www.http.HttpClient.parseHTTPHeader(HttpClient.java:745)\r\n	at sun.net.www.http.HttpClient.parseHTTP(HttpClient.java:680)\r\n	at sun.net.www.protocol.http.HttpURLConnection.getInputStream0(HttpURLConnection.java:1610)\r\n	at sun.net.www.protocol.http.HttpURLConnection.getInputStream(HttpURLConnection.java:1515)\r\n	at java.net.HttpURLConnection.getResponseCode(HttpURLConnection.java:480)\r\n	at feign.Client$Default.convertResponse(Client.java:109)\r\n	at feign.Client$Default.execute(Client.java:105)\r\n	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:57)\r\n	at org.springframework.cloud.openfeign.loadbalancer.LoadBalancerUtils.executeWithLoadBalancerLifecycleProcessing(LoadBalancerUtils.java:95)\r\n	at org.springframework.cloud.openfeign.loadbalancer.FeignBlockingLoadBalancerClient.execute(FeignBlockingLoadBalancerClient.java:114)\r\n	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)\r\n	... 121 more\r\n', 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:11:34', NULL, NULL, '2025-06-20 11:11:34');
INSERT INTO `behavior_log` VALUES (188, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":\"罗蓉\"}', '2025-06-20 11:18:05', '/pc-customer/delivery/submit/detail', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:18:08', NULL, NULL, '2025-06-20 11:18:08');
INSERT INTO `behavior_log` VALUES (189, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250620000061\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":\"罗蓉\"}', '2025-06-20 11:18:53', '/pc-customer/delivery/submit/detail', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:18:56', NULL, NULL, '2025-06-20 11:18:56');
INSERT INTO `behavior_log` VALUES (190, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750389743568\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:55697\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 11:22:23', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:22:25', NULL, NULL, '2025-06-20 11:22:25');
INSERT INTO `behavior_log` VALUES (191, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":\"罗蓉\"}', '2025-06-20 11:28:29', '/pc-customer/delivery/submit/detail', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:28:31', NULL, NULL, '2025-06-20 11:28:31');
INSERT INTO `behavior_log` VALUES (192, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:29:41', '/pc-customer/delivery/save', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:29:43', NULL, NULL, '2025-06-20 11:29:43');
INSERT INTO `behavior_log` VALUES (193, NULL, NULL, 'PC', '1', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":true,\"enterpriseId\":\"1\",\"admissionApplying\":false,\"industry\":\"Biotechnology\",\"customerOrgNature\":\"Government\",\"erpBusinessEntity\":\"1\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"50\",\"provinceName\":null,\"cityCode\":\"500100\",\"cityName\":null,\"postalCode\":null,\"streetCode\":null,\"streetName\":\"文月街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160001\",\"contactName\":\"test20250616\",\"businessLine\":\"Tracker\",\"contactMobile\":\"***********\",\"legalPersonMobile\":null,\"mainBusiness\":\"\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":null,\"bankId\":null,\"applyTime\":null,\"currentUserId\":\"IU202506190001\",\"currentUserName\":\"陈文见\",\"currentUserCode\":\"700009\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-20 11:30:04', '/enterprise/saveEnterpriseBusiness', 'POST', 'FAIL', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-20 11:30:09', NULL, NULL, '2025-06-20 11:30:09');
INSERT INTO `behavior_log` VALUES (194, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:30:36', '/pc-customer/delivery/save', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:30:38', NULL, NULL, '2025-06-20 11:30:38');
INSERT INTO `behavior_log` VALUES (195, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 4d0ccdca-8129-4be1-a7b1-b65178c33d76\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750390310970\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:56590\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 11:31:51', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', 'luolan', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:31:52', NULL, NULL, '2025-06-20 11:31:52');
INSERT INTO `behavior_log` VALUES (196, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 11:32:45', '/pc-customer/delivery/save', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 11:33:36', NULL, NULL, '2025-06-20 11:33:36');
INSERT INTO `behavior_log` VALUES (197, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":null,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 13:00:10', '/pc-customer/delivery/save', 'POST', 'FAIL', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 13:01:01', NULL, NULL, '2025-06-20 13:01:01');
INSERT INTO `behavior_log` VALUES (198, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer bd6e4e09-f851-4181-9fb9-5966e2676a16\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398349872\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54572\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 13:45:50', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 13:45:51', NULL, NULL, '2025-06-20 13:45:51');
INSERT INTO `behavior_log` VALUES (199, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer dae98d33-18fe-4dde-93a4-c91e9dbf52dd\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398360275\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65510\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 13:46:00', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-20 13:46:01', NULL, NULL, '2025-06-20 13:46:01');
INSERT INTO `behavior_log` VALUES (200, NULL, NULL, 'PC', '13452357702', NULL, NULL, '{\"mobile\":\"13452357702\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398406913\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65511\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:46:46', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 13:46:47', NULL, NULL, '2025-06-20 13:46:47');
INSERT INTO `behavior_log` VALUES (201, NULL, NULL, 'PC', '13452357702', NULL, NULL, '{\"mobile\":\"13452357702\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398415018\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65511\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:46:55', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 13:46:55', NULL, NULL, '2025-06-20 13:46:55');
INSERT INTO `behavior_log` VALUES (202, NULL, NULL, 'PC', '13452357702', NULL, NULL, '{\"mobile\":\"13452357702\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398425440\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65511\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:47:05', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 13:47:06', NULL, NULL, '2025-06-20 13:47:06');
INSERT INTO `behavior_log` VALUES (203, NULL, NULL, 'PC', '13452357702', NULL, NULL, '{\"mobile\":\"13452357702\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398439052\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65511\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:47:19', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 13:47:19', NULL, NULL, '2025-06-20 13:47:19');
INSERT INTO `behavior_log` VALUES (204, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 04704fb3-fe88-44a2-b2e3-57712ae267da\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398469908\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54805\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 13:47:50', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 13:47:51', NULL, NULL, '2025-06-20 13:47:51');
INSERT INTO `behavior_log` VALUES (205, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398480926\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54811\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:48:00', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 13:48:02', NULL, NULL, '2025-06-20 13:48:02');
INSERT INTO `behavior_log` VALUES (206, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 301484c8-04f7-441b-8dfc-5a264223aa6a\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398494811\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54925\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 13:48:15', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 13:48:15', NULL, NULL, '2025-06-20 13:48:15');
INSERT INTO `behavior_log` VALUES (207, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750398935821\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:49954\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 13:55:35', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 13:55:37', NULL, NULL, '2025-06-20 13:55:37');
INSERT INTO `behavior_log` VALUES (208, NULL, NULL, 'PC', 'DRE-20250620-000010', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":1,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 14:03:18', '/pc-customer/delivery/save', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:03:49', NULL, NULL, '2025-06-20 14:03:49');
INSERT INTO `behavior_log` VALUES (209, NULL, NULL, 'PC', 'DRE-20250620-000011', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":1,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 14:04:29', '/pc-customer/delivery/save', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:04:33', NULL, NULL, '2025-06-20 14:04:33');
INSERT INTO `behavior_log` VALUES (210, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer fd180da7-2b6e-4a04-ab5b-233bc3fa8f88\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750399538179\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:56851\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 14:05:38', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 14:05:39', NULL, NULL, '2025-06-20 14:05:39');
INSERT INTO `behavior_log` VALUES (211, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750399543026\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:56851\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 14:05:43', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:05:44', NULL, NULL, '2025-06-20 14:05:44');
INSERT INTO `behavior_log` VALUES (212, NULL, NULL, 'PC', 'DRE-20250620-000012', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"EXW\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州丽曜光电科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":null,\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_1\",\"contractItems\":[{\"contractId\":\"test_ci_1\",\"contractBusinessItemId\":\"test_cbii_1\",\"productId\":\"1\",\"quantity\":8000000,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":1,\"userId\":\"EU202506190008\",\"userName\":null}', '2025-06-20 14:05:54', '/pc-customer/delivery/save', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:05:58', NULL, NULL, '2025-06-20 14:05:58');
INSERT INTO `behavior_log` VALUES (213, NULL, NULL, 'PC', 'DRE-20250620-000010', NULL, NULL, '{\"deliverNo\":\"DRE-20250620-000010\",\"userId\":\"EU202506190008\"}', '2025-06-20 14:08:46', '/pc-customer/delivery/delete', 'DELETE', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:08:48', NULL, NULL, '2025-06-20 14:08:48');
INSERT INTO `behavior_log` VALUES (214, NULL, NULL, 'PC', 'DRE-20250620-000011', NULL, NULL, '{\"deliverNo\":\"DRE-20250620-000011\",\"userId\":\"EU202506190008\"}', '2025-06-20 14:08:51', '/pc-customer/delivery/delete', 'DELETE', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:08:53', NULL, NULL, '2025-06-20 14:08:53');
INSERT INTO `behavior_log` VALUES (215, NULL, NULL, 'PC', 'DRE-20250620-000012', NULL, NULL, '{\"deliveryNo\":\"DRE-20250620-000012\",\"userId\":\"EU202506190008\"}', '2025-06-20 14:09:01', '/pc-customer/delivery/submit/list', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:09:03', NULL, NULL, '2025-06-20 14:09:03');
INSERT INTO `behavior_log` VALUES (216, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 6931321b-0a45-4ac6-85a3-8d12222f2b9f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750400202365\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:57881\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 14:16:42', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:16:43', NULL, NULL, '2025-06-20 14:16:43');
INSERT INTO `behavior_log` VALUES (217, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer aa525b49-ec90-4c52-9716-0aedf9a6c35f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750400577079\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58346\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 14:22:57', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 14:22:58', NULL, NULL, '2025-06-20 14:22:58');
INSERT INTO `behavior_log` VALUES (218, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750400582400\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58345\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 14:23:02', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:23:03', NULL, NULL, '2025-06-20 14:23:03');
INSERT INTO `behavior_log` VALUES (219, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 6481e9e4-cefd-4749-8a30-120356f9bf92\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750400632198\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58431\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 14:23:52', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 14:23:53', NULL, NULL, '2025-06-20 14:23:53');
INSERT INTO `behavior_log` VALUES (220, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer bfec768f-8144-4e21-87df-da244be3c668\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750403405798\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:56379\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:10:06', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:10:07', NULL, NULL, '2025-06-20 15:10:07');
INSERT INTO `behavior_log` VALUES (221, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer def3671f-e178-448f-bc21-aa33c43ec79e\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404587793\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:64903\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:29:48', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:29:49', NULL, NULL, '2025-06-20 15:29:49');
INSERT INTO `behavior_log` VALUES (222, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404614947\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:64903\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:30:14', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:30:15', NULL, NULL, '2025-06-20 15:30:15');
INSERT INTO `behavior_log` VALUES (223, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer d9511e04-4a14-4d36-8f5f-9851ace83cb8\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404786058\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:33:06', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:33:07', NULL, NULL, '2025-06-20 15:33:07');
INSERT INTO `behavior_log` VALUES (224, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404790226\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:33:10', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:33:10', NULL, NULL, '2025-06-20 15:33:10');
INSERT INTO `behavior_log` VALUES (225, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404824658\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:33:44', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:33:45', NULL, NULL, '2025-06-20 15:33:45');
INSERT INTO `behavior_log` VALUES (226, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404851984\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:34:11', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:34:12', NULL, NULL, '2025-06-20 15:34:12');
INSERT INTO `behavior_log` VALUES (227, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404859684\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:34:19', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:34:31', NULL, NULL, '2025-06-20 15:34:31');
INSERT INTO `behavior_log` VALUES (228, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404882102\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:34:42', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:34:45', NULL, NULL, '2025-06-20 15:34:45');
INSERT INTO `behavior_log` VALUES (229, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750404895466\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:65336\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:34:55', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:34:56', NULL, NULL, '2025-06-20 15:34:56');
INSERT INTO `behavior_log` VALUES (230, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405043044\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:49824\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:37:23', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:37:24', NULL, NULL, '2025-06-20 15:37:24');
INSERT INTO `behavior_log` VALUES (231, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"mobile\":\"19102344811\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405061508\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:49824\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 15:37:41', '/login/loginByMobile', 'POST', 'FAIL', NULL, NULL, NULL, NULL, 0, NULL, NULL, '2025-06-20 15:37:42', NULL, NULL, '2025-06-20 15:37:42');
INSERT INTO `behavior_log` VALUES (232, NULL, NULL, 'PC', '19102344811', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer f0a9e11c-fdef-4ba6-8609-b46d0deef079\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405348685\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:50303\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:42:28', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190002', '李庆', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:42:29', NULL, NULL, '2025-06-20 15:42:29');
INSERT INTO `behavior_log` VALUES (233, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 8b3aed86-f00c-4317-9468-6394e9b2d528\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405375031\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:50302\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:42:55', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:42:56', NULL, NULL, '2025-06-20 15:42:56');
INSERT INTO `behavior_log` VALUES (234, NULL, NULL, 'PC', 'R001300', NULL, NULL, '{\"roleId\":\"R001300\",\"permissionIds\":[\"PC_INTENT_ORDER_MANAGE_PAGE\",\"PC_CONTRACT_MANAGE_PAGE\",\"PC_DELIVER_MANAGE_PAGE\",\"PC_INVOICE_MANAGE_PAGE\",\"PC_REQUIRE_ORDER_MANAGE_PAGE\",\"PC_CUSTOMER_MANAGE_PAGE\",\"PC_LOG_MANAGE_PAGE\",\"APP_MY_INTENT_ORDER_BTN\",\"APP_INTENT_ORDER_CREATE_BTN\",\"APP_INTENT_ORDER_DELETE_BTN\",\"APP_INTENT_ORDER_CANCEL_BTN\",\"APP_INTENT_ORDER_CONFIRM_BTN\",\"APP_INTENT_ORDER_COPY_BTN\",\"APP_MY_CONTRACT_BTN\",\"APP_CONTRACT_VIEW_CONTRACT_FILE_BTN\",\"APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN\",\"APP_MY_DELIVER_BTN\",\"APP_DELIVER_CREATE_BTN\",\"APP_DELIVER_DELETE_BTN\",\"APP_DELIVER_CANCEL_BTN\",\"APP_DELIVER_CONFIRM_BTN\",\"APP_MY_REQUIRE_ORDER_BTN\",\"APP_REQUIRE_ORDER_CANCEL_BTN\",\"APP_REQUIRE_ORDER_EDIT_BTN\",\"APP_REQUIRE_ORDER_CONFIRM_BTN\",\"APP_REQUIRE_ORDER_ALLOCATION_BTN\",\"APP_INVOICE_CREATE\",\"APP_INVOICE\",\"APP_INVOICE_QUERY\",\"APP_INVOICE_DEL\",\"APP_MY_QUICK_ORDER_BTN\",\"APP_QUICK_ORDER_CREATE_BTN\",\"APP_QUICK_ORDER_DELETE_BTN\",\"APP_QUICK_ORDER_CANCEL_BTN\",\"APP_QUICK_ORDER_CONFIRM_BTN\",\"APP_QUICK_ORDER_COPY_BTN\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-20 15:45:09', '/role/rolePerms', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:45:15', NULL, NULL, '2025-06-20 15:45:15');
INSERT INTO `behavior_log` VALUES (235, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer d6ad9bf4-31fd-41bd-897b-ba47cb437018\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405517509\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:50614\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:45:17', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:45:18', NULL, NULL, '2025-06-20 15:45:18');
INSERT INTO `behavior_log` VALUES (236, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 554d3f68-79ce-41f0-97bf-d6251610987d\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405684950\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:50614\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:48:05', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:48:06', NULL, NULL, '2025-06-20 15:48:06');
INSERT INTO `behavior_log` VALUES (237, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer b53aa861-c6de-4927-b4bf-93206405396a\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750405902147\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:51207\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 15:51:42', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 15:51:43', NULL, NULL, '2025-06-20 15:51:43');
INSERT INTO `behavior_log` VALUES (238, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 2820c134-7ea3-43f9-a03f-f71e6d86b636\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750406912788\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:52805\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:08:33', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:08:33', NULL, NULL, '2025-06-20 16:08:33');
INSERT INTO `behavior_log` VALUES (239, NULL, NULL, 'PC', 'R001300', NULL, NULL, '{\"roleId\":\"R001300\",\"permissionIds\":[\"PC_INTENT_ORDER_MANAGE_PAGE\",\"PC_CONTRACT_MANAGE_PAGE\",\"PC_DELIVER_MANAGE_PAGE\",\"PC_CUSTOMER_MANAGE_PAGE\",\"PC_REQUIRE_ORDER_MANAGE_PAGE\",\"PC_LOG_MANAGE_PAGE\",\"PC_INVOICE_MANAGE_PAGE\",\"PC_DELIVER_LIST_PAGE\",\"PC_DELIVER_APPROVE_PAGE\",\"APP_MY_INTENT_ORDER_BTN\",\"APP_INTENT_ORDER_CREATE_BTN\",\"APP_INTENT_ORDER_DELETE_BTN\",\"APP_INTENT_ORDER_CANCEL_BTN\",\"APP_INTENT_ORDER_CONFIRM_BTN\",\"APP_INTENT_ORDER_COPY_BTN\",\"APP_MY_CONTRACT_BTN\",\"APP_CONTRACT_VIEW_CONTRACT_FILE_BTN\",\"APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN\",\"APP_MY_DELIVER_BTN\",\"APP_DELIVER_CREATE_BTN\",\"APP_DELIVER_DELETE_BTN\",\"APP_DELIVER_CANCEL_BTN\",\"APP_DELIVER_CONFIRM_BTN\",\"APP_MY_REQUIRE_ORDER_BTN\",\"APP_REQUIRE_ORDER_CANCEL_BTN\",\"APP_REQUIRE_ORDER_EDIT_BTN\",\"APP_REQUIRE_ORDER_CONFIRM_BTN\",\"APP_REQUIRE_ORDER_ALLOCATION_BTN\",\"APP_INVOICE_CREATE\",\"APP_INVOICE\",\"APP_INVOICE_QUERY\",\"APP_INVOICE_DEL\",\"APP_MY_QUICK_ORDER_BTN\",\"APP_QUICK_ORDER_CREATE_BTN\",\"APP_QUICK_ORDER_DELETE_BTN\",\"APP_QUICK_ORDER_CANCEL_BTN\",\"APP_QUICK_ORDER_CONFIRM_BTN\",\"APP_QUICK_ORDER_COPY_BTN\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-20 16:08:55', '/role/rolePerms', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:09:01', NULL, NULL, '2025-06-20 16:09:01');
INSERT INTO `behavior_log` VALUES (240, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer cc770e9a-e518-4473-9db1-d50d7da9e3e6\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750406943945\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:52851\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:09:04', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:09:05', NULL, NULL, '2025-06-20 16:09:05');
INSERT INTO `behavior_log` VALUES (241, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 8e91ed19-9740-4449-b0ac-f5a63cfb22ee\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750406970779\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:52806\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:09:31', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:09:32', NULL, NULL, '2025-06-20 16:09:32');
INSERT INTO `behavior_log` VALUES (242, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 9b43f3a3-9540-4f68-91f4-c8fd2b4ceb3f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750407413522\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:53537\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:16:53', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:16:54', NULL, NULL, '2025-06-20 16:16:54');
INSERT INTO `behavior_log` VALUES (243, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 9a2a5c48-f812-406d-8f61-a0ca8a8b3e13\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750407652282\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54042\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:20:52', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:20:53', NULL, NULL, '2025-06-20 16:20:53');
INSERT INTO `behavior_log` VALUES (244, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 4b97fb4d-9274-4433-85b4-338de0ca4fd2\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750407676326\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:54248\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:21:16', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:21:17', NULL, NULL, '2025-06-20 16:21:17');
INSERT INTO `behavior_log` VALUES (245, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 5a6e40dc-4108-40bc-bdb1-7b5a155fb73f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750409798733\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58521\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 16:56:38', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-20 16:56:39', NULL, NULL, '2025-06-20 16:56:39');
INSERT INTO `behavior_log` VALUES (246, NULL, NULL, 'PC', '18215644525', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 81a1b0cc-7b03-4518-94a1-42a03a98a39c\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750410519338\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:59067\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 17:08:39', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-20 17:08:40', NULL, NULL, '2025-06-20 17:08:40');
INSERT INTO `behavior_log` VALUES (247, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750410526925\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:59099\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-20 17:08:46', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 17:08:54', NULL, NULL, '2025-06-20 17:08:54');
INSERT INTO `behavior_log` VALUES (248, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer bd0d4b53-322f-48e1-8c88-492e2c10eb25\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750411070410\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:59961\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 17:17:50', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-20 17:17:52', NULL, NULL, '2025-06-20 17:17:52');
INSERT INTO `behavior_log` VALUES (249, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer a7c96a80-350a-4a8f-86cc-3b3188274f78\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://localhost:8080\"],\"referer\":[\"http://localhost:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750413604697\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63376\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-20 18:00:04', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202401010002', '184运营', 'INTERNAL', 0, NULL, NULL, '2025-06-20 18:00:05', NULL, NULL, '2025-06-20 18:00:05');
INSERT INTO `behavior_log` VALUES (250, NULL, NULL, 'PC', 'DRE-20250620-000014', NULL, NULL, '{\"deliverNo\":\"DRE-20250620-000014\",\"userId\":\"IU202506190001\",\"userName\":null,\"userEmail\":\"<EMAIL>\",\"deliverContractRows\":[{\"id\":\"5\",\"sfContractId\":\"test_sf_contract_id_1\",\"erpOrderTypeId\":\"ET005\"}],\"contractList\":[{\"sfContractNo\":\"test_sf_contract_no_3\",\"approvalType\":\"Normal shipping\",\"approvalFiles\":[]}]}', '2025-06-23 10:11:11', '/delivery/delivery/ops/confirm', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 10:11:21', NULL, NULL, '2025-06-23 10:11:21');
INSERT INTO `behavior_log` VALUES (251, NULL, NULL, 'PC', '18215644525', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 1f2ce079-0134-4b00-a575-9c901b95d5d8\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750646897215\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:57776\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 10:48:17', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 10:48:19', NULL, NULL, '2025-06-23 10:48:19');
INSERT INTO `behavior_log` VALUES (252, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750646905346\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:57777\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 10:48:25', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 10:48:26', NULL, NULL, '2025-06-23 10:48:26');
INSERT INTO `behavior_log` VALUES (253, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 1a691b6d-f8de-483e-943b-884e7d1bd35d\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750647009712\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:57777\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 10:50:10', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 10:50:11', NULL, NULL, '2025-06-23 10:50:11');
INSERT INTO `behavior_log` VALUES (254, NULL, NULL, 'PC', '15730329749', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer bac48ebb-4c74-4a93-a5da-9baccdf3919e\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750647309190\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58392\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 10:55:09', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190005', '刘佳', 'INTERNAL', 0, NULL, NULL, '2025-06-23 10:55:10', NULL, NULL, '2025-06-23 10:55:10');
INSERT INTO `behavior_log` VALUES (255, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750647323001\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58393\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 10:55:23', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 10:55:24', NULL, NULL, '2025-06-23 10:55:24');
INSERT INTO `behavior_log` VALUES (256, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer e8cf93c1-d126-41a2-b74d-4866abc3292d\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750647344628\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58417\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 10:55:44', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 10:55:45', NULL, NULL, '2025-06-23 10:55:45');
INSERT INTO `behavior_log` VALUES (257, NULL, NULL, 'PC', 'O000001', NULL, NULL, '{\"organizationCode\":null,\"organizationName\":\"分布式苏沪皖区\",\"organizationType\":\"SALES\",\"organizationLevel\":4,\"description\":null,\"parentOrganizationCode\":\"TRINA_CN_SALES\",\"areaCodes\":null,\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-23 14:26:06', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:26:08', NULL, NULL, '2025-06-23 14:26:08');
INSERT INTO `behavior_log` VALUES (258, NULL, NULL, 'PC', 'O000001', NULL, NULL, '{\"organizationCode\":\"O000001\",\"organizationName\":\"分布式苏沪皖区\",\"organizationType\":\"SERVICE\",\"organizationLevel\":4,\"description\":null,\"parentOrganizationCode\":\"TRINA_CN_SALES\",\"areaCodes\":[],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-23 14:26:32', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:26:33', NULL, NULL, '2025-06-23 14:26:33');
INSERT INTO `behavior_log` VALUES (259, NULL, NULL, 'PC', 'O000001', NULL, NULL, '{\"organizationCode\":\"O000001\",\"organizationName\":null,\"organizationType\":null,\"organizationLevel\":null,\"description\":null,\"parentOrganizationCode\":null,\"areaCodes\":null,\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-23 14:26:36', '/organization/delete', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:26:37', NULL, NULL, '2025-06-23 14:26:37');
INSERT INTO `behavior_log` VALUES (260, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506230001\",\"roleIds\":[\"R002200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"LL\"}', '2025-06-23 14:30:10', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:30:11', NULL, NULL, '2025-06-23 14:30:11');
INSERT INTO `behavior_log` VALUES (261, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":\"123456\",\"streetCode\":null,\"streetName\":\"312\",\"subordinateGroup\":\"AAA\",\"annualTurnover\":null,\"contactUserId\":\"EU202506190008\",\"contactName\":\"罗蓉\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"************\",\"mainBusiness\":\"O&M\",\"mainBrand\":\"KIADD\",\"totalAnnualSales\":\"100000\",\"percentage\":\"10%\",\"bankName\":\"工商银行\",\"bankId\":\"631*******321\",\"applyTime\":\"2024-01-01\",\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-23 14:30:52', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:30:55', NULL, NULL, '2025-06-23 14:30:55');
INSERT INTO `behavior_log` VALUES (262, NULL, NULL, 'PC', '6', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"6\",\"admissionApplying\":false,\"industry\":\"Environmental\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG00000004ASYAY\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"35\",\"provinceName\":\"福建\",\"cityCode\":\"350200\",\"cityName\":\"厦门市\",\"postalCode\":\"214561\",\"streetCode\":null,\"streetName\":\"天街\",\"subordinateGroup\":null,\"annualTurnover\":null,\"contactUserId\":\"EU202506160003\",\"contactName\":\"luolan\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"***********\",\"mainBusiness\":\"Investors\",\"mainBrand\":null,\"totalAnnualSales\":null,\"percentage\":null,\"bankName\":\"招商银行\",\"bankId\":\"\",\"applyTime\":null,\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"LL\",\"currentUserCode\":\"145337\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-23 14:39:04', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', 'LL', 'INTERNAL', 0, NULL, NULL, '2025-06-23 14:39:06', NULL, NULL, '2025-06-23 14:39:06');
INSERT INTO `behavior_log` VALUES (263, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750661287274\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62588\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 14:48:07', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160001', '周巧', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 14:48:08', NULL, NULL, '2025-06-23 14:48:08');
INSERT INTO `behavior_log` VALUES (264, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 2c9ab86c-6a9f-4544-909f-b658a05719ef\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750661425656\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62857\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 14:50:25', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160001', '周巧', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 14:50:26', NULL, NULL, '2025-06-23 14:50:26');
INSERT INTO `behavior_log` VALUES (265, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750661430082\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62858\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 14:50:30', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 14:50:31', NULL, NULL, '2025-06-23 14:50:31');
INSERT INTO `behavior_log` VALUES (266, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 662054d4-b4a4-43de-9dc9-6a7c0010a46f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750661707439\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63323\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 14:55:07', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 14:55:08', NULL, NULL, '2025-06-23 14:55:08');
INSERT INTO `behavior_log` VALUES (267, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750661772459\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63322\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 14:56:12', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 14:56:13', NULL, NULL, '2025-06-23 14:56:13');
INSERT INTO `behavior_log` VALUES (268, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer cef85427-0ff2-4542-8fd3-ce4ad752592b\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750662189411\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:49197\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 15:03:09', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:03:10', NULL, NULL, '2025-06-23 15:03:10');
INSERT INTO `behavior_log` VALUES (269, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750662195044\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:49197\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 15:03:15', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:03:16', NULL, NULL, '2025-06-23 15:03:16');
INSERT INTO `behavior_log` VALUES (270, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750663057417\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:51696\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 15:17:37', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:17:38', NULL, NULL, '2025-06-23 15:17:38');
INSERT INTO `behavior_log` VALUES (271, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 47c64917-6e32-408b-ad01-6d3fa5ab574f\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750663168656\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:51949\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 15:19:28', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:19:29', NULL, NULL, '2025-06-23 15:19:29');
INSERT INTO `behavior_log` VALUES (272, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750663178825\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:51949\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 15:19:38', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:19:40', NULL, NULL, '2025-06-23 15:19:40');
INSERT INTO `behavior_log` VALUES (273, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":\"6\",\"enterpriseName\":\"厦门BB新能源有限责任公司\",\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":100,\"quantityW\":\"100\",\"requireOrderDescription\":null,\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160003\",\"currentUserName\":\"罗兰\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-23 15:28:14', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:28:16', NULL, NULL, '2025-06-23 15:28:16');
INSERT INTO `behavior_log` VALUES (274, NULL, NULL, 'PC', 'null', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":\"6\",\"enterpriseName\":\"厦门BB新能源有限责任公司\",\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":100,\"quantityW\":\"100\",\"requireOrderDescription\":null,\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160003\",\"currentUserName\":\"罗兰\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-23 15:30:20', '/pc-customer/requireOrder/submit', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:30:21', NULL, NULL, '2025-06-23 15:30:21');
INSERT INTO `behavior_log` VALUES (275, NULL, NULL, 'PC', 'REQ-20250623-000003', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":\"6\",\"enterpriseName\":\"厦门BB新能源有限责任公司\",\"installProvinceCode\":\"50\",\"installProvinceName\":\"重庆\",\"installCityCode\":\"500100\",\"installCityName\":\"重庆市\",\"quantityP\":100,\"quantityW\":\"100\",\"requireOrderDescription\":null,\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160003\",\"currentUserName\":\"罗兰\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-23 15:33:38', '/pc-customer/requireOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 15:33:39', NULL, NULL, '2025-06-23 15:33:39');
INSERT INTO `behavior_log` VALUES (276, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:22:09', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:22:10', NULL, NULL, '2025-06-23 16:22:10');
INSERT INTO `behavior_log` VALUES (277, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:23:25', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:23:26', NULL, NULL, '2025-06-23 16:23:26');
INSERT INTO `behavior_log` VALUES (278, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:23:40', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:23:41', NULL, NULL, '2025-06-23 16:23:41');
INSERT INTO `behavior_log` VALUES (279, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:24:08', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:24:09', NULL, NULL, '2025-06-23 16:24:09');
INSERT INTO `behavior_log` VALUES (280, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:26:42', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:27:04', NULL, NULL, '2025-06-23 16:27:04');
INSERT INTO `behavior_log` VALUES (281, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"4\",\"productName\":\"工商储-产品2\",\"power\":\"4\",\"quantityP\":\"100\",\"quantityW\":400,\"guidePrice\":3,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.0004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":1200,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"50\",\"area\":\"500100\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190007\",\"salesInternalUserName\":\"陈秦\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Environmental protection\",\"industryAttributesText\":\"能源/化工/环保|环保\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|重庆|重庆市\",\"efc\":false,\"capitalEnterpriseId\":\"\",\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:28:23', '/pc-customer/intentOrder/save', 'POST', 'FAIL', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:28:44', NULL, NULL, '2025-06-23 16:28:44');
INSERT INTO `behavior_log` VALUES (282, NULL, NULL, 'PC', 'OPP-20250623-000001', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"2\",\"productName\":\"组件-产品2\",\"power\":\"2\",\"quantityP\":\"20\",\"quantityW\":40,\"guidePrice\":2,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.00004,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":80,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"34\",\"area\":\"340800\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|New energy\",\"industryAttributesText\":\"能源/化工/环保|新能源\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|安徽|安庆市\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 16:32:27', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:32:30', NULL, NULL, '2025-06-23 16:32:30');
INSERT INTO `behavior_log` VALUES (283, NULL, NULL, 'PC', 'OPP-20250623-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250623-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506160003\",\"userName\":\"罗兰\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-23 16:32:52', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:32:53', NULL, NULL, '2025-06-23 16:32:53');
INSERT INTO `behavior_log` VALUES (284, NULL, NULL, 'PC', '18215644525', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 77c75a33-9f4c-434c-9032-5417c0d588c5\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7\"],\"requesttime\":[\"1750667745550\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58025\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 16:35:45', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 16:35:46', NULL, NULL, '2025-06-23 16:35:46');
INSERT INTO `behavior_log` VALUES (285, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7\"],\"requesttime\":[\"1750667750506\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58027\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 16:35:50', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:35:52', NULL, NULL, '2025-06-23 16:35:52');
INSERT INTO `behavior_log` VALUES (286, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer f1ae133c-279b-4187-8126-d9e7742222d0\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7\"],\"requesttime\":[\"1750668378334\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58378\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 16:46:18', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:46:19', NULL, NULL, '2025-06-23 16:46:19');
INSERT INTO `behavior_log` VALUES (287, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750668529857\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:58483\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 16:48:49', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506190008', '罗蓉', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 16:48:51', NULL, NULL, '2025-06-23 16:48:51');
INSERT INTO `behavior_log` VALUES (288, NULL, NULL, 'PC', 'AD20250623000072', NULL, NULL, '{\"enterpriseId\":\"6\",\"recipientName\":\"张三\",\"recipientPhone\":\"12345677777\",\"provinceCode\":\"11\",\"cityCode\":\"110100\",\"districtCode\":null,\"addressDetail\":\"朝阳区3333\",\"addressType\":\"RECEIVE\",\"isDefault\":1,\"loginUserId\":\"EU202506160003\",\"loginUserName\":\"罗兰\"}', '2025-06-23 17:02:51', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:02:52', NULL, NULL, '2025-06-23 17:02:52');
INSERT INTO `behavior_log` VALUES (289, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer a3150e2e-a905-4274-af4e-597cfa901d70\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750669793990\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62951\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 17:09:54', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:09:55', NULL, NULL, '2025-06-23 17:09:55');
INSERT INTO `behavior_log` VALUES (290, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750670000281\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:63231\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-23 17:13:20', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:13:21', NULL, NULL, '2025-06-23 17:13:21');
INSERT INTO `behavior_log` VALUES (291, NULL, NULL, 'PC', 'REQ-20250623-000004', NULL, NULL, '{\"fileList\":[],\"enterpriseId\":\"6\",\"enterpriseName\":\"厦门BB新能源有限责任公司\",\"installProvinceCode\":\"35\",\"installProvinceName\":\"福建\",\"installCityCode\":\"350200\",\"installCityName\":\"厦门市\",\"quantityP\":50000,\"quantityW\":\"2000\",\"requireOrderDescription\":null,\"opportunitySegment\":\"C&I\",\"currentUserId\":\"EU202506160003\",\"currentUserName\":\"罗兰\",\"currentUserType\":\"EXTERNAL\",\"requireType\":null,\"requireProduct\":null,\"quantity\":null,\"electricity\":null}', '2025-06-23 17:14:40', '/pc-customer/requireOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:14:42', NULL, NULL, '2025-06-23 17:14:42');
INSERT INTO `behavior_log` VALUES (292, NULL, NULL, 'PC', 'OPP-20250623-000002', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"1\",\"quantityP\":\"1000\",\"quantityW\":1000,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.001,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":11000,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Module\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Module\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"35\",\"area\":\"350200\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-30\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|New energy\",\"industryAttributesText\":\"能源/化工/环保|新能源\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506160003\",\"createdName\":\"罗兰\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|福建|厦门市\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-23 17:15:29', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:15:31', NULL, NULL, '2025-06-23 17:15:31');
INSERT INTO `behavior_log` VALUES (293, NULL, NULL, 'PC', 'OPP-20250623-000002', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250623-000002\",\"intentOrderStatus\":null,\"userId\":\"EU202506160003\",\"userName\":\"罗兰\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-23 17:15:34', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:15:35', NULL, NULL, '2025-06-23 17:15:35');
INSERT INTO `behavior_log` VALUES (294, NULL, NULL, 'PC', 'AD20250623000073', NULL, NULL, '{\"enterpriseId\":\"5\",\"recipientName\":\"张三\",\"recipientPhone\":\"12321232322\",\"provinceCode\":\"11\",\"cityCode\":\"110100\",\"districtCode\":null,\"addressDetail\":\"朝阳区\",\"addressType\":\"RECEIVE\",\"isDefault\":1,\"loginUserId\":\"EU202506160003\",\"loginUserName\":\"罗兰\"}', '2025-06-23 17:16:29', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:16:30', NULL, NULL, '2025-06-23 17:16:30');
INSERT INTO `behavior_log` VALUES (295, NULL, NULL, 'PC', 'DRE-20250623-000001', NULL, NULL, '{\"deliverNo\":null,\"deliverMethod\":\"DDP\",\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":null,\"addressId\":\"AD20250623000073\",\"comment\":\"\",\"contact\":null,\"contracts\":[{\"contractId\":\"test_ci_5\",\"contractItems\":[{\"contractId\":\"test_ci_5\",\"contractBusinessItemId\":\"test_cbii_5\",\"productId\":\"1\",\"quantity\":200,\"power\":595}]}],\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"orderType\":1,\"userId\":\"EU202506160003\",\"userName\":\"罗兰\"}', '2025-06-23 17:16:46', '/pc-customer/delivery/submit/detail', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:16:50', NULL, NULL, '2025-06-23 17:16:50');
INSERT INTO `behavior_log` VALUES (296, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 0a655f2f-dab6-4e17-9c1e-4e0adddb2970\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750670667293\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:64703\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 17:24:27', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506160003', '罗兰', 'EXTERNAL', 0, NULL, NULL, '2025-06-23 17:24:28', NULL, NULL, '2025-06-23 17:24:28');
INSERT INTO `behavior_log` VALUES (297, NULL, NULL, 'PC', 'O000011', NULL, NULL, '{\"organizationCode\":null,\"organizationName\":\"分布式京津冀\",\"organizationType\":\"SALES\",\"organizationLevel\":4,\"description\":null,\"parentOrganizationCode\":\"TRINA_CN_SALES\",\"areaCodes\":null,\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-23 17:26:15', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 17:26:16', NULL, NULL, '2025-06-23 17:26:16');
INSERT INTO `behavior_log` VALUES (298, NULL, NULL, 'PC', 'O000011', NULL, NULL, '{\"organizationCode\":\"O000011\",\"organizationName\":null,\"organizationType\":null,\"organizationLevel\":null,\"description\":null,\"parentOrganizationCode\":null,\"areaCodes\":null,\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-23 17:26:25', '/organization/delete', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 17:26:26', NULL, NULL, '2025-06-23 17:26:26');
INSERT INTO `behavior_log` VALUES (299, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190003\",\"roleIds\":[\"R000501\",\"R001600\"],\"updateBy\":\"IU202506190001\",\"updateName\":\"陈文见\"}', '2025-06-23 17:26:50', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 17:26:51', NULL, NULL, '2025-06-23 17:26:51');
INSERT INTO `behavior_log` VALUES (300, NULL, NULL, 'PC', '18215644525', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 7849348c-2a6e-4054-a16d-6d47fbe11bf8\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750671454115\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:50268\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-23 17:37:34', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506190001', '陈文见', 'INTERNAL', 0, NULL, NULL, '2025-06-23 17:37:35', NULL, NULL, '2025-06-23 17:37:35');
INSERT INTO `behavior_log` VALUES (301, NULL, NULL, 'PC', 'IU202506190007', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190007\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"黄宇\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"\",\"mobile\":\"18723894698\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190007\",\"bizOrganizationCode\":\"TRINA_CN_SALES_DONGBEI\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_DONGBEI\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:11:19', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:11:21', NULL, NULL, '2025-06-24 17:11:21');
INSERT INTO `behavior_log` VALUES (302, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506190002\",\"roleIds\":[\"R001300\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:24:05', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:24:06', NULL, NULL, '2025-06-24 17:24:06');
INSERT INTO `behavior_log` VALUES (303, NULL, NULL, 'PC', 'IU202506190007', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190007\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"黄宇\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"\",\"mobile\":\"17725150879\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190007\",\"bizOrganizationCode\":\"TRINA_CN_SALES_HUANAN\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_DONGBEI\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:26:08', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:26:10', NULL, NULL, '2025-06-24 17:26:10');
INSERT INTO `behavior_log` VALUES (304, NULL, NULL, 'PC', 'IU202506190007', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190007\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"黄宇\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"\",\"mobile\":\"17725150879\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190007\",\"bizOrganizationCode\":\"TRINA_CN_SALES_HUANAN\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_HUANAN\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:27:13', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:27:14', NULL, NULL, '2025-06-24 17:27:14');
INSERT INTO `behavior_log` VALUES (305, NULL, NULL, 'PC', 'O000021', NULL, NULL, '{\"organizationCode\":null,\"organizationName\":\"分布式苏沪皖区\",\"organizationType\":\"SALES\",\"organizationLevel\":4,\"description\":null,\"parentOrganizationCode\":\"TRINA_CN_SALES\",\"areaCodes\":null,\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:30:12', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:30:13', NULL, NULL, '2025-06-24 17:30:13');
INSERT INTO `behavior_log` VALUES (306, NULL, NULL, 'PC', 'IU202506190009', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190009\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"刘彦麟\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JBxRYAW\",\"mobile\":\"13262772720\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190009\",\"bizOrganizationCode\":\"O000021\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"O000021\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:31:22', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:31:23', NULL, NULL, '2025-06-24 17:31:23');
INSERT INTO `behavior_log` VALUES (307, NULL, NULL, 'PC', 'IU202506130001', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506130001\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"罗蓉\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JC3tYAG\",\"mobile\":\"***********\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506130001\",\"bizOrganizationCode\":\"TRINA_CN_SALES_HUABEI\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"TRINA_CN_SALES_HUABEI\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:34:20', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:34:21', NULL, NULL, '2025-06-24 17:34:21');
INSERT INTO `behavior_log` VALUES (308, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506130001\",\"roleIds\":[\"R001200\",\"admin\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:37:41', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:37:42', NULL, NULL, '2025-06-24 17:37:42');
INSERT INTO `behavior_log` VALUES (309, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506130002\",\"roleIds\":[\"R001200\",\"admin\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:38:00', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:38:01', NULL, NULL, '2025-06-24 17:38:01');
INSERT INTO `behavior_log` VALUES (310, NULL, NULL, 'PC', 'IU202506130002', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506130002\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"陈秦\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JC0fYAG\",\"mobile\":\"18723894698\",\"salesRelations\":[],\"organizationCode\":\"TRINA_CN_SALES\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:39:12', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:39:13', NULL, NULL, '2025-06-24 17:39:13');
INSERT INTO `behavior_log` VALUES (311, NULL, NULL, 'PC', 'O000022', NULL, NULL, '{\"organizationCode\":null,\"organizationName\":\"分布式山东区\",\"organizationType\":\"SALES\",\"organizationLevel\":4,\"description\":null,\"parentOrganizationCode\":\"TRINA_CN_SALES\",\"areaCodes\":null,\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:39:51', '/organization/createOrUpdate', 'POST', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:39:51', NULL, NULL, '2025-06-24 17:39:51');
INSERT INTO `behavior_log` VALUES (312, NULL, NULL, 'PC', 'IU202506130002', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506130002\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"陈秦\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JC0fYAG\",\"mobile\":\"18723894698\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506130002\",\"bizOrganizationCode\":\"O000022\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506190001\"}],\"organizationCode\":\"O000022\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:40:37', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:40:38', NULL, NULL, '2025-06-24 17:40:38');
INSERT INTO `behavior_log` VALUES (313, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"IU202506240001\",\"roleIds\":[\"R001500\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:45:37', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:45:38', NULL, NULL, '2025-06-24 17:45:38');
INSERT INTO `behavior_log` VALUES (314, NULL, NULL, 'PC', 'IU202506240001', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506240001\",\"parentUserId\":\"IU202506190001\",\"originParentUserId\":null,\"userName\":\"李庆\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"1111\",\"mobile\":\"19102344811\",\"salesRelations\":[],\"organizationCode\":\"TRINA_CN_OPERATION\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:46:08', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:46:09', NULL, NULL, '2025-06-24 17:46:09');
INSERT INTO `behavior_log` VALUES (315, NULL, NULL, 'PC', 'IU202506190003', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190003\",\"parentUserId\":\"IU202506240001\",\"originParentUserId\":null,\"userName\":\"任洋杉\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JCFBYA4\",\"mobile\":\"***********\",\"salesRelations\":[],\"organizationCode\":\"TRINA_CN_OPERATION\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 17:52:21', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:52:22', NULL, NULL, '2025-06-24 17:52:22');
INSERT INTO `behavior_log` VALUES (316, NULL, NULL, 'PC', 'EU202506230004', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230004\",\"userName\":\"徐璐\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"12345678901\",\"oldMobile\":\"12345678901\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"7\",\"parentDealerUserId\":\"EU202506230001\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"34\",\"userProvinceName\":\"安徽\",\"userCityCode\":\"340800\",\"userCityName\":\"安庆市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 17:53:29', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:53:32', NULL, NULL, '2025-06-24 17:53:32');
INSERT INTO `behavior_log` VALUES (317, NULL, NULL, 'PC', 'EU202506230004', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230004\",\"userName\":\"徐璐\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"12345678901\",\"oldMobile\":\"12345678901\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"7\",\"parentDealerUserId\":\"EU202506230001\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"32\",\"userProvinceName\":\"江苏\",\"userCityCode\":\"320400\",\"userCityName\":\"常州市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 17:56:32', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:56:34', NULL, NULL, '2025-06-24 17:56:34');
INSERT INTO `behavior_log` VALUES (318, NULL, NULL, 'PC', 'EU202506160003', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506160003\",\"userName\":\"罗蓉\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"***********\",\"oldMobile\":\"***********\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"5\",\"parentDealerUserId\":\"\",\"authStatus\":1,\"authStatusStr\":null},{\"salesUserIdList\":[\"IU202506190007\"],\"enterpriseId\":\"6\",\"parentDealerUserId\":\"\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"50\",\"userProvinceName\":\"重庆\",\"userCityCode\":\"500100\",\"userCityName\":\"重庆市\",\"followStatus\":2,\"followFeedback\":null,\"followRemark\":\"t4\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 17:58:04', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:58:06', NULL, NULL, '2025-06-24 17:58:06');
INSERT INTO `behavior_log` VALUES (319, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160003\",\"roleIds\":[\"R002100\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 17:58:22', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:58:23', NULL, NULL, '2025-06-24 17:58:23');
INSERT INTO `behavior_log` VALUES (320, NULL, NULL, 'PC', 'EU202506190008', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506190008\",\"userName\":\"张耀华\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18122222222\",\"oldMobile\":\"18122222222\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"5\",\"parentDealerUserId\":\"EU202506160003\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"32\",\"userProvinceName\":\"江苏\",\"userCityCode\":\"320400\",\"userCityName\":\"常州市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 17:58:41', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:58:43', NULL, NULL, '2025-06-24 17:58:43');
INSERT INTO `behavior_log` VALUES (321, NULL, NULL, 'PC', 'EU202506190008', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506190008\",\"userName\":\"张耀华\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18122222222\",\"oldMobile\":\"18122222222\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"5\",\"parentDealerUserId\":\"EU202506160003\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"43\",\"userProvinceName\":\"湖南\",\"userCityCode\":\"430700\",\"userCityName\":\"常德市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 17:59:24', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 17:59:27', NULL, NULL, '2025-06-24 17:59:27');
INSERT INTO `behavior_log` VALUES (322, NULL, NULL, 'PC', 'EU202506160002', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506160002\",\"userName\":\"苏洪乙\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18758588073\",\"oldMobile\":\"18758588073\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190007\"],\"enterpriseId\":\"7\",\"parentDealerUserId\":null,\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"50\",\"userProvinceName\":\"重庆\",\"userCityCode\":\"500100\",\"userCityName\":\"重庆市\",\"followStatus\":2,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:00:51', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:00:53', NULL, NULL, '2025-06-24 18:00:53');
INSERT INTO `behavior_log` VALUES (323, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160002\",\"roleIds\":[\"externalRole\",\"R002100\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 18:01:03', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:01:04', NULL, NULL, '2025-06-24 18:01:04');
INSERT INTO `behavior_log` VALUES (324, NULL, NULL, 'PC', 'EU202506230004', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230004\",\"userName\":\"徐璐\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"12345678901\",\"oldMobile\":\"12345678901\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"7\",\"parentDealerUserId\":\"EU202506160002\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"32\",\"userProvinceName\":\"江苏\",\"userCityCode\":\"320400\",\"userCityName\":\"常州市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:01:48', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:01:50', NULL, NULL, '2025-06-24 18:01:50');
INSERT INTO `behavior_log` VALUES (325, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506230001\",\"roleIds\":[\"R002200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 18:02:07', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:02:08', NULL, NULL, '2025-06-24 18:02:08');
INSERT INTO `behavior_log` VALUES (326, NULL, NULL, 'PC', 'EU202506230001', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230001\",\"userName\":\"张荣\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18825130386\",\"oldMobile\":\"18825130386\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190007\"],\"enterpriseId\":\"7\",\"parentDealerUserId\":\"\",\"authStatus\":1,\"authStatusStr\":null},{\"salesUserIdList\":[\"IU202506190005\",\"IU202506190007\"],\"enterpriseId\":\"10\",\"parentDealerUserId\":\"EU202506160003\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"11\",\"userProvinceName\":\"北京\",\"userCityCode\":\"110100\",\"userCityName\":\"北京市\",\"followStatus\":2,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:02:58', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:03:00', NULL, NULL, '2025-06-24 18:03:00');
INSERT INTO `behavior_log` VALUES (327, NULL, NULL, 'PC', 'EU202506230001', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230001\",\"userName\":\"张荣\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18825130386\",\"oldMobile\":\"18825130386\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\",\"IU202506190007\"],\"enterpriseId\":\"10\",\"parentDealerUserId\":\"EU202506160003\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"11\",\"userProvinceName\":\"北京\",\"userCityCode\":\"110100\",\"userCityName\":\"北京市\",\"followStatus\":2,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:03:06', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:03:08', NULL, NULL, '2025-06-24 18:03:08');
INSERT INTO `behavior_log` VALUES (328, NULL, NULL, 'PC', 'EU202506230001', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506230001\",\"userName\":\"张荣\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"18825130386\",\"oldMobile\":\"18825130386\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"10\",\"parentDealerUserId\":\"EU202506160003\",\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"11\",\"userProvinceName\":\"北京\",\"userCityCode\":\"110100\",\"userCityName\":\"北京市\",\"followStatus\":2,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:03:21', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:03:23', NULL, NULL, '2025-06-24 18:03:23');
INSERT INTO `behavior_log` VALUES (329, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506190008\",\"roleIds\":[\"R002200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 18:04:15', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:04:16', NULL, NULL, '2025-06-24 18:04:16');
INSERT INTO `behavior_log` VALUES (330, NULL, NULL, 'PC', 'EU202506160001', NULL, NULL, '{\"id\":null,\"userId\":\"EU202506160001\",\"userName\":\"陈秦\",\"status\":\"ENABLE\",\"userType\":\"EXTERNAL\",\"mobile\":\"***********\",\"oldMobile\":\"***********\",\"roleIds\":null,\"dealerOrg\":[{\"salesUserIdList\":[\"IU202506190005\"],\"enterpriseId\":\"8\",\"parentDealerUserId\":null,\"authStatus\":1,\"authStatusStr\":null}],\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\",\"userProvinceCode\":\"50\",\"userProvinceName\":\"重庆\",\"userCityCode\":\"500100\",\"userCityName\":\"重庆市\",\"followStatus\":1,\"followFeedback\":null,\"followRemark\":\"\",\"customerBranch\":null,\"productClass\":null,\"isPurchasingIntention\":null,\"requestRemark\":null}', '2025-06-24 18:04:58', '/user/external-users', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:05:00', NULL, NULL, '2025-06-24 18:05:00');
INSERT INTO `behavior_log` VALUES (331, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506160001\",\"roleIds\":[\"R002100\",\"R001200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 18:05:09', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:05:10', NULL, NULL, '2025-06-24 18:05:10');
INSERT INTO `behavior_log` VALUES (332, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"userId\":\"EU202506240002\",\"roleIds\":[\"R002200\"],\"updateBy\":\"IU202506130002\",\"updateName\":\"李亮\"}', '2025-06-24 18:06:35', '/role/userRoles', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:06:36', NULL, NULL, '2025-06-24 18:06:36');
INSERT INTO `behavior_log` VALUES (333, NULL, NULL, 'PC', 'IU202506190002', NULL, NULL, '{\"id\":null,\"userId\":\"IU202506190002\",\"parentUserId\":\"IU202506130001\",\"originParentUserId\":null,\"userName\":\"周巧\",\"status\":\"ENABLE\",\"userType\":\"INTERNAL\",\"sfId\":\"005VG000000JBqzYAG\",\"mobile\":\"13452357702\",\"salesRelations\":[{\"id\":null,\"salesUserId\":\"IU202506190002\",\"bizOrganizationCode\":\"TRINA_CN_SALES_HUABEI\",\"operationUserId\":\"IU202401010002\",\"reportToUserId\":\"IU202506130001\"}],\"organizationCode\":\"TRINA_CN_SALES_HUABEI\",\"updatedBy\":\"IU202506130002\",\"updatedName\":\"李亮\"}', '2025-06-24 18:10:36', '/user/internal-user', 'PUT', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:10:37', NULL, NULL, '2025-06-24 18:10:37');
INSERT INTO `behavior_log` VALUES (334, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 426ea9e5-5dfb-4104-8969-5e6987141f65\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"en-US,en;q=0.9\"],\"requesttime\":[\"1750760046595\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:62818\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-24 18:14:06', '/logout/token', 'POST', 'SUCCESS', NULL, 'IU202506130002', '李亮', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:14:08', NULL, NULL, '2025-06-24 18:14:08');
INSERT INTO `behavior_log` VALUES (335, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":\"123456\",\"streetCode\":null,\"streetName\":\"312\",\"subordinateGroup\":\"AAA\",\"annualTurnover\":null,\"contactUserId\":\"EU202506190008\",\"contactName\":\"罗蓉\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"************\",\"mainBusiness\":\"O&M\",\"mainBrand\":\"KIADD\",\"totalAnnualSales\":\"100000\",\"percentage\":\"10%\",\"bankName\":\"工商银行\",\"bankId\":\"631*******321\",\"applyTime\":\"2024-01-01\",\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"陈秦\",\"currentUserCode\":\"700006\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-24 18:17:27', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', '陈秦', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:17:30', NULL, NULL, '2025-06-24 18:17:30');
INSERT INTO `behavior_log` VALUES (336, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":\"123456\",\"streetCode\":null,\"streetName\":\"312号\",\"subordinateGroup\":\"AAA\",\"annualTurnover\":null,\"contactUserId\":\"EU202506190008\",\"contactName\":\"罗蓉\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"************\",\"mainBusiness\":\"O&M\",\"mainBrand\":\"KIADD\",\"totalAnnualSales\":\"100000\",\"percentage\":\"10%\",\"bankName\":\"工商银行\",\"bankId\":\"631*******321\",\"applyTime\":\"2024-01-01\",\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"陈秦\",\"currentUserCode\":\"700006\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-24 18:21:13', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', '陈秦', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:21:16', NULL, NULL, '2025-06-24 18:21:16');
INSERT INTO `behavior_log` VALUES (337, NULL, NULL, 'PC', NULL, NULL, NULL, '{\"loginUserId\":\"IU202506130002\",\"salesUserIds\":null,\"enterpriseIds\":null,\"enterpriseId\":\"\",\"name\":\"\",\"taxNumber\":\"\",\"legalPersonName\":null,\"regNumber\":null,\"creditCode\":null,\"mainSalesUserName\":null,\"customerStatus\":\"\",\"customerCategory\":\"\",\"customerOrgNature\":null,\"businessLine\":null,\"customerRole\":\"\",\"bizOrganizationCode\":null,\"mdmId\":\"\"}', '2025-06-24 18:21:25', '/enterprise/listEnterpriseResPcExcel', 'POST', 'SUCCESS', NULL, 'IU202506130002', '陈秦', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:21:29', NULL, NULL, '2025-06-24 18:21:29');
INSERT INTO `behavior_log` VALUES (338, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":\"123456\",\"streetCode\":null,\"streetName\":\"312号\",\"subordinateGroup\":\"AAA\",\"annualTurnover\":null,\"contactUserId\":\"EU202506190008\",\"contactName\":\"罗蓉\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"************\",\"mainBusiness\":\"O&M\",\"mainBrand\":\"KIADD\",\"totalAnnualSales\":\"100000\",\"percentage\":\"10%\",\"bankName\":\"工商银行\",\"bankId\":\"631*******321\",\"applyTime\":\"2024-01-01\",\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"陈秦\",\"currentUserCode\":\"700006\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-24 18:42:43', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', '陈秦', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:42:45', NULL, NULL, '2025-06-24 18:42:45');
INSERT INTO `behavior_log` VALUES (339, NULL, NULL, 'PC', '5', NULL, NULL, '{\"createBusiness\":false,\"syncSaleForce\":false,\"enterpriseId\":\"5\",\"admissionApplying\":false,\"industry\":\"Technology\",\"customerOrgNature\":\"Company\",\"erpBusinessEntity\":\"a20VG000000042PYAQ\",\"transactionScale\":\"1400\",\"countryCode\":\"CHN\",\"countryName\":\"中国\",\"provinceCode\":\"32\",\"provinceName\":\"江苏\",\"cityCode\":\"320400\",\"cityName\":\"常州市\",\"postalCode\":\"123456\",\"streetCode\":null,\"streetName\":\"312号\",\"subordinateGroup\":\"AAA\",\"annualTurnover\":null,\"contactUserId\":\"EU202506190008\",\"contactName\":\"罗蓉\",\"businessLine\":\"Module\",\"contactMobile\":\"***********\",\"legalPersonMobile\":\"************\",\"mainBusiness\":\"O&M\",\"mainBrand\":\"KIADD\",\"totalAnnualSales\":\"1000000\",\"percentage\":\"10%\",\"bankName\":\"工商银行\",\"bankId\":\"631*******321\",\"applyTime\":\"2024-01-01\",\"currentUserId\":\"IU202506130002\",\"currentUserName\":\"陈秦\",\"currentUserCode\":\"700006\",\"fileDataList\":null,\"admissionDesc\":null,\"setUpAdmission\":false,\"authDTO\":null}', '2025-06-24 18:43:58', '/enterprise/modifyEnterpriseBusiness', 'POST', 'SUCCESS', NULL, 'IU202506130002', '陈秦', 'INTERNAL', 0, NULL, NULL, '2025-06-24 18:44:01', NULL, NULL, '2025-06-24 18:44:01');
INSERT INTO `behavior_log` VALUES (340, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"mobile\":\"***********\"}{\"content-length\":[\"24\"],\"language\":[\"CN\"],\"authorization\":[\"Basic QWRuYkltUkhDc0pQeVRKczZkN09VbldsOm42M0FVNUdjelN0eWtRRzhLc3RkSlJzNzJhSnpNRG1l\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"content-type\":[\"application/json\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750818270415\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:57957\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"]}', '2025-06-25 10:24:30', '/login/loginByMobile', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:24:32', NULL, NULL, '2025-06-25 10:24:32');
INSERT INTO `behavior_log` VALUES (341, NULL, NULL, 'PC', 'AD20250625000081', NULL, NULL, '{\"enterpriseId\":\"5\",\"recipientName\":\"张三\",\"recipientPhone\":\"12321232322\",\"provinceCode\":\"32\",\"cityCode\":\"320400\",\"districtCode\":null,\"addressDetail\":\"江苏常州xx区\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506240002\",\"loginUserName\":\"陈秋逸\"}', '2025-06-25 10:40:39', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:40:41', NULL, NULL, '2025-06-25 10:40:41');
INSERT INTO `behavior_log` VALUES (342, NULL, NULL, 'PC', 'AD20250625000081', NULL, NULL, '{\"addressId\":\"AD20250625000081\",\"recipientName\":\"张三\",\"recipientPhone\":\"12321232322\",\"provinceCode\":\"34\",\"cityCode\":\"340800\",\"districtCode\":null,\"addressDetail\":\"江苏常州xx区\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506240002\",\"loginUserName\":\"陈秋逸\"}', '2025-06-25 10:42:02', '/enterprise/updateEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:42:03', NULL, NULL, '2025-06-25 10:42:03');
INSERT INTO `behavior_log` VALUES (343, NULL, NULL, 'PC', 'AD20250625000081', NULL, NULL, '{\"addressId\":\"AD20250625000081\",\"recipientName\":\"张三\",\"recipientPhone\":\"12321232322\",\"provinceCode\":\"34\",\"cityCode\":\"340800\",\"districtCode\":null,\"addressDetail\":\"江苏常州xx区\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506240002\",\"loginUserName\":\"陈秋逸\"}', '2025-06-25 10:42:35', '/enterprise/updateEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:42:36', NULL, NULL, '2025-06-25 10:42:36');
INSERT INTO `behavior_log` VALUES (344, NULL, NULL, 'PC', 'AD20250625000081', NULL, NULL, '{\"addressId\":\"AD20250625000081\",\"recipientName\":\"张三\",\"recipientPhone\":\"12321232322\",\"provinceCode\":\"11\",\"cityCode\":\"110100\",\"districtCode\":null,\"addressDetail\":\"江苏常州xx区\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506240002\",\"loginUserName\":\"陈秋逸\"}', '2025-06-25 10:44:17', '/enterprise/updateEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:44:56', NULL, NULL, '2025-06-25 10:44:56');
INSERT INTO `behavior_log` VALUES (345, NULL, NULL, 'PC', 'AD20250625000081', NULL, NULL, '\"AD20250625000081\"', '2025-06-25 10:45:11', '/enterprise/deleteAddress', 'DELETE', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:45:12', NULL, NULL, '2025-06-25 10:45:12');
INSERT INTO `behavior_log` VALUES (346, NULL, NULL, 'PC', 'AD20250625000082', NULL, NULL, '{\"enterpriseId\":\"5\",\"recipientName\":\"张三\",\"recipientPhone\":\"12322212211\",\"provinceCode\":\"32\",\"cityCode\":\"320400\",\"districtCode\":null,\"addressDetail\":\"江苏常州市新北区**路*号*栋*层**室\",\"addressType\":\"BILLING\",\"isDefault\":0,\"loginUserId\":\"EU202506240002\",\"loginUserName\":\"陈秋逸\"}', '2025-06-25 10:47:13', '/enterprise/saveEnterpriseAddress', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:47:14', NULL, NULL, '2025-06-25 10:47:14');
INSERT INTO `behavior_log` VALUES (347, NULL, NULL, 'PC', 'OPP-20250625-000001', NULL, NULL, '{\"itemList\":[{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"1\",\"productName\":\"组件-产品1\",\"power\":\"1\",\"quantityP\":\"10\",\"quantityW\":10,\"guidePrice\":11,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.00001,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":110,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Module\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Module\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null},{\"intentOrderNo\":null,\"intentOrderItemNo\":null,\"groupId\":null,\"productId\":\"2\",\"productName\":\"组件-产品2\",\"power\":\"2\",\"quantityP\":\"10\",\"quantityW\":20,\"guidePrice\":2,\"busBar\":null,\"minPower\":null,\"maxPower\":null,\"moduleWidth\":null,\"moduleLength\":null,\"giftAmount\":null,\"inputType\":\"PCs/ASP\",\"estimatedDeliveryDate\":null,\"quantityMw\":0.00002,\"cableLengthPositive\":null,\"cableLengthCathode\":null,\"plugConnector\":null,\"plugConnectorQuantity\":null,\"salePrice\":null,\"guideTotalPrice\":40,\"saleTotalPrice\":0,\"productVersion\":\"1\",\"moduleType\":\"Storage\",\"itemType\":\"PRODUCT\",\"productImgUrl\":\"http://rongcloud-web.qiniudn.com/docs_demo_rongcloud_logo.png\",\"productCategory\":\"Storage\",\"createdBy\":null,\"createdName\":null,\"createdTime\":null,\"updatedBy\":null,\"updatedName\":null,\"updatedTime\":null,\"status\":null,\"opType\":null,\"netPrice\":null,\"otherCostPrice\":null,\"relationId\":null}],\"enterpriseId\":\"5\",\"enterpriseName\":\"常州AAAA科技有限公司\",\"requestedOu\":null,\"requestedOuName\":null,\"opportunitySegment\":\"C&I\",\"goodsArrivalRegion\":\"CHN\",\"goodsArrivalSubRegion\":\"34\",\"area\":\"340800\",\"estimatedDeliveryDate\":null,\"expectDeliveryDate\":\"2025-06-27\",\"opportunityCurrency\":null,\"salesInternalUserId\":\"IU202506190005\",\"salesInternalUserName\":\"刘佳\",\"externalUserPhone\":null,\"externalUserName\":null,\"externalUserId\":null,\"incoterm\":\"DDP\",\"industryAttributes\":\"Energy/Chemical/Environmental Protection|Mineral exploitation\",\"industryAttributesText\":\"能源/化工/环保|矿产开采\",\"isSurrogateOrder\":\"False\",\"remark\":\"\",\"createdBy\":\"EU202506240002\",\"createdName\":\"陈秋逸\",\"createdPhone\":\"***********\",\"updatedBy\":null,\"updatedName\":null,\"updatedPhone\":null,\"createdTime\":null,\"status\":\"UN_SUBMIT\",\"intentOrderNo\":\"\",\"fullRegion\":\"中国|安徽|安庆市\",\"efc\":false,\"capitalEnterpriseId\":null,\"capitalEnterpriseName\":\"\",\"sourceBizNo\":null,\"sourceType\":null,\"customerCategory\":\"PARTNER\",\"contractFrameId\":null,\"deliverContactor\":null,\"deliverContactPhone\":null,\"deliverAddress\":null,\"deliverMultiPower\":null,\"orderType\":null}', '2025-06-25 10:48:36', '/pc-customer/intentOrder/save', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:48:40', NULL, NULL, '2025-06-25 10:48:40');
INSERT INTO `behavior_log` VALUES (348, NULL, NULL, 'PC', 'OPP-20250625-000001', NULL, NULL, '{\"intentOrderNo\":\"OPP-20250625-000001\",\"intentOrderStatus\":null,\"userId\":\"EU202506240002\",\"userName\":\"陈秋逸\",\"userType\":\"EXTERNAL\",\"estimatedDeliveryDate\":null,\"requestedOu\":null,\"requestedOuName\":null,\"deliverMultiPower\":null}', '2025-06-25 10:48:45', '/pc-customer/intentOrder/submit', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:48:46', NULL, NULL, '2025-06-25 10:48:46');
INSERT INTO `behavior_log` VALUES (349, NULL, NULL, 'PC', '***********', NULL, NULL, '{\"language\":[\"CN\"],\"authorization\":[\"Bearer 77bb29a9-9d44-4d0e-98b1-fe4c41f9f0b3\"],\"user-agent\":[\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"],\"accept\":[\"application/json, text/plain, */*\"],\"platform\":[\"PLATFORM_PC\"],\"origin\":[\"http://*************:8080\"],\"referer\":[\"http://*************:8080/\"],\"accept-encoding\":[\"gzip, deflate\"],\"accept-language\":[\"zh-CN,zh;q=0.9\"],\"requesttime\":[\"1750820208401\"],\"x-log\":[\"false\"],\"forwarded\":[\"proto=http;host=*************;for=\\\"*************:60546\\\"\"],\"x-forwarded-for\":[\"*************\"],\"x-forwarded-proto\":[\"http\"],\"x-forwarded-prefix\":[\"/api/sharedapi\"],\"x-forwarded-port\":[\"80\"],\"x-forwarded-host\":[\"*************\"],\"host\":[\"*************:78\"],\"content-length\":[\"0\"]}', '2025-06-25 10:56:48', '/logout/token', 'POST', 'SUCCESS', NULL, 'EU202506240002', '陈秋逸', 'EXTERNAL', 0, NULL, NULL, '2025-06-25 10:56:49', NULL, NULL, '2025-06-25 10:56:49');

-- ----------------------------
-- Table structure for behavior_log_scenario
-- ----------------------------
DROP TABLE IF EXISTS `behavior_log_scenario`;
CREATE TABLE `behavior_log_scenario`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `behavior` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为',
  `behavior_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据类型',
  `biz_type_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `op_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作URL',
  `op_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作Method',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_bls_op_method_op_url`(`op_url` ASC, `op_method` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为与场景映射表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of behavior_log_scenario
-- ----------------------------

-- ----------------------------
-- Table structure for integration_data_record
-- ----------------------------
DROP TABLE IF EXISTS `integration_data_record`;
CREATE TABLE `integration_data_record`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务单号',
  `biz_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型',
  `biz_operation` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务操作',
  `integration_system` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '集成系统:SF，QYS-契约锁',
  `integration_forward` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '集成方向:OUT，IN',
  `request_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求报文 json',
  `request_time` datetime NULL DEFAULT NULL COMMENT '请求时间',
  `response_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '返回报文 json',
  `response_status` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '响应状态（SUCCESS, FAIL）',
  `response_time` datetime NULL DEFAULT NULL COMMENT '响应时间',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `is_deleted` int NULL DEFAULT NULL COMMENT '是否已删除;1：已删除 0：未删除',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人ID',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作员id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '三方交互日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of integration_data_record
-- ----------------------------
INSERT INTO `integration_data_record` VALUES (1, 'CONTRACT-2023-001', 'CONTRACT', 'SIGN', 'QYS', 'OUT', '{\"contractId\": \"CT001\", \"signer\": \"张三\"}', '2023-01-10 09:00:00', '{\"status\": \"SUCCESS\", \"signUrl\": \"https://qys.com/sign/123\"}', 'SUCCESS', '2023-01-10 09:02:30', 'system', '系统', '2023-01-10 09:00:00', 0, 'system', '系统', '2023-01-10 09:02:30', 'user001');
INSERT INTO `integration_data_record` VALUES (2, 'LOGISTICS-2023-002', 'LOGISTICS', 'SHIP', 'SF', 'OUT', '{\"orderNo\": \"LO002\", \"address\": \"上海\"}', '2023-01-11 14:30:00', '{\"error\": \"地址不完整\"}', 'FAIL', '2023-01-11 14:31:15', 'admin', '管理员', '2023-01-11 14:30:00', 0, 'admin', '管理员', '2023-01-11 14:31:15', 'user002');
INSERT INTO `integration_data_record` VALUES (3, 'QUERY-2023-003', 'CONTRACT', 'QUERY', 'QYS', 'IN', '{\"contractId\": \"CT003\"}', '2023-01-12 10:15:00', '{\"status\": \"SIGNED\", \"signDate\": \"2023-01-12\"}', 'SUCCESS', '2023-01-12 10:15:45', 'auto', '自动任务', '2023-01-12 10:15:00', 0, 'auto', '自动任务', '2023-01-12 10:15:45', 'user003');
INSERT INTO `integration_data_record` VALUES (4, 'LOGISTICS-2023-004', 'LOGISTICS', 'TRACK', 'SF', 'IN', '{\"trackNo\": \"SF123456789\"}', '2023-01-13 16:20:00', '{\"status\": \"DELIVERED\", \"time\": \"2023-01-13 15:00:00\"}', 'SUCCESS', '2023-01-13 16:20:30', 'user004', '李四', '2023-01-13 16:20:00', 0, 'user004', '李四', '2023-01-13 16:20:30', 'user004');
INSERT INTO `integration_data_record` VALUES (5, 'NOTIFY-2023-005', 'CONTRACT', 'NOTIFY', 'QYS', 'IN', '{\"contractId\": \"CT005\", \"action\": \"SIGNED\"}', '2023-01-14 11:05:00', '{\"ack\": \"OK\"}', 'SUCCESS', '2023-01-14 11:05:05', 'system', '系统', '2023-01-14 11:05:00', 0, 'system', '系统', '2023-01-14 11:05:05', 'system');
INSERT INTO `integration_data_record` VALUES (6, 'CONTRACT-2023-006', 'CONTRACT', 'SIGN', 'QYS', 'OUT', '{\"contractId\": \"CT006\", \"signer\": \"王五\"}', '2023-01-15 13:45:00', '{\"error\": \"用户未认证\"}', 'FAIL', '2023-01-15 13:46:20', 'user005', '王五', '2023-01-15 13:45:00', 0, 'user005', '王五', '2023-01-15 13:46:20', 'user005');
INSERT INTO `integration_data_record` VALUES (7, 'LOGISTICS-2023-007', 'LOGISTICS', 'CREATE', 'SF', 'OUT', '{\"orderNo\": \"LO007\", \"items\": [\"ITEM001\", \"ITEM002\"]}', '2023-01-16 09:30:00', '{\"sfNo\": \"SF987654321\", \"price\": 25.50}', 'SUCCESS', '2023-01-16 09:32:15', 'user006', '赵六', '2023-01-16 09:30:00', 0, 'user006', '赵六', '2023-01-16 09:32:15', 'user006');
INSERT INTO `integration_data_record` VALUES (8, 'TEMPLATE-2023-008', 'CONTRACT', 'DOWNLOAD', 'QYS', 'OUT', '{\"templateId\": \"TPL008\"}', '2023-01-17 15:10:00', '{\"url\": \"https://qys.com/template/TPL008.pdf\"}', 'SUCCESS', '2023-01-17 15:10:45', 'user007', '钱七', '2023-01-17 15:10:00', 0, 'user007', '钱七', '2023-01-17 15:10:45', 'user007');
INSERT INTO `integration_data_record` VALUES (9, 'LOGISTICS-2023-009', 'LOGISTICS', 'CALCULATE', 'SF', 'OUT', '{\"from\": \"上海\", \"to\": \"北京\", \"weight\": 2.5}', '2023-01-18 11:20:00', '{\"price\": 35.00, \"time\": \"2天\"}', 'SUCCESS', '2023-01-18 11:20:30', 'user008', '孙八', '2023-01-18 11:20:00', 0, 'user008', '孙八', '2023-01-18 11:20:30', 'user008');
INSERT INTO `integration_data_record` VALUES (10, 'CONTRACT-2023-010', 'CONTRACT', 'INVALID', 'QYS', 'OUT', '{\"contractId\": \"CT010\", \"reason\": \"条款变更\"}', '2023-01-19 14:00:00', '{\"status\": \"INVALIDATED\"}', 'SUCCESS', '2023-01-19 14:01:10', 'admin', '管理员', '2023-01-19 14:00:00', 0, 'admin', '管理员', '2023-01-19 14:01:10', 'user009');

-- ----------------------------
-- Table structure for mq_send
-- ----------------------------
DROP TABLE IF EXISTS `mq_send`;
CREATE TABLE `mq_send`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `topic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主题',
  `partition_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分区key',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `retry_time` datetime NOT NULL COMMENT '重试时间',
  `retry_count` bigint NOT NULL COMMENT '重试次数',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 56 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '本地可靠消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mq_send
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
