/*
 Navicat Premium Dump SQL

 Source Server         : *************_3306
 Source Server Type    : MySQL
 Source Server Version : 80027 (8.0.27)
 Source Host           : *************:3306
 Source Schema         : trinax_partner

 Target Server Type    : MySQL
 Target Server Version : 80027 (8.0.27)
 File Encoding         : 65001

 Date: 25/06/2025 11:06:06
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for behavior_log
-- ----------------------------
DROP TABLE IF EXISTS `behavior_log`;
CREATE TABLE `behavior_log`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `behavior` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为',
  `behavior_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为编码',
  `source` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '日志来源（APP、PC）',
  `biz_no` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据单号',
  `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据类型',
  `biz_type_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型编码',
  `biz_req` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '业务数据',
  `op_time` datetime NOT NULL COMMENT '操作时间',
  `op_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作URL',
  `op_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作Method',
  `op_result` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '日志状态（成功、失败、异常）',
  `op_exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '日志异常',
  `op_user` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人ID',
  `op_user_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人姓名',
  `op_user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作人类型',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of behavior_log
-- ----------------------------

-- ----------------------------
-- Table structure for behavior_log_scenario
-- ----------------------------
DROP TABLE IF EXISTS `behavior_log_scenario`;
CREATE TABLE `behavior_log_scenario`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `behavior` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户行为',
  `behavior_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `biz_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务数据类型',
  `biz_type_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `op_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作URL',
  `op_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作Method',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_bls_op_method_op_url`(`op_url` ASC, `op_method` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户行为与场景映射表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of behavior_log_scenario
-- ----------------------------

-- ----------------------------
-- Table structure for contract_business_item_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `contract_business_item_snapshot`;
CREATE TABLE `contract_business_item_snapshot`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `is_deleted` bigint NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人ID',
  `updated_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `contract_business_item_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '德勤通合同行主键ID',
  `sf_parent_item_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '原始合同行 id',
  `contract_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '德勤通合同主键ID',
  `sf_opportunity_item_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SF商机行编号',
  `plug_connector` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '端子',
  `installation` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '安装方式编码',
  `cable_length` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '线缆长度',
  `cable_length_positive` decimal(20, 2) NULL DEFAULT NULL COMMENT '正极电缆长度（M）',
  `cable_Length_cathode` decimal(20, 2) NULL DEFAULT NULL COMMENT '负极电缆长度（M）',
  `power` decimal(20, 2) NULL DEFAULT NULL COMMENT '功率',
  `expected_deliver_date` datetime NULL DEFAULT NULL COMMENT '期望交付时间',
  `quantity_p` decimal(20, 2) NULL DEFAULT NULL COMMENT '数量（片）',
  `quantity_mw` decimal(20, 2) NULL DEFAULT NULL COMMENT '数量（MW）',
  `quantity_w` decimal(20, 2) NULL DEFAULT NULL COMMENT '数量（W）',
  `net_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '净价',
  `total_additional_cost` decimal(20, 2) NULL DEFAULT NULL COMMENT '其他总成本',
  `unit_price_w` decimal(20, 2) NULL DEFAULT NULL COMMENT '销售价（每瓦）',
  `unit_price_p` decimal(20, 2) NULL DEFAULT NULL COMMENT '销售价（每片）',
  `notes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `destination_port_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '目的港ID',
  `loading_port_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '启运港ID',
  `currency` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '币种',
  `total_amount` decimal(20, 2) NULL DEFAULT NULL COMMENT '产品小计金额（含税）',
  `product_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品名称',
  `product_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品类型',
  `price_book` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '价格手册ID',
  `efc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'EFC',
  `sf_product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SF产品ID',
  `pbi_product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'PBI产品ID',
  `module_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '产品类型:组件;备件和其他',
  `item_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '行类型：PRODUCT-产品；COMPLIMENTARY - 赠品',
  `sf_contract_item_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'SF合同行ID',
  `input_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '输入方式 PCs/ASP 或者 Requirement(MW)/ASP',
  `multi_power_begin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '开始功率',
  `multi_power_end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '结束功率',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_contract_id`(`contract_id` ASC) USING BTREE,
  INDEX `idx_contract_business_item_id`(`contract_business_item_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '合同业务行快照信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of contract_business_item_snapshot
-- ----------------------------

-- ----------------------------
-- Table structure for enterprise
-- ----------------------------
DROP TABLE IF EXISTS `enterprise`;
CREATE TABLE `enterprise`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务主键ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业名称',
  `tax_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税号',
  `credit_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `detail_region` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所在地区',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `reg_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '企业状态',
  `legal_person_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '法人代表',
  `reg_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '工商注册号',
  `org_number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组织机构代码',
  `company_org_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '公司类型',
  `industry` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '行业',
  `registed_time` datetime NULL DEFAULT NULL COMMENT '注册时间',
  `reg_capital` decimal(16, 2) NULL DEFAULT NULL COMMENT '注册资本',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_e_enterprise_id`(`enterprise_id` ASC) USING BTREE,
  INDEX `enterprise_credit_code_IDX`(`credit_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '合作伙伴企业信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise
-- ----------------------------
INSERT INTO `enterprise` VALUES (5, '5', '常州AAAA科技有限公司', '91320********62R3A', '5', '中国江苏常州', NULL, NULL, NULL, NULL, NULL, NULL, '正常', '张三', '52*********5018', '21******46', '其他股份有限公司', '光伏行业', '2001-01-01 00:00:00', 70000.12);
INSERT INTO `enterprise` VALUES (6, '6', '厦门BB新能源有限责任公司', '91350********M34W', '6', '中国福建厦门', NULL, NULL, NULL, NULL, NULL, NULL, '正常', '李四', '32*********1213', '21******12', '其他股份有限公司', '光伏行业', '2011-01-01 00:00:00', 50000.12);
INSERT INTO `enterprise` VALUES (7, '7', '江苏CC新能源有限公司', '91320********3338D', '7', '中国江苏无锡', NULL, NULL, NULL, NULL, NULL, NULL, '正常', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise` VALUES (8, '8', '青岛DDD新能源有限公司', '91370********TWY9XH', '8', '中国山东青岛', NULL, NULL, NULL, NULL, NULL, NULL, '正常', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise` VALUES (9, '9', 'EEEE（厦门）新能源有限公司', '91350********GPC2C', '9', '中国福建厦门', NULL, NULL, NULL, NULL, NULL, NULL, '正常', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise` VALUES (10, '10', '北京FF集团GGGG股份有限公司', '91500********74544F', '10', '中国江苏常州', NULL, NULL, NULL, NULL, NULL, NULL, '正常', NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for enterprise_address
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_address`;
CREATE TABLE `enterprise_address`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `address_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址ID',
  `address_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址类型：BILLING-开单地址   SHIPPING-收货地址；REGIST-注册地址',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业ID',
  `sf_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SF ID',
  `recipient_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收件人名字',
  `recipient_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收件人联系方式',
  `address_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址名称',
  `country_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家编码',
  `country_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '国家名称',
  `province_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省编码',
  `province_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省名称',
  `city_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市编码',
  `city_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '市名称',
  `district_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区编码',
  `district_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区名称',
  `street_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '街道编码',
  `street_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '街道名称',
  `postal_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮政编码',
  `address_detail` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
  `full_address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '完整地址',
  `address_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址状态',
  `is_default` int NULL DEFAULT NULL COMMENT '是否为默认;1：是    0：否',
  `is_deleted` int NULL DEFAULT 0 COMMENT '是否已删除;1：已删除    0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ea_enterprise_id`(`enterprise_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_address
-- ----------------------------
INSERT INTO `enterprise_address` VALUES (1, 'AD20250617000001', 'BILLING', '1', NULL, 'yoyo', '18976542345', NULL, 'CHN', '中国', '50', NULL, '500100', NULL, NULL, NULL, NULL, '文月街', NULL, '中国nullnull文月街', '中国nullnull文月街', NULL, 1, 0, 'EU202506160003', '罗兰', '2025-06-17 14:39:24', 'IU202506190001', '陈文见', '2025-06-20 11:30:06');
INSERT INTO `enterprise_address` VALUES (2, 'AD20250617000002', 'SHIPPING', '1', NULL, 'yoyo', '18976542345', NULL, 'CHN', '中国', '50', NULL, '500100', NULL, NULL, NULL, NULL, NULL, NULL, '江北区石马河街道江山云著', '重庆重庆市江北区石马河街道江山云著', NULL, 1, 0, 'EU202506160003', '罗兰', '2025-06-17 14:40:35', 'EU202506160003', '罗兰', '2025-06-17 14:40:35');
INSERT INTO `enterprise_address` VALUES (4, 'AD20250617000004', 'BILLING', '2', NULL, 'abc', '17693847654', NULL, 'CHN', '中国', '50', NULL, '500100', NULL, NULL, NULL, NULL, NULL, NULL, '重庆市渝中区化龙桥企业天地', '重庆重庆市重庆市渝中区化龙桥企业天地', NULL, 1, 0, 'EU202506160001', '黄叶', '2025-06-17 16:36:56', 'EU202506160001', '黄叶', '2025-06-17 16:36:56');
INSERT INTO `enterprise_address` VALUES (17, 'AD20250620000061', 'SHIPPING', '5', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', NULL, '500100', NULL, NULL, NULL, NULL, NULL, NULL, '重庆市渝中区企业天地8号楼', '重庆重庆市重庆市渝中区企业天地8号楼', NULL, 0, 0, 'EU202506190008', '罗蓉', '2025-06-20 10:26:01', 'EU202506190008', '罗蓉', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (18, 'AD20250623000071', 'BILLING', '6', NULL, NULL, NULL, NULL, 'CHN', '中国', '35', '福建', '350200', '厦门市', NULL, NULL, NULL, '天街', '214561', '中国福建厦门市天街', '中国福建厦门市天街', NULL, 1, 0, 'IU202506130002', '李亮', '2025-06-17 16:36:56', 'IU202506130002', '李亮', '2025-06-23 14:39:05');
INSERT INTO `enterprise_address` VALUES (19, 'AD20250620000081', 'REGIST', '5', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '江苏', '500100', '常州市', NULL, NULL, NULL, '312号', NULL, '常州市新北区**路*号*栋*层**室', '江苏常州市新*区**路*号*栋*层**室', NULL, 0, 0, 'EU202506190008', '罗蓉', '2025-06-20 10:26:01', 'EU202506190008', '罗蓉', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (20, 'AD20250620000082', 'REGIST', '6', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '福建', '500100', '厦门市', NULL, NULL, NULL, '天街', NULL, '厦门市思明区*路*号*H', '福建厦门市思明区*路*号*H', NULL, 0, 0, 'EU202506160003', '罗兰', '2025-06-20 10:26:01', 'EU202506160003', '罗兰', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (21, 'AD20250623000072', 'SHIPPING', '6', NULL, '张三', '12345677777', NULL, 'CHN', '中国', '11', NULL, '110100', NULL, NULL, NULL, NULL, NULL, NULL, '朝阳区3333', '北京北京市朝阳区3333', NULL, 1, 0, 'EU202506160003', '罗兰', '2025-06-23 17:02:51', 'EU202506160003', '罗兰', '2025-06-23 17:02:51');
INSERT INTO `enterprise_address` VALUES (22, 'AD20250623000073', 'SHIPPING', '5', NULL, '张三', '12321232322', NULL, 'CHN', '中国', '11', NULL, '110100', NULL, NULL, NULL, NULL, NULL, NULL, '朝阳区', '北京北京市朝阳区', NULL, 1, 0, 'EU202506160003', '罗兰', '2025-06-23 17:16:29', 'EU202506160003', '罗兰', '2025-06-23 17:16:29');
INSERT INTO `enterprise_address` VALUES (23, 'AD20250620000083', 'REGIST', '7', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '江苏', '500100', '无锡市', NULL, NULL, NULL, 'xx号', NULL, '无锡市**路*号*栋*层**室', '江苏无锡市**路*号', NULL, 0, 0, 'EU202506160002', '苏洪乙', '2025-06-20 10:26:01', 'EU202506160002', '苏洪乙', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (24, 'AD20250620000084', 'REGIST', '8', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '山东', '500100', '青岛市', NULL, NULL, NULL, 'xx号', NULL, '青岛市**路*号*栋*层**室', '山东青岛市**路*号', NULL, 0, 0, 'EU202506160001', '陈秦', '2025-06-20 10:26:01', 'EU202506160001', '陈秦', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (25, 'AD20250620000085', 'REGIST', '9', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '福建', '500100', '厦门市', NULL, NULL, NULL, 'xx号', NULL, '厦门市**路*号*栋*层**室', '福建厦门市路*号*栋', NULL, 0, 0, 'EU202506190008', '罗蓉', '2025-06-20 10:26:01', 'EU202506190008', '罗蓉', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (26, 'AD20250620000086', 'REGIST', '10', NULL, '陈文见', '18215644525', NULL, 'CHN', '中国', '50', '江苏', '500100', '常州市', NULL, NULL, NULL, 'xx号', NULL, '常州市新北区**路*号*栋*层**室', '江苏常州市新*区**路*号*栋', NULL, 0, 0, 'EU202506190008', '罗蓉', '2025-06-20 10:26:01', 'EU202506190008', '罗蓉', '2025-06-20 10:26:01');
INSERT INTO `enterprise_address` VALUES (27, 'AD20250625000081', 'BILLING', '5', NULL, '张三', '12321232322', NULL, NULL, NULL, '11', NULL, '110100', NULL, NULL, NULL, NULL, NULL, NULL, '江苏常州xx区', '北京北京市江苏常州xx区', NULL, 0, 1, 'EU202506240002', '陈秋逸', '2025-06-25 10:40:40', 'EU202506240002', '陈秋逸', '2025-06-25 10:45:11');
INSERT INTO `enterprise_address` VALUES (28, '****************', 'BILLING', '5', NULL, '张三', '***********', NULL, NULL, NULL, '32', NULL, '320400', NULL, NULL, NULL, NULL, NULL, NULL, '江苏常州市新北区**路*号*栋*层**室', '江苏常州市江苏常州市新北区**路*号*栋*层**室', NULL, 0, 0, 'EU202506240002', '陈秋逸', '2025-06-25 10:47:13', 'EU202506240002', '陈秋逸', '2025-06-25 10:47:13');

-- ----------------------------
-- Table structure for enterprise_address_account_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_address_account_relation`;
CREATE TABLE `enterprise_address_account_relation`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `address_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业地址ID',
  `marketing_account_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '营销账套ID',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除    0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_address_account_relation
-- ----------------------------

-- ----------------------------
-- Table structure for enterprise_biz
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_biz`;
CREATE TABLE `enterprise_biz`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业id',
  `sf_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SF 客户ID',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户状态',
  `oa_customer_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户 id(oa系统)',
  `customer_category` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户类别',
  `customer_org_nature` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户组织性质',
  `business_line` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务线',
  `customer_role` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户角色',
  `is_region_ka` int NULL DEFAULT NULL COMMENT '是否区域KA',
  `customer_level` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户等级',
  `intercompany_order_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '是否内部公司 Y:是 N:否',
  `industry` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '行业',
  `main_sales_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主销售人员ID',
  `origin_main_sales_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `main_sales_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主销售人员姓名',
  `erp_business_entity` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ERP业务实体',
  `erp_tax_rate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ERP税率',
  `currency` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '币种',
  `mdm_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'MDM id',
  `subordinate_group` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '隶属集团(最顶层母公司)',
  `source_system` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '来源系统',
  `transaction_scale` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易规模',
  `main_segment` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Main Segment',
  `main_market_segment` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Main Market Segment',
  `main_channel_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Main Channel Type',
  `customer_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户建商状态',
  `business_created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建商发起人',
  `business_created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '建商发起人名称',
  `business_created_time` datetime NULL DEFAULT NULL COMMENT '建商发起时间',
  `legal_person_mobile` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话(法人代表)',
  `contact_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系人用户 Id',
  `contact_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务联系人',
  `contact_mobile` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话(业务联系人)',
  `total_annual_sales` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '年销售总量(MW)',
  `percentage` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '德勤占比',
  `main_business` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开户行',
  `bank_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '账号',
  `apply_time` datetime NULL DEFAULT NULL COMMENT '申请加盟时间',
  `admission_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '准入用户 id',
  `admission_user_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '准入用户姓名',
  `admission_status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '准入状态',
  `admission_time` datetime NULL DEFAULT NULL COMMENT '准入时间',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `main_brand` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主营品牌',
  `annual_turnover` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '年营业额',
  `admission_desc` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_biz
-- ----------------------------
INSERT INTO `enterprise_biz` VALUES (3, '5', '001VG000000rVAZYA2', 'active', '1', 'PARTNER', 'Company', 'Module', 'O&M', 1, 'C - Other Customer', 'N', 'Technology', 'EU202506190008', 'EU202506190008', '罗蓉', 'a20VG000000042PYAQ', '13', 'CNY', '1', 'AAA', 'WEB', '1400', 'Distribution', 'C&I', 'Distribution', 'BPM Approved', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', '112121212212', 'EU202506190008', '罗蓉', '***********', '1000000', '10%', 'O&M', '工商银行', '631*******321', '2024-01-01 00:00:00', 'IU202506190001', '陈文见', 'Pending admittance', '2024-01-01 00:00:00', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', 'KIADD', '*********', 'xxxxx');
INSERT INTO `enterprise_biz` VALUES (4, '6', '001VG000000rYl9YAE', 'active', '2', 'BUSINESS', 'Company', 'Module', 'Investor', 1, 'C - Other Customer', 'N', 'Environmental', 'EU202506160003', 'EU202506160003', '罗兰', 'a20VG00000004ASYAY', '13', 'CNY', '2', 'BBB', 'WEB', '1400', 'Distribution', 'C&I', 'Distribution', 'BPM Approved', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', '***********', 'EU202506160003', '罗兰', '***********', '80000', '5%', 'Investors', '招商银行', '622*******231', '2024-01-01 00:00:00', 'IU202506190001', '陈文见', 'Pending admittance', '2025-06-23 15:01:12', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', 'IU202506190001', '陈文见', '2025-01-01 00:00:00', 'JS', '80000000', 'xxxx');
INSERT INTO `enterprise_biz` VALUES (5, '7', '001VG000000rXHDYA2', 'active', '3', 'PARTNER', 'Company', 'Module', 'O&M', 1, 'C - Other Customer', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '3', NULL, NULL, NULL, NULL, NULL, NULL, 'BPM Approved', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz` VALUES (6, '8', '001VG000000rXE3YAM', 'active', '4', 'PARTNER', 'Company', 'Module', 'O&M', 1, 'C - Other Customer', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '4', NULL, NULL, NULL, NULL, NULL, NULL, 'BPM Approved', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz` VALUES (7, '9', '001VG000000ra0XYAQ', 'active', '5', 'PARTNER', 'Company', 'Module', 'O&M', 1, 'C - Other Customer', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '5', NULL, NULL, NULL, NULL, NULL, NULL, 'BPM Approved', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz` VALUES (8, '10', '001VG000000CwJaYAP', 'active', '6', 'PARTNER', 'Company', 'Module', 'O&M', 1, 'C - Other Customer', 'N', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '6', NULL, NULL, NULL, NULL, NULL, NULL, 'BPM Approved', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for enterprise_biz_authorize
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_biz_authorize`;
CREATE TABLE `enterprise_biz_authorize`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '内容主键',
  `auth_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授权委托人',
  `auth_user_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授权委托人姓名',
  `relate_co_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联渠道商',
  `auth_year` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '授权年份',
  `auth_status` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '审核状态',
  `auth_desc` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '审批意见',
  `is_remind` int NOT NULL DEFAULT 0 COMMENT '是否已创建待办提醒;1：已提醒    0：未提醒',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '企业授权准入信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of enterprise_biz_authorize
-- ----------------------------

-- ----------------------------
-- Table structure for enterprise_biz_authorize_file_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_biz_authorize_file_relation`;
CREATE TABLE `enterprise_biz_authorize_file_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `target_id` bigint UNSIGNED NOT NULL COMMENT '关联授权信息id',
  `file_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件id主键(upload_file)',
  `business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务大类',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '企业授权准入文件关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of enterprise_biz_authorize_file_relation
-- ----------------------------

-- ----------------------------
-- Table structure for enterprise_biz_file_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_biz_file_relation`;
CREATE TABLE `enterprise_biz_file_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容主键',
  `file_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件id主键(upload_file)',
  `business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务大类',
  `sub_business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务子类',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文件关联关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_biz_file_relation
-- ----------------------------

-- ----------------------------
-- Table structure for enterprise_biz_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_biz_relation`;
CREATE TABLE `enterprise_biz_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '企业ID',
  `biz_organization_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务组织CODE',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ebr_biz_organization_code`(`biz_organization_code` ASC) USING BTREE,
  INDEX `idx_ebr_enterprise_id`(`enterprise_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '企业业务区域（战区）关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_biz_relation
-- ----------------------------
INSERT INTO `enterprise_biz_relation` VALUES (3, '5', 'TRINA_CN_SALES_HUANAN', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz_relation` VALUES (4, '6', 'TRINA_CN_SALES_HUANAN', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz_relation` VALUES (5, '7', 'TRINA_CN_SALES_HUANAN', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz_relation` VALUES (6, '8', 'TRINA_CN_SALES_HUABEI', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz_relation` VALUES (7, '9', 'TRINA_CN_SALES_HUABEI', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_biz_relation` VALUES (8, '10', 'TRINA_CN_SALES_HUABEI', 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for enterprise_header
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_header`;
CREATE TABLE `enterprise_header`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键ID',
  `header_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '抬头业务主键id',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业id',
  `address_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '企业地址id',
  `dealer_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '外部经销商用户id',
  `header_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '抬头类型',
  `bank` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开户银行',
  `bank_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '银行账号',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `eh_address_id_IDX`(`address_id` ASC) USING BTREE,
  INDEX `eh_dealer_user_id_IDX`(`dealer_user_id` ASC) USING BTREE,
  INDEX `eh_enterprise_id_IDX`(`enterprise_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '企业抬头表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_header
-- ----------------------------
INSERT INTO `enterprise_header` VALUES (4, 'EH20250620000004', '5', 'AD20250619000051', 'EU202506190008', 'institution', '工商银行', '123455675233215235', 'EU202506190008', '罗蓉', '2025-06-20 14:09:41', NULL, NULL, NULL);
INSERT INTO `enterprise_header` VALUES (5, 'EH20250623000007', '5', 'AD20250619000051', 'EU202506160003', 'institution', '工商银行', '2121321214214214', 'EU202506160003', '罗兰', '2025-06-23 16:10:21', NULL, NULL, NULL);
INSERT INTO `enterprise_header` VALUES (6, 'EH20250623000008', '6', 'AD20250623000071', 'EU202506160003', 'institution', '招商银行', '2123143423111133', 'EU202506160003', '罗兰', '2025-06-23 16:11:08', NULL, NULL, NULL);
INSERT INTO `enterprise_header` VALUES (7, 'EH20250625000010', '5', '****************', 'EU202506240002', 'institution', '工商银行', '62112121223213211', 'EU202506240002', '陈秋逸', '2025-06-25 10:47:39', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for enterprise_relation
-- ----------------------------
DROP TABLE IF EXISTS `enterprise_relation`;
CREATE TABLE `enterprise_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '企业ID',
  `enterprise_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '企业类型;CAPITAL-资方，PARTNER-生态伙伴',
  `link_enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联企业ID',
  `link_enterprise_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '关联企业类型;CAPITAL-资方，PARTNER-生态伙伴',
  `status` int NULL DEFAULT NULL COMMENT '状态：0失效，1有效',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '客户企业关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of enterprise_relation
-- ----------------------------
INSERT INTO `enterprise_relation` VALUES (1, '5', 'PARTNER', '6', 'CAPITAL', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_relation` VALUES (2, '6', 'PARTNER', '5', 'CAPITAL', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_relation` VALUES (3, '7', 'PARTNER', '8', 'CAPITAL', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_relation` VALUES (4, '8', 'PARTNER', '7', 'CAPITAL', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `enterprise_relation` VALUES (5, '9', 'PARTNER', '7', 'CAPITAL', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for erp_order_type
-- ----------------------------
DROP TABLE IF EXISTS `erp_order_type`;
CREATE TABLE `erp_order_type`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `erp_order_type_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ERP订单类型ID',
  `erp_order_type_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'ERP订单类型名称',
  `marketing_account_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营销账套ID',
  `record_type_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `ts_order_function_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `intercompany_order_type` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Y/N',
  `item_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'PRODUCT-产品；COMPLIMENTARY - 赠品',
  `is_default` int NULL DEFAULT NULL COMMENT '是否默认：0否 1是',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'ERP订单类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of erp_order_type
-- ----------------------------
INSERT INTO `erp_order_type` VALUES (1, 'ET001', '标准订单', 'a20VG000000042PYAQ', 'RT001', 'SALE', 'Y', 'PRODUCT', 1, 0, 'admin', '系统管理员', '2023-01-01 10:00:00', 'admin', '系统管理员', '2023-01-01 10:00:00');
INSERT INTO `erp_order_type` VALUES (2, 'ET002', '退货订单', 'a20VG000000042PYAQ', 'RT002', 'RETURN', 'N', 'PRODUCT', 0, 0, 'admin', '系统管理员', '2023-01-01 10:05:00', 'admin', '系统管理员', '2023-01-01 10:05:00');
INSERT INTO `erp_order_type` VALUES (3, 'ET003', '样品订单', 'a20VG000000042PYAQ', 'RT003', 'SAMPLE', 'Y', 'PRODUCT', 0, 0, 'user1', '张三', '2023-01-02 09:00:00', 'user1', '张三', '2023-01-02 09:00:00');
INSERT INTO `erp_order_type` VALUES (4, 'ET004', '内部订单', 'a20VG000000042PYAQ', 'RT004', 'INTERNAL', 'Y', 'PRODUCT', 1, 0, 'user2', '李四', '2023-01-03 14:30:00', 'user2', '李四', '2023-01-03 14:30:00');
INSERT INTO `erp_order_type` VALUES (5, 'ET005', '促销订单', 'a20VG000000042PYAQ', 'RT005', 'PROMO', 'N', 'PRODUCT', 0, 0, 'user3', '王五', '2023-01-04 16:45:00', 'user3', '王五', '2023-01-04 16:45:00');

-- ----------------------------
-- Table structure for integration_data_record
-- ----------------------------
DROP TABLE IF EXISTS `integration_data_record`;
CREATE TABLE `integration_data_record`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务单号',
  `biz_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务类型',
  `biz_operation` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务操作',
  `integration_system` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '集成系统:SF，QYS-契约锁',
  `integration_forward` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '集成方向:OUT，IN',
  `request_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求报文 json',
  `request_time` datetime NULL DEFAULT NULL COMMENT '请求时间',
  `response_data` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '返回报文 json',
  `response_status` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '响应状态（SUCCESS, FAIL）',
  `response_time` datetime NULL DEFAULT NULL COMMENT '响应时间',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人ID',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人名称',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `is_deleted` int NULL DEFAULT NULL COMMENT '是否已删除;1：已删除 0：未删除',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人ID',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人名称',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作员id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '三方交互日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of integration_data_record
-- ----------------------------

-- ----------------------------
-- Table structure for marketing_account
-- ----------------------------
DROP TABLE IF EXISTS `marketing_account`;
CREATE TABLE `marketing_account`  (
  `id` bigint UNSIGNED NOT NULL COMMENT '自增主键',
  `account_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账套ID',
  `account_ou` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '账套名称',
  `account_ou_en` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `oa_account_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务实体 code (oa系统)',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除    0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `account_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_ma_account_id`(`account_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '营销账套信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of marketing_account
-- ----------------------------
INSERT INTO `marketing_account` VALUES (2, 'a20VG000000042PYAQ', '常州TAAA有限公司', 'OU_TCZ[N]_TAAAA Co.,Ltd', NULL, 0, NULL, NULL, '2025-06-19 14:31:51', NULL, NULL, '2025-06-19 14:31:51', '常州新*区电子产业园天*路*号');
INSERT INTO `marketing_account` VALUES (3, 'a20VG00000004ASYAY', 'TABB能源(上海)有限公司', 'OU_THS(SH)_TAAAA Energy (Shanghai) Co', NULL, 0, NULL, NULL, '2025-06-19 14:31:51', NULL, NULL, '2025-06-19 14:31:51', '上海市闵*区沪青*公路*号');
INSERT INTO `marketing_account` VALUES (4, 'a20VG000000047FYAQ', 'TACC(上海)有限公司', 'OU_TSH_TAAAA Energy (Shanghai) Co.,Ltd', NULL, 0, NULL, NULL, '2025-06-19 14:31:51', NULL, NULL, '2025-06-19 14:31:51', 'TAAA(上海)有限公司地址');
INSERT INTO `marketing_account` VALUES (5, 'a20VG000000047oYAA', 'TADD(常州)科技有限公司', 'OU_TST[N]_TAAAA (Changzhou) Science & Technology Co.,L', NULL, 0, NULL, NULL, '2025-06-19 14:31:51', NULL, NULL, '2025-06-19 14:31:51', '常州新*区电子产业园天*路*号');

-- ----------------------------
-- Table structure for marketing_account_tax_relation
-- ----------------------------
DROP TABLE IF EXISTS `marketing_account_tax_relation`;
CREATE TABLE `marketing_account_tax_relation`  (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '自增主键ID',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SF税率ID',
  `account_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '营销账套主键ID',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除    0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '创建人',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建人姓名',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of marketing_account_tax_relation
-- ----------------------------

-- ----------------------------
-- Table structure for marketing_tax
-- ----------------------------
DROP TABLE IF EXISTS `marketing_tax`;
CREATE TABLE `marketing_tax`  (
  `id` bigint UNSIGNED NOT NULL COMMENT '自增主键ID',
  `tax_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税率主键ID',
  `tax_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '税率名称',
  `tax_value` int NULL DEFAULT NULL COMMENT '税率值',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除    0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '创建人',
  `created_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建人姓名',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '更新人',
  `updated_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '税率信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of marketing_tax
-- ----------------------------
INSERT INTO `marketing_tax` VALUES (1, 'a21VG000000066mYAA', 'OUT-CN-13%-专用未开价内', 13, 0, '0', '', '2025-06-19 14:36:12', '0', '', '2025-06-19 14:36:12');

-- ----------------------------
-- Table structure for mq_send
-- ----------------------------
DROP TABLE IF EXISTS `mq_send`;
CREATE TABLE `mq_send`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `topic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主题',
  `partition_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分区key',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `retry_time` datetime NOT NULL COMMENT '重试时间',
  `retry_count` bigint NOT NULL COMMENT '重试次数',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ms_retry_time`(`retry_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '本地可靠消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mq_send
-- ----------------------------

-- ----------------------------
-- Table structure for segment
-- ----------------------------
DROP TABLE IF EXISTS `segment`;
CREATE TABLE `segment`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `business_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `max_id` bigint NULL DEFAULT NULL,
  `limit_id` bigint NULL DEFAULT NULL,
  `step` bigint NULL DEFAULT NULL,
  `version` bigint NULL DEFAULT NULL,
  `create_time` datetime NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_business_type`(`business_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of segment
-- ----------------------------
INSERT INTO `segment` VALUES (1, 'EnterpriseAddressId', 90, NULL, 10, 10, '2025-06-17 14:39:24', '2025-06-25 10:40:40');
INSERT INTO `segment` VALUES (2, 'EnterpriseHeaderId', 12, 9999, 3, 5, '2025-06-17 14:42:05', '2025-06-25 10:47:38');

SET FOREIGN_KEY_CHECKS = 1;
