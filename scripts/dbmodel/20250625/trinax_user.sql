/*
 Navicat Premium Dump SQL

 Source Server         : *************_3306
 Source Server Type    : MySQL
 Source Server Version : 80027 (8.0.27)
 Source Host           : *************:3306
 Source Schema         : trinax_user

 Target Server Type    : MySQL
 Target Server Version : 80027 (8.0.27)
 File Encoding         : 65001

 Date: 25/06/2025 11:06:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for authority_app_client
-- ----------------------------
DROP TABLE IF EXISTS `authority_app_client`;
CREATE TABLE `authority_app_client`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `client_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端ID',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端访问密钥',
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'app名称',
  `app_en_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'app英文名称',
  `app_platform` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台类型: PC应用 android手机应用 ios手机应用 H5应用 小程序应用',
  `app_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'app描述',
  `status` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '状态:DISABLE-无效 ENABLE-有效',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后修改人',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '最后修改时间',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `am_client_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'am平台客户端ID',
  `am_client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'am平台访问密钥',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `appid_unique`(`client_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '应用客户端' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of authority_app_client
-- ----------------------------
INSERT INTO `authority_app_client` VALUES (1, 'AdnbImRHCsJPyTJs6d7OUnWl', 'n63AU5GczStykQG8KstdJRs72aJzMDme', '德勤通服务平台', 'authority_pc', 'PC', '权限系统应用客户端2', 'ENABLE', NULL, '2', NULL, '2023-07-08 07:03:59', 0, 'trinax', 'M0hwTejHWNupSuqPLniv');
INSERT INTO `authority_app_client` VALUES (2, 'YReHm69vMb9SjjrchjmCsK5P', 'SsUNZFQwzzV4UHa9XzGD4UKsRbqmOzES', '德勤通APP', 'authority_app', 'APP', '德勤通APP客户端', 'ENABLE', NULL, '2', NULL, '2023-07-08 07:03:59', 0, 'trinax', 'M0hwTejHWNupSuqPLniv');

-- ----------------------------
-- Table structure for external_user_git_receive
-- ----------------------------
DROP TABLE IF EXISTS `external_user_git_receive`;
CREATE TABLE `external_user_git_receive`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户id：同sys_user表user_id',
  `receive_status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '领取状态：UN_DO,DONE',
  `receive_time` datetime NULL DEFAULT NULL COMMENT '认领时间',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_eugr_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '外部用户礼品认领登记表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of external_user_git_receive
-- ----------------------------
INSERT INTO `external_user_git_receive` VALUES (1, 'EU202506160001', 'DONE', '2025-06-20 16:50:05', '已领取', 'admin', '系统管理员', '2025-06-16 10:00:00', 'IU202506190001', '陈文见', '2025-06-20 16:50:05');
INSERT INTO `external_user_git_receive` VALUES (2, 'EU202506160002', 'DONE', '2025-06-01 11:30:00', '已领取礼品', 'admin', '系统管理员', '2025-06-16 11:00:00', 'admin', '系统管理员', '2025-06-16 11:30:00');
INSERT INTO `external_user_git_receive` VALUES (3, 'EU202506160003', 'UN_DO', NULL, '等待领取', 'admin', '系统管理员', '2025-06-16 09:00:00', 'admin', '系统管理员', '2025-06-16 09:00:00');

-- ----------------------------
-- Table structure for market_collect_mobile
-- ----------------------------
DROP TABLE IF EXISTS `market_collect_mobile`;
CREATE TABLE `market_collect_mobile`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `sms_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '验证码',
  `channel` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '渠道',
  `download_count` int NULL DEFAULT 0 COMMENT '下载次数',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `is_new` tinyint NULL DEFAULT NULL COMMENT '是否新客 ',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '市场活动收集' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of market_collect_mobile
-- ----------------------------

-- ----------------------------
-- Table structure for mq_send
-- ----------------------------
DROP TABLE IF EXISTS `mq_send`;
CREATE TABLE `mq_send`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `topic` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主题',
  `partition_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分区key',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `retry_time` datetime NOT NULL COMMENT '重试时间',
  `retry_count` bigint NOT NULL COMMENT '重试次数',
  `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '状态',
  `create_time` datetime(3) NOT NULL COMMENT '创建时间',
  `update_time` datetime(3) NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_ms_retry_time`(`retry_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '本地可靠消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of mq_send
-- ----------------------------

-- ----------------------------
-- Table structure for oauth_client_details
-- ----------------------------
DROP TABLE IF EXISTS `oauth_client_details`;
CREATE TABLE `oauth_client_details`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `client_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '客户端ID',
  `resource_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '资源ID集合,多个资源时用逗号(,)分隔',
  `client_secret` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端密匙',
  `scope` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端申请的权限范围',
  `authorized_grant_types` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端支持的grant_type(授权类型)',
  `web_server_redirect_uri` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '重定向URI',
  `authorities` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '客户端所拥有的Spring Security的权限值，多个用逗号(,)分隔',
  `access_token_validity` int NULL DEFAULT NULL COMMENT '访问令牌有效时间值(单位:秒)',
  `refresh_token_validity` int NULL DEFAULT NULL COMMENT '更新令牌有效时间值(单位:秒)',
  `additional_information` varchar(4096) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '预留字段',
  `autoapprove` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户是否自动Approval操作',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'oauth客户端令牌信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of oauth_client_details
-- ----------------------------
INSERT INTO `oauth_client_details` VALUES (1, 'AdnbImRHCsJPyTJs6d7OUnWl', NULL, '0ef0df4f49127db4510d3fb99b60c5a225cb6d56971ce22a243fc53fa8a2e676', 'userProfile', 'password,refresh_token,', NULL, NULL, 86400, 86400, NULL, 'false');
INSERT INTO `oauth_client_details` VALUES (2, 'YReHm69vMb9SjjrchjmCsK5P', NULL, '7ae19b5b6e664b24d3a77d8222490331c2e14c6a900bd308b5f57321cab5a62d', 'userProfile', 'password,refresh_token,', NULL, NULL, 86400, 86400, NULL, 'false');

-- ----------------------------
-- Table structure for report_forms
-- ----------------------------
DROP TABLE IF EXISTS `report_forms`;
CREATE TABLE `report_forms`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '编码：与权限表编码保持一致',
  `name` varchar(180) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '报表名称',
  `description` varchar(600) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '报表说明',
  `url` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '报表跳转地址',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '报表记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of report_forms
-- ----------------------------
INSERT INTO `report_forms` VALUES (1, 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', '各战区意向单每月累计数量', '查看不同战区月累计的意向单下单数量，可按年份、意向单状态、客户类型筛选', '/report/pre-order-num-acc', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (2, 'REPORT_INTENT_ORDER_AMOUNT_NEW', '各战区意向单每月新增数量', '查看不同战区月新增的意向单下单数量，可按年份、意向单状态、客户类型筛选', '/report/pre-order-num-add', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (3, 'REPORT_INTENT_ORDER_POWER_TOTAL', '各战区意向单每月累计确认的总功率', '查看不同战区月累计的意向单确认下单总功率，可按年份、客户类型筛选', '/report/pre-order-power-acc', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (4, 'REPORT_INTENT_ORDER_POWER_NEW', '各战区意向单每月新增确认的总功率', '查看不同战区月新增的意向单确认下单总功率，可按年份、客户类型筛选', '/report/pre-order-power-add', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (5, 'REPORT_INTENT_ORDER_PRODUCT', '意向单月下单产品排名', '查看每月下单的产品组总功率排名，可按照客户类型、意向单状态筛选', '/report/product-family-sum-order', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (6, 'REPORT_CONTRACT_PRODUCT', '合同月下单产品排名', '查看每月合同上的产品组总功率排名，可按照客户类型、合同状态筛选', '/report/product-family-sum-contract', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (7, 'REPORT_PARTNER_AMOUNT_TOTAL', '不同客户类型的累计数量', '查看不同客户类型的累计数量，客户类型包括工商业客户和生态伙伴', '/report/enterprise-sum-acc', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (8, 'REPORT_PARTNER_AMOUNT_NEW', '不同客户类型的增长数量', '查看不同年份，不同月份下不同客户类型的新增数量，客户类型包括工商业客户和生态伙伴', '/report/enterprise-sum-add', NULL, NULL, '2024-03-22 13:50:00', NULL, NULL, '2024-03-22 13:50:00');
INSERT INTO `report_forms` VALUES (9, 'REPORT_CONTRACT_POWER_TOTAL', '各战区合同每月累计成单量（总功率）', '查看不同战区月累计双签的合同总功率，可按照客户类型筛选', '/report/contract-power-sum-acc', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (10, 'REPORT_CONTRACT_POWER_NEW', '各战区合同每月新增成单量（总功率）', '查看不同战区月新增双签的合同总功率，可按照客户类型筛选', '/report/contract-power-sum-add', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (11, 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', '月意向单下单次数最多和最少的客户', '查看每月下单次数最多和最少的客户，可按照客户类型、意向单状态、战区筛选', '/report/customer-intent-order-sum', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (12, 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', '月意向单下单总量最多和最少的客户', '查看每月下单总功率最多和最少的客户，可按照客户类型、意向单状态、战区筛选', '/report/customer-intent-order-power', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (13, 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', '各战区需求单累计数量', '查看不同战区月累计的需求单数量，可按照需求单状态筛选', '/report/require-order-num-acc', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (14, 'REPORT_REQIORE_ORDER_AMOUNT_NEW', '各战区需求单月新增量', '查看不同战区月新增的需求单数量，可按照需求单状态筛选', '/report/require-order-num-add', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (15, 'REPORT_CONTRACT_NONEY_TOTAL', '各战区月下单金额（累计）', '查看不同战区月累计的合同总金额，可按照客户类型、合同状态筛选', '/report/org-contract-order-sum-acc', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (16, 'REPORT_CONTRACT_NONEY_NEW', '各战区月下单金额（新增）', '查看不同战区月新增的合同总金额，可按照客户类型、合同状态筛选', '/report/org-contract-order-sum-add', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (17, 'REPORT_PRODUCT_FAVORITE_AMOUNT', '客户用户产品收藏次数', '查看客户对各产品族的收藏总数（处于收藏状态计算，若收藏后取消收藏不计入计算）', '/report/user-product-favorite-sum', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (18, 'REPORT_DELIVERY_AMOUNT_TOTAL', '各战区发货申请每月数量（累计）', '查看不同战区月累计的发货申请数量，可按照客户类型、发货申请状态筛选', '/report/org-delivery-sum-acc', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (19, 'REPORT_DELIVERY_AMOUNT_NEW', '各战区发货申请每月数量（新增）', '查看不同战区月新增的发货申请数量，可按照客户类型、发货申请状态筛选', '/report/org-delivery-sum-add', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (20, 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', '合同审批后（≥15）未双签的合同清单', '查看合同审批通过后，≥15天未完成合同双签的合同数据清单', '/report/contract-unsign-over-15-days', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (21, 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', '合同审批后（≥5＜15）未双签的合同清单', '查看合同审批通过后，≥5天且＜15天未完成合同双签的合同数据清单', '/report/contract-unsign-between-5-and-15-days', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (22, 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', '合同双签后5天内未提交发货申请清单', '查看合同双签完成后，5天内未提交发货申请的合同数据清单', '/report/contract-undeliver-in-5-days', NULL, NULL, '2024-03-28 16:20:00', NULL, NULL, '2024-03-28 16:20:00');
INSERT INTO `report_forms` VALUES (23, 'REPORT_DELIVERY_DATA_LIST', '放货数据表', '查看SO放货相关数据', '/report/so-delivery-outbound', NULL, NULL, '2024-07-18 16:20:00', NULL, NULL, '2024-07-18 16:20:00');
INSERT INTO `report_forms` VALUES (24, 'REPORT_CONTRACT_ITEM_DATA_LIST', '销售合同订单表', '查看销售合同订单表', '/report/contract-item', NULL, NULL, '2024-07-18 16:20:00', NULL, NULL, '2024-07-18 16:20:00');

-- ----------------------------
-- Table structure for segment
-- ----------------------------
DROP TABLE IF EXISTS `segment`;
CREATE TABLE `segment`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `business_type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '业务类型，唯一',
  `max_id` bigint NOT NULL DEFAULT 0 COMMENT '当前最大id',
  `limit_id` bigint NULL DEFAULT NULL COMMENT '允许出现的极限值，为空表示不限制',
  `step` bigint NOT NULL DEFAULT 0 COMMENT '步长',
  `version` bigint NOT NULL DEFAULT 0 COMMENT '版本号',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique_business_type`(`business_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '号段表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of segment
-- ----------------------------
INSERT INTO `segment` VALUES (1, 'user-id-20250613', 3, 999999, 3, 2, '2025-06-13 14:16:10', '2025-06-13 14:16:11');
INSERT INTO `segment` VALUES (2, 'user-id-20250616', 3, 999999, 3, 2, '2025-06-16 14:46:30', '2025-06-16 14:46:30');
INSERT INTO `segment` VALUES (3, 'role', 10, NULL, 10, 2, '2025-06-17 17:09:40', '2025-06-17 17:09:40');
INSERT INTO `segment` VALUES (4, 'user-id-20250618', 6, 999999, 3, 3, '2025-06-18 16:33:19', '2025-06-18 16:48:00');
INSERT INTO `segment` VALUES (5, 'user-id-20250619', 9, 999999, 3, 4, '2025-06-19 10:22:18', '2025-06-19 15:11:08');
INSERT INTO `segment` VALUES (6, 'organization', 30, NULL, 10, 4, '2025-06-23 14:26:07', '2025-06-24 17:30:13');
INSERT INTO `segment` VALUES (7, 'user-id-20250623', 6, 999999, 3, 3, '2025-06-23 14:29:35', '2025-06-23 17:27:35');
INSERT INTO `segment` VALUES (8, 'user-id-20250624', 3, 999999, 3, 2, '2025-06-24 17:45:25', '2025-06-24 17:45:25');

-- ----------------------------
-- Table structure for sys_area
-- ----------------------------
DROP TABLE IF EXISTS `sys_area`;
CREATE TABLE `sys_area`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域自定义code',
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域名称',
  `country` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属国家;默认 CN',
  `real_province_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '隶属省份CODE（sys_region表code值）',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '业务区域表（虚拟省份）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_area
-- ----------------------------
INSERT INTO `sys_area` VALUES (1, '11', '北京', 'CN', '11', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (2, '12', '天津', 'CN', '12', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (3, '13', '河北', 'CN', '13', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (4, '14', '山西', 'CN', '14', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (5, '15_001', '蒙东', 'CN', '15', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (6, '15_002', '蒙西', 'CN', '15', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (7, '15_003', '蒙中', 'CN', '15', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (8, '21', '辽宁', 'CN', '21', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (9, '22', '吉林', 'CN', '22', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (10, '23', '黑龙江', 'CN', '23', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (11, '31', '上海', 'CN', '31', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (12, '32', '江苏', 'CN', '32', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (13, '33', '浙江', 'CN', '33', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (14, '34', '安徽', 'CN', '34', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (15, '35', '福建', 'CN', '35', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (16, '36', '江西', 'CN', '36', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (17, '37', '山东', 'CN', '37', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (18, '41', '河南', 'CN', '41', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (19, '42', '湖北', 'CN', '42', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (20, '43', '湖南', 'CN', '43', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (21, '44', '广东', 'CN', '44', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (22, '45', '广西', 'CN', '45', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (23, '46', '海南', 'CN', '46', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (24, '50', '重庆', 'CN', '50', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (25, '51', '四川', 'CN', '51', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (26, '52', '贵州', 'CN', '52', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (27, '53', '云南', 'CN', '53', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (28, '54', '西藏', 'CN', '54', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (29, '61', '陕西', 'CN', '61', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (30, '62', '甘肃', 'CN', '62', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (31, '63', '青海', 'CN', '63', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (32, '64', '宁夏', 'CN', '64', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_area` VALUES (33, '65', '新疆', 'CN', '65', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');

-- ----------------------------
-- Table structure for sys_area_city_relation
-- ----------------------------
DROP TABLE IF EXISTS `sys_area_city_relation`;
CREATE TABLE `sys_area_city_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域自定义code',
  `area_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域名称',
  `country` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属国家;默认 CN',
  `real_province_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '隶属省份CODE（sys_region表code值）',
  `city_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市编码',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '业务区域与城市映射表(特殊)' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_area_city_relation
-- ----------------------------
INSERT INTO `sys_area_city_relation` VALUES (1, '15_001', '蒙东', 'CHN', '15', '150400', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (2, '15_001', '蒙东', 'CHN', '15', '150500', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (3, '15_001', '蒙东', 'CHN', '15', '150700', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (4, '15_001', '蒙东', 'CHN', '15', '152200', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (5, '15_002', '蒙西', 'CHN', '15', '150200', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (6, '15_002', '蒙西', 'CHN', '15', '152900', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (7, '15_002', '蒙西', 'CHN', '15', '150600', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (8, '15_002', '蒙西', 'CHN', '15', '150300', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (9, '15_002', '蒙西', 'CHN', '15', '150800', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (10, '15_003', '蒙中', 'CHN', '15', '150100', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (11, '15_003', '蒙中', 'CHN', '15', '150900', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_area_city_relation` VALUES (12, '15_003', '蒙中', 'CHN', '15', '152500', 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_client
-- ----------------------------
DROP TABLE IF EXISTS `sys_client`;
CREATE TABLE `sys_client`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用id',
  `app_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用密钥',
  `app_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用名称',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `appid_unique`(`app_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'API接口client' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_client
-- ----------------------------

-- ----------------------------
-- Table structure for sys_client_detail
-- ----------------------------
DROP TABLE IF EXISTS `sys_client_detail`;
CREATE TABLE `sys_client_detail`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用id',
  `access_token` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '授权token',
  `access_token_validity` bigint UNSIGNED NULL DEFAULT NULL COMMENT 'token有效期',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'API接口详情' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_client_detail
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dealer_sales_relation
-- ----------------------------
DROP TABLE IF EXISTS `sys_dealer_sales_relation`;
CREATE TABLE `sys_dealer_sales_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `dealer_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '经销商用户id',
  `origin_dealer_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `dealer_sf_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '企业联系人 sfid',
  `enterprise_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '企业id',
  `parent_dealer_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上级经销商用户id',
  `sales_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '销售人员id',
  `origin_sales_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `auth_status` int NULL DEFAULT NULL COMMENT '企业成员关系认证状态 1:认证通过 0:认证失败 2:认证中',
  `enterprise_customer_category` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户类别',
  `enabled` int NULL DEFAULT 1 COMMENT '是否启用   1启用  0禁用',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '修改人',
  `updated_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_default` int NOT NULL DEFAULT 0 COMMENT '是否默认企业',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_dealer_user_id_enterprise_id_sales_user_id`(`dealer_user_id` ASC, `enterprise_id` ASC, `sales_user_id` ASC) USING BTREE,
  INDEX `idx_sdsr_enterprise_id`(`enterprise_id` ASC) USING BTREE,
  INDEX `idx_sdsr_sales_user_id`(`sales_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '经销商用户与销售人员关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dealer_sales_relation
-- ----------------------------
INSERT INTO `sys_dealer_sales_relation` VALUES (17, 'EU202506160003', 'EU202506160003', NULL, '5', '', 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 17:58:06', 'IU202506130002', '李亮', '2025-06-24 17:58:06', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (18, 'EU202506160003', 'EU202506160003', NULL, '6', '', 'IU202506190007', 'IU202506190007', 1, 'BUSINESS', 1, 'IU202506130002', '李亮', '2025-06-24 17:58:06', 'IU202506130002', '李亮', '2025-06-24 17:58:06', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (20, 'EU202506190008', 'EU202506190008', NULL, '5', 'EU202506160003', 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 17:59:26', 'IU202506130002', '李亮', '2025-06-24 17:59:26', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (21, 'EU202506160002', 'EU202506160002', NULL, '7', NULL, 'IU202506190007', 'IU202506190007', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 18:00:52', 'IU202506130002', '李亮', '2025-06-24 18:00:52', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (22, 'EU202506230004', 'EU202506230004', NULL, '7', 'EU202506160002', 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 18:01:49', 'IU202506130002', '李亮', '2025-06-24 18:01:49', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (28, 'EU202506230001', 'EU202506230001', NULL, '10', 'EU202506160003', 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 18:03:22', 'IU202506130002', '李亮', '2025-06-24 18:03:22', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (29, 'EU202506160001', 'EU202506160001', NULL, '8', NULL, 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 18:05:00', 'IU202506130002', '李亮', '2025-06-24 18:05:00', 0);
INSERT INTO `sys_dealer_sales_relation` VALUES (30, 'EU202506240002', 'EU202506240002', NULL, '5', 'EU202506160003', 'IU202506190005', 'IU202506190005', 1, 'PARTNER', 1, 'IU202506130002', '李亮', '2025-06-24 18:06:27', 'IU202506130002', '李亮', '2025-06-24 18:06:27', 0);

-- ----------------------------
-- Table structure for sys_organization
-- ----------------------------
DROP TABLE IF EXISTS `sys_organization`;
CREATE TABLE `sys_organization`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组织架构CODE',
  `organization_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组织架构名称',
  `organization_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组织架构类型',
  `organization_level` int NULL DEFAULT NULL COMMENT '组织架构层级',
  `parent_organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父级组织架构CODE',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_organization_organization_code_index`(`organization_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统组织架构表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_organization
-- ----------------------------
INSERT INTO `sys_organization` VALUES (1, 'TRINA', 'Deloitte', 'COMMON', 1, '0', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-13 14:21:27');
INSERT INTO `sys_organization` VALUES (2, 'TRINA_QPDIT', 'QPD IT', 'COMMON', 2, 'TRINA', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (3, 'TRINA_CN', '中国区MU', 'COMMON', 2, 'TRINA', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (4, 'TRINA_CN_OPERATION', '中国区运营', 'COMMON', 3, 'TRINA_CN', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (5, 'TRINA_CN_MARKET', '中国区分布式市场', 'COMMON', 3, 'TRINA_CN', NULL, 0, 'IU202401010001', '系统管理员', '2023-12-26 15:54:33', 'IU202401010001', '系统管理员', '2023-12-26 15:54:59');
INSERT INTO `sys_organization` VALUES (6, 'TRINA_CN_SALES', '中国区分布式销售', 'COMMON', 3, 'TRINA_CN', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (7, 'TRINA_CN_SALES_SERVICE', '分布式​技术服务​', 'SERVICE', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (8, 'TRINA_CN_SALES_OPERATION', '分布式运营​', 'OPERATION', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (9, 'TRINA_CN_SALES_HUABEI', '分布式华北区​', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (10, 'TRINA_CN_SALES_HUANAN', '分布式华南区​', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (11, 'TRINA_CN_SALES_HUADONG', '分布式华东区​', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2023-12-26 15:54:33', 'IU202401010001', '系统管理员', '2023-12-26 15:54:59');
INSERT INTO `sys_organization` VALUES (12, 'TRINA_CN_SALES_DONGBEI', '分布式东北区​', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (13, 'TRINA_CN_SALES_ZHEJIANG', '分布式浙江区​', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_organization` VALUES (14, 'TRINA_CN_SALES_LUEXIANG', '分布式鲁鄂湘', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-26 14:27:33', 'IU202401010001', '系统管理员', '2024-01-26 14:27:33');
INSERT INTO `sys_organization` VALUES (15, 'TRINA_CN_SALES_STRATEGICMARKETING', '分布式战略营销', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-29 09:27:33', 'IU202401010001', '系统管理员', '2024-01-29 09:27:33');
INSERT INTO `sys_organization` VALUES (16, 'O001001', '虚拟战区-3层', 'COMMON', 3, 'TRINA_QPDIT', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-31 20:57:56', 'IU202401010001', '系统管理员', '2024-01-31 20:57:56');
INSERT INTO `sys_organization` VALUES (17, 'O000502', '虚拟战区-销售', 'SALES', 4, 'O001001', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-31 20:58:12', 'IU202401010001', '系统管理员', '2024-01-31 20:58:42');
INSERT INTO `sys_organization` VALUES (18, 'O001002', '虚拟战区-运营', 'OPERATION', 4, 'O001001', NULL, 0, 'IU202401010001', '系统管理员', '2024-01-31 20:59:41', 'IU202401010001', '系统管理员', '2024-01-31 20:59:41');
INSERT INTO `sys_organization` VALUES (19, 'O001501', '用户运营', 'OPERATION', 4, 'TRINA_CN_OPERATION', NULL, 0, 'IU202401310001', '付翱', '2024-08-28 13:28:21', 'IU202401310001', '付翱', '2024-12-16 11:03:44');
INSERT INTO `sys_organization` VALUES (22, 'O000021', '分布式苏沪皖区', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202506130002', '李亮', '2025-06-24 17:30:13', 'IU202506130002', '李亮', '2025-06-24 17:30:13');
INSERT INTO `sys_organization` VALUES (23, 'O000022', '分布式山东区', 'SALES', 4, 'TRINA_CN_SALES', NULL, 0, 'IU202506130002', '李亮', '2025-06-24 17:39:51', 'IU202506130002', '李亮', '2025-06-24 17:39:51');

-- ----------------------------
-- Table structure for sys_organization_area_relation
-- ----------------------------
DROP TABLE IF EXISTS `sys_organization_area_relation`;
CREATE TABLE `sys_organization_area_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组织架构CODE',
  `area_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '业务区域CODE',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '组织与区域关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_organization_area_relation
-- ----------------------------
INSERT INTO `sys_organization_area_relation` VALUES (1, 'TRINA_CN_SALES_HUADONG', '31', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:08', 'IU202310080001', '管理员1', '2024-01-28 09:09:08');
INSERT INTO `sys_organization_area_relation` VALUES (2, 'TRINA_CN_SALES_HUADONG', '36', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:08', 'IU202310080001', '管理员1', '2024-01-28 09:09:08');
INSERT INTO `sys_organization_area_relation` VALUES (3, 'TRINA_CN_SALES_HUADONG', '32', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:08', 'IU202310080001', '管理员1', '2024-01-28 09:09:08');
INSERT INTO `sys_organization_area_relation` VALUES (4, 'TRINA_CN_SALES_HUADONG', '34', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:08', 'IU202310080001', '管理员1', '2024-01-28 09:09:08');
INSERT INTO `sys_organization_area_relation` VALUES (5, 'TRINA_CN_SALES_HUANAN', '44', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (6, 'TRINA_CN_SALES_HUANAN', '35', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (7, 'TRINA_CN_SALES_HUANAN', '45', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (8, 'TRINA_CN_SALES_HUANAN', '50', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (9, 'TRINA_CN_SALES_HUANAN', '46', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (10, 'TRINA_CN_SALES_HUANAN', '53', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (11, 'TRINA_CN_SALES_HUANAN', '51', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (12, 'TRINA_CN_SALES_HUANAN', '52', 0, 'IU202310080001', '管理员1', '2024-01-28 09:09:56', 'IU202310080001', '管理员1', '2024-01-28 09:09:56');
INSERT INTO `sys_organization_area_relation` VALUES (13, 'TRINA_CN_SALES_ZHEJIANG', '33', 0, 'IU202310080001', '管理员1', '2024-01-28 09:11:09', 'IU202310080001', '管理员1', '2024-01-28 09:11:09');
INSERT INTO `sys_organization_area_relation` VALUES (14, 'TRINA_CN_SALES_DONGBEI', '23', 0, 'IU202310080001', '管理员1', '2024-01-28 09:14:44', 'IU202310080001', '管理员1', '2024-01-28 09:14:44');
INSERT INTO `sys_organization_area_relation` VALUES (15, 'TRINA_CN_SALES_DONGBEI', '22', 0, 'IU202310080001', '管理员1', '2024-01-28 09:14:44', 'IU202310080001', '管理员1', '2024-01-28 09:14:44');
INSERT INTO `sys_organization_area_relation` VALUES (16, 'TRINA_CN_SALES_DONGBEI', '21', 0, 'IU202310080001', '管理员1', '2024-01-28 09:14:44', 'IU202310080001', '管理员1', '2024-01-28 09:14:44');
INSERT INTO `sys_organization_area_relation` VALUES (17, 'TRINA_CN_SALES_DONGBEI', '15_001', 0, 'IU202310080001', '管理员1', '2024-01-28 09:14:44', 'IU202310080001', '管理员1', '2024-01-28 09:14:44');
INSERT INTO `sys_organization_area_relation` VALUES (18, 'TRINA_CN_SALES_HUABEI', '13', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (19, 'TRINA_CN_SALES_HUABEI', '11', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (20, 'TRINA_CN_SALES_HUABEI', '12', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (21, 'TRINA_CN_SALES_HUABEI', '14', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (22, 'TRINA_CN_SALES_HUABEI', '15_002', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (23, 'TRINA_CN_SALES_HUABEI', '15_003', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (24, 'TRINA_CN_SALES_HUABEI', '41', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (25, 'TRINA_CN_SALES_HUABEI', '61', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (26, 'TRINA_CN_SALES_HUABEI', '62', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (27, 'TRINA_CN_SALES_HUABEI', '64', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (28, 'TRINA_CN_SALES_HUABEI', '63', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (29, 'TRINA_CN_SALES_HUABEI', '65', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:29', 'IU202310080001', '管理员1', '2024-01-28 09:20:29');
INSERT INTO `sys_organization_area_relation` VALUES (30, 'TRINA_CN_SALES_LUEXIANG', '37', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:47', 'IU202310080001', '管理员1', '2024-01-28 09:20:47');
INSERT INTO `sys_organization_area_relation` VALUES (31, 'TRINA_CN_SALES_LUEXIANG', '42', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:47', 'IU202310080001', '管理员1', '2024-01-28 09:20:47');
INSERT INTO `sys_organization_area_relation` VALUES (32, 'TRINA_CN_SALES_LUEXIANG', '43', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:47', 'IU202310080001', '管理员1', '2024-01-28 09:20:47');
INSERT INTO `sys_organization_area_relation` VALUES (33, 'TRINA_CN_SALES_LUEXIANG', '54', 0, 'IU202310080001', '管理员1', '2024-01-28 09:20:47', 'IU202310080001', '管理员1', '2024-01-28 09:20:47');

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `permission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限ID',
  `permission_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限名称',
  `permission_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限编码',
  `permission_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限类型 PAGE-界面， BUTTON-按钮',
  `permission_application` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限所属应用 TRINAX_APP-德勤通APP， TRINAX_MANAGE-德勤通后台',
  `parent_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '父级权限ID',
  `seq` int NULL DEFAULT NULL COMMENT '系统权限排序',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 128 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统功能权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_permission
-- ----------------------------
INSERT INTO `sys_permission` VALUES (1, 'APP_MY_INTENT_ORDER_BTN', '我的意向单', 'APP_MY_INTENT_ORDER_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (2, 'APP_INTENT_ORDER_CREATE_BTN', '创建意向单', 'APP_INTENT_ORDER_CREATE_BTN', 'ELEMENT', 'APP', 'APP_MY_INTENT_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (3, 'APP_INTENT_ORDER_DELETE_BTN', '删除意向单', 'APP_INTENT_ORDER_DELETE_BTN', 'ELEMENT', 'APP', 'APP_MY_INTENT_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (4, 'APP_INTENT_ORDER_CANCEL_BTN', '取消意向单', 'APP_INTENT_ORDER_CANCEL_BTN', 'ELEMENT', 'APP', 'APP_MY_INTENT_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (5, 'APP_INTENT_ORDER_CONFIRM_BTN', '确认意向单', 'APP_INTENT_ORDER_CONFIRM_BTN', 'ELEMENT', 'APP', 'APP_MY_INTENT_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (6, 'APP_INTENT_ORDER_COPY_BTN', '复制意向单', 'APP_INTENT_ORDER_COPY_BTN', 'ELEMENT', 'APP', 'APP_MY_INTENT_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (7, 'APP_MY_CONTRACT_BTN', '我的合同', 'APP_MY_CONTRACT_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (8, 'APP_CONTRACT_SIGN_BTN', '签署合同', 'APP_CONTRACT_SIGN_BTN', 'ELEMENT', 'APP', 'APP_MY_CONTRACT_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (9, 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', '查看合同文件', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 'ELEMENT', 'APP', 'APP_MY_CONTRACT_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (10, 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', '查看合同发货', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 'ELEMENT', 'APP', 'APP_MY_CONTRACT_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (11, 'APP_MY_DELIVER_BTN', '我的发货申请', 'APP_MY_DELIVER_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (12, 'APP_DELIVER_CREATE_BTN', '创建发货申请', 'APP_DELIVER_CREATE_BTN', 'ELEMENT', 'APP', 'APP_MY_DELIVER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (13, 'APP_DELIVER_DELETE_BTN', '删除发货申请', 'APP_DELIVER_DELETE_BTN', 'ELEMENT', 'APP', 'APP_MY_DELIVER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (14, 'APP_DELIVER_CANCEL_BTN', '取消发货申请', 'APP_DELIVER_CANCEL_BTN', 'ELEMENT', 'APP', 'APP_MY_DELIVER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (15, 'APP_DELIVER_CONFIRM_BTN', '确认发货申请', 'APP_DELIVER_CONFIRM_BTN', 'ELEMENT', 'APP', 'APP_MY_DELIVER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (16, 'PC_ORGANIZATION_MANAGE_PAGE', '组织架构', 'PC_ORGANIZATION_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (17, 'PC_USER_MANAGE_PAGE', '用户管理', 'PC_USER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (18, 'PC_ROLE_MANAGE_PAGE', '权限管理', 'PC_ROLE_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (19, 'PC_INTENT_ORDER_MANAGE_PAGE', '意向管理', 'PC_INTENT_ORDER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (20, 'PC_INTENT_ORDER_EXPORT_BTN', '导出意向单', 'PC_INTENT_ORDER_EXPORT_BTN', 'ELEMENT', 'PC', 'PC_INTENT_ORDER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (21, 'PC_CONTRACT_MANAGE_PAGE', '合同管理', 'PC_CONTRACT_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (22, 'PC_CONTRACT_EXPORT_BTN', '导出合同', 'PC_CONTRACT_EXPORT_BTN', 'ELEMENT', 'PC', 'PC_CONTRACT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (23, 'PC_DELIVER_MANAGE_PAGE', '发货管理', 'PC_DELIVER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (24, 'PC_DELIVER_CONFIRM_BTN', '确认发货申请', 'PC_DELIVER_CONFIRM_BTN', 'ELEMENT', 'PC', 'PC_DELIVER_LIST_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (25, 'PC_DELIVER_RETURN_BTN', '退回发货申请', 'PC_DELIVER_RETURN_BTN', 'ELEMENT', 'PC', 'PC_DELIVER_LIST_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (26, 'PC_DELIVER_EXPORT_BTN', '导出发货申请', 'PC_DELIVER_EXPORT_BTN', 'ELEMENT', 'PC', 'PC_DELIVER_LIST_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (27, 'PC_PRODUCT_MANAGE_PAGE', '产品列表', 'PC_PRODUCT_MANAGE_PAGE', 'PAGE', 'PC', 'PC_MASTER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (28, 'PC_PRODUCT_EXPORT_BTN', '导出产品', 'PC_PRODUCT_EXPORT_BTN', 'ELEMENT', 'PC', 'PC_PRODUCT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (29, 'PC_PRODUCT_EDIT_BTN', '编辑产品', 'PC_PRODUCT_EDIT_BTN', 'ELEMENT', 'PC', 'PC_PRODUCT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (30, 'PC_PRODUCT_RELEASE_POWER_BTN', '上下架功率', 'PC_PRODUCT_RELEASE_POWER_BTN', 'ELEMENT', 'PC', 'PC_PRODUCT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (31, 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', '批量上下架', 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', 'ELEMENT', 'PC', 'PC_PRODUCT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (32, 'PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN', '上下架开关', 'PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN', 'ELEMENT', 'PC', 'PC_PRODUCT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (33, 'PC_OPTION_MANAGE_PAGE', '字典管理', 'PC_OPTION_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (34, 'PC_PICTURE_MANAGE_PAGE', '图片管理', 'PC_PICTURE_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (35, 'PC_FEEDBACK_MANAGE_PAGE', '反馈管理', 'PC_FEEDBACK_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (36, 'PC_GIVEN_MANAGE_PAGE', '赠品管理', 'PC_GIVEN_MANAGE_PAGE', 'PAGE', 'PC', 'PC_MASTER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (37, 'PC_MASTER_MANAGE_PAGE', '产品管理', 'PC_MASTER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (38, 'PC_ACCESSORY_MANAGE_PAGE', '备件管理', 'PC_ACCESSORY_MANAGE_PAGE', 'PAGE', 'PC', 'PC_MASTER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (39, 'PC_CONTENT_MANAGE_PAGE', '内容管理', 'PC_CONTENT_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (40, 'PC_CUSTOMER_MANAGE_PAGE', '客户管理', 'PC_CUSTOMER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (41, 'APP_SET_MAIN_SALES_BTN', '分配主销售', 'APP_SET_MAIN_SALES_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (42, 'APP_MY_REQUIRE_ORDER_BTN', '我的需求单', 'APP_MY_REQUIRE_ORDER_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (43, 'APP_REQUIRE_ORDER_CANCEL_BTN', '取消需求', 'APP_REQUIRE_ORDER_CANCEL_BTN', 'ELEMENT', 'APP', 'APP_MY_REQUIRE_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (44, 'APP_REQUIRE_ORDER_CREATE_BTN', '创建需求单', 'APP_REQUIRE_ORDER_CREATE_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (45, 'APP_REQUIRE_ORDER_EDIT_BTN', '编辑需求', 'APP_REQUIRE_ORDER_EDIT_BTN', 'ELEMENT', 'APP', 'APP_MY_REQUIRE_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (46, 'APP_REQUIRE_ORDER_CONFIRM_BTN', '确认需求', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 'ELEMENT', 'APP', 'APP_MY_REQUIRE_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (47, 'APP_REQUIRE_ORDER_ALLOCATION_BTN', '分配需求', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 'ELEMENT', 'APP', 'APP_MY_REQUIRE_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (48, 'PC_REQUIRE_ORDER_MANAGE_PAGE', '需求管理', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (49, 'PC_QUESTIONNAIRE_MANAGE_PAGE', '问卷管理', 'PC_QUESTIONNAIRE_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (50, 'PC_QUESTIONNAIRE_ADD_BTN', '新增问卷', 'PC_QUESTIONNAIRE_ADD_BTN', 'ELEMENT', 'PC', 'PC_QUESTIONNAIRE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (51, 'PC_QUESTIONNAIRE_EDIT_BTN', '编辑问卷', 'PC_QUESTIONNAIRE_EDIT_BTN', 'ELEMENT', 'PC', 'PC_QUESTIONNAIRE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (52, 'PC_QUESTIONNAIRE_RELEASE_BTN', '发布问卷', 'PC_QUESTIONNAIRE_RELEASE_BTN', 'ELEMENT', 'PC', 'PC_QUESTIONNAIRE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (53, 'PC_QUESTIONNAIRE_DELETE_BTN', '删除问卷', 'PC_QUESTIONNAIRE_DELETE_BTN', 'ELEMENT', 'PC', 'PC_QUESTIONNAIRE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (54, 'PC_LOG_MANAGE_PAGE', '日志管理', 'PC_LOG_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (55, 'PC_OPERATION_LOG_MANAGE_PAGE', '操作日志', 'PC_OPERATION_LOG_MANAGE_PAGE', 'PAGE', 'PC', 'PC_LOG_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (56, 'PC_INTEGRATION_LOG_MANAGE_PAGE', '集成日志', 'PC_INTEGRATION_LOG_MANAGE_PAGE', 'PAGE', 'PC', 'PC_LOG_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (57, 'REPORT_MANAGE_PAGE', '报表管理', 'REPORT_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (58, 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', '各战区意向单每月累计数量', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (59, 'REPORT_INTENT_ORDER_AMOUNT_NEW', '各战区意向单每月新增数量', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (60, 'REPORT_INTENT_ORDER_POWER_TOTAL', '各战区意向单每月累计确认的总功率', 'REPORT_INTENT_ORDER_POWER_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (61, 'REPORT_INTENT_ORDER_POWER_NEW', '各战区意向单每月新增确认的总功率', 'REPORT_INTENT_ORDER_POWER_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (62, 'REPORT_INTENT_ORDER_PRODUCT', '意向单月下单产品排名', 'REPORT_INTENT_ORDER_PRODUCT', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (63, 'REPORT_CONTRACT_PRODUCT', '合同月下单产品排名', 'REPORT_CONTRACT_PRODUCT', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (64, 'REPORT_PARTNER_AMOUNT_TOTAL', '不同客户类型的累计数量', 'REPORT_PARTNER_AMOUNT_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (65, 'REPORT_PARTNER_AMOUNT_NEW', '不同客户类型的增长数量', 'REPORT_PARTNER_AMOUNT_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (66, 'REPORT_CONTRACT_POWER_TOTAL', '各战区合同每月累计成单量（总功率）', 'REPORT_CONTRACT_POWER_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (67, 'REPORT_CONTRACT_POWER_NEW', '各战区合同每月新增成单量（总功率）', 'REPORT_CONTRACT_POWER_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (68, 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', '月意向单下单次数最多和最少的客户', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (69, 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', '月意向单下单总量最多和最少的客户', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (70, 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', '各战区需求单累计数量', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (71, 'REPORT_REQIORE_ORDER_AMOUNT_NEW', '各战区需求单月新增量', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (72, 'REPORT_CONTRACT_NONEY_TOTAL', '各战区月下单金额（累计）', 'REPORT_CONTRACT_NONEY_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (73, 'REPORT_CONTRACT_NONEY_NEW', '各战区月下单金额（新增）', 'REPORT_CONTRACT_NONEY_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (74, 'REPORT_PRODUCT_FAVORITE_AMOUNT', '客户用户产品收藏次数', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (75, 'REPORT_DELIVERY_AMOUNT_TOTAL', '各战区发货申请每月数量（累计）', 'REPORT_DELIVERY_AMOUNT_TOTAL', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (76, 'REPORT_DELIVERY_AMOUNT_NEW', '各战区发货申请每月数量（新增）', 'REPORT_DELIVERY_AMOUNT_NEW', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (77, 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', '合同审批后（≥15）未双签的合同清单', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (78, 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', '合同审批后（≥5＜15）未双签的合同清单', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (79, 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', '合同双签后5天内未提交发货申请清单', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (80, 'PC_INVOICE_MANAGE_PAGE', '开票申请管理页面', 'PC_INVOICE_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (81, 'APP_INVOICE_CREATE', '创建/提交开票申请', 'APP_INVOICE_CREATE', 'ELEMENT', 'APP', 'APP_INVOICE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (82, 'APP_INVOICE', '开票申请', 'APP_INVOICE', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (83, 'APP_INVOICE_QUERY', '我的开票申请列表', 'APP_INVOICE_QUERY', 'ELEMENT', 'APP', 'APP_INVOICE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (84, 'APP_INVOICE_DEL', '删除开票申请', 'APP_INVOICE_DEL', 'ELEMENT', 'APP', 'APP_INVOICE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (85, 'PC_MARKET_ACTIVITY_MANAGE_PAGE', '市场活动管理', 'PC_MARKET_ACTIVITY_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (86, 'PC_EXHIBITION_GIFT_RECEIVE_PAGE', 'SNEC展会礼物认领登记', 'PC_EXHIBITION_GIFT_RECEIVE_PAGE', 'PAGE', 'PC', 'PC_MARKET_ACTIVITY_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (87, 'PC_DELIVER_LIST_PAGE', '发货列表', 'PC_DELIVER_LIST_PAGE', 'PAGE', 'PC', 'PC_DELIVER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (88, 'PC_DELIVER_APPROVE_PAGE', '放货审批', 'PC_DELIVER_APPROVE_PAGE', 'PAGE', 'PC', 'PC_DELIVER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (89, 'REPORT_DELIVERY_DATA_LIST', '放货数据表', 'REPORT_DELIVERY_DATA_LIST', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (90, 'REPORT_CONTRACT_ITEM_DATA_LIST', '销售合同订单表', 'REPORT_CONTRACT_ITEM_DATA_LIST', 'PAGE', 'PC', 'REPORT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (91, 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', '我的意向单', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', 'PAGE', 'CUSTOMER', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (92, 'PC_CUSTOMER_INTENT_ORDER_CREATE_BTN', '创建意向单', 'PC_CUSTOMER_INTENT_ORDER_CREATE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (93, 'PC_CUSTOMER_INTENT_ORDER_DELETE_BTN', '删除意向单', 'PC_CUSTOMER_INTENT_ORDER_DELETE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (94, 'PC_CUSTOMER_ORDER_CANCEL_BTN', '取消意向单', 'PC_CUSTOMER_ORDER_CANCEL_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (95, 'PC_CUSTOMER_ORDER_COPY_BTN', '复制意向单', 'PC_CUSTOMER_ORDER_COPY_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (96, 'PC_CUSTOMER_ORDER_EXPORT_BTN', '导出', 'PC_CUSTOMER_ORDER_EXPORT_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (97, 'PC_CUSTOMER_MY_CONTRACT_PAGE', '我的合同', 'PC_CUSTOMER_MY_CONTRACT_PAGE', 'PAGE', 'CUSTOMER', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (98, 'PC_CUSTOMER_CONTRACT_SIGN_BTN', '签署合同', 'PC_CUSTOMER_CONTRACT_SIGN_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_CONTRACT_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (99, 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', '查看合同文件', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_CONTRACT_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (100, 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', '查看合同发货', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_CONTRACT_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (101, 'PC_CUSTOMER_CONTRACT_EXPORT_BTN', '导出', 'PC_CUSTOMER_CONTRACT_EXPORT_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_CONTRACT_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (102, 'PC_CUSTOMER_MY_DELIVER_PAGE', '我的发货申请', 'PC_CUSTOMER_MY_DELIVER_PAGE', 'PAGE', 'CUSTOMER', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (103, 'PC_CUSTOMER_DELIVER_CREATE_BTN', '创建发货申请', 'PC_CUSTOMER_DELIVER_CREATE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_DELIVER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (104, 'PC_CUSTOMER_DELIVER_DELETE_BTN', '删除发货申请', 'PC_CUSTOMER_DELIVER_DELETE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_DELIVER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (105, 'PC_CUSTOMER_DELIVER_CANCEL_BTN', '取消发货申请', 'PC_CUSTOMER_DELIVER_CANCEL_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_DELIVER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (106, 'PC_CUSTOMER_DELIVER_EXPORT_BTN', '导出', 'PC_CUSTOMER_DELIVER_EXPORT_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_DELIVER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (107, 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', '我的需求单', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 'PAGE', 'CUSTOMER', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (108, 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', '取消需求', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (109, 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', '创建需求单', 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (110, 'PC_CUSTOMER_REQUIRE_ORDER_EXPORT_BTN', '导出', 'PC_CUSTOMER_REQUIRE_ORDER_EXPORT_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (111, 'PC_CUSTOMER_MY_INVOICE_PAGE', '我的开票申请', 'PC_CUSTOMER_MY_INVOICE_PAGE', 'PAGE', 'CUSTOMER', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (112, 'PC_CUSTOMER_INVOICE_CREATE', '创建开票申请', 'PC_CUSTOMER_INVOICE_CREATE', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INVOICE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (113, 'PC_CUSTOMER_INVOICE_DEL', '删除开票申请', 'PC_CUSTOMER_INVOICE_DEL', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INVOICE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (114, 'PC_CUSTOMER_INVOICE_EXPORT_BTN', '导出', 'PC_CUSTOMER_INVOICE_EXPORT_BTN', 'ELEMENT', 'CUSTOMER', 'PC_CUSTOMER_MY_INVOICE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (115, 'PC_CHANGE_MANAGE_PAGE', '所有人变更', 'PC_CHANGE_MANAGE_PAGE', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (116, 'PC_CHANGE_OWNER_PAGE', '变更执行', 'PC_CHANGE_OWNER_PAGE', 'PAGE', 'PC', 'PC_CHANGE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (117, 'PC_CHANGE_RECORD_PAGE', '变更记录', 'PC_CHANGE_RECORD_PAGE', 'PAGE', 'PC', 'PC_CHANGE_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (118, 'PC_FOLLOW_UP_WITH_NEW_USERS', '新用户跟进', 'PC_FOLLOW_UP_WITH_NEW_USERS', 'PAGE', 'PC', '0', NULL, 0, NULL, NULL, '2024-12-16 18:55:34', NULL, NULL, '2024-12-16 18:55:34');
INSERT INTO `sys_permission` VALUES (119, 'APP_COCKPIT_PAGE', '数据驾驶舱', 'APP_COCKPIT_PAGE', 'PAGE', 'APP', '0', NULL, 0, NULL, NULL, '2025-01-15 17:55:42', NULL, NULL, '2025-01-15 17:55:42');
INSERT INTO `sys_permission` VALUES (120, 'APP_MY_QUICK_ORDER_BTN', '我的渠道订单', 'APP_MY_QUICK_ORDER_BTN', 'ELEMENT', 'APP', '0', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (121, 'APP_QUICK_ORDER_CREATE_BTN', '创建渠道订单', 'APP_QUICK_ORDER_CREATE_BTN', 'ELEMENT', 'APP', 'APP_MY_QUICK_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (122, 'APP_QUICK_ORDER_DELETE_BTN', '删除渠道订单', 'APP_QUICK_ORDER_DELETE_BTN', 'ELEMENT', 'APP', 'APP_MY_QUICK_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (123, 'APP_QUICK_ORDER_CANCEL_BTN', '取消渠道订单', 'APP_QUICK_ORDER_CANCEL_BTN', 'ELEMENT', 'APP', 'APP_MY_QUICK_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (124, 'APP_QUICK_ORDER_CONFIRM_BTN', '确认渠道订单', 'APP_QUICK_ORDER_CONFIRM_BTN', 'ELEMENT', 'APP', 'APP_MY_QUICK_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (125, 'APP_QUICK_ORDER_COPY_BTN', '复制渠道订单', 'APP_QUICK_ORDER_COPY_BTN', 'ELEMENT', 'APP', 'APP_MY_QUICK_ORDER_BTN', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (126, 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', '渠道订单管理', 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', 'ELEMENT', 'PC', 'PC_INTENT_ORDER_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_permission` VALUES (127, 'PC_CONTRACT_CHANNEL_MANAGE_BTN', '渠道合同订单管理', 'PC_CONTRACT_CHANNEL_MANAGE_BTN', 'ELEMENT', 'PC', 'PC_CONTRACT_MANAGE_PAGE', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_permission_url
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission_url`;
CREATE TABLE `sys_permission_url`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `permission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限ID',
  `permission_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限编码',
  `application_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用名',
  `permission_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限路径',
  `permission_url_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限描述',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id_code_unique`(`permission_code` ASC, `permission_url` ASC) USING BTREE,
  UNIQUE INDEX `permission_url_uk`(`permission_url` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统功能权限url映射表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_permission_url
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统角色ID',
  `role_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统角色名称',
  `role_label` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内部用户 INTERNAL；外部用户 EXTERNAL',
  `role_status` int NOT NULL COMMENT '0 启用  1禁用',
  `role_desc` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色描述',
  `special_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '特殊类型：基础角色-BASE   超管-SUPER   普通-COMMON',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;     0：未删除   1：已删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_role_role_id_index`(`role_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统角色表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, 'admin', '系统管理员', 'INTERNAL', 0, '负责系统后台的基础管理、基础配置等工作', 'SUPER', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (2, 'R001100', '分销负责人', 'INTERNAL', 0, '负责中国区分销组件的销售、运营等管理，查看相关报表', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (3, 'R001200', '销售区域管理', 'INTERNAL', 0, '负责中国区分销组件的销售，可代客户下意向单、发货申请单', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (4, 'R001300', '分销销售', 'INTERNAL', 0, '负责中国区分销组件的销售，可代客户下意向单、发货申请单', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (5, 'R001400', '产品管理', 'INTERNAL', 0, '负责中国区分销组件的产品的编辑和产品上下架', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (6, 'R001500', '运营管理', 'INTERNAL', 0, '负责中国区分销组件的发货申请确认和销售业务的跟进沟通', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (7, 'R001600', '运营', 'INTERNAL', 0, '负责中国区分销组件的发货申请确认和销售业务的跟进沟通', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (8, 'R002100', '管理', 'EXTERNAL', 0, '负责生态伙伴的整体管理，可开放所有功能，包括合同签署', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010002', '184运营', '2025-06-17 17:16:02');
INSERT INTO `sys_role` VALUES (9, 'R002200', '采购', 'EXTERNAL', 0, '负责生态伙伴的意向单下单', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-01-01 01:00:00');
INSERT INTO `sys_role` VALUES (10, 'R000001', '业务管理员', 'INTERNAL', 0, '', 'COMMON', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:28:25', 'IU202401010001', '系统管理员', '2023-12-27 15:28:25');
INSERT INTO `sys_role` VALUES (11, 'R000002', 'app上架审核角色', 'INTERNAL', 0, '', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:34:07', 'IU202401010001', '系统管理员', '2024-01-02 15:34:07');
INSERT INTO `sys_role` VALUES (12, 'externalRole', '外部用户基础角色', 'EXTERNAL', 0, '', 'BASE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:28:25', 'IU202401010001', '系统管理员', '2023-12-27 15:28:25');
INSERT INTO `sys_role` VALUES (13, 'reportRole', '报表查看角色', 'INTERNAL', 0, '', 'COMMON', 0, 'admin', '系统管理员', '2024-03-22 13:50:00', 'admin', '系统管理员', '2024-03-22 13:50:00');
INSERT INTO `sys_role` VALUES (14, 'externalPartnerRole', '外部生态用户基础角色', 'EXTERNAL', 0, '', 'BASE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:28:25', 'IU202401010001', '系统管理员', '2023-12-27 15:28:25');
INSERT INTO `sys_role` VALUES (15, 'R000501', '市场专员', 'INTERNAL', 0, '', 'COMMON', 0, 'IU202401010001', '系统管理员', '2024-06-04 10:33:40', 'IU202401010001', '系统管理员', '2024-06-13 17:13:24');
INSERT INTO `sys_role` VALUES (16, 'R001001', '用户运营', 'INTERNAL', 0, '', 'COMMON', 0, 'IU202401310001', '付翱', '2024-08-28 13:31:12', 'IU202401310001', '付翱', '2024-12-16 11:06:45');
INSERT INTO `sys_role` VALUES (17, 'R001501', '管理&采购', 'EXTERNAL', 0, '', 'COMMON', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:42:33', 'IU202407120013', '夏文茜', '2024-11-18 17:43:27');
INSERT INTO `sys_role` VALUES (18, 'R010002', '新用户运营', 'INTERNAL', 0, '', 'COMMON', 0, '', '', '2024-12-16 18:55:34', '', '', '2024-12-16 18:55:34');
INSERT INTO `sys_role` VALUES (22, 'R0000021', '新用户运营11', 'INTERNAL', 0, '111', 'COMMON', 0, 'IU202401010002', '184运营', '2025-06-17 17:29:33', 'IU202401010002', '184运营', '2025-06-17 17:29:33');
INSERT INTO `sys_role` VALUES (23, 'R000003', '新用户运营1111', 'INTERNAL', 0, 'ddd', 'COMMON', 1, 'IU202401010002', '184运营', '2025-06-17 17:29:50', 'IU202401010002', '184运营', '2025-06-17 17:30:21');

-- ----------------------------
-- Table structure for sys_role_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统角色ID',
  `permission_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统权限ID',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 602 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统角色-权限关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_permission
-- ----------------------------
INSERT INTO `sys_role_permission` VALUES (1, 'admin', 'PC_ORGANIZATION_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (2, 'admin', 'PC_USER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (3, 'admin', 'PC_ROLE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (4, 'admin', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (5, 'admin', 'PC_INTENT_ORDER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (6, 'admin', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (7, 'admin', 'PC_CONTRACT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (8, 'admin', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (9, 'admin', 'PC_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (10, 'admin', 'PC_DELIVER_RETURN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (11, 'admin', 'PC_PRODUCT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (12, 'admin', 'PC_PRODUCT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (13, 'admin', 'PC_PRODUCT_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (14, 'admin', 'PC_PRODUCT_RELEASE_POWER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (15, 'admin', 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (16, 'R001100', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (17, 'R001100', 'PC_INTENT_ORDER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (18, 'R001100', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (19, 'R001100', 'PC_CONTRACT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (20, 'R001100', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (21, 'R001100', 'PC_PRODUCT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (22, 'R001100', 'PC_PRODUCT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (23, 'R001100', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (24, 'R001100', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (25, 'R001100', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (26, 'R001100', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (27, 'R001100', 'APP_INTENT_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (28, 'R001100', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (29, 'R001100', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (30, 'R001100', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (31, 'R001100', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (32, 'R001100', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (33, 'R001100', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (34, 'R001100', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (35, 'R001100', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (36, 'R001100', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (37, 'R001100', 'APP_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (38, 'R001200', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (39, 'R001200', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (40, 'R001200', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (41, 'R001200', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (42, 'R001200', 'APP_INTENT_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (43, 'R001200', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (44, 'R001200', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (45, 'R001200', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (46, 'R001200', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (47, 'R001200', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (48, 'R001200', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (49, 'R001200', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (50, 'R001200', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (51, 'R001200', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (52, 'R001200', 'APP_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (53, 'R001300', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (54, 'R001300', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (55, 'R001300', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (56, 'R001300', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (57, 'R001300', 'APP_INTENT_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (58, 'R001300', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (59, 'R001300', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (60, 'R001300', 'APP_CONTRACT_SIGN_BTN', 1, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (61, 'R001300', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (62, 'R001300', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (63, 'R001300', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (64, 'R001300', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (65, 'R001300', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (66, 'R001300', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (67, 'R001300', 'APP_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (68, 'R001400', 'PC_PRODUCT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (69, 'R001400', 'PC_PRODUCT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (70, 'R001400', 'PC_PRODUCT_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (71, 'R001400', 'PC_PRODUCT_RELEASE_POWER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (72, 'R001400', 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (73, 'R001500', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (74, 'R001500', 'PC_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (75, 'R001500', 'PC_DELIVER_RETURN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (76, 'R001600', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (77, 'R001600', 'PC_INTENT_ORDER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (78, 'R001600', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (79, 'R001600', 'PC_CONTRACT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (80, 'R001600', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (81, 'R001600', 'PC_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (82, 'R001600', 'PC_DELIVER_RETURN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (83, 'R001600', 'PC_PRODUCT_RELEASE_POWER_BTN', 1, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (84, 'R001600', 'PC_PRODUCT_EDIT_BTN', 1, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (85, 'R001600', 'PC_PRODUCT_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (86, 'R001600', 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', 1, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (87, 'R002100', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (88, 'R002100', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (89, 'R002100', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (90, 'R002100', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (91, 'R002100', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (92, 'R002100', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (93, 'R002100', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (94, 'R002100', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (95, 'R002100', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (96, 'R002100', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (97, 'R002100', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (98, 'R002100', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (99, 'R002100', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (100, 'R002200', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (101, 'R002200', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (102, 'R002200', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (103, 'R002200', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (104, 'R002200', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (105, 'R002200', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (106, 'admin', 'PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (107, 'admin', 'PC_DELIVER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (108, 'R001500', 'PC_DELIVER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:18:48', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (109, 'R001600', 'PC_DELIVER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:19:21', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (110, 'R001500', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:19:34', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (111, 'R001500', 'PC_INTENT_ORDER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:19:34', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (112, 'R001500', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:19:34', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (113, 'R001500', 'PC_CONTRACT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:19:34', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (114, 'R000001', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (115, 'R000001', 'PC_INTENT_ORDER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (116, 'R000001', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (117, 'R000001', 'PC_CONTRACT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (118, 'R000001', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (119, 'R000001', 'PC_DELIVER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (120, 'R000001', 'PC_DELIVER_RETURN_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (121, 'R000001', 'PC_DELIVER_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (122, 'R000001', 'PC_PRODUCT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (123, 'R000001', 'PC_PRODUCT_EXPORT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (124, 'R000001', 'PC_PRODUCT_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (125, 'R000001', 'PC_PRODUCT_RELEASE_POWER_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (126, 'R000001', 'PC_PRODUCT_BATCH_RELEASE_PRODUCT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (127, 'R000001', 'PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 15:29:03', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (128, 'R002200', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 18:16:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (129, 'R002200', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 18:16:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (130, 'R002200', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 18:16:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (131, 'R002200', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2023-12-27 18:16:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (132, 'R002200', 'APP_DELIVER_CONFIRM_BTN', 1, 'IU202401010001', '系统管理员', '2023-12-27 18:16:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (133, 'R000002', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:34:38', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (134, 'R000002', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:34:38', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (135, 'R000002', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:34:38', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (136, 'R000002', 'APP_INTENT_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:34:38', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (137, 'R000002', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-01-02 15:35:35', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (138, 'admin', 'PC_FEEDBACK_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-05 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (139, 'admin', 'PC_MASTER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-12 01:00:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (140, 'R001400', 'PC_MASTER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-12 01:00:00', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (141, 'R000001', 'PC_MASTER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-01-31 16:41:48', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (142, 'admin', 'PC_CONTENT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-02 15:12:43', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (143, 'admin', 'PC_PICTURE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-02 15:12:43', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (144, 'admin', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-02 15:12:43', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (145, 'R001200', 'APP_SET_MAIN_SALES_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-02 15:14:50', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (146, 'R001400', 'PC_GIVEN_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:03', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (147, 'R001400', 'PC_ACCESSORY_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:03', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (148, 'R001400', 'PC_PRODUCT_SWITCH_RELEASE_PRODUCT_BTN', 1, 'IU202401010001', '系统管理员', '2024-02-04 13:27:03', 'IU202401010001', '系统管理员', '2024-02-05 10:23:39');
INSERT INTO `sys_role_permission` VALUES (149, 'R000001', 'PC_GIVEN_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:29', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (150, 'R000001', 'PC_ACCESSORY_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:29', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (151, 'admin', 'PC_GIVEN_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:38', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (152, 'admin', 'PC_ACCESSORY_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-04 13:27:38', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (153, 'admin', 'PC_OPTION_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-02-05 10:36:02', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (154, 'admin', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (155, 'admin', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (156, 'admin', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (157, 'admin', 'APP_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (158, 'admin', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (159, 'admin', 'APP_INTENT_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (160, 'admin', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (161, 'admin', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (162, 'admin', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (163, 'admin', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (164, 'admin', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (165, 'admin', 'APP_SET_MAIN_SALES_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-19 11:20:46', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (166, 'R001100', 'APP_SET_MAIN_SALES_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-22 16:08:56', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (167, 'externalRole', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401250003', '管理员2', '2024-02-18 14:02:26', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (168, 'externalRole', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401250003', '管理员2', '2024-02-19 09:28:25', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (169, 'externalRole', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202402200003', 'Lola', '2024-02-21 10:49:57', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (170, 'R001100', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:26', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (171, 'R001100', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:26', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (172, 'R001100', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:26', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (173, 'R001200', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:41', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (174, 'R001200', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:41', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (175, 'R001200', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:41', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (176, 'R001300', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:57', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (177, 'R001300', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:57', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (178, 'R001300', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 0, 'IU202401010001', '系统管理员', '2024-02-28 15:50:57', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (179, 'admin', 'PC_QUESTIONNAIRE_MANAGE_PAGE', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (180, 'admin', 'PC_QUESTIONNAIRE_ADD_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (181, 'admin', 'PC_QUESTIONNAIRE_EDIT_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (182, 'admin', 'PC_QUESTIONNAIRE_RELEASE_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (183, 'admin', 'PC_QUESTIONNAIRE_DELETE_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (184, 'R000001', 'PC_QUESTIONNAIRE_MANAGE_PAGE', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (185, 'R000001', 'PC_QUESTIONNAIRE_ADD_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (186, 'R000001', 'PC_QUESTIONNAIRE_EDIT_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (187, 'R000001', 'PC_QUESTIONNAIRE_RELEASE_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (188, 'R000001', 'PC_QUESTIONNAIRE_DELETE_BTN', 0, 'IU202401250003', '系统管理员', '2024-02-07 12:00:40', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (189, 'admin', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:22:05', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (190, 'R000001', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:22:34', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (191, 'R000001', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:22:34', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (192, 'R001300', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:31:47', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (193, 'R001200', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:31:53', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (194, 'R001100', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:32:59', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (195, 'R000002', 'APP_MY_CONTRACT_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:13', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (196, 'R000002', 'APP_MY_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:13', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (197, 'R000002', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:35', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (198, 'R000002', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:35', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (199, 'R000002', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:35', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (200, 'R000002', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 0, 'IU202401010001', '系统管理员', '2024-03-02 22:36:35', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (201, 'externalPartnerRole', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401250003', '管理员2', '2024-02-19 09:28:25', 'IU202401010001', '系统管理员', '2024-08-07 20:57:15');
INSERT INTO `sys_role_permission` VALUES (202, 'externalPartnerRole', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202402200003', 'Lola', '2024-02-21 10:49:57', 'IU202401010001', '系统管理员', '2024-08-07 20:57:15');
INSERT INTO `sys_role_permission` VALUES (203, 'admin', 'PC_LOG_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:14:32', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (204, 'admin', 'PC_OPERATION_LOG_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:14:32', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (205, 'admin', 'PC_INTEGRATION_LOG_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:14:32', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (206, 'R001500', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:40:03', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (207, 'reportRole', 'REPORT_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (208, 'reportRole', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (209, 'reportRole', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (210, 'reportRole', 'REPORT_INTENT_ORDER_POWER_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (211, 'reportRole', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (212, 'reportRole', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (213, 'reportRole', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (214, 'reportRole', 'REPORT_DELIVERY_AMOUNT_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (215, 'reportRole', 'REPORT_DELIVERY_AMOUNT_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (216, 'reportRole', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (217, 'reportRole', 'REPORT_CONTRACT_NONEY_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (218, 'reportRole', 'REPORT_CONTRACT_NONEY_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (219, 'reportRole', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (220, 'reportRole', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (221, 'reportRole', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (222, 'reportRole', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (223, 'reportRole', 'REPORT_CONTRACT_POWER_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (224, 'reportRole', 'REPORT_CONTRACT_POWER_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (225, 'reportRole', 'REPORT_PARTNER_AMOUNT_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (226, 'reportRole', 'REPORT_PARTNER_AMOUNT_TOTAL', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (227, 'reportRole', 'REPORT_CONTRACT_PRODUCT', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (228, 'reportRole', 'REPORT_INTENT_ORDER_PRODUCT', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (229, 'reportRole', 'REPORT_INTENT_ORDER_POWER_NEW', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:44:10', 'IU202401010001', '系统管理员', '2024-07-16 11:17:01');
INSERT INTO `sys_role_permission` VALUES (230, 'R001500', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 00:54:53', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (231, 'R001600', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-04-08 09:38:51', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (232, 'externalRole', 'APP_MY_CONTRACT_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (233, 'externalRole', 'APP_CONTRACT_SIGN_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (234, 'externalRole', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (235, 'externalRole', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (236, 'externalRole', 'APP_MY_INTENT_ORDER_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (237, 'externalRole', 'APP_MY_DELIVER_BTN', 1, 'IU202401010001', '系统管理员', '2024-04-29 17:09:56', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (238, 'externalRole', 'APP_INVOICE', 1, 'IU202401010001', '系统管理员', '2024-05-17 19:03:50', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (239, 'externalRole', 'APP_INVOICE_CREATE', 1, 'IU202401010001', '系统管理员', '2024-05-17 19:03:50', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (240, 'externalRole', 'APP_INVOICE_QUERY', 1, 'IU202401010001', '系统管理员', '2024-05-17 19:03:50', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (241, 'externalRole', 'APP_INVOICE_DEL', 1, 'IU202401010001', '系统管理员', '2024-05-17 19:03:50', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (242, 'R001300', 'APP_INVOICE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:01', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (243, 'R001300', 'APP_INVOICE_QUERY', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:01', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (244, 'R001200', 'APP_INVOICE_QUERY', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:10', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (245, 'R001200', 'APP_INVOICE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:10', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (246, 'R001100', 'APP_INVOICE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:23', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (247, 'R001100', 'APP_INVOICE_QUERY', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:23', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (248, 'R000001', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (249, 'admin', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:45', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (250, 'R001500', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:53', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (251, 'R001600', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:05:57', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (252, 'R000002', 'APP_INVOICE', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:07:19', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (253, 'R000002', 'APP_INVOICE_QUERY', 0, 'IU202401010001', '系统管理员', '2024-05-17 19:07:19', 'IU202401010001', '系统管理员', '2024-05-17 19:07:19');
INSERT INTO `sys_role_permission` VALUES (254, 'admin', 'PC_MARKET_ACTIVITY_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-06-07 17:39:33', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (255, 'admin', 'PC_EXHIBITION_GIFT_RECEIVE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-06-07 17:40:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (256, 'R000501', 'PC_MARKET_ACTIVITY_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-06-13 17:23:09', 'IU202401010001', '系统管理员', '2024-06-13 17:25:58');
INSERT INTO `sys_role_permission` VALUES (257, 'R000501', 'PC_EXHIBITION_GIFT_RECEIVE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-06-13 17:25:58', 'IU202401010001', '系统管理员', '2024-06-13 17:25:58');
INSERT INTO `sys_role_permission` VALUES (258, 'admin', 'PC_DELIVER_APPROVE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:35:39', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (259, 'admin', 'PC_DELIVER_LIST_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:35:39', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (260, 'R001500', 'PC_DELIVER_LIST_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:37:03', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (261, 'R001500', 'PC_DELIVER_APPROVE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:37:03', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (262, 'R001600', 'PC_DELIVER_LIST_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:37:16', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (263, 'R001600', 'PC_DELIVER_APPROVE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-07-12 21:37:16', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (264, 'admin', 'REPORT_DELIVERY_DATA_LIST', 0, 'IU202401010001', '系统管理员', '2024-03-28 17:14:17', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (265, 'admin', 'REPORT_CONTRACT_ITEM_DATA_LIST', 0, 'IU202401010001', '系统管理员', '2024-03-28 17:14:17', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (266, 'R001100', 'REPORT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (267, 'R001100', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (268, 'R001100', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (269, 'R001100', 'REPORT_INTENT_ORDER_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (270, 'R001100', 'REPORT_INTENT_ORDER_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (271, 'R001100', 'REPORT_INTENT_ORDER_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (272, 'R001100', 'REPORT_CONTRACT_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (273, 'R001100', 'REPORT_PARTNER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (274, 'R001100', 'REPORT_PARTNER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (275, 'R001100', 'REPORT_CONTRACT_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (276, 'R001100', 'REPORT_CONTRACT_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (277, 'R001100', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (278, 'R001100', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (279, 'R001100', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (280, 'R001100', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (281, 'R001100', 'REPORT_CONTRACT_NONEY_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (282, 'R001100', 'REPORT_CONTRACT_NONEY_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (283, 'R001100', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (284, 'R001100', 'REPORT_DELIVERY_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (285, 'R001100', 'REPORT_DELIVERY_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (286, 'R001100', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (287, 'R001100', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (288, 'R001100', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (289, 'R001100', 'REPORT_DELIVERY_DATA_LIST', 1, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (290, 'R001100', 'REPORT_CONTRACT_ITEM_DATA_LIST', 0, 'IU202401310001', '付翱', '2024-07-31 21:55:47', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (291, 'admin', 'REPORT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (292, 'admin', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (293, 'admin', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (294, 'admin', 'REPORT_INTENT_ORDER_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (295, 'admin', 'REPORT_INTENT_ORDER_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (296, 'admin', 'REPORT_INTENT_ORDER_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (297, 'admin', 'REPORT_CONTRACT_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (298, 'admin', 'REPORT_PARTNER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (299, 'admin', 'REPORT_CONTRACT_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (300, 'admin', 'REPORT_CONTRACT_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (301, 'admin', 'REPORT_PARTNER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (302, 'admin', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (303, 'admin', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (304, 'admin', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (305, 'admin', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (306, 'admin', 'REPORT_CONTRACT_NONEY_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (307, 'admin', 'REPORT_CONTRACT_NONEY_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (308, 'admin', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (309, 'admin', 'REPORT_DELIVERY_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (310, 'admin', 'REPORT_DELIVERY_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (311, 'admin', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (312, 'admin', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (313, 'admin', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 0, 'IU202401310001', '付翱', '2024-07-31 21:56:42', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (314, 'R001500', 'REPORT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (315, 'R001500', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (316, 'R001500', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (317, 'R001500', 'REPORT_INTENT_ORDER_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (318, 'R001500', 'REPORT_INTENT_ORDER_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (319, 'R001500', 'REPORT_INTENT_ORDER_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (320, 'R001500', 'REPORT_CONTRACT_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (321, 'R001500', 'REPORT_PARTNER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (322, 'R001500', 'REPORT_PARTNER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (323, 'R001500', 'REPORT_CONTRACT_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (324, 'R001500', 'REPORT_CONTRACT_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (325, 'R001500', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (326, 'R001500', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (327, 'R001500', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (328, 'R001500', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (329, 'R001500', 'REPORT_CONTRACT_NONEY_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (330, 'R001500', 'REPORT_CONTRACT_NONEY_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (331, 'R001500', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (332, 'R001500', 'REPORT_DELIVERY_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (333, 'R001500', 'REPORT_DELIVERY_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (334, 'R001500', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (335, 'R001500', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (336, 'R001500', 'REPORT_DELIVERY_DATA_LIST', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (337, 'R001500', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (338, 'R001500', 'REPORT_CONTRACT_ITEM_DATA_LIST', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:06', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (339, 'R000001', 'REPORT_CONTRACT_5_TO_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (340, 'R000001', 'REPORT_DELIVERY_DATA_LIST', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (341, 'R000001', 'REPORT_CONTRACT_ITEM_DATA_LIST', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (342, 'R000001', 'REPORT_CONTRACT_DOUBLE_SINGED_5_DAYS_UNDELIVER', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (343, 'R000001', 'REPORT_CONTRACT_15_DAYS_NOT_DOUBLE_SINGED', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (344, 'R000001', 'REPORT_DELIVERY_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (345, 'R000001', 'REPORT_DELIVERY_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (346, 'R000001', 'REPORT_PRODUCT_FAVORITE_AMOUNT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (347, 'R000001', 'REPORT_CONTRACT_NONEY_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (348, 'R000001', 'REPORT_CONTRACT_NONEY_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (349, 'R000001', 'REPORT_REQIORE_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (350, 'R000001', 'REPORT_REQIORE_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (351, 'R000001', 'REPORT_INTENT_ORDER_POWER_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (352, 'R000001', 'REPORT_INTENT_ORDER_AMOUNT_TOP_BOTTOM', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (353, 'R000001', 'REPORT_CONTRACT_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (354, 'R000001', 'REPORT_CONTRACT_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (355, 'R000001', 'REPORT_PARTNER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (356, 'R000001', 'REPORT_PARTNER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (357, 'R000001', 'REPORT_CONTRACT_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (358, 'R000001', 'REPORT_INTENT_ORDER_PRODUCT', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (359, 'R000001', 'REPORT_INTENT_ORDER_POWER_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (360, 'R000001', 'REPORT_INTENT_ORDER_POWER_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (361, 'R000001', 'REPORT_INTENT_ORDER_AMOUNT_NEW', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (362, 'R000001', 'REPORT_INTENT_ORDER_AMOUNT_TOTAL', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (363, 'R000001', 'REPORT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-07-31 22:01:35', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (364, 'R002100', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (365, 'R002100', 'PC_CUSTOMER_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (366, 'R002100', 'PC_CUSTOMER_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (367, 'R002100', 'PC_CUSTOMER_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (368, 'R002100', 'PC_CUSTOMER_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (369, 'R002100', 'PC_CUSTOMER_ORDER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401010001', '系统管理员', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (370, 'R002100', 'PC_CUSTOMER_MY_CONTRACT_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (371, 'R002100', 'PC_CUSTOMER_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (372, 'R002100', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (373, 'R002100', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (374, 'R002100', 'PC_CUSTOMER_CONTRACT_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401010001', '系统管理员', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (375, 'R002100', 'PC_CUSTOMER_MY_DELIVER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (376, 'R002100', 'PC_CUSTOMER_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (377, 'R002100', 'PC_CUSTOMER_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (378, 'R002100', 'PC_CUSTOMER_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (379, 'R002100', 'PC_CUSTOMER_DELIVER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401010001', '系统管理员', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (380, 'R002100', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (381, 'R002100', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (382, 'R002100', 'PC_CUSTOMER_MY_INVOICE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (383, 'R002100', 'PC_CUSTOMER_INVOICE_CREATE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (384, 'R002100', 'PC_CUSTOMER_INVOICE_DEL', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (385, 'R002100', 'PC_CUSTOMER_INVOICE_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:47:45', 'IU202401010001', '系统管理员', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (386, 'R002200', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (387, 'R002200', 'PC_CUSTOMER_INTENT_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (388, 'R002200', 'PC_CUSTOMER_INTENT_ORDER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (389, 'R002200', 'PC_CUSTOMER_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (390, 'R002200', 'PC_CUSTOMER_ORDER_COPY_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (391, 'R002200', 'PC_CUSTOMER_ORDER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (392, 'R002200', 'PC_CUSTOMER_MY_CONTRACT_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (393, 'R002200', 'PC_CUSTOMER_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (394, 'R002200', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (395, 'R002200', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (396, 'R002200', 'PC_CUSTOMER_CONTRACT_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (397, 'R002200', 'PC_CUSTOMER_MY_DELIVER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (398, 'R002200', 'PC_CUSTOMER_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (399, 'R002200', 'PC_CUSTOMER_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (400, 'R002200', 'PC_CUSTOMER_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (401, 'R002200', 'PC_CUSTOMER_DELIVER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (402, 'R002200', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (403, 'R002200', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (404, 'R002200', 'PC_CUSTOMER_REQUIRE_ORDER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (405, 'R002200', 'PC_CUSTOMER_MY_INVOICE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (406, 'R002200', 'PC_CUSTOMER_INVOICE_CREATE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (407, 'R002200', 'PC_CUSTOMER_INVOICE_DEL', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (408, 'R002200', 'PC_CUSTOMER_INVOICE_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:49:02', 'IU202401010001', '系统管理员', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (409, 'R002100', 'PC_CUSTOMER_REQUIRE_ORDER_EXPORT_BTN', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:50:10', 'IU202401010001', '系统管理员', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (410, 'externalPartnerRole', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:52:10', 'IU202401010001', '系统管理员', '2024-08-07 20:57:15');
INSERT INTO `sys_role_permission` VALUES (411, 'externalRole', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:54:43', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (412, 'externalRole', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:54:43', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (413, 'externalRole', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:54:43', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (414, 'externalRole', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (415, 'externalRole', 'PC_CUSTOMER_MY_CONTRACT_PAGE', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (416, 'externalRole', 'PC_CUSTOMER_CONTRACT_SIGN_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (417, 'externalRole', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (418, 'externalRole', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (419, 'externalRole', 'PC_CUSTOMER_MY_DELIVER_PAGE', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (420, 'externalRole', 'PC_CUSTOMER_DELIVER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (421, 'externalRole', 'PC_CUSTOMER_DELIVER_DELETE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (422, 'externalRole', 'PC_CUSTOMER_DELIVER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (423, 'externalRole', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (424, 'externalRole', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (425, 'externalRole', 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (426, 'externalRole', 'PC_CUSTOMER_MY_INVOICE_PAGE', 1, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401010001', '系统管理员', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (427, 'externalRole', 'PC_CUSTOMER_INVOICE_CREATE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (428, 'externalRole', 'PC_CUSTOMER_INVOICE_DEL', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:56:12', 'IU202401310001', '付翱', '2024-11-21 15:27:37');
INSERT INTO `sys_role_permission` VALUES (429, 'externalPartnerRole', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:57:15', 'IU202401010001', '系统管理员', '2024-08-07 20:57:15');
INSERT INTO `sys_role_permission` VALUES (430, 'R002200', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 20:59:07', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (431, 'R002100', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401010001', '系统管理员', '2024-08-07 21:02:52', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (432, 'admin', 'PC_CHANGE_MANAGE_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 21:41:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (433, 'admin', 'PC_CHANGE_OWNER_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 21:41:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (434, 'admin', 'PC_CHANGE_RECORD_PAGE', 0, 'IU202401010001', '系统管理员', '2024-08-07 21:41:00', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (435, 'R001001', 'PC_USER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-08-28 13:32:04', 'IU202401010002', '184运营', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (436, 'R001001', 'PC_INTENT_ORDER_MANAGE_PAGE', 1, 'IU202401310001', '付翱', '2024-08-28 13:38:50', 'IU202401310001', '付翱', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (437, 'R001001', 'PC_CONTRACT_MANAGE_PAGE', 1, 'IU202401310001', '付翱', '2024-08-28 13:38:50', 'IU202401310001', '付翱', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (438, 'R001001', 'PC_DELIVER_MANAGE_PAGE', 1, 'IU202401310001', '付翱', '2024-08-28 13:38:50', 'IU202401310001', '付翱', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (439, 'R001001', 'PC_CUSTOMER_MANAGE_PAGE', 1, 'IU202401310001', '付翱', '2024-10-08 10:27:36', 'IU202401310001', '付翱', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (440, 'R001100', 'PC_MARKET_ACTIVITY_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:10', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (441, 'R001100', 'PC_EXHIBITION_GIFT_RECEIVE_PAGE', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:10', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (442, 'R001100', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:10', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (443, 'R001100', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:10', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (444, 'R001100', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:48', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (445, 'R001100', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:48', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (446, 'R001100', 'APP_INVOICE_DEL', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:48', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (447, 'R001100', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-10-09 13:10:48', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (448, 'R001501', 'PC_CUSTOMER_MY_INTENT_ORDER_PAGE', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (449, 'R001501', 'PC_CUSTOMER_MY_CONTRACT_PAGE', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (450, 'R001501', 'PC_CUSTOMER_MY_DELIVER_PAGE', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (451, 'R001501', 'PC_CUSTOMER_MY_INVOICE_PAGE', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (452, 'R001501', 'APP_MY_INTENT_ORDER_BTN', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (453, 'R001501', 'APP_MY_CONTRACT_BTN', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (454, 'R001501', 'APP_MY_DELIVER_BTN', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (455, 'R001501', 'APP_INVOICE', 0, 'IU202407120013', '夏文茜', '2024-11-18 17:43:49', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (456, 'R001200', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-20 11:29:47', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (457, 'R001300', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-20 11:32:19', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (458, 'R001300', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-20 11:32:19', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (459, 'R001300', 'APP_INVOICE_DEL', 0, 'IU202401310001', '付翱', '2024-11-20 11:32:40', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (460, 'R001200', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-20 11:32:59', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (461, 'R001200', 'APP_INVOICE_DEL', 0, 'IU202401310001', '付翱', '2024-11-20 11:32:59', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (462, 'R001501', 'PC_CUSTOMER_MY_REQUIRE_ORDER_PAGE', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (463, 'R001501', 'PC_CUSTOMER_DELIVER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (464, 'R001501', 'PC_CUSTOMER_DELIVER_DELETE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (465, 'R001501', 'PC_CUSTOMER_DELIVER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (466, 'R001501', 'PC_CUSTOMER_CONTRACT_SIGN_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (467, 'R001501', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (468, 'R001501', 'PC_CUSTOMER_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (469, 'R001501', 'PC_CUSTOMER_INTENT_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (470, 'R001501', 'PC_CUSTOMER_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (471, 'R001501', 'PC_CUSTOMER_INTENT_ORDER_DELETE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (472, 'R001501', 'PC_CUSTOMER_ORDER_COPY_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (473, 'R001501', 'PC_CUSTOMER_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (474, 'R001501', 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (475, 'R001501', 'PC_CUSTOMER_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (476, 'R001501', 'PC_CUSTOMER_INVOICE_DEL', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (477, 'R001501', 'APP_INTENT_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (478, 'R001501', 'APP_INTENT_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (479, 'R001501', 'APP_INTENT_ORDER_COPY_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (480, 'R001501', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (481, 'R001501', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (482, 'R001501', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (483, 'R001501', 'APP_SET_MAIN_SALES_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (484, 'R001501', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (485, 'R001501', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (486, 'R001501', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (487, 'R001501', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (488, 'R001501', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (489, 'R001501', 'APP_INVOICE_QUERY', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:05', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (490, 'R002200', 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:49', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (491, 'R002200', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:49', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (492, 'R002200', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:49', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (493, 'R002200', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:31:49', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (494, 'R002100', 'PC_CUSTOMER_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (495, 'R002100', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (496, 'R002100', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (497, 'R002100', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (498, 'R002100', 'APP_INVOICE', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (499, 'R002100', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (500, 'R002100', 'APP_INVOICE_QUERY', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:19', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (501, 'R002200', 'APP_INVOICE', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:37', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (502, 'R002200', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:37', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (503, 'R002200', 'APP_INVOICE_QUERY', 0, 'IU202401310001', '付翱', '2024-11-22 10:32:37', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (504, 'R010002', 'PC_FOLLOW_UP_WITH_NEW_USERS', 0, NULL, '', '2024-12-16 18:55:34', 'IU202401310001', '付翱', '2025-02-26 16:19:14');
INSERT INTO `sys_role_permission` VALUES (505, 'admin', 'PC_FOLLOW_UP_WITH_NEW_USERS', 0, 'IU202401310001', '付翱', '2024-12-16 19:18:23', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (506, 'R002200', 'APP_COCKPIT_PAGE', 0, 'IU202401310001', '付翱', '2025-01-18 12:05:59', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (507, 'R002100', 'APP_COCKPIT_PAGE', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:11', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (508, 'admin', 'APP_COCKPIT_PAGE', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (509, 'admin', 'APP_REQUIRE_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (510, 'admin', 'APP_MY_REQUIRE_ORDER_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (511, 'admin', 'APP_INVOICE', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (512, 'admin', 'APP_REQUIRE_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (513, 'admin', 'APP_REQUIRE_ORDER_EDIT_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (514, 'admin', 'APP_REQUIRE_ORDER_CONFIRM_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (515, 'admin', 'APP_INVOICE_CREATE', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (516, 'admin', 'APP_INVOICE_QUERY', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (517, 'admin', 'APP_INVOICE_DEL', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (518, 'admin', 'APP_DELIVER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (519, 'admin', 'APP_DELIVER_DELETE_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (520, 'admin', 'APP_DELIVER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (521, 'admin', 'APP_DELIVER_CONFIRM_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (522, 'admin', 'APP_REQUIRE_ORDER_ALLOCATION_BTN', 0, 'IU202401310001', '付翱', '2025-01-18 12:06:53', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (523, 'R002200', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (524, 'R002200', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (525, 'R002200', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (526, 'R002200', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (527, 'R002200', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (528, 'R002200', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:30:55', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (529, 'R002100', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (530, 'R002100', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (531, 'R002100', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (532, 'R002100', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (533, 'R002100', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (534, 'R002100', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:31:38', 'IU202401310001', '付翱', '2025-05-06 08:29:01');
INSERT INTO `sys_role_permission` VALUES (535, 'R001501', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (536, 'R001501', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (537, 'R001501', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (538, 'R001501', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (539, 'R001501', 'APP_QUICK_ORDER_CONFIRM_BTN', 1, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202503050011', '张杨', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (540, 'R001501', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:32:20', 'IU202401310001', '付翱', '2025-05-06 08:29:41');
INSERT INTO `sys_role_permission` VALUES (541, 'R001600', 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', 0, 'IU202503050011', '张杨', '2025-04-07 20:39:09', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (542, 'R001600', 'PC_CONTRACT_CHANNEL_MANAGE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:39:09', 'IU202503050011', '张杨', '2025-04-07 20:39:09');
INSERT INTO `sys_role_permission` VALUES (543, 'R001500', 'PC_CONTRACT_CHANNEL_MANAGE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 20:39:33', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (544, 'R001500', 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', 0, 'IU202503050011', '张杨', '2025-04-07 20:39:33', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (545, 'admin', 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (546, 'admin', 'PC_CONTRACT_CHANNEL_MANAGE_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (547, 'admin', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (548, 'admin', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (549, 'admin', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (550, 'admin', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (551, 'admin', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (552, 'admin', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202401310001', '付翱', '2025-04-07 22:31:20', 'IU202401310001', '付翱', '2025-04-07 22:31:20');
INSERT INTO `sys_role_permission` VALUES (553, 'R001300', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:37:46', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (554, 'R001300', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:37:46', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (555, 'R001300', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:37:46', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (556, 'R001300', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:37:46', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (557, 'R001300', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:37:46', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (558, 'R001200', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (559, 'R001200', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (560, 'R001200', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (561, 'R001200', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (562, 'R001200', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (563, 'R001200', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202503050011', '张杨', '2025-04-07 22:38:05', 'IU202503050011', '张杨', '2025-04-07 22:38:05');
INSERT INTO `sys_role_permission` VALUES (564, 'R002200', 'APP_CONTRACT_VIEW_CONTRACT_FILE_BTN', 0, 'IU202401180001', '马玲', '2025-04-30 14:40:36', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (565, 'R002200', 'APP_CONTRACT_VIEW_CONTRACT_DELIVER_BTN', 0, 'IU202401180001', '马玲', '2025-04-30 14:40:36', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (566, 'R002200', 'APP_CONTRACT_SIGN_BTN', 0, 'IU202401180001', '马玲', '2025-04-30 14:40:36', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (567, 'R002200', 'APP_INTENT_ORDER_CONFIRM_BTN', 1, 'IU202401310001', '付翱', '2025-05-06 08:28:39', 'IU202401310001', '付翱', '2025-05-06 08:29:14');
INSERT INTO `sys_role_permission` VALUES (568, 'R001500', 'PC_USER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:05:36', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (569, 'R001500', 'PC_CONTENT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:05:36', 'IU202401310001', '付翱', '2025-05-14 17:05:36');
INSERT INTO `sys_role_permission` VALUES (570, 'R001100', 'PC_USER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (571, 'R001100', 'PC_INTENT_ORDER_CHANNEL_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (572, 'R001100', 'PC_CONTRACT_CHANNEL_MANAGE_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (573, 'R001100', 'PC_DELIVER_LIST_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (574, 'R001100', 'PC_DELIVER_CONFIRM_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (575, 'R001100', 'PC_DELIVER_EXPORT_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (576, 'R001100', 'PC_DELIVER_RETURN_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (577, 'R001100', 'PC_DELIVER_APPROVE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (578, 'R001100', 'PC_MASTER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (579, 'R001100', 'PC_CONTENT_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (580, 'R001100', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (581, 'R001100', 'APP_COCKPIT_PAGE', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (582, 'R001100', 'APP_MY_QUICK_ORDER_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (583, 'R001100', 'APP_QUICK_ORDER_CREATE_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (584, 'R001100', 'APP_QUICK_ORDER_DELETE_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (585, 'R001100', 'APP_QUICK_ORDER_CANCEL_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (586, 'R001100', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (587, 'R001100', 'APP_QUICK_ORDER_CONFIRM_BTN', 0, 'IU202401310001', '付翱', '2025-05-14 17:06:44', 'IU202401310001', '付翱', '2025-05-14 17:06:44');
INSERT INTO `sys_role_permission` VALUES (588, 'R000001', 'PC_USER_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-16 14:05:52', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (589, 'R000001', 'PC_ORGANIZATION_MANAGE_PAGE', 0, 'IU202401310001', '付翱', '2025-05-16 14:05:52', 'IU202401310001', '付翱', '2025-05-16 14:05:52');
INSERT INTO `sys_role_permission` VALUES (590, 'R001300', 'APP_QUICK_ORDER_COPY_BTN', 0, 'IU202401310001', '付翱', '2025-05-26 16:03:58', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (591, 'R001001', 'PC_LOG_MANAGE_PAGE', 0, 'IU202401010002', '184运营', '2025-06-17 17:29:10', 'IU202401010002', '184运营', '2025-06-17 17:29:10');
INSERT INTO `sys_role_permission` VALUES (592, 'R000003', 'PC_FOLLOW_UP_WITH_NEW_USERS', 0, 'IU202401010002', '184运营', '2025-06-17 17:29:53', 'IU202401010002', '184运营', '2025-06-17 17:29:53');
INSERT INTO `sys_role_permission` VALUES (593, 'R001300', 'PC_INTENT_ORDER_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (594, 'R001300', 'PC_CONTRACT_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (595, 'R001300', 'PC_DELIVER_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (596, 'R001300', 'PC_INVOICE_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (597, 'R001300', 'PC_REQUIRE_ORDER_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (598, 'R001300', 'PC_CUSTOMER_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (599, 'R001300', 'PC_LOG_MANAGE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 15:45:10', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (600, 'R001300', 'PC_DELIVER_LIST_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 16:08:56', 'IU202506130002', 'LL', '2025-06-20 16:08:56');
INSERT INTO `sys_role_permission` VALUES (601, 'R001300', 'PC_DELIVER_APPROVE_PAGE', 0, 'IU202506130002', 'LL', '2025-06-20 16:08:56', 'IU202506130002', 'LL', '2025-06-20 16:08:56');

-- ----------------------------
-- Table structure for sys_sales_operation_relation
-- ----------------------------
DROP TABLE IF EXISTS `sys_sales_operation_relation`;
CREATE TABLE `sys_sales_operation_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `sales_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '销售人员ID',
  `origin_sales_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `biz_organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务组织CODE',
  `operation_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '运营人员ID',
  `origin_operation_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售与运营关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_sales_operation_relation
-- ----------------------------
INSERT INTO `sys_sales_operation_relation` VALUES (4, 'IU202506190005', NULL, 'TRINA_CN_SALES_HUABEI', 'IU202401010002', 'IU202401010002', 0, 'IU202506190001', '陈文见', '2025-06-19 10:57:08', 'IU202506190001', '陈文见', '2025-06-19 10:57:08');
INSERT INTO `sys_sales_operation_relation` VALUES (6, 'IU202506190005', 'IU202506190005', 'TRINA_CN_SALES_HUANAN', 'IU202506190002', 'IU202506190002', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_sales_operation_relation` VALUES (12, 'IU202506190007', 'IU202506190007', 'TRINA_CN_SALES_HUANAN', 'IU202401010002', 'IU202401010002', 0, 'IU202506130002', '李亮', '2025-06-24 17:27:14', 'IU202506130002', '李亮', '2025-06-24 17:27:14');
INSERT INTO `sys_sales_operation_relation` VALUES (13, 'IU202506190009', 'IU202506190009', 'O000021', 'IU202401010002', 'IU202401010002', 0, 'IU202506130002', '李亮', '2025-06-24 17:31:23', 'IU202506130002', '李亮', '2025-06-24 17:31:23');
INSERT INTO `sys_sales_operation_relation` VALUES (14, 'IU202506130001', 'IU202506130001', 'TRINA_CN_SALES_HUABEI', 'IU202401010002', 'IU202401010002', 0, 'IU202506130002', '李亮', '2025-06-24 17:34:20', 'IU202506130002', '李亮', '2025-06-24 17:34:20');
INSERT INTO `sys_sales_operation_relation` VALUES (15, 'IU202506130002', 'IU202506130002', 'O000022', 'IU202401010002', 'IU202401010002', 0, 'IU202506130002', '李亮', '2025-06-24 17:40:38', 'IU202506130002', '李亮', '2025-06-24 17:40:38');
INSERT INTO `sys_sales_operation_relation` VALUES (16, 'IU202506190002', 'IU202506190002', 'TRINA_CN_SALES_HUABEI', 'IU202401010002', 'IU202401010002', 0, 'IU202506130002', '李亮', '2025-06-24 18:10:37', 'IU202506130002', '李亮', '2025-06-24 18:10:37');

-- ----------------------------
-- Table structure for sys_sales_organization_relation
-- ----------------------------
DROP TABLE IF EXISTS `sys_sales_organization_relation`;
CREATE TABLE `sys_sales_organization_relation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `sales_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '销售人员ID',
  `biz_organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '服务组织CODE',
  `report_to_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '汇报对象人员ID',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `origin_report_to_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '源汇报对象用户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '销售与战区关系' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_sales_organization_relation
-- ----------------------------
INSERT INTO `sys_sales_organization_relation` VALUES (1, 'IU202506190005', 'TRINA_CN_SALES_HUABEI', 'IU202506190001', 0, 'IU202506190001', '陈文见', '2025-06-19 10:57:08', 'IU202506190001', '陈文见', '2025-06-19 10:57:08', 'IU202506190001');
INSERT INTO `sys_sales_organization_relation` VALUES (8, 'IU202506190007', 'TRINA_CN_SALES_HUANAN', 'IU202506190001', 0, 'IU202506130002', '李亮', '2025-06-24 17:27:14', 'IU202506130002', '李亮', '2025-06-24 17:27:14', 'IU202506190001');
INSERT INTO `sys_sales_organization_relation` VALUES (9, 'IU202506190009', 'O000021', 'IU202506190001', 0, 'IU202506130002', '李亮', '2025-06-24 17:31:23', 'IU202506130002', '李亮', '2025-06-24 17:31:23', 'IU202506190001');
INSERT INTO `sys_sales_organization_relation` VALUES (10, 'IU202506130001', 'TRINA_CN_SALES_HUABEI', 'IU202506190001', 0, 'IU202506130002', '李亮', '2025-06-24 17:34:20', 'IU202506130002', '李亮', '2025-06-24 17:34:20', 'IU202506190001');
INSERT INTO `sys_sales_organization_relation` VALUES (11, 'IU202506130002', 'O000022', 'IU202506190001', 0, 'IU202506130002', '李亮', '2025-06-24 17:40:38', 'IU202506130002', '李亮', '2025-06-24 17:40:38', 'IU202506190001');
INSERT INTO `sys_sales_organization_relation` VALUES (12, 'IU202506190002', 'TRINA_CN_SALES_HUABEI', 'IU202506130001', 0, 'IU202506130002', '李亮', '2025-06-24 18:10:37', 'IU202506130002', '李亮', '2025-06-24 18:10:37', 'IU202506130001');

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户唯一标识',
  `sf_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'SF用户ID',
  `user_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户编码（内部员工则是员工编码）',
  `user_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户类型：INTERNAL-内部员工，EXTERNAL-外部用户',
  `user_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `user_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户邮箱',
  `mobile` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `parent_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上级人员ID',
  `origin_parent_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Change Owner 原始的 id',
  `organization_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '内部用户-隶属组织CODE 外部人员-所属战区CODE',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ENABLE' COMMENT '用户状态：ENABLE-启用，FREEZE-冻结，WITHDRAW-注销',
  `head_img` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像地址',
  `registration_time` datetime NULL DEFAULT NULL COMMENT '注册时间',
  `registration_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '极光设备ID',
  `last_login_time` datetime NULL DEFAULT NULL COMMENT '最近登录时间',
  `is_deleted` int NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `user_province_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属省份编码',
  `user_province_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属省份名称',
  `user_city_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属城市编码',
  `user_city_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属城市名称',
  `follow_status` smallint NULL DEFAULT 0 COMMENT '跟进状态 0:待跟进，1:跟进中，2:有效 3:无效',
  `follow_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '跟进备注',
  `follow_update_time` datetime NULL DEFAULT NULL COMMENT '跟进状态更新时间',
  `customer_branch` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '客户分类',
  `product_class` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '产品类别',
  `is_purchasing_intention` smallint NULL DEFAULT NULL COMMENT '是否有采购意向',
  `request_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '需求备注',
  `follow_feedback` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '反馈结果 0:其他 1:不需要 2:无人接听/或挂断 3:下载错误 4:电话停机',
  `valid_time` datetime NULL DEFAULT NULL COMMENT '标记有效时间',
  `assign_status` tinyint NULL DEFAULT 0 COMMENT '分配状态 0:待处理 1:已下发区域总 2:已分配销售',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '同步区域总时间',
  `sale_follow_status` smallint NULL DEFAULT 0 COMMENT '销售跟进状态 0:待跟进，1:跟进中，2:有效 3:无效',
  `sale_follow_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '销售跟进备注',
  `sale_follow_update_time` datetime NULL DEFAULT NULL COMMENT '销售跟进状态更新时间',
  `sale_follow_feedback` smallint NULL DEFAULT NULL COMMENT '销售反馈结果 0:其他 1:不需要 2:无人接听/或挂断 3:下载错误 4:电话停机',
  `sale_valid_time` datetime NULL DEFAULT NULL COMMENT '销售标记有效时间',
  `exclusive_sale` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '专属销售',
  `assign_sale_time` datetime NULL DEFAULT NULL COMMENT '分配销售时间',
  `user_email_password` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱密码',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_su_user_id`(`user_id` ASC) USING BTREE,
  INDEX `sys_user_user_code_IDX`(`user_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17106 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (17085, 'IU202401010002', NULL, NULL, 'INTERNAL', '184运营', '<EMAIL>', '18696725971', 'IU202401310001', 'IU202401310001', 'O001002', 'ENABLE', NULL, NULL, NULL, '2025-06-20 16:56:51', 0, 'IU202401010001', '系统管理员', '2024-01-01 01:00:00', 'IU202401010001', '系统管理员', '2025-06-20 18:00:05', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17086, 'IU202506130001', '005VG000000JC3tYAG', '700008', 'INTERNAL', '罗蓉', '<EMAIL>', '18696725971', 'IU202506190001', 'IU202506190001', 'TRINA_CN_SALES_HUABEI', 'ENABLE', NULL, '2025-06-13 14:16:10', NULL, '2025-06-18 14:41:25', 0, 'IU202401010002', '184运营', '2025-06-13 14:16:10', 'IU202506130002', '李亮', '2025-06-24 17:34:20', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17087, 'IU202506130002', '005VG000000JC0fYAG', '700006', 'INTERNAL', '陈秦', '<EMAIL>', '18723894698', 'IU202506190001', 'IU202506190001', 'O000022', 'ENABLE', NULL, '2025-06-13 14:17:53', NULL, '2025-06-25 10:21:07', 0, 'IU202401010002', '184运营', '2025-06-13 14:17:53', 'IU202506130002', '李亮', '2025-06-24 18:14:07', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17088, 'EU202506160001', NULL, NULL, 'EXTERNAL', '陈秦', NULL, '18723894698', NULL, NULL, 'TRINA_CN_SALES_HUANAN', 'ENABLE', NULL, '2025-06-16 14:46:29', NULL, '2025-06-23 14:48:08', 0, 'IU202401010002', '184运营', '2025-06-16 14:46:29', 'IU202506130002', '李亮', '2025-06-24 18:04:59', '50', '重庆', '500100', '重庆市', 1, '', '2025-06-16 14:46:29', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17089, 'EU202506160002', NULL, NULL, 'EXTERNAL', '苏洪乙', NULL, '13146353077', NULL, NULL, 'TRINA_CN_SALES_HUANAN', 'ENABLE', NULL, '2025-06-16 14:54:43', NULL, '2025-06-16 14:54:46', 0, NULL, NULL, '2025-06-16 14:54:43', 'IU202506130002', '李亮', '2025-06-24 18:00:52', '50', '重庆', '500100', '重庆市', 2, '', '2025-06-24 18:00:52', NULL, NULL, NULL, NULL, NULL, '2025-06-24 18:00:52', 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17090, 'EU202506160003', NULL, NULL, 'EXTERNAL', '罗蓉', NULL, '18696725971', NULL, NULL, 'TRINA_CN_SALES_HUANAN', 'ENABLE', NULL, '2025-06-16 15:10:22', NULL, '2025-06-23 17:13:21', 0, 'IU202401010002', '184运营', '2025-06-16 15:10:22', 'IU202506130002', '李亮', '2025-06-24 17:58:05', '50', '重庆', '500100', '重庆市', 2, 't4', '2025-06-16 15:10:22', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17094, 'IU202506190001', '005VG000000JC5VYAW', '700009', 'INTERNAL', '陈文见', '<EMAIL>', '18215644525', '', '', 'TRINA', 'ENABLE', NULL, '2025-06-19 10:22:16', NULL, '2025-06-23 17:38:00', 0, 'IU202401010002', '184运营', '2025-06-19 10:22:16', 'IU202401010002', '184运营', '2025-06-23 17:37:34', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17095, 'IU202506190002', '005VG000000JBqzYAG', '700002', 'INTERNAL', '周巧', '<EMAIL>', '13452357702', 'IU202506130001', 'IU202506130001', 'TRINA_CN_SALES_HUABEI', 'ENABLE', NULL, '2025-06-19 10:37:27', NULL, '2025-06-20 15:37:47', 0, 'IU202506190001', '陈文见', '2025-06-19 10:37:27', 'IU202506130002', '李亮', '2025-06-24 18:10:37', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17096, 'IU202506190003', '005VG000000JCFBYA4', '700015', 'INTERNAL', '任洋杉', '<EMAIL>', '15621439012', 'IU202506240001', 'IU202506240001', 'TRINA_CN_OPERATION', 'ENABLE', NULL, '2025-06-19 10:40:12', NULL, '2025-06-25 10:57:06', 0, 'IU202506190001', '陈文见', '2025-06-19 10:40:12', 'IU202506130002', '李亮', '2025-06-24 17:52:22', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17098, 'IU202506190005', '005VG000000JBnlYAG', '700001', 'INTERNAL', '刘佳', '<EMAIL>', '15730329749', 'IU202506190009', 'IU202506130001', 'TRINA_CN_SALES_HUABEI', 'ENABLE', NULL, '2025-06-19 10:57:08', NULL, '2025-06-23 10:50:15', 0, 'IU202506190001', '陈文见', '2025-06-19 10:57:08', 'IU202506190001', '陈文见', '2025-06-23 10:55:10', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17099, 'IU202506190007', ' 005VG000000JC2HYAW', '700007', 'INTERNAL', '黄宇', '<EMAIL>', '17725150879', 'IU202506190001', 'IU202506190001', 'TRINA_CN_SALES_HUANAN', 'ENABLE', NULL, '2025-06-19 15:11:08', NULL, NULL, 0, 'IU202506130002', 'LL', '2025-06-19 15:11:08', 'IU202506130002', '李亮', '2025-06-24 17:27:14', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17100, 'EU202506190008', NULL, NULL, 'EXTERNAL', '张耀华', '', '18059864526', NULL, NULL, 'TRINA_CN_SALES_LUEXIANG', 'ENABLE', NULL, '2025-06-19 16:22:12', NULL, '2025-06-23 16:48:50', 0, 'IU202506130002', 'LL', '2025-06-19 16:22:12', 'IU202506130002', '李亮', '2025-06-24 17:59:26', '43', '湖南', '430700', '常德市', 1, '', '2025-06-19 16:22:12', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17101, 'IU202506190009', '005VG000000JBxRYAW', '700005', 'INTERNAL', '刘彦麟', '<EMAIL>', '13262772720', 'IU202506190001', 'IU202506190001', 'O000021', 'ENABLE', NULL, '2025-06-19 16:32:10', NULL, NULL, 0, 'IU202506130002', 'LL', '2025-06-19 16:32:10', 'IU202506130002', '李亮', '2025-06-24 17:31:22', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17102, 'EU202506230001', NULL, NULL, 'EXTERNAL', '张荣', NULL, '18726785678', NULL, NULL, 'TRINA_CN_SALES_HUABEI', 'ENABLE', NULL, '2025-06-23 14:29:35', NULL, NULL, 0, 'IU202506130002', 'LL', '2025-06-23 14:29:35', 'IU202506130002', '李亮', '2025-06-24 18:03:22', '11', '北京', '110100', '北京市', 2, '', '2025-06-23 14:29:35', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17103, 'EU202506230004', NULL, NULL, 'EXTERNAL', '徐璐', NULL, '15961568258', NULL, NULL, 'TRINA_CN_SALES_HUADONG', 'ENABLE', NULL, '2025-06-23 17:27:35', NULL, NULL, 0, 'IU202506190001', '陈文见', '2025-06-23 17:27:35', 'IU202506130002', '李亮', '2025-06-24 18:01:49', '32', '江苏', '320400', '常州市', 1, '', '2025-06-23 17:27:35', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17104, 'IU202506240001', '005VG000000JC77YAG', '700010', 'INTERNAL', '李庆', '<EMAIL>', '19102344811', 'IU202506190001', 'IU202506190001', 'TRINA_CN_OPERATION', 'ENABLE', NULL, '2025-06-24 17:45:24', NULL, NULL, 0, 'IU202506130002', '李亮', '2025-06-24 17:45:24', 'IU202506130002', '李亮', '2025-06-24 17:46:09', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user` VALUES (17105, 'EU202506240002', NULL, NULL, 'EXTERNAL', '陈秋逸', NULL, '15112148275', NULL, NULL, 'TRINA_CN_SALES_HUABEI', 'ENABLE', NULL, '2025-06-24 18:06:27', NULL, '2025-06-25 10:24:31', 0, 'IU202506130002', '李亮', '2025-06-24 18:06:27', 'IU202506130002', '李亮', '2025-06-25 10:56:49', '41', '河南', '410500', '安阳市', 2, '', '2025-06-24 18:06:27', NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统用户ID',
  `role_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '系统角色ID',
  `is_deleted` tinyint NOT NULL DEFAULT 0 COMMENT '是否已删除;1：已删除 0：未删除',
  `created_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人姓名',
  `created_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `updated_by` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
  `updated_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人姓名',
  `updated_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `sys_user_role_role_id_IDX`(`role_id` ASC) USING BTREE,
  INDEX `sys_user_role_user_id_index`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统用户-角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 'IU202401010002', 'R000001', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (2, 'IU202401010002', 'admin', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (3, 'IU202401010002', 'R001100', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (4, 'IU202401010002', 'R001200', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sys_user_role` VALUES (41, 'EU202506160002', 'externalRole', 0, NULL, NULL, '2025-06-16 14:54:43', 'IU202506130002', '李亮', '2025-06-24 18:01:04');
INSERT INTO `sys_user_role` VALUES (54, 'IU202506190001', 'R001100', 0, 'IU202401010002', '184运营', '2025-06-19 10:23:20', 'IU202401010002', '184运营', '2025-06-19 10:23:20');
INSERT INTO `sys_user_role` VALUES (55, 'IU202506190001', 'admin', 0, 'IU202401010002', '184运营', '2025-06-19 10:23:20', 'IU202401010002', '184运营', '2025-06-19 10:23:20');
INSERT INTO `sys_user_role` VALUES (58, 'IU202506190005', 'R001300', 0, 'IU202506190001', '陈文见', '2025-06-19 10:58:03', 'IU202506190001', '陈文见', '2025-06-19 10:58:03');
INSERT INTO `sys_user_role` VALUES (59, 'IU202506190009', 'R001200', 0, 'IU202506130002', 'LL', '2025-06-19 16:32:49', 'IU202506130002', 'LL', '2025-06-19 16:32:49');
INSERT INTO `sys_user_role` VALUES (60, 'IU202506190007', 'R001200', 0, 'IU202506130002', 'LL', '2025-06-19 16:33:09', 'IU202506130002', 'LL', '2025-06-19 16:33:09');
INSERT INTO `sys_user_role` VALUES (63, 'IU202506190003', 'R000501', 0, 'IU202506190001', '陈文见', '2025-06-23 17:26:51', 'IU202506190001', '陈文见', '2025-06-23 17:26:51');
INSERT INTO `sys_user_role` VALUES (64, 'IU202506190003', 'R001600', 0, 'IU202506190001', '陈文见', '2025-06-23 17:26:51', 'IU202506190001', '陈文见', '2025-06-23 17:26:51');
INSERT INTO `sys_user_role` VALUES (65, 'IU202506190002', 'R001300', 0, 'IU202506130002', '李亮', '2025-06-24 17:24:06', 'IU202506130002', '李亮', '2025-06-24 17:24:06');
INSERT INTO `sys_user_role` VALUES (66, 'IU202506130001', 'R001200', 0, 'IU202506130002', '李亮', '2025-06-24 17:37:42', 'IU202506130002', '李亮', '2025-06-24 17:37:42');
INSERT INTO `sys_user_role` VALUES (67, 'IU202506130001', 'admin', 0, 'IU202506130002', '李亮', '2025-06-24 17:37:42', 'IU202506130002', '李亮', '2025-06-24 17:37:42');
INSERT INTO `sys_user_role` VALUES (68, 'IU202506130002', 'R001200', 0, 'IU202506130002', '李亮', '2025-06-24 17:38:01', 'IU202506130002', '李亮', '2025-06-24 17:38:01');
INSERT INTO `sys_user_role` VALUES (69, 'IU202506130002', 'admin', 0, 'IU202506130002', '李亮', '2025-06-24 17:38:01', 'IU202506130002', '李亮', '2025-06-24 17:38:01');
INSERT INTO `sys_user_role` VALUES (70, 'IU202506240001', 'R001500', 0, 'IU202506130002', '李亮', '2025-06-24 17:45:38', 'IU202506130002', '李亮', '2025-06-24 17:45:38');
INSERT INTO `sys_user_role` VALUES (71, 'EU202506160003', 'R002100', 0, 'IU202506130002', '李亮', '2025-06-24 17:58:23', 'IU202506130002', '李亮', '2025-06-24 17:58:23');
INSERT INTO `sys_user_role` VALUES (72, 'EU202506160002', 'R002100', 0, 'IU202506130002', '李亮', '2025-06-24 18:01:04', 'IU202506130002', '李亮', '2025-06-24 18:01:04');
INSERT INTO `sys_user_role` VALUES (73, 'EU202506230001', 'R002200', 0, 'IU202506130002', '李亮', '2025-06-24 18:02:07', 'IU202506130002', '李亮', '2025-06-24 18:02:07');
INSERT INTO `sys_user_role` VALUES (74, 'EU202506190008', 'R002200', 0, 'IU202506130002', '李亮', '2025-06-24 18:04:16', 'IU202506130002', '李亮', '2025-06-24 18:04:16');
INSERT INTO `sys_user_role` VALUES (75, 'EU202506160001', 'R002100', 0, 'IU202506130002', '李亮', '2025-06-24 18:05:10', 'IU202506130002', '李亮', '2025-06-24 18:05:10');
INSERT INTO `sys_user_role` VALUES (76, 'EU202506160001', 'R001200', 0, 'IU202506130002', '李亮', '2025-06-24 18:05:10', 'IU202506130002', '李亮', '2025-06-24 18:05:10');
INSERT INTO `sys_user_role` VALUES (77, 'EU202506240002', 'R002200', 0, 'IU202506130002', '李亮', '2025-06-24 18:06:36', 'IU202506130002', '李亮', '2025-06-24 18:06:36');

SET FOREIGN_KEY_CHECKS = 1;
