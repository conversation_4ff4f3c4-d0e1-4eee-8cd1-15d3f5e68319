package com.trinasolar.trinax.gateway.config;


import com.trinasolar.trinax.gateway.caffeine.UserCache;
import com.trinasolar.trinax.gateway.filter.AccessAuthorizationFilter;
import com.trinasolar.trinax.gateway.filter.AccessLogFilter;
import com.trinasolar.trinax.gateway.filter.RequestPreFilter;
import com.trinasolar.trinax.gateway.handler.JsonAccessDeniedHandler;
import com.trinasolar.trinax.gateway.service.impl.AccessLogService;
import com.trinasolar.trinax.gateway.config.properties.IgnoreWhiteProperties;
import com.trinasolar.trinax.gateway.handler.JsonAuthenticationEntryPoint;
import com.trinasolar.trinax.gateway.oauth2.RedisAuthenticationManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.oauth2.server.resource.web.server.ServerBearerTokenAuthenticationConverter;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.authentication.AuthenticationWebFilter;
import org.springframework.security.web.server.authentication.ServerAuthenticationEntryPointFailureHandler;
import org.springframework.security.web.server.context.SecurityContextServerWebExchange;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import static io.netty.handler.codec.http.cookie.CookieHeaderNames.MAX_AGE;

/**
 * <pre>
 * Title: 网关过滤链
 *
 * Description:
 * <pre>
 * <AUTHOR>
 **/
@Slf4j
@Configuration
public class RedisResourceConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Autowired
    private AccessLogService accessLogService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private UserCache userCache;

    @Bean
    public TokenStore tokenStore() {
        return new RedisTokenStore(redisConnectionFactory);
    }


    /**
     * spring security网关过滤链
     *
     * @param http
     * @return
     * @throws Exception
     */
    @Bean
    SecurityWebFilterChain springWebFilterChain(ServerHttpSecurity http) throws Exception {
         //认证异常处理
        JsonAuthenticationEntryPoint entryPoint = new JsonAuthenticationEntryPoint(accessLogService);

         //权限异常处理
        JsonAccessDeniedHandler accessDeniedHandler = new JsonAccessDeniedHandler();

         //动态鉴权
        AccessAuthorizationFilter accessAuthorizationFilter = new AccessAuthorizationFilter(tokenStore(), redisTemplate, ignoreWhite,userCache);

         //oauth2认证
        AuthenticationWebFilter oauth2 =
                new AuthenticationWebFilter(new RedisAuthenticationManager(tokenStore()));
        oauth2.setServerAuthenticationConverter(new ServerBearerTokenAuthenticationConverter());
        oauth2.setAuthenticationFailureHandler(new ServerAuthenticationEntryPointFailureHandler(entryPoint));

        oauth2.setAuthenticationSuccessHandler((webFilterExchange, authentication) -> {
            ServerWebExchange exchange = webFilterExchange.getExchange();
            SecurityContextServerWebExchange securityContextServerWebExchange = new SecurityContextServerWebExchange(exchange, ReactiveSecurityContextHolder.getContext().subscriberContext(
                    ReactiveSecurityContextHolder.withAuthentication(authentication)
            ));
            return webFilterExchange.getChain().filter(securityContextServerWebExchange);
        });

        http.httpBasic().disable()
                .csrf().disable()
                .authorizeExchange()
                .pathMatchers("/").permitAll()
                // 动态权限验证
                .anyExchange().access(accessAuthorizationFilter)
                .and().exceptionHandling()
                .accessDeniedHandler(accessDeniedHandler)
                .authenticationEntryPoint(entryPoint)
                .and()
                // 前置过滤器
                .addFilterAt(new RequestPreFilter(), SecurityWebFiltersOrder.FIRST)
                //跨域过滤器
                .addFilterAt(corsFilter(), SecurityWebFiltersOrder.CORS)
                 //oauth2认证过滤器
                .addFilterAt(oauth2, SecurityWebFiltersOrder.AUTHENTICATION);
                // 日志过滤器
//                .addFilterAt(new AccessLogFilter(accessLogService), SecurityWebFiltersOrder.SECURITY_CONTEXT_SERVER_WEB_EXCHANGE);

        return http.build();
    }


    /**
     * 跨域配置
     *
     * @return
     */
    public WebFilter corsFilter() {

        return (ServerWebExchange ctx, WebFilterChain chain) -> {

            ServerHttpRequest request = ctx.getRequest();
            if (CorsUtils.isCorsRequest(request)) {

                HttpHeaders requestHeaders = request.getHeaders();
                ServerHttpResponse response = ctx.getResponse();
                HttpMethod requestMethod = requestHeaders.getAccessControlRequestMethod();
                HttpHeaders headers = response.getHeaders();
                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, requestHeaders.getOrigin());
                headers.addAll(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, requestHeaders.getAccessControlRequestHeaders());

                if (requestMethod != null) {
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, "POST,GET,PUT,DELETE,PATCH,TRACE,HEAD,CONNECT,OPTIONS");
                }

                headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
                headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
                headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, MAX_AGE);

                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }

            return chain.filter(ctx);
        };
    }
}
