apiVersion: apps/v1
kind: Deployment
metadata:
  name: drb-dtt-gateway
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: drb-dtt-gateway
  template:
    metadata:
      labels:
        app: drb-dtt-gateway
    spec:
      containers:
        - name: drb-dtt-gateway
          image: your-registry/dtt-gateway:latest  # 替换为自己的镜像地址
          ports:
            - containerPort: 8080
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "1Gi"
              cpu: "1"
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/ready
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 5
      dnsPolicy: ClusterFirst
      restartPolicy: Always
