package com.trinasolar.trinax.log.consumer;

import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.log.constants.LogTopic;
import com.trinasolar.trinax.log.dto.mq.BehaviorLogMqDTO;
import com.trinasolar.trinax.log.service.BehaviorLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(topic = LogTopic.BEHAVIOR_LOG_TOPIC, consumerGroup = "Report_" + LogTopic.BEHAVIOR_LOG_TOPIC)
public class BehaviorLogConsumer implements RocketMQListener<String> {

    private final BehaviorLogService behaviorLogService;

    @Override
    public void onMessage(String reqStr) {
        BehaviorLogMqDTO req = JacksonUtil.json2Bean(reqStr,  BehaviorLogMqDTO.class);
        behaviorLogService.saveLog(req);
    }

}
