<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.trinasolar</groupId>
        <artifactId>trinax-shared</artifactId>
        <version>0.0.2.RELEASE</version>
        <relativePath>../trinax-shared</relativePath>
    </parent>


    <groupId>com.trinasolar</groupId>
    <artifactId>trinax-report-root</artifactId>
    <version>0.0.4-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>trinax-report</name>
    <description>trina日志服务</description>
    <modules>
        <module>trinax-report-api</module>
        <module>trinax-report-domain</module>
        <module>trinax-report-boot</module>
        <module>trinax-behavior-log</module>
    </modules>
    <properties>
        <java.version>17</java.version>
        <mybatis-starter.version>0.0.2.RELEASE</mybatis-starter.version>
        <dtt-mq.version>0.0.2.RELEASE</dtt-mq.version>
        <dtt-xxljob.version>0.0.2.RELEASE</dtt-xxljob.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-report-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-report-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-basic-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-integration-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-user-api</artifactId>
                <version>0.0.4-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.trinasolar</groupId>
                <artifactId>trinax-common</artifactId>
                <version>0.0.2.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>dtt.asset</groupId>
                <artifactId>dtt-db-mybatis-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>dtt.asset</groupId>
                <artifactId>dtt-framework-mq</artifactId>
                <version>${dtt-mq.version}</version>
            </dependency>
            <dependency>
                <groupId>dtt.asset</groupId>
                <artifactId>dtt-framework-xxljob</artifactId>
                <version>${dtt-xxljob.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>