package com.trinasolar.trinax.log.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作结果
 */
@Getter
@AllArgsConstructor
public enum SourceEnum {

    PC("PC", "PC端","PLATFORM_PC"),
    APP("APP", "移动端","PLATFORM_APP"),
    MINI_PROGRAM("MINI_PROGRAM", "小程序","PLATFORM_MINI_PROGRAM"),
    ;

    private String code;
    private String desc;
    private String source;

    public static String getCodeBySource(String source) {
        for (SourceEnum value : SourceEnum.values()) {
            if (value.getSource().equals(source)) {
                return value.getCode();
            }
        }
        return "";
    }
    public static String getDescByCode(String code) {
        for (SourceEnum value : SourceEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
    public boolean equalsCode(String code) {
        return this.getCode().equals(code);
    }

}
