package com.trinasolar.trinax.log.dto.input.integration;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@Schema
public class IntegrationQryReqDTO {

    @Schema(description = "业务单号")
    private String bizNo;

    @Schema(description = "业务类型")
    private String bizType;

    @Schema(description = "业务操作:DELIVER_COMFIRM(运营确认创建so)")
    private String bizOperation;

    @Schema(description = "集成系统:SF，QYS-契约锁")
    private String integrationSystem;

    @Schema(description = "集成方向:OUT，IN")
    private String integrationForward;

    @Schema(description = "响应状态（SUCCESS, FAIL）")
    private String responseStatus;

    @Schema(description = "响应时间范围 秒级别")
    private String maxResponseTime;

    @Schema(description = "响应时间范围 秒级别")
    private String minResponseTime;

    @Schema(description = "操作人")
    private String userId;

    @Schema(description = "请求时间")
    private String requestTime;

    @Schema(description = "创建时间起")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建时间止")
    private LocalDateTime createdTimeEnd;
}
