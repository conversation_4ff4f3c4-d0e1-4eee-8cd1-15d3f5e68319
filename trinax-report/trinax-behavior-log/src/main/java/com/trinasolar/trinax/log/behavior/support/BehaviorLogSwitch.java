package com.trinasolar.trinax.log.behavior.support;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RefreshScope
public class BehaviorLogSwitch {

    @Value("${behavior.log.switch:open}")
    private String behaviorLogSwitch;

    public boolean isOpen() {
        if (StrUtil.equals(behaviorLogSwitch, "open")) {
            return true;
        }
        return false;
    }

}
