package com.trinasolar.trinax.log.behavior.support;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.log.constants.LogTopic;
import com.trinasolar.trinax.log.constants.enums.OpResultEnum;
import com.trinasolar.trinax.log.constants.enums.SourceEnum;
import com.trinasolar.trinax.log.dto.mq.BehaviorLogMqDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;

@Slf4j
@Aspect
@Component
@Order(-2)
//@RefreshScope 不能再切面类上使用此注解，会导致一次请求触发2次切面方法
@RequiredArgsConstructor
public class BehaviorLogAspect {

    private static final ThreadLocal<LocalDateTime> requestThreadLocal = new ThreadLocal();
    private SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
    private LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
    private SendCallback sendCallback = new SendCallback() {
        @Override
        public void onSuccess(SendResult sendResult) {
            log.info("发送用户行为日志MQ成功，msgId={}", sendResult.getMsgId());
        }
        @Override
        public void onException(Throwable throwable) {
            log.error("发送用户行为日志MQ失败，message={}", throwable.getMessage());
        }
    };

    private final ObjectMapper mapper;
    private final RocketMQTemplate rocketMQTemplate;
    private final BehaviorLogSwitch behaviorLogSwitch;

    @Pointcut("(execution(* com.trinasolar.trinax..*(..))) && (@within(org.springframework.web.bind.annotation.RestController) || @within(org.springframework.stereotype.Controller))")
    public void behaviorLog() {
    }

    @Before("behaviorLog()")
    public void doBefore(JoinPoint joinPoint) {
        if (!behaviorLogSwitch.isOpen()) {
            requestThreadLocal.remove();
            return;
        }
        requestThreadLocal.set(LocalDateTime.now());
    }

    @AfterReturning(pointcut = "behaviorLog()", returning = "result")
    public void afterReturning(JoinPoint joinPoint, Object result) {
        if (!behaviorLogSwitch.isOpen()) {
            return;
        }
        sendLog(joinPoint, result, null);
    }

    @AfterThrowing(pointcut = "behaviorLog()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Throwable e) {
        if (!behaviorLogSwitch.isOpen()) {
            return;
        }
        sendLog(joinPoint, null, e);
    }

    private void sendLog(JoinPoint joinPoint, Object result, Throwable e) {
        try {
            // 忽略使用注解指定需要忽略的
            BehaviorLog annotation = getAnnotation(joinPoint);
            log.info("BehaviorLogAspect sendLog annotation:{}",JSONUtil.toJsonPrettyStr(annotation));
            log.info("BehaviorLogAspect sendLog result:{}",JSONUtil.toJsonPrettyStr(result));
            log.info("BehaviorLogAspect sendLog joinPoint:{}",JSONUtil.toJsonPrettyStr(joinPoint));
            if (annotation == null || annotation.ignore()) {
                return;
            }

            // 忽略查询方法（部分非GET请求的查询方法，需要使用注解进行忽略）
            String requestMethod = getRequestMethod();
            if (StrUtil.equals("GET", requestMethod)) {
                return;
            }


            String bizNo = getBizNo(annotation, joinPoint, result);
            String args = getArg(annotation, joinPoint);
            String url = getRequestURL();
            String source = getSource();

            BehaviorLogMqDTO logMq = new BehaviorLogMqDTO();
            logMq.setSource(source);
            logMq.setBizNo(bizNo);
            logMq.setBizReq(args);
            logMq.setOpTime(requestThreadLocal.get());
            logMq.setOpUrl(url);
            logMq.setOpMethod(requestMethod);
            logMq.setOpResult(calcOpResult(result, e));
            logMq.setOpException(e==null?null: ExceptionUtils.getStackTrace(e));
            AuthUserDetails user = AuthUserHelper.getAuthUser();
            if(ObjectUtil.isNotEmpty(user)){
                logMq.setOpUser(user.getUserIdStr());
                logMq.setOpUserName(user.getName());
                logMq.setOpUserType(user.getUserType());
            }else{
                logMq.setOpUser(getUserId(annotation, joinPoint, result));
                String username = getUserName(annotation, joinPoint, result);
                if("null".equals(username)){
                    username = null;
                }
                logMq.setOpUserName(username);
                logMq.setOpUserType(getUserType(annotation, joinPoint, result));
            }
            rocketMQTemplate.asyncSend(LogTopic.BEHAVIOR_LOG_TOPIC, JacksonUtil.bean2Json(logMq), sendCallback);

            String methodName = joinPoint.getSignature().getName();
            log.info("发送行为日志消息成功，methodName={}", methodName);
        } catch (Exception ex) {
            String methodName = joinPoint.getSignature().getName();
            log.error("发送行为日志消息失败，methodName={}", methodName, ex);
        } finally {
            requestThreadLocal.remove();
        }
    }

    private String getSource() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        if (ObjectUtil.isEmpty(request)) {
            return null;
        }
        String source = ServletUtil.getHeaderIgnoreCase(request, "Platform");
        log.info("获取平台来源信息成功，source:{}", source);
        if(StrUtil.isEmpty(source)){
            return SourceEnum.PC.getCode();
        }
        return SourceEnum.getCodeBySource(source);
    }

    private String calcOpResult(Object result, Throwable e) {
        if (e != null) {
            return OpResultEnum.EXCEPTION.getCode();
        }
        if (result == null) {
            return OpResultEnum.SUCCESS.getCode();
        }

        Result rst = (Result)result;
        if (StrUtil.equals(ResultCode.OK.getCode(), rst.getCode())) {
            return OpResultEnum.SUCCESS.getCode();
        }
        if (StrUtil.equals(ResultCode.SYSTEM_ERROR.getCode(), rst.getCode())) {
            return OpResultEnum.EXCEPTION.getCode();
        }
        return OpResultEnum.FAIL.getCode();
    }

    private String getRequestURL() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        if (ObjectUtil.isEmpty(request)) {
            return null;
        }
        log.info("request:{}", JSONUtil.toJsonPrettyStr(request));
        return request.getRequestURI();
    }

    private String getRequestMethod() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        if (ObjectUtil.isEmpty(request)) {
            return null;
        }
        return request.getMethod();
    }

    private String getArg(BehaviorLog annotation, JoinPoint joinPoint) {
        if (annotation != null &&  annotation.ignoreReq()) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        try {
            if(joinPoint.getArgs() != null && joinPoint.getArgs().length > 0){
                for(int i = 0; i < joinPoint.getArgs().length; ++i){
                    sb.append(this.mapper.writeValueAsString(joinPoint.getArgs()[i]));
                }
            }else{
                sb.append(this.mapper.writeValueAsString(joinPoint.getArgs()));
            }
        } catch (JsonProcessingException var4) {
        }
        return sb.toString();
    }

    /**
     * 获取指定注解实例
     */
    private BehaviorLog getAnnotation(JoinPoint joinPoint) {
        MethodSignature sign = (MethodSignature) joinPoint.getSignature();
        Method method = sign.getMethod();
        return method.getAnnotation(BehaviorLog.class);
    }

    private String getBizNo(BehaviorLog annotation, JoinPoint joinPoint, Object result) {
        if (annotation == null || StrUtil.isEmpty(annotation.bizNo())) {
            return null;
        }

        String expression = annotation.bizNo();
        if (StrUtil.startWith(expression, "#result") && result == null) {
            return null;
        }

        try {
            Expression spelExpression = spelExpressionParser.parseExpression(annotation.bizNo());
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Object value = spelExpression.getValue(context);
            if (value instanceof String) {
                return (String)value;
            } else {
                return JacksonUtil.bean2Json(value);
            }
        } catch (Exception e) {
            String methodName = joinPoint.getSignature().getName();
            log.warn("记录行为日志时获取bizNo失败，请检查表达式是否正确。methodName={}，expression={}", methodName, annotation.bizNo());
            return null;
        }
    }

    private String getUserId(BehaviorLog annotation, JoinPoint joinPoint, Object result) {
        if (annotation == null || StrUtil.isEmpty(annotation.userId())) {
            return null;
        }

        String expression = annotation.userId();
        if (StrUtil.startWith(expression, "#result") && result == null) {
            return null;
        }

        try {
            Expression spelExpression = spelExpressionParser.parseExpression(annotation.userId());
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Object value = spelExpression.getValue(context);
            if (value instanceof String) {
                return (String)value;
            } else {
                return JacksonUtil.bean2Json(value);
            }
        } catch (Exception e) {
            String methodName = joinPoint.getSignature().getName();
            log.warn("记录行为日志时获取userId失败，请检查表达式是否正确。methodName={}，expression={}", methodName, annotation.userId());
            return null;
        }
    }

    private String getUserType(BehaviorLog annotation, JoinPoint joinPoint, Object result) {
        if (annotation == null || StrUtil.isEmpty(annotation.userType())) {
            return null;
        }

        String expression = annotation.userType();
        if (StrUtil.startWith(expression, "#result") && result == null) {
            return null;
        }

        try {
            Expression spelExpression = spelExpressionParser.parseExpression(annotation.userType());
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Object value = spelExpression.getValue(context);
            if (value instanceof String) {
                return (String)value;
            } else {
                return JacksonUtil.bean2Json(value);
            }
        } catch (Exception e) {
            String methodName = joinPoint.getSignature().getName();
            log.warn("记录行为日志时获取userType失败，请检查表达式是否正确。methodName={}，expression={}", methodName, annotation.userType());
            return null;
        }
    }

    private String getUserName(BehaviorLog annotation, JoinPoint joinPoint, Object result) {
        if (annotation == null || StrUtil.isEmpty(annotation.userName())) {
            return null;
        }

        String expression = annotation.userName();
        if (StrUtil.startWith(expression, "#result") && result == null) {
            return null;
        }

        try {
            Expression spelExpression = spelExpressionParser.parseExpression(annotation.userName());
            EvaluationContext context = createEvaluationContext(joinPoint, result);
            Object value = spelExpression.getValue(context);
            if (value instanceof String) {
                return (String)value;
            } else {
                return JacksonUtil.bean2Json(value);
            }
        } catch (Exception e) {
            String methodName = joinPoint.getSignature().getName();
            log.warn("记录行为日志时获取userName失败，请检查表达式是否正确。methodName={}，expression={}", methodName, annotation.userName());
            return null;
        }
    }

    /**
     * 获取当前执行的方法
     */
    private Method getMethod(JoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        return method;
    }

    /**
     * 将参数名与参数值对应起来
     */
    private EvaluationContext createEvaluationContext(JoinPoint joinPoint, Object result) throws NoSuchMethodException {
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("result", result);

        // 获取方法的参数名
        String[] params = discoverer.getParameterNames(getMethod(joinPoint));
        if (params == null || params.length == 0) {
            return context;
        }

        for (int len = 0; len < params.length; len++) {
            context.setVariable(params[len], joinPoint.getArgs()[len]);
        }
        return context;
    }

}
