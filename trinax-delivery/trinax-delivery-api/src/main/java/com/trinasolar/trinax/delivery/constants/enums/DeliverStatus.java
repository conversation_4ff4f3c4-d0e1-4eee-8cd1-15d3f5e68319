package com.trinasolar.trinax.delivery.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliverStatus {
    UN_SUBMIT("UN_SUBMIT","未提交"),
    SUBMIT("SUBMIT","已提交"),
    SALES_CONFIRMED("SALES_CONFIRMED","销售已确认"),
    OPERATION_CONFIRMED("OPERATION_CONFIRMED","运营已确认"),
    CANCELED("CANCELED","已取消");

    /**
     * 状态编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    public static String getDescByCode(String code) {
        for (DeliverStatus value : DeliverStatus.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
