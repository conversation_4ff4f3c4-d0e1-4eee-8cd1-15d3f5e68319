package com.trinasolar.trinax.delivery.service.biz.ops;

import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.delivery.constants.DeliveryConstant;
import com.trinasolar.trinax.delivery.constants.enums.DeliverStatus;
import com.trinasolar.trinax.delivery.constants.enums.DeliverySituationEnum;
import com.trinasolar.trinax.delivery.dto.input.delivery.reverse.ReverseDeliverReqDTO;
import com.trinasolar.trinax.delivery.manager.DeliveryTodoListManger;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.service.biz.lock.business.DeliverBusinessLock;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverManager;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trinasolar.trinax.delivery.constants.DeliverResultCode.DELIVER_CANCEL_ERROR;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverOpsReverseBiz implements DeliverBusinessLock {
    final DeliverMapperService deliverMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final DeliverBizManager deliverBizManager;
    final SysUserFeign sysUserFeign;
    final DeliverManager deliverManager;
    final MqManager mqManager;
    final TodoListFeign todoListFeign;
    final RedissonClient redissonClient;
    final DeliveryTodoListManger deliveryTodoListManger;


    public Result<Object> reverseDeliver(ReverseDeliverReqDTO req) {
        List<String> lockList = new ArrayList<>();
        lockList.add(req.getDeliverNo());
        return this.lockBusiness(JacksonUtil.bean2Json(req), lockList, ReverseDeliverReqDTO.class, redissonClient);
    }

    @Override
    public Result<Object> doBusiness(Object reqBody) {
        ReverseDeliverReqDTO req = (ReverseDeliverReqDTO) reqBody;
        String deliverNo = req.getDeliverNo();
        LocalDateTime now = LocalDateTime.now();
        String userId = req.getUserId();
        SysUserRespDTO sysUser = sysUserFeign.getUserByUserId(userId).getData();
        if (1 == deliverManager.getDeliverCount(req.getDeliverNo(), DeliverStatus.CANCELED.getCode())) {
            return Result.fail(DELIVER_CANCEL_ERROR.getCode(), DELIVER_CANCEL_ERROR.getMessage());
        }
        if (deliverManager.notExistedDeliver(req.getDeliverNo(), DeliverStatus.SALES_CONFIRMED.getCode())) {
            return Result.fail("发货单：" + req.getDeliverNo() + "已取消或不存在");
        }

        deliverMapperService.lambdaUpdate()
                .eq(DeliverPO::getDeliverNo, deliverNo)
                .set(DeliverPO::getDeliverStatus, DeliverStatus.SUBMIT.getCode())
                .set(DeliverPO::getDeliverStatusFalse, DeliverStatus.SUBMIT.getCode())
                .set(DeliverPO::getOperationReturnReason, req.getReturnReason())
                .set(DeliverPO::getOperationReturnUserId, userId)
                .set(DeliverPO::getOperationReturnTime, now)
                .update();

        // 查询基本数据
        DeliverPO deliverPO = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();
        deliverPO.setOperationInternalUserId(userId); // 发消息的时候，谁退的发谁的消息
        List<DeliverContractItemPO> deliverContractItemPOList = deliverContractItemMapperService.lambdaQuery()
                .eq(DeliverContractItemPO::getDeliverNo, deliverNo).list();
        // 发送消息
        mqManager.sendTopic(DeliveryConstant.DELIVERY_REQUEST_MESSAGE_TOPIC,
                JacksonUtil.bean2Json(deliverBizManager.composeDeliverMessage(
                        DeliverySituationEnum.DR_INTERNAL_OPERATION_BACK,
                        sysUser, deliverPO, deliverContractItemPOList)));
        // 更新待办状态
        deliveryTodoListManger.updateTodoList(deliverPO, now, null, TodoBizCodeEnum.DR_INTERNAL_CONFIRM.getCode());
        return Result.ok(deliverNo);
    }

    @Override
    public Result<Object> lockBusiness(String req, List lockKeyListStr, Class beanType, RedissonClient redissonClient) {
        return DeliverBusinessLock.super.lockBusiness(req, lockKeyListStr, beanType, redissonClient);
    }
}
