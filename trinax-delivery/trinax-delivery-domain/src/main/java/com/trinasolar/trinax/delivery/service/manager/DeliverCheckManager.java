package com.trinasolar.trinax.delivery.service.manager;

import com.trinasolar.trinax.cart.dto.input.ContractQueryReqItemIdListDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryItemResDTO;
import com.trinasolar.trinax.delivery.constants.DeliverResultCode;
import com.trinasolar.trinax.delivery.constants.enums.DeliveryStatusFake;
import com.trinasolar.trinax.delivery.dto.output.delivery.check.DetailCheckResDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.dao.DeliverDetailCheckDTO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.mapper.DeliverMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverCheckManager {

    final DeliverMapper deliverMapper;
    final DeliverMapperService deliverMapperService;
    final DeliverContractMapperService deliverContractMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final ContractFeign contractFeign;

    public Result<DetailCheckResDTO> deliverDetailCheck(String deliverNo, DeliverPO deliver) {
        if (ObjectUtils.isEmpty(deliver)) {
            throw new BizException(ResultCode.FAIL.getCode(), "发货申请单:" + deliverNo + "无效");
        }

        //step1 查询当前发货对应 的所有合同行信息 不包含已取消
        List<DeliverDetailCheckDTO> items = null;

        if (DeliveryStatusFake.UN_SUBMIT.getCode().equals(deliver.getDeliverStatusFalse())) {
            //未取消的
            items = deliverContractItemMapperService.selNoCanceledItems(deliverNo);
        } else {
            //已确认，已提交的
            items = deliverContractItemMapperService.selSubmitItems(deliverNo);
        }

        log.info("发货校验 items： {} ", JacksonUtil.bean2Json(items));

        //step2 放入 itemId 和合同的映射关系
        Map<String, DeliverDetailCheckDTO> deliverNoMap = new HashMap<>();
        for (DeliverDetailCheckDTO item : items) {
            if (StringUtils.equals(item.getDeliverNo(), deliverNo)) {
                deliverNoMap.put(item.getContractBusinessItemId(), item);
            }
        }

        List<ContractDeliveryItemResDTO> remoteItemList = contractFeign.deliveryListItemByContractItemIds(
                new ContractQueryReqItemIdListDTO(
                        items.stream().map(DeliverDetailCheckDTO::getContractBusinessItemId)
                                .collect(Collectors.toList()))).getData();

//        String str = "[{\"contractBusinessItemId\":\"20240425000019\",\"contractId\":\"20240425000007\",\"sfOpportunityItemId\":\"00kWI000000HIoXYAW\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":600.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":9000.000000,\"quantityMw\":5.400000,\"quantityW\":0.0000054,\"netPrice\":0.998000,\"totalAdditionalCost\":0.002000,\"unitPriceW\":1.000000,\"unitPriceP\":600.000000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":9000,\"totalAmount\":5400000.000000,\"productName\":\"DE20\",\"productCatgory\":null,\"priceBook\":\"01u2I00000eJTNHQA4\",\"efc\":\"false\",\"sfProductId\":\"01t2I0000082OMuQAM\",\"pbiProductId\":\"DE20_CHN_2022B\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rOTYAY\",\"moduleType\":\"Module\"},{\"contractBusinessItemId\":\"20240425000001\",\"contractId\":\"20240425000005\",\"sfOpportunityItemId\":\"00kWI000000HIoXYAW\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":600.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":9000.000000,\"quantityMw\":5.400000,\"quantityW\":0.0000054,\"netPrice\":0.998000,\"totalAdditionalCost\":0.002000,\"unitPriceW\":1.000000,\"unitPriceP\":600.000000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":9000,\"totalAmount\":5400000.000000,\"productName\":\"DE20\",\"productCatgory\":null,\"priceBook\":\"01u2I00000eJTNHQA4\",\"efc\":\"false\",\"sfProductId\":\"01t2I0000082OMuQAM\",\"pbiProductId\":\"DE20_CHN_2022B\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rLFYAY\",\"moduleType\":\"Module\"},{\"contractBusinessItemId\":\"20240425000002\",\"contractId\":\"20240425000005\",\"sfOpportunityItemId\":\"00kWI000000HIoYYAW\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":540.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":500.000000,\"quantityMw\":0.270000,\"quantityW\":2.7E-7,\"netPrice\":0.888000,\"totalAdditionalCost\":0.222000,\"unitPriceW\":1.110000,\"unitPriceP\":599.400000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":500,\"totalAmount\":299700.000000,\"productName\":\"DE18\",\"productCatgory\":null,\"priceBook\":\"01u2I00000erebsQAA\",\"efc\":\"false\",\"sfProductId\":\"01t2I000004n1dGQAQ\",\"pbiProductId\":\"DE18_CHN_2023A\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rLGYAY\",\"moduleType\":\"Module\"},{\"contractBusinessItemId\":\"20240425000003\",\"contractId\":\"20240425000005\",\"sfOpportunityItemId\":\"00kWI000000HIoZYAW\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":605.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":1500.000000,\"quantityMw\":0.907500,\"quantityW\":9.075E-7,\"netPrice\":0.777000,\"totalAdditionalCost\":0.123000,\"unitPriceW\":0.900000,\"unitPriceP\":544.500000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":1500,\"totalAmount\":816750.000000,\"productName\":\"NEG19RC.20\",\"productCatgory\":null,\"priceBook\":\"01u2I00000e3CgCQAU\",\"efc\":\"false\",\"sfProductId\":\"01t2I000006t7SDQAY\",\"pbiProductId\":\"NEG19RC.20_CHN_2023A\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rLHYAY\",\"moduleType\":\"Module\"},{\"contractBusinessItemId\":\"20240425000004\",\"contractId\":\"20240425000005\",\"sfOpportunityItemId\":\"00kWI000000HIoaYAG\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":570.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":5000.000000,\"quantityMw\":2.850000,\"quantityW\":0.00000285,\"netPrice\":0.666000,\"totalAdditionalCost\":0.333000,\"unitPriceW\":0.999000,\"unitPriceP\":569.430000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":5000,\"totalAmount\":2847150.000000,\"productName\":\"DE19R\",\"productCatgory\":null,\"priceBook\":\"01u2I00000e3CWQQA2\",\"efc\":\"false\",\"sfProductId\":\"01t2I00000842ZeQAI\",\"pbiProductId\":\"DE19R_CHN_2023B\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rLIYAY\",\"moduleType\":\"Module\"},{\"contractBusinessItemId\":\"20240425000005\",\"contractId\":\"20240425000005\",\"sfOpportunityItemId\":\"00kWI000000HIobYAG\",\"plugConnector\":\"TS4\",\"installation\":\"Portrait\",\"cableLength\":\"\",\"cableLengthPositive\":0.350000,\"cableLengthCathode\":0.280000,\"power\":545.000000,\"expectedDeliverDate\":\"2024-04-30 00:00:00\",\"quantityP\":1000.000000,\"quantityMw\":0.545000,\"quantityW\":5.45E-7,\"netPrice\":0.900000,\"totalAdditionalCost\":0.000000,\"unitPriceW\":0.900000,\"unitPriceP\":490.500000,\"notes\":\"\",\"destinationPortId\":\"\",\"loadingPortId\":\"\",\"currency\":\"CNY\",\"remainingQuantityP\":1000,\"totalAmount\":490500.000000,\"productName\":\"DE18\",\"productCatgory\":null,\"priceBook\":\"01u2I00000erebsQAA\",\"efc\":\"false\",\"sfProductId\":\"01t2I000004n1dGQAQ\",\"pbiProductId\":\"DE18_CHN_2023A\",\"itemType\":\"PRODUCT\",\"sfContractItemId\":\"a0sWI0000009rLJYAY\",\"moduleType\":\"Module\"}]";
//        List<ContractDeliveryItemResDTO> remoteItemList = JacksonUtil.json2List(str, ContractDeliveryItemResDTO.class);
        log.info("合同服务返回 data : {}", JacksonUtil.bean2Json(remoteItemList));
        HashMap<String, Integer> itemMap = new HashMap<>();
        //step3 累计计算合同的剩余量
        items.forEach(a -> {
            Integer count = itemMap.get(a.getContractBusinessItemId());
            if (ObjectUtils.isEmpty(count)) {
                itemMap.put(a.getContractBusinessItemId(), a.getQuantityP());
            } else {
                itemMap.put(a.getContractBusinessItemId(), a.getQuantityP() + count);
            }
        });
        //step4 sf的剩余数量
        Map<String, ContractDeliveryItemResDTO> remoteMap = remoteItemList.stream().collect(Collectors.toMap(ContractDeliveryItemResDTO::getContractBusinessItemId, Function.identity()));

        StringBuilder errMsg = new StringBuilder();

        //默认 发货申请超发校验
        StringBuilder errCode = new StringBuilder();


        List<String> deliverContractItemIds = new ArrayList<>();

        log.info("发货校验 itemMap： {} ", JacksonUtil.bean2Json(itemMap));

        for (Map.Entry<String, Integer> entry : itemMap.entrySet()) {
            ContractDeliveryItemResDTO remoteItem = remoteMap.get(entry.getKey());

            DeliverDetailCheckDTO checkDTO = deliverNoMap.get(entry.getKey());
            if (ObjectUtils.isEmpty(checkDTO)){
                continue;
            }

            if (ObjectUtils.isEmpty(remoteItem)) {
                deliverContractItemIds.add(deliverNoMap.get(entry.getKey()).getDeliverContractItemId());
                String template = "%s：%s | %s、";
                String result = String.format(template, checkDTO.getSfContractNo(), checkDTO.getProductName(), checkDTO.getPower() + "w");
                errMsg.append(result);

                if (StringUtils.isBlank(errCode)) {
                    errCode.append(DeliverResultCode.DELIVER_DETAIL_LOST_ERROR.getCode());
                }
                continue;
            }
            int currentQuantity = ObjectUtils.isEmpty(remoteItem.getRemainingQuantityP()) ? 0 : remoteItem.getRemainingQuantityP();
            if (entry.getValue() > currentQuantity) {
                String template = "%s：%s | %s、";
                String result = String.format(template, checkDTO.getSfContractNo(), checkDTO.getProductName(), checkDTO.getPower() + "w");
                errMsg.append(result);
                if (StringUtils.isBlank(errCode)) {
                    errCode.append(DeliverResultCode.DELIVER_DETAIL_QUANTITY_ERROR.getCode());
                }
                if (StringUtils.equals(errCode, DeliverResultCode.DELIVER_DETAIL_LOST_ERROR.getCode())) {
                    errCode.replace(0, errCode.length(), DeliverResultCode.DELIVER_DETAIL_QUANTITY_ERROR.getCode());
                }
            }

        }

        if (StringUtils.isNotBlank(errMsg.toString())) {
            String errMessage = errMsg.substring(0, errMsg.length() - 1);
            return Result.result(errCode.toString(), errCode.toString(), DetailCheckResDTO.builder()
                    .setErrMessage(errMessage)
                    .setDeliverContractItemIds(deliverContractItemIds));
        } else {
            return Result.ok();
        }
    }


}
