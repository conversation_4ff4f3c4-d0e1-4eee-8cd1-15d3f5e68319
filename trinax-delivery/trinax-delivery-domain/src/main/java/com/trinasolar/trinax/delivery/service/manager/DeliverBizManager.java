package com.trinasolar.trinax.delivery.service.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.cart.dto.input.ContractQueryReqIdListDTO;
import com.trinasolar.trinax.cart.dto.input.ContractQueryReqItemIdListDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryItemResDTO;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryResDTO;
import com.trinasolar.trinax.delivery.constants.DeliverResultCode;
import com.trinasolar.trinax.delivery.constants.enums.DeliverMethod;
import com.trinasolar.trinax.delivery.constants.enums.DeliverOperationTypeEnum;
import com.trinasolar.trinax.delivery.constants.enums.DeliverySituationEnum;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.SaveDeliveryContract;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.SaveDeliveryContractItem;
import com.trinasolar.trinax.delivery.dto.input.delivery.save.SaveDeliveryReqDTO;
import com.trinasolar.trinax.delivery.dto.mq.delivery.DeliveryMessageMqDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.domain.list.LockedQuantityListDTO;
import com.trinasolar.trinax.delivery.repository.mapper.DeliverBaseMapper;
import com.trinasolar.trinax.delivery.util.DeliverNoGenerator;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.enterprise.AddressStructure;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRespDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.dto.output.SysEnterpriseUserRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum.COUNTERSIGNED;
import static com.trinasolar.trinax.delivery.constants.BusinessPrefix.DREC;
import static com.trinasolar.trinax.delivery.constants.BusinessPrefix.DRECI;
import static com.trinasolar.trinax.delivery.constants.DeliverResultCode.NON_COUNTERSIGNED_ERROR;
import static com.trinasolar.trinax.delivery.constants.DeliverResultCode.ORDER_TYPE_MAP_ERROR;

@Service
@Slf4j
@RequiredArgsConstructor
@RefreshScope
public class DeliverBizManager<T> {
    final DeliverMapperService deliverMapperService;
    final DeliverContractMapperService deliverContractMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final ContractFeign contractFeign;
    final DeliverNoGenerator deliverNoGenerator;
    final EnterpriseFeign enterpriseFeign;
    final SysUserFeign sysUserFeign;
    final DeliverManager deliverManager;
    final ProductFeign productFeign;
    final DeliverBaseMapper deliverBaseMapper;

    @Value("${deliver.createDeliver.on-off}")
    private boolean switchOnOff;

    /**
     * @param req                       原始请求报文
     * @param deliverPO                 已经组装好的发货单主表
     * @param submit                    是否为提交
     * @param now                       当前时间戳
     * @param deliverContractPOList     已经组装好的合同信息
     * @param deliverContractItemPOList 准备组装的合同行信息容器
     * @return 准备数据状态
     */
    public Result<String> prepareDeliveryContractItem(SaveDeliveryReqDTO req, String userId, String userName,
                                                      DeliverPO deliverPO, Boolean submit, LocalDateTime now,
                                                      List<DeliverContractPO> deliverContractPOList,
                                                      List<DeliverContractItemPO> deliverContractItemPOList) {

        // 所有请求合同行
        List<SaveDeliveryContractItem> allContractItemReq = getAllContractItemReq(req);
        if (allContractItemReq.isEmpty()) return Result.ok();
        // 远程调用获取所有的合同行信息
        List<ContractDeliveryItemResDTO> remoteContractItemList = contractFeign.deliveryListItemByContractItemIds(
                new ContractQueryReqItemIdListDTO(getAllContractItemIdReq(allContractItemReq))).getData();
        if (remoteContractItemList.isEmpty()) {
            return Result.fail("查不到合同行不能确认发货");
        }
        Map<String, ContractDeliveryItemResDTO> businessItemId2ContractDeliveryItemResDTO = remoteContractItemList
                .stream().collect(Collectors.toMap(ContractDeliveryItemResDTO::getContractBusinessItemId, Function.identity()));
        // 循环处理准备好的合同
        for (DeliverContractPO contractPO : deliverContractPOList) {
            String deliveryContractId = contractPO.getDeliverContractId();
            String contractId = contractPO.getContractId();
            // 当前合同下的合同行
            List<SaveDeliveryContractItem> currentContractItemReqs = allContractItemReq.stream()
                    .filter(e -> contractId.equals(e.getContractId())).toList();
            int applicationQuantity = 0;
            BigDecimal applicationVolumeMw = new BigDecimal(0);
            // 处理合同行
            for (SaveDeliveryContractItem itemReq : currentContractItemReqs) {
                // 如果数量为空，设置为默认值0
                if (ObjectUtil.isNull(itemReq.getQuantity())) {
                    itemReq.setQuantity(0);
                }
                String deliveryContractItemId = deliverNoGenerator.getId(DRECI, "deliveryContractItem");
                ContractDeliveryItemResDTO itemResDTO = businessItemId2ContractDeliveryItemResDTO.get(itemReq.getContractBusinessItemId());
                // 开启提交的校验

                if (ObjectUtils.isEmpty(itemResDTO)) {
                    throw new BizException(ResultCode.FAIL.getCode(), "合同:" + contractPO.getSfContractNo() + "产品行已发生变更，请移除后添加");
                }

//                Map<String,SaveDeliveryContractItem> saveDeliveryContractItemMap=allContractItemReq.stream().collect(Collectors.toMap(SaveDeliveryContractItem::getContractBusinessItemId, Function.identity(),(a,b)->a));
                DeliverContractItemPO deliverContractItemPO = composeDeliverContractItemPO(
                        deliveryContractItemId, deliveryContractId, deliverPO.getDeliverNo(),
                        itemReq, itemResDTO, now, userId, userName);
                deliverContractItemPOList.add(deliverContractItemPO);

                applicationQuantity += deliverContractItemPO.getQuantityP();

                applicationVolumeMw = applicationVolumeMw.add(deliverContractItemPO.getQuantityMw());
            }
            contractPO.setApplicationQuantity(applicationQuantity);
            contractPO.setApplicationVolumeMw(applicationVolumeMw);
        }

        //加载发货单总功率 合同列表
        additionalDeliverInit(deliverPO, deliverContractPOList, deliverContractItemPOList);

        return Result.ok();
    }


    private void additionalDeliverInit(DeliverPO deliverPO, List<DeliverContractPO> deliverContractPOList, List<DeliverContractItemPO> itemPOList) {
        if (ObjectUtils.isNotEmpty(deliverContractPOList)) {
            StringBuilder sfContract = new StringBuilder();
            deliverContractPOList.forEach(a -> {
                sfContract.append(a.getSfContractNo()).append(",");
            });
            String sfContractList = sfContract.toString();

            if (StringUtils.isNotBlank(sfContractList) && sfContractList.length() > 1) {
                sfContractList = sfContractList.substring(0, sfContractList.length() - 1);
            }

            deliverPO.setSfContractList(sfContractList);
        }
        BigDecimal quantityMw = new BigDecimal(0);
        int totalQuantityP = 0;
        if (ObjectUtils.isNotEmpty(itemPOList)) {
            for (DeliverContractItemPO item : itemPOList) {
                if (ObjectUtils.isNotEmpty(item.getQuantityMw())) {
                    quantityMw = quantityMw.add(item.getQuantityMw());
                }
                totalQuantityP += item.getQuantityP();
            }
            deliverPO.setQuantityMw(quantityMw);
            deliverPO.setTotalQuantityP(totalQuantityP);
        }
    }

    /**
     * 检查所有的合同行的报错信息
     *
     * @param checkQuantityResultList 所有合同行的校验信息
     */
    public Result<String> checkList(List<Result<String>> checkQuantityResultList) {
        boolean checkFailed = false;
        StringBuilder errorString = new StringBuilder();
        for (Result<String> checkResult : checkQuantityResultList) {
            if (!checkResult.getSuccess()) {
                checkFailed = true;
                errorString.append(checkResult.getMessage());
            }
        }
        if (checkFailed) {
            String finalErrorString = errorString.toString();
            finalErrorString = finalErrorString.substring(0, finalErrorString.length() - 1);
            return Result.fail(DeliverResultCode.REQ_QUANTITY_ERROR.getCode(), finalErrorString);
        }
        return Result.ok();
    }


    /**
     * @param deliverPO  发货单
     * @param contractPO 当前合同
     * @param itemReq    请求合同行
     * @param itemResDTO 远程合同行
     * @return 发货数量是否超过剩余数量
     */
    private Result<String> checkRemainQuantityPass(DeliverPO deliverPO, DeliverContractPO contractPO,
                                                   SaveDeliveryContractItem itemReq,
                                                   ContractDeliveryItemResDTO itemResDTO) {
        if (itemReq.getQuantity() == 0) {
            return Result.fail("发货申请数量不能为0");
        }
        long beginTime = System.currentTimeMillis();
        Integer alreadySubmitInteger = sumSubmitItemQuantity(deliverPO.getPrevDeliverStatus(), itemReq.getContractBusinessItemId());
        log.info("发货申请校验数量", System.currentTimeMillis() - beginTime);
        int alreadySubmit = Optional.ofNullable(alreadySubmitInteger).orElse(0);
        log.info("alreadySubmit : {}", alreadySubmit);
        log.info("发货申请数量 : {}", itemReq.getQuantity());
        int totalQuantity = itemReq.getQuantity() + alreadySubmit;
        int remain = itemResDTO.getRemainingQuantityP();
        log.info("remain quantity {}", itemResDTO);
        if (remain == 0) return Result.fail("剩余发货数量为0");
        // 校验数量
        if (totalQuantity <= remain) return Result.ok();
        String template = "%s：%s、";
        String result = String.format(template, contractPO.getSfContractNo(), itemResDTO.getProductName());
        return Result.fail(DeliverResultCode.REQ_QUANTITY_ERROR.getCode(), result);
    }

    private Integer sumSubmitItemQuantity(String deliverStatus, String contractBusinessItemId) {
        return deliverContractItemMapperService.sumSubmitItemQuantity(deliverStatus, contractBusinessItemId);
    }


    /**
     * 本次发货合同行
     *
     * @param contractItemPOList 发货合同行
     * @return 锁定的库存
     */
    public List<LockedQuantityListDTO> getLockedQuantity(List<DeliverContractItemPO> contractItemPOList) {
        // 本次发货对应的所有发货行信息
        List<String> contractItemIdList = contractItemPOList.stream()
                .map(DeliverContractItemPO::getContractBusinessItemId).toList();
        // 获取该合同行，过去所有的发货单已经 SUBMIT SALES_CONFIRM的所有信息
        return deliverContractItemMapperService.getLockedQuantity(contractItemIdList);
    }


    /**
     * 准备合同发货内容
     *
     * @param req                   前端请求
     * @param deliverPO             发货信息
     * @param now                   当前时间戳
     * @param submit                是否为提交或者确认，此时需要开启校验
     * @param deliverContractPOList 合同结果容器
     */
    public Result<String> prepareDeliveryContract(SaveDeliveryReqDTO req, String userId, String userName, DeliverPO deliverPO,
                                                  Boolean submit, LocalDateTime now, List<DeliverContractPO> deliverContractPOList) {
        if (req.getContracts().isEmpty()) return Result.ok();
        // 远程查询所有的合同信息
        List<ContractDeliveryResDTO> contractResDTOList = contractFeign
                .deliveryListByContractIds(new ContractQueryReqIdListDTO(
                        req.getContracts().stream().map(SaveDeliveryContract::getContractId)
                                .toList())).getData();
        if (contractResDTOList.isEmpty()) return Result.fail("查不到合同不能确认发货");
        Optional<ContractDeliveryResDTO> contractOptional= contractResDTOList.stream().filter(item->!item.getOrderType().equals(req.getOrderType())).findAny();
        if(contractOptional.isPresent()){
            return Result.fail(ORDER_TYPE_MAP_ERROR.getCode(), ORDER_TYPE_MAP_ERROR.getMessage());
        }
        // 填充本地合同信息
        List<String> nonCounterSignList = new ArrayList<>();
        for (ContractDeliveryResDTO contractResDTO : contractResDTOList) {
            if (submit) {
                // 如果没有双签，就不能发货
                if (!COUNTERSIGNED.getCode().equals(contractResDTO.getContractStatus())) {
                    nonCounterSignList.add(contractResDTO.getSfContractNo());
                    continue;
                }
            }
            // 只要有非双签的合同存在，就不要继续去组装报对象，只是扫描所有非双签的合同
            if (!nonCounterSignList.isEmpty()) continue;
            String deliveryContractNo = deliverNoGenerator.getId(DREC, "deliveryContract");
            DeliverContractPO deliverContractPO = composeDeliverContractPO(deliveryContractNo, deliverPO.getDeliverNo(),
                    contractResDTO, now, userId, userName);
            deliverContractPOList.add(deliverContractPO);
        }
        if (!nonCounterSignList.isEmpty()) {
            StringBuilder errorString = new StringBuilder();
            for (String conCounterSign : nonCounterSignList) {
                errorString.append(conCounterSign).append("、");
            }
            return Result.fail(NON_COUNTERSIGNED_ERROR.getCode(), errorString.toString());
        }
        return Result.ok();
    }

    /**
     * 获取所有请求合同
     *
     * @param req 请求
     */
    private List<SaveDeliveryContractItem> getAllContractItemReq(SaveDeliveryReqDTO req) {
        List<SaveDeliveryContractItem> allContractItemReq = new ArrayList<>();

        for (SaveDeliveryContract save : req.getContracts()) {
            List<SaveDeliveryContractItem> saveItemList = save.getContractItems();
            saveItemList.forEach(si -> si.setContractId(save.getContractId()));
            allContractItemReq.addAll(saveItemList);
        }

        return allContractItemReq;
    }

    /**
     * 获取所有请求合同行 Id
     */
    private List<String> getAllContractItemIdReq(List<SaveDeliveryContractItem> contractItemReq) {
        return contractItemReq.stream().map(SaveDeliveryContractItem::getContractBusinessItemId)
                .toList();
    }


    /**
     * @param deliveryContractNo 生成的发货合同号
     * @param deliveryNo         生成的发货号
     * @param res                远程合同信息
     * @param now                当前时间戳
     * @return 组装好的合同信息
     */
    public DeliverContractPO composeDeliverContractPO(String deliveryContractNo, String deliveryNo,
                                                      ContractDeliveryResDTO res,
                                                      LocalDateTime now, String userId, String userName) {
        return DeliverContractPO.builder()
                .setDeliverContractId(deliveryContractNo)
                .setDeliverNo(deliveryNo)
                .setContractId(res.getContractId())
                .setSfRecordId(res.getSfRecordId())
                .setSfContractNo(res.getSfContractNo())
                .setSfContractId(res.getSfContractId())
                .setStatus(res.getContractStatus())
                .setOrderTypeName("Sales Order")
                .setOrderTypeId(null)
                .setCurrency(res.getCurrency())
                .setSubRegionCode(res.getSubRegionCode())
                .setArrivalCityCode(res.getArrivalCityCode())
                .setArrivalCountryCode(res.getArrivalCountryCode())
                .setArrivalProvinceCode(res.getArrivalProvinceCode())
                .setMarketingAccountId(res.getMarketingAccountId())
                .setPriceBookId(res.getPriceBookId())
                .setOpportunityId(res.getSfOpportunityId())
                .setIncoterm(res.getIncoterm())
                .setChannel(res.getChannel())
                .setOrderStartDate(res.getOrderStartDate())
                .setScheduledDeliveryDate(res.getScheduledDeliveryDate().atTime(LocalTime.of(0, 0)))
                .setCounterSignedDate(res.getCounterSignedDate())
                .setApplication(res.getApplication())
                .setBeneficiaryBankId(res.getBeneficiaryBankId())
                .setCountryOfInstallation(res.getCountryOfInstallation())
                .setDestinationPortId(res.getDestinationPortId())
                .setLoadingPortId(res.getLoadingPortId())
                .setBillToAddressId(res.getBillToAddressId())
                .setShipToAddressId(res.getShipToAddressId())
                .setTaxClassificationId(res.getTaxClassificationId())
                .setOdm(res.getOdm())
                .setTotalVolumeMW(res.getTotalVolumeMw())
                .setIsDeleted(0)
                .setCreatedBy(userId)
                .setCreatedName(userName)
                .setCreatedTime(now)
                .setUpdatedBy(userId)
                .setUpdatedName(userName)
                .setRebate(res.getRebate())
                .setIsOpenContract(res.getIsOpenContract())
                .setOpenContract(res.getOpenContract())
                .setUpdatedTime(now);
    }


    /**
     * 构造发货申请合同行对象
     *
     * @param deliveryContractItemId      生成的发货合同行ID
     * @param deliveryContractId          生成的发货合同Id
     * @param deliverNo                   生成或者沿用的发货合同
     * @param itemReq                     请求的合同行
     * @param itemResDTO                  远程调用返回的合同
     * @param now                         当前时间戳
     * @return 构造好的合同行信息
     */
    private DeliverContractItemPO composeDeliverContractItemPO(String deliveryContractItemId, String deliveryContractId, String deliverNo,
                                                               SaveDeliveryContractItem itemReq, ContractDeliveryItemResDTO itemResDTO,
                                                               LocalDateTime now, String userId, String userName) {

        // 具体本次申请的发货数量
        int reqItemQuantity = itemReq.getQuantity() == null ? 0 : itemReq.getQuantity();
        BigDecimal power = ObjectUtils.isEmpty(itemReq.getPower()) ? new BigDecimal(0) : new BigDecimal(itemReq.getPower());

        // 兆瓦
        BigDecimal powerInW = BigDecimal.valueOf(reqItemQuantity).multiply(power);

        return DeliverContractItemPO.builder()
                .setDeliverContractItemId(deliveryContractItemId)
                .setDeliverContractId(deliveryContractId)
                .setDeliverNo(deliverNo)
                .setSfContractId(null)
                .setContractBusinessItemId(itemResDTO.getContractBusinessItemId())
                .setSfProductId(itemResDTO.getSfProductId())
                .setSfProductName(itemResDTO.getProductName())
                .setPbiProductId(itemResDTO.getPbiProductId())
                .setPriceBookId(itemResDTO.getPriceBook())
                .setPlugConnector(itemResDTO.getPlugConnector())
                .setInstallation(itemResDTO.getInstallation())
                .setOrderTypeId(null)
                .setCableLength(itemResDTO.getCableLength())
                .setCableLengthPositive(itemResDTO.getCableLengthPositive())
                .setCableLengthCathode(itemResDTO.getCableLengthCathode())
                .setPower(power)
                .setExpectedDeliverDate(itemResDTO.getExpectedDeliverDate())
                .setUnitPriceW(itemResDTO.getUnitPriceW())
                .setNotes(itemResDTO.getNotes())
                .setFromProductEditor(true)
                .setQuantityW(powerInW)
                .setQuantityMw(powerInW.divide(BigDecimal.valueOf(1e6), 6,RoundingMode.FLOOR))
                .setQuantityP(reqItemQuantity)
                .setItemType(itemResDTO.getItemType())
                .setIsDeleted(0)
                .setCreatedBy(userId)
                .setCreatedTime(now)
                .setCreatedName(userName)
                .setUpdatedBy(userId)
                .setUpdatedName(userName)
                .setUpdatedTime(now)
                .setCurrency(itemResDTO.getCurrency())
                .setSfContractItemId(itemResDTO.getSfContractItemId());
    }


    /**
     * 持久化发货信息
     *
     * @param DeliverPO                 发货单主表
     * @param deliverContractPOList     发货合同信息
     * @param deliverContractItemPOList 发货合同行信息
     * @return 发货单
     */
    @Transactional
    public Result<String> persistDeliver(DeliverPO DeliverPO,
                                         List<DeliverContractPO> deliverContractPOList,
                                         List<DeliverContractItemPO> deliverContractItemPOList) {
        deliverMapperService.saveOrUpdate(DeliverPO);
        String deliverNo = DeliverPO.getDeliverNo();

        // ===============================================
        LambdaQueryWrapper<DeliverContractPO> contractWrapper = new LambdaQueryWrapper<>();
        contractWrapper.eq(DeliverContractPO::getDeliverNo, deliverNo);
        LambdaQueryWrapper<DeliverContractItemPO> contractItemWrapper = new LambdaQueryWrapper<>();
        contractItemWrapper.eq(DeliverContractItemPO::getDeliverNo, deliverNo);

        //高并发下，直接remove 可能导致死锁（间隙锁）
        long deliverContractCount = deliverContractMapperService.lambdaQuery().eq(DeliverContractPO::getDeliverNo, deliverNo).count();
        if (deliverContractCount > 0) {
            deliverContractMapperService.remove(contractWrapper);
        }
        long itemCount = deliverContractItemMapperService.lambdaQuery().eq(DeliverContractItemPO::getDeliverNo, deliverNo).count();
        if (itemCount > 0) {
            deliverContractItemMapperService.remove(contractItemWrapper);
        }
        deliverContractMapperService.saveBatch(deliverContractPOList);
        // ===============================================
        deliverContractItemMapperService.saveBatch(deliverContractItemPOList);
        return Result.ok(deliverNo);
    }


    /**
     * 持久化发货信息
     *
     * @param deliverPO                 发货单主表
     * @param deliverContractPOList     发货合同信息
     * @param deliverContractItemPOList 发货合同行信息
     * @return 发货单
     */
    @Transactional
    public void saveDeliver(DeliverPO deliverPO,
                            List<DeliverContractPO> deliverContractPOList,
                            List<DeliverContractItemPO> deliverContractItemPOList) {
        if (deliverContractItemPOList.size() < 200) {
            deliverBaseMapper.saveDeliverInfo(deliverPO, deliverContractPOList, deliverContractItemPOList);
            return;
        }
        deliverBaseMapper.saveDeliverInfo(deliverPO, deliverContractPOList, null);
        deliverBaseMapper.saveDeliverInfo(null, null, deliverContractItemPOList);
    }


    /**
     * 构造发货消息发送体
     *
     * @param deliverPO 发货信息主表
     * @return 发货创建消息体
     */
    public DeliveryMessageMqDTO composeDeliverMessage(DeliverySituationEnum deliverySituationEnum,
                                                      SysUserRespDTO sysUser, DeliverPO deliverPO,
                                                      List<DeliverContractItemPO> deliverContractItemPOList) {
        // 所有容量
        BigDecimal allVolume = deliverContractItemPOList.stream()
                .map(DeliverContractItemPO::getQuantityMw).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 企业信息
//        EnterpriseBriefDTO enterpriseBriefDTO = enterpriseFeign.getEnterpriseBriefByEnterpriseId(deliverPO.getEnterpriseId());
        // 外部用户
        SysUserRespDTO externalUser = StringUtil.isNullOrEmpty(deliverPO.getExternalUserId())
                ? new SysUserRespDTO() : sysUserFeign.getUserByUserId(deliverPO.getExternalUserId()).getData();
        // 内部用户
        SysUserRespDTO internalSaleUser = StringUtil.isNullOrEmpty(deliverPO.getSalesInternalUserId())
                ? new SysUserRespDTO() : sysUserFeign.getUserByUserId(deliverPO.getSalesInternalUserId()).getData();
        // 运营用户

        // if else 区分取消和确认
        SysUserRespDTO operationUser = StringUtil.isNullOrEmpty(deliverPO.getOperationInternalUserId())
                ? new SysUserRespDTO() : sysUserFeign.getUserByUserId(deliverPO.getOperationInternalUserId()).getData();

        DeliveryMessageMqDTO mqDTO = new DeliveryMessageMqDTO();
        mqDTO.setSituationType(deliverySituationEnum.getCode());
        mqDTO.setCurrentUserId(sysUser.getUserId());
        mqDTO.setCurrentUserName(sysUser.getUserName());
        mqDTO.setDeliveryNo(deliverPO.getDeliverNo());
        mqDTO.setPotentialSalesVolume(allVolume);
        mqDTO.setEnterpriseName(deliverPO.getEnterpriseName());
        mqDTO.setExternalUserId(deliverPO.getExternalUserId());
        mqDTO.setExternalUserName(externalUser == null ? null : externalUser.getUserName());
        mqDTO.setStatus(deliverPO.getDeliverStatus());
        mqDTO.setSalesInternalUserId(deliverPO.getSalesInternalUserId());
        mqDTO.setSalesInternalUserName(internalSaleUser == null ? null : internalSaleUser.getUserName());
        mqDTO.setOperationInternalUserId(deliverPO.getOperationInternalUserId());
        mqDTO.setOperationInternalUserName(operationUser == null ? null : operationUser.getUserName());
        return mqDTO;

    }


    /**
     * 构造发货消息发送体
     *
     * @param deliverPO 发货信息主表
     * @return 发货创建消息体
     */
    public DeliveryMessageMqDTO deliverMessageInit(DeliverySituationEnum deliverySituationEnum,
                                                   SysUserRespDTO sysUser, DeliverPO deliverPO,
                                                   List<DeliverContractItemPO> deliverContractItemPOList,
                                                   SysEnterpriseUserRespDTO sale, SysSalesOperationRelationRespDTO operation) {
        // 所有容量
        BigDecimal allVolume = deliverContractItemPOList.stream()
                .map(DeliverContractItemPO::getQuantityMw).reduce(BigDecimal.ZERO, BigDecimal::add);

        DeliveryMessageMqDTO mqDTO = new DeliveryMessageMqDTO();
        mqDTO.setSituationType(deliverySituationEnum.getCode());
        mqDTO.setCurrentUserId(sysUser.getUserId());
        mqDTO.setCurrentUserName(sysUser.getUserName());
        mqDTO.setDeliveryNo(deliverPO.getDeliverNo());
        mqDTO.setPotentialSalesVolume(allVolume);
        mqDTO.setEnterpriseName(deliverPO.getEnterpriseName());
        mqDTO.setExternalUserId(deliverPO.getExternalUserId());
        mqDTO.setExternalUserName(sysUser.getUserName());
        mqDTO.setStatus(deliverPO.getDeliverStatus());
        mqDTO.setSalesInternalUserId(deliverPO.getSalesInternalUserId());
        mqDTO.setSalesInternalUserName(sale.getUserName());
        mqDTO.setOperationInternalUserId(deliverPO.getOperationInternalUserId());
        mqDTO.setOperationInternalUserName(operation.getOperationUserName());
        return mqDTO;

    }


    public Result<String> complexDeliverCheck(String deliverNo, DeliverPO deliver, Boolean ops, String operationType) {

        if (ObjectUtils.isEmpty(deliver)) {
            deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();
        }

        Result<String> check = deliverManager.simpleListDeliveryCheck(deliverNo, deliver);
        if (!check.getSuccess()) {
            return check;
        }

        List<DeliverContractPO> localDeliverContractList = deliverContractMapperService.lambdaQuery()
                .eq(DeliverContractPO::getDeliverNo, deliverNo).list();
        if (localDeliverContractList.isEmpty()) return Result.fail(" 请完善发货申请信息后再尝试提交");
        List<String> contractIdList = localDeliverContractList.stream().map(DeliverContractPO::getContractId).toList();
        // 远程查询所有的合同信息
        List<ContractDeliveryResDTO> contractResDTOList = contractFeign.deliveryListByContractIds(new ContractQueryReqIdListDTO(contractIdList)).getData();
        if (contractResDTOList.isEmpty()) return Result.fail(" 请完善发货申请信息后再尝试提交");


        // 表内所有合同行
        List<DeliverContractItemPO> deliverContractItemPOList = deliverContractItemMapperService.lambdaQuery()
                .eq(DeliverContractItemPO::getDeliverNo, deliverNo).list();
        if (deliverContractItemPOList.isEmpty()) {
            return Result.fail("请完善发货申请信息后再尝试提交");
        }
        boolean checkQuantityErr = false;
        for (int i = 0; i < deliverContractItemPOList.size(); i++) {
            if (ObjectUtils.isEmpty(deliverContractItemPOList.get(i).getQuantityP())) {
                checkQuantityErr = true;
                break;
            }
            if (deliverContractItemPOList.get(i).getQuantityP() == 0) {
                checkQuantityErr = true;
                break;
            }
        }
        if (checkQuantityErr) {
            return Result.fail("请完善发货申请信息后再尝试提交");
        }


        // 校验合同双签状态
        List<String> nonCounterSignList = new ArrayList<>();
        for (ContractDeliveryResDTO contractResDTO : contractResDTOList) {
            // 如果没有双签，就不能发货
            if (!COUNTERSIGNED.getCode().equals(contractResDTO.getContractStatus())) {
                nonCounterSignList.add(contractResDTO.getSfContractNo());
            }
        }

        if (!nonCounterSignList.isEmpty()) {
            StringBuilder errorString = new StringBuilder();
            for (String conCounterSign : nonCounterSignList) {
                errorString.append(conCounterSign).append("、");
            }
            String result = errorString.toString();
            result = result.substring(0, errorString.length() - 1);
            return Result.fail(NON_COUNTERSIGNED_ERROR.getCode(), result);
        }


        return Result.ok();
    }


    /**
     * 发货校验发货数据
     * 草稿状态
     *
     * @param operationType 操作类型
     */
    public void checkDeliverInfo(String deliverNo, DeliverPO deliver, DeliverContractPO deliverContract, DeliverContractItemPO item, String operationType) {
        if (ObjectUtils.isEmpty(deliver)) {
            deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();
        }
        //校验 deliver 基础信息
        checkBaseDeliver(deliver, operationType);

    }

    /**
     * 校验 deliver 基础数据
     */
    private void checkBaseDeliver(DeliverPO deliver, String operationType) {
        validPara(deliver.getExternalUserId(), operationType);
        validPara(deliver.getIncoterm(), operationType);
        validPara(deliver.getSalesInternalUserId(), operationType);
        validPara(deliver.getOperationInternalUserId(), operationType);
        checkAddressInfo(deliver, operationType);
    }

    /**
     * 校验发货信息
     */
    private void checkAddressInfo(DeliverPO deliver, String operationType) {
        validPara(deliver.getIncoterm(), operationType);
        if (DeliverMethod.DDP.getCode().equals(deliver.getIncoterm())) {
            validPara(deliver.getBillToAddressId(), operationType);
        }
    }

    private void validPara(String para, String operationType) {
        if (StringUtils.isNotBlank(para)) {
            return;
        }
        if (DeliverOperationTypeEnum.SALE_CONFIRM_LIST.getCode().equals(operationType)) {
            throw new BizException(ResultCode.FAIL.getCode(), "请完善发货申请信息后再尝试确认申请");
        }
        if (DeliverOperationTypeEnum.EXTERNAL_COMMIT_LIST.getCode().equals(operationType)) {
            throw new BizException(ResultCode.FAIL.getCode(), "请完善发货申请信息后再尝试提交申请");
        }
    }


    /**
     * 查找 beanStr（json 字符串）中 addressId对应的
     *
     * @Schema(description = "地址Id")
     * private String addressId;
     * @Schema(description = "详细地址")
     * private String detailAddress;
     * @Schema(description = "省")
     * private AddressStructure province;
     * @Schema(description = "市")
     * private AddressStructure city;
     * @Schema(description = "区")
     * private AddressStructure area;
     */
    public String getAddress(String addressId, String beanStr) {
        if (StringUtils.isBlank(beanStr)) {
            return "";
        }
        List<EnterpriseAddressRespDTO> list = JacksonUtil.json2List(beanStr, EnterpriseAddressRespDTO.class);

        return getAddressById(addressId, list);
    }

    public EnterpriseAddressRespDTO getAddressVO(String addressId, List<EnterpriseAddressRespDTO> list) {
        if (ObjectUtils.isEmpty(list)) {
            new EnterpriseAddressRespDTO();
        }
        return list.stream().filter(a -> StringUtils.equals(a.getAddressId(), addressId)).findFirst().orElse(new EnterpriseAddressRespDTO());
    }

    public String getAddressById(String addressId, List<EnterpriseAddressRespDTO> list) {
        EnterpriseAddressRespDTO addressInfo = getAddressVO(addressId, list);
        return getFullAddress(addressInfo);
    }

    public String getFullAddress(EnterpriseAddressRespDTO addressInfo) {
        if (ObjectUtils.isEmpty(addressInfo)) {
            return "";
        }
        AddressStructure emptyStructure = new AddressStructure("", "", false, "");
        AddressStructure province = ObjectUtils.isEmpty(addressInfo.getProvince()) ?
                emptyStructure : addressInfo.getProvince();
        AddressStructure city = ObjectUtils.isEmpty(addressInfo.getCity()) ?
                emptyStructure : addressInfo.getCity();
        AddressStructure area = ObjectUtils.isEmpty(addressInfo.getArea()) ?
                emptyStructure : addressInfo.getArea();
        String fullAddress = new StringBuilder().append(province.getName())
                .append(city.getName())
                .append(area.getName())
                .append(addressInfo.getDetailAddress())
                .toString();
        return StringUtils.equals(fullAddress, "null") ? "" : fullAddress;
    }


}
