package com.trinasolar.trinax.delivery.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import dtt.segment.id.generator.service.IdService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 企业地址号生成
 */
@Component
public class DeliverNoGenerator {

    @Autowired
    private IdService idService;


    /**
     * 企业地址号生成
     */
    public String getId(String prefixHeader, String businessType) {
        String prefix = prefixHeader + "-" + DateFormatUtils.format(new Date(), "yyyyMMdd") + "-";
        String date = DateUtil.format(LocalDateTime.now(), DatePattern.PURE_DATE_PATTERN);
        Long num = idService.nextId(businessType + "-" + date, businessType);
        String suffix = String.format("%06d", num);
        return prefix + suffix;
    }

}
