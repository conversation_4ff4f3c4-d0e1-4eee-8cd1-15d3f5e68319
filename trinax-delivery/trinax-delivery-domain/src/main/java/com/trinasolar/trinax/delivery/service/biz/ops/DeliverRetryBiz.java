package com.trinasolar.trinax.delivery.service.biz.ops;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.delivery.constants.enums.*;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.ApprovalStatusReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryApprovalReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.submit.DeliverRetryReqDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverFileRelationMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DistributeAccountBillMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverFileRelationPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.domain.DistributeAccountBillPO;
import com.trinasolar.trinax.delivery.service.manager.DeliverApprovalManger;
import com.trinasolar.trinax.delivery.service.manager.DeliverOrderSyncManger;
import com.trinasolar.trinax.delivery.util.DeliverNoGenerator;
import com.trinasolar.trinax.integration.api.IntegrationDeliveryFeign;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.trinasolar.trinax.delivery.constants.BusinessPrefix.DR_FILE;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverRetryBiz {

    private final DeliverApprovalManger deliverApprovalManger;
    private final DeliverOrderSyncManger deliverOrderSyncManger;
    private final DeliverContractMapperService deliverContractMapperService;
    private final DistributeAccountBillMapperService distributeAccountBillMapperService;
    private final DeliverNoGenerator deliverNoGenerator;
    private final DeliverFileRelationMapperService deliverFileRelationMapperService;
    private final IntegrationDeliveryFeign integrationDeliveryFeign;
    @Value("${deliver.openSystemOrgCode}")
    String openSystemOrgCode;
    private final DeliverMapperService deliverMapperService;
    private final DeliverOpsConfirmBiz deliverOpsConfirmBiz;
    private final DistributeAmountBiz distributeAmountBiz;


    public Result<String> retrySubmitApproval(DeliverRetryApprovalReqDTO req) {

        DeliverContractPO drContract = deliverContractMapperService.qryDrContract(req.getDeliverNo(), req.getSfContractNo());

        if (ObjectUtils.isEmpty(drContract)) {
            throw new BizException(ResultCode.FAIL.getCode(), "查询发货合同失败");
        }

        boolean notPermission = StringUtils.equals(drContract.getBpmStatus(), BpmStatusEnum.APPROVED.getCode()) ||
                StringUtils.equals(drContract.getBpmStatus(), BpmStatusEnum.APPROVING.getCode());

        if (notPermission) {
            throw new BizException(ResultCode.FAIL.getCode(), "禁止重复提交审批");
        }
        boolean advanceAndReject = StringUtils.equals(drContract.getApprovalType(), ApprovalTypeEnum.ADVANCE_SHIPPING.getCode())
                && StringUtils.equals(drContract.getBpmStatus(), BpmStatusEnum.REJECT.getCode());

        if (advanceAndReject) {
            throw new BizException(ResultCode.FAIL.getCode(), "提前发货审批已拒绝");
        }
        List<DeliverContractPO> deliverContractList = new ArrayList<>();
        deliverContractList.add(drContract);
        List<DeliverFileRelationPO> approvalFiles = approvalFileInit(deliverContractList, req);

        saveApprovalFiles(approvalFiles, req.getDeliverNo(), req.getSfContractNo());


        deliverApprovalManger.bpmApproval(req.getDeliverNo(), req.getLoginUserEmail(), deliverContractList);
        //重提bpm审批也要同步给sf
        deliverOpsConfirmBiz.deliverOrderSyncSf(req.getDeliverNo(),deliverContractList);

        DeliverContractPO newDrContract = deliverContractMapperService.qryDrContract(req.getDeliverNo(), req.getSfContractNo());

        if (StringUtils.equals(BpmStatusEnum.FAIL.getCode(), newDrContract.getBpmStatus())) {
            throw new BizException(ResultCode.FAIL.getCode(), newDrContract.getBpmSubmitDesc());
        }

        return Result.ok();
    }



    private void saveApprovalFiles(List<DeliverFileRelationPO> approvalFiles, String deliverNo, String sfContractNo) {
        deliverFileRelationMapperService.lambdaUpdate()
                .eq(DeliverFileRelationPO::getDeliverNo, deliverNo)
                .eq(DeliverFileRelationPO::getSfContractNo, sfContractNo)
                .remove();
        deliverFileRelationMapperService.saveBatch(approvalFiles);

    }

    public Result<String> retrySyncDr(DeliverRetryReqDTO req) {

        LambdaQueryWrapper<DeliverContractPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeliverContractPO::getDeliverNo, req.getDeliverNo());
        if (ObjectUtils.isNotEmpty(req.getSfContractNos())) {
            queryWrapper.in(DeliverContractPO::getSfContractNo, req.getSfContractNos());
        }
        List<DeliverContractPO> contractPOList = deliverContractMapperService.list(queryWrapper);
        long notPermission = contractPOList.stream()
                .filter(a -> StringUtils.equals(a.getSyncStatusSf(), SyncStatusSfEnum.SUCCESS.getCode()))
                .count();
        if (notPermission > 0) {
            throw new BizException(ResultCode.FAIL.getCode(), "已同步 salesforce，无需再次提交");
        }

        List<String> sfContractNos = contractPOList.stream().map(DeliverContractPO::getSfContractNo).collect(Collectors.toList());

        Result<String> result = deliverOrderSyncManger.deliverOrderSync(req.getDeliverNo(), sfContractNos);
        List<DeliverContractPO> advanceAndReject = contractPOList.stream()
                .filter(a -> StringUtils.equals(a.getBpmStatus(), BpmStatusEnum.REJECT.getCode())
                        && StringUtils.equals(a.getApprovalType(), ApprovalTypeEnum.ADVANCE_SHIPPING.getCode()))
                .collect(Collectors.toList());
        if (ObjectUtils.isNotEmpty(advanceAndReject)) {
            List<String> advanceSfContractNos = advanceAndReject.stream().map(DeliverContractPO::getSfContractNo).collect(Collectors.toList());
            //拒绝同步成功
            Integer syncStatus = result.getSuccess() ? 1 : 2;
            String syncStatusSf = result.getSuccess() ? SyncStatusSfEnum.SUCCESS_REJECT.getCode() : SyncStatusSfEnum.FAIL.getCode();

            //存在拒绝同步
            deliverContractMapperService.lambdaUpdate()
                    .set(DeliverContractPO::getSyncStatusSf, syncStatusSf)
                    .set(DeliverContractPO::getQuantitySyncSf, syncStatus)
                    .in(DeliverContractPO::getSfContractNo, advanceSfContractNos)
                    .eq(DeliverContractPO::getDeliverNo, req.getDeliverNo())
                    .update();
        }

        return Result.ok();

    }

    /**
     * bpm审批节点通知
     */
    public Result<String> approvalStatusAccess(ApprovalStatusReqDTO req) {
        DeliverContractPO deliverContract = deliverContractMapperService.lambdaQuery().eq(DeliverContractPO::getDeliverContractId, req.getZzbId()).one();

        if (ObjectUtils.isEmpty(deliverContract)) {
            throw new BizException(ResultCode.FAIL.getCode(), "zzbId异常");
        }
        DeliverPO deliverPO = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverContract.getDeliverNo()).one();
        if (ObjectUtils.isEmpty(deliverPO)) {
            throw new BizException(ResultCode.FAIL.getCode(), "发货单异常");
        }
        String bpmStatus = deliverContract.getBpmStatus();
        deliverContract.setBpmStatus(req.getStatus());
        deliverContract.setBpmApprovalOpinions(req.getApprovalOpinions());
        deliverContract.setApprover(req.getApprover());
        deliverContract.setApprovalNodeDescription(req.getApprovalNodeDescription());
        deliverContract.setNextApprover(req.getNextApprover());

        if (StringUtils.isBlank(req.getNextApprover())) {
            //需要设置空字符串 ，不然 mybatise 不会更新
            deliverContract.setNextApprover(" ");
        }
        deliverContractMapperService.saveOrUpdate(deliverContract);

        String approvalType = deliverContract.getApprovalType();

        if (StringUtils.equals(req.getStatus(), BpmStatusEnum.REJECT.getCode())) {
            //如果是审批拒绝，触发 sf 回滚分销金额
            distributeAmountBiz.callbackDistributeAmount(deliverPO,deliverContract);
        }

        if (StringUtils.equals(bpmStatus, BpmStatusEnum.APPROVED.getCode())) {
            //重复审批通过，不触发 sf 逻辑
            return Result.ok();
        }

        //提前发货且 bpm 拒绝 需要调用 sf 去同步
        if (!ApprovalTypeEnum.ADVANCE_SHIPPING.getCode().equals(approvalType)) {
            //非提前发货需要bpm 状态
            if (!StringUtils.equals(req.getStatus(), BpmStatusEnum.APPROVED.getCode())) {
                //非审批通过，不触发同步 sf 的逻辑
                return Result.ok();
            }
        } else {
            //提前发货 非审批通过  非审批拒绝 不调 sf
            if (!(StringUtils.equals(req.getStatus(), BpmStatusEnum.APPROVED.getCode()) || StringUtils.equals(req.getStatus(), BpmStatusEnum.REJECT.getCode()))) {
                return Result.ok();
            }
        }

        try {
            List<String> sfContractNos = new ArrayList<>();
            sfContractNos.add(deliverContract.getSfContractNo());
            Result<String> result = deliverOrderSyncManger.deliverOrderSync(deliverContract.getDeliverNo(), sfContractNos);
            //提前发货审批拒绝，需要更新 sf 的状态为成功同步拒绝
            if (ApprovalTypeEnum.ADVANCE_SHIPPING.getCode().equals(approvalType)
                    && BpmStatusEnum.REJECT.getCode().equals(req.getStatus())) {

                //拒绝同步成功
                Integer syncStatus = result.getSuccess() ? 1 : 2;
                String syncStatusSf = result.getSuccess() ? SyncStatusSfEnum.SUCCESS_REJECT.getCode() : SyncStatusSfEnum.FAIL.getCode();
                deliverContractMapperService.lambdaUpdate()
                        .set(DeliverContractPO::getSyncStatusSf, syncStatusSf)
                        .set(DeliverContractPO::getQuantitySyncSf, syncStatus)
                        .eq(DeliverContractPO::getDeliverContractId, req.getZzbId())
                        .update();
            }

        } catch (Exception e) {
            log.error("同步 sf 异常：", e);
        }

        return Result.ok();
    }



    private List<DeliverFileRelationPO> approvalFileInit(List<DeliverContractPO> deliverContractList, DeliverRetryApprovalReqDTO req) {
        String deliverNo = req.getDeliverNo();
        String sfContractNo = req.getSfContractNo();

        Map<String, String> drContractIdMap = deliverContractList.stream().collect(Collectors.toMap(DeliverContractPO::getSfContractNo, DeliverContractPO::getDeliverContractId));
        List<DeliverFileRelationPO> fileRelations = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtils.isEmpty(req.getApprovalFiles())) {
            return fileRelations;
        }

        req.getApprovalFiles().forEach(a -> {

            String drFileId = deliverNoGenerator.getId(DR_FILE, "deliverFileId");

            DeliverFileRelationPO deliverFileRelation = DeliverFileRelationPO.builder()
                    .setDeliverFileId(drFileId)
                    .setDeliverNo(deliverNo)
                    .setSfContractNo(sfContractNo)
                    .setDeliverContractId(drContractIdMap.get(sfContractNo))
                    .setFileId(a.getFileId())
                    .setBusinessType(BusinessTypeEnum.BPM_APPROVAL.getCode())
                    .setFileName(a.getFileName())
                    .setFileSize(a.getFileSize())
                    .setFileType(a.getFileType())
                    .setIsDeleted(0)
                    .setCreatedBy(req.getLoginUserId())
                    .setCreatedName(req.getLoginUserName())
                    .setCreatedTime(now)
                    .setUpdatedBy(req.getLoginUserId())
                    .setUpdatedName(req.getLoginUserName())
                    .setUpdatedTime(now);
            fileRelations.add(deliverFileRelation);
        });
        return fileRelations;
    }
}
