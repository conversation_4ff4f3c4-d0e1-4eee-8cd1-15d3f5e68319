package com.trinasolar.trinax.delivery.manager;

import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoStatusEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListUpdateReqDTO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@AllArgsConstructor
public class DeliveryTodoListManger {
    final TodoListFeign todoListFeign;


    /**
     * 更新待办状态
     *
     * @param deliverPO 发货单
     */
    public void updateTodoList(DeliverPO deliverPO, LocalDateTime now, List<String> bizCodeList, String bizCode) {
        if (bizCodeList == null) {
            bizCodeList = new ArrayList<>();
        }
        if (StringUtils.isNotBlank(bizCode)) {
            bizCodeList.add(bizCode);
        }
        List<TodoListUpdateReqDTO> updateReqList = new ArrayList<>();

        bizCodeList.forEach(bizCodeInfo -> {
            updateReqList.add(TodoListUpdateReqDTO.builder()
                    .bizNo(deliverPO.getDeliverNo())
                    .bizCode(bizCodeInfo)
                    .todoStatus(TodoStatusEnum.DONE.getCode())
                    .doneTime(now)
                    .updatedBy(deliverPO.getUpdatedBy())
                    .updatedName(deliverPO.getUpdatedName())
                    .build());
        });

        todoListFeign.batchUpdateStatus(updateReqList);
    }
}
