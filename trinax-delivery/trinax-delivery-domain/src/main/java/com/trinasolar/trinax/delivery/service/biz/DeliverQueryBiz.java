package com.trinasolar.trinax.delivery.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.cart.dto.input.ContractDetailQueryReqIdListDTO;
import com.trinasolar.trinax.cart.dto.input.ContractQueryReqItemIdListDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.dto.output.*;
import com.trinasolar.trinax.delivery.constants.enums.DeliverMethod;
import com.trinasolar.trinax.delivery.constants.enums.DeliverStatus;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.*;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.*;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverContractItemQuantityPDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverListRespDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.dao.DeliveredItemDTO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.domain.list.LockedQuantityListDTO;
import com.trinasolar.trinax.delivery.service.manager.DeliverAuthManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.input.ProductSummaryQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductSummaryDTO;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SubordinateQueryReqDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesSubordinateRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import io.netty.util.internal.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverQueryBiz {
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final DeliverContractMapperService deliverContractMapperService;
    final DeliverMapperService deliverMapperService;
    final DeliverBizManager deliverBizManager;
    final EnterpriseFeign enterpriseFeign;
    final ProductFeign productFeign;
    final SysUserFeign sysUserFeign;
    final ContractFeign contractFeign;
    final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;
    final DeliverAuthManager deliverAuthManager;

    @Autowired
    private Executor ioTaskExecutor;


    public List<DeliverContractItemQuantityPDTO> contractItemQuantityPList(DeliveryContractItemQueryReqDTO req) {
        List<DeliverContractItemPO> deliverContractItemPOS = deliverContractItemMapperService.contractItemQuantityPList(req);
        return BeanUtil.copyToList(deliverContractItemPOS, DeliverContractItemQuantityPDTO.class);
    }

    public Result<DeliverDetailRespDTO> detailDelivery(DeliverDetailReqDTo req) {
        String deliverNo = req.getDeliverNo();

        LambdaQueryWrapper<DeliverPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeliverPO::getDeliverNo, deliverNo);
        DeliverPO deliverPO = deliverMapperService.getOne(queryWrapper);
        if (deliverPO == null) {
            log.info("发货申请详情查询无数据 deliverNo {} ", deliverNo);
            //查询无数据
            return Result.ok();
        }
        // ====所有的合同=====
        List<DeliverContractPO> deliverContractPOList = deliverContractMapperService
                .lambdaQuery().eq(DeliverContractPO::getDeliverNo, deliverNo).eq(DeliverContractPO::getIsDeleted, 0)
                .list();
        // 表内所有的合同行
        List<DeliverContractItemPO> deliverContractItemPOS = deliverContractItemMapperService
                .lambdaQuery().eq(DeliverContractItemPO::getDeliverNo, deliverNo).eq(DeliverContractItemPO::getIsDeleted, 0)
                .list();
        //基于原始行进行分组
        Map<String, ContractDetailResAppDTO> contractItemMap;

        // 远程产品信息
        CompletableFuture<List<ProductSummaryDTO>> prodSummaryListFuture = CompletableFuture.supplyAsync(() ->
                getProdSummaryList(deliverContractItemPOS), ioTaskExecutor);

        // 远程合同信息
        CompletableFuture<ContractDeliveryDetailResDTO> remoteContractDetailResListFuture = CompletableFuture.supplyAsync(() ->
                getRemoteContractDetailResList(deliverContractPOList, deliverContractItemPOS), ioTaskExecutor);

        // 远程用户信息
        CompletableFuture<SysUserRespDTO> externalUserFuture = CompletableFuture.supplyAsync(() ->
                StringUtil.isNullOrEmpty(deliverPO.getExternalUserId()) ? new SysUserRespDTO() : sysUserFeign.getUserByUserId(deliverPO.getExternalUserId()).getData(), ioTaskExecutor);

        // 远程地址信息
        CompletableFuture<EnterpriseAddressRespDTO> addressInfoFuture = CompletableFuture.supplyAsync(() ->
                BeanUtil.copyProperties(enterpriseFeign.getEnterpriseAddressByAddressId(deliverPO.getBillToAddressId()).getData(), EnterpriseAddressRespDTO.class), ioTaskExecutor);

        List<CompletableFuture<?>> allFutures = new ArrayList<>();
        allFutures.add(prodSummaryListFuture);
        allFutures.add(remoteContractDetailResListFuture);
        allFutures.add(externalUserFuture);
        allFutures.add(addressInfoFuture);

        // 合并汇总计算结果
        CompletableFuture.allOf(allFutures.toArray(new CompletableFuture[0])).join();

        // 获取异步结果
        List<ProductSummaryDTO> prodSummaryList = prodSummaryListFuture.join();
        ContractDeliveryDetailResDTO remoteContractResList = remoteContractDetailResListFuture.join();
        SysUserRespDTO externalUser = externalUserFuture.join();
        EnterpriseAddressRespDTO addressInfo = addressInfoFuture.join();

        contractItemMap = remoteContractResList.getContractDetailResAppDTOs().stream()
                .collect(toMap(ContractDetailResAppDTO::getContractId, dto -> dto,
                        (k1, k2) -> k1, HashMap::new));

        // 组装合同信息
        List<DeliverDetailContractDTO> contractList = new ArrayList<>();
        for (DeliverContractPO contractPO : deliverContractPOList) {
            DeliverDetailContractDTO deliverContractDTO = new DeliverDetailContractDTO();
            // 查找表内对应的合同行
            List<DeliverContractItemPO> contractItemPOList = getDeliverContractItem(contractPO, deliverContractItemPOS);
            Map<String, String> contractItemFieldMapping = new HashMap<>();
            // key 为源字段，value为目标字段
            contractItemFieldMapping.put("quantityMw", "applyQuantityMw");
            contractItemFieldMapping.put("quantityW", "applyQuantityW");
            contractItemFieldMapping.put("quantityP", "applyQuantityP");
            List<DeliverDetailContractItemDTO> deliverDetailContractItemDTOList = BeanUtil.copyToList(contractItemPOList,
                    DeliverDetailContractItemDTO.class, new CopyOptions().setFieldMapping(contractItemFieldMapping));
            log.info("外部用户查看合同详情 deliverDetailContractItemDTOList：{},contractItemPOList: {},deliverContractItemPOS: {}",
                    JacksonUtil.bean2Json(deliverDetailContractItemDTOList), JacksonUtil.bean2Json(contractItemPOList),
                    JacksonUtil.bean2Json(deliverContractItemPOS));

            // 产品信息
            setProdInfo(deliverDetailContractItemDTOList, prodSummaryList);
            // 设置合同行信息（远程的，本地的）
            setContractItemInfo(contractItemPOList, deliverDetailContractItemDTOList, remoteContractResList.getContractDeliveryItemResDTOs());
            List<DeliverDetailContractItemDTO> groupedDeliverDetailContractItemDTOList = groupDetailContractItem(deliverDetailContractItemDTOList, contractItemMap);
            // 设置回去所有的合同行
            deliverContractDTO.setContractItemList(groupedDeliverDetailContractItemDTOList);

            fillMultiPowerList(groupedDeliverDetailContractItemDTOList);

            // 设置当前合同信息
            setContractInfo(deliverContractDTO, contractPO, remoteContractResList.getContractDeliveryResDTOs());
            contractList.add(deliverContractDTO);
        }

        // 组装最后的报文
        DeliverDetailRespDTO deliveryDetail = DeliverDetailRespDTO.builder()
                .deliverNo(deliverPO.getDeliverNo())
                .deliverStatus(deliverPO.getDeliverStatus())
                .enterpriseId(deliverPO.getEnterpriseId())
                .enterpriseName(deliverPO.getEnterpriseName())
                .capitalEnterpriseId(deliverPO.getCapitalEnterpriseId())
                .capitalEnterpriseName(deliverPO.getCapitalEnterpriseName())
                .deliverMethod(deliverPO.getIncoterm())
                .returnReason(deliverPO.getOperationReturnReason())
                .comment(deliverPO.getComment())
                .contactId(deliverPO.getExternalUserId())
                .contactName(externalUser.getUserName())
                .address(addressInfo)
                .createdTime(deliverPO.getCreatedTime())
                .contractList(contractList)
                .salesInternalUserId(deliverPO.getSalesInternalUserId())
                .salesInternalUserName(deliverPO.getSalesInternalUserName())
                .orderType(deliverPO.getOrderType())
                .build();

        //权限校验
        if (permissionCheck(deliverPO, req)) {
            return Result.ok(deliveryDetail);
        }
        return Result.ok();

    }

    /**
     * 填充功率范围
     * @param contractItemResAppDTOS
     */
    public void fillMultiPowerList(List<DeliverDetailContractItemDTO> contractItemResAppDTOS) {
        Set<String> productIdSet=contractItemResAppDTOS.stream().map(DeliverDetailContractItemDTO::getPbiProductId).collect(Collectors.toSet());
        ProductModulePowerQueryReqDTO productModulePowerQueryReqDTO=new ProductModulePowerQueryReqDTO();
        productModulePowerQueryReqDTO.setProductIds(productIdSet);
        Result<List<ProductModulePowerDTO>> result=productFeign.queryProductModulePowerList(productModulePowerQueryReqDTO);
        Assert.isTrue(result.getSuccess(),"查询产品功率范围出错");

        List<ProductModulePowerDTO> productModulePowerDTOS=result.getData();
        Map<String, List<ProductModulePowerDTO>> productModulePowerDTOMap=productModulePowerDTOS.stream().collect(Collectors.groupingBy(ProductModulePowerDTO::getProductId));
        contractItemResAppDTOS.forEach(item->{
            item.setMultiPowerList(calcMultiPowerList(item, productModulePowerDTOMap.get(item.getPbiProductId())));
        });
    }

    private List<Integer> calcMultiPowerList(DeliverDetailContractItemDTO item, List<ProductModulePowerDTO> productModulePowerDTOs) {
        if(StringUtils.isNotBlank(item.getMultiPowerBegin())&& StringUtils.isNotBlank(item.getMultiPowerEnd())){
            Integer begin=Integer.parseInt(item.getMultiPowerBegin());
            Integer end=Integer.parseInt(item.getMultiPowerEnd());

            return productModulePowerDTOs.stream().map(ProductModulePowerDTO::getOutputPower).map(Integer::parseInt).
                    filter(power->power<=end&&power>=begin).collect(Collectors.toList());
        }else{
            if(item.getPower()!=null){
                return Arrays.asList(item.getPower().intValue());
            }else{
                return Collections.emptyList();
            }
        }

    }

    /**
     * 将拆分出来的功率行进行分组
     *
     * @param deliverDetailContractItemDTOList
     * @param contractItemMap 从合同侧查询过来的已汇总过的数据
     */
    private List<DeliverDetailContractItemDTO> groupDetailContractItem(List<DeliverDetailContractItemDTO> deliverDetailContractItemDTOList,
                                         Map<String,ContractDetailResAppDTO> contractItemMap) {
        List<DeliverDetailContractItemDTO> resultDetailContractItemDTOList = new ArrayList<>();

//        Map<String,DeliverDetailContractItemDTO> deliverContractMap=deliverDetailContractItemDTOList.stream().collect(Collectors.toMap(DeliverDetailContractItemDTO::getContractBusinessItemId, Function.identity()));

        //在contractItemMap中获取value，并获取value的contractItemResAppDTOS合并成一个list
        List<ContractItemResAppDTO> contractItemResAppDTOS=new ArrayList<>();
        contractItemMap.entrySet().forEach(entry->{
            contractItemResAppDTOS.addAll(entry.getValue().getContractItemResAppDTOS());
        });

        Map<String,ContractItemResAppDTO> contractItemResAppDTOMap=contractItemResAppDTOS.stream().collect(Collectors.toMap(ContractItemResAppDTO::getSfContractItemId, Function.identity()));
//        List<DeliverDetailContractItemDTO> parentDetailContractItemDTOList=deliverDetailContractItemDTOList.stream()
//                .filter(item->contractItemResAppDTOMap.containsKey(item.getSfContractItemId())
//                ||contractItemResAppDTOMap.containsKey(item.getSfParentItemId()))
//                .collect(Collectors.toList());
        //将deliverDetailContractItemDTOList中sfParentItemId和sfContractItemId相同的数据分为一组
//        Map<String,List<DeliverDetailContractItemDTO>> parentDetailContractItemDTOList=deliverDetailContractItemDTOList.stream().collect(Collectors.groupingBy(item->item.getSfParentItemId()));
//        Map<String,List<DeliverDetailContractItemDTO>> childDetailContractItemDTOList=deliverDetailContractItemDTOList.stream().collect(Collectors.groupingBy(item->item.getSfContractItemId()));

        Map<String,List<DeliverDetailContractItemDTO>> groupByParentMap=new HashMap<>();
        deliverDetailContractItemDTOList.forEach(item->{
            if(StringUtils.isBlank(item.getSfParentItemId())){
                //如果是一级节点
                if(contractItemResAppDTOMap.containsKey(item.getSfContractItemId())){
                    groupByParentMap.putIfAbsent(item.getSfContractItemId(),  new ArrayList<>());
                    groupByParentMap.get(item.getSfContractItemId()).add(item);
                }
            }else{
                //如果是二级节点,使用父节点检索
                if(contractItemResAppDTOMap.containsKey(item.getSfParentItemId())){
                    groupByParentMap.putIfAbsent(item.getSfParentItemId(), new ArrayList<>());
                    groupByParentMap.get(item.getSfParentItemId()).add(item);
                }
            }
        });

        groupByParentMap.forEach((key,value)->{
            DeliverDetailContractItemDTO deliverDetailContractItemDTO=new DeliverDetailContractItemDTO();
            ContractItemResAppDTO contractItemResAppDTO= contractItemResAppDTOMap.get(key);
            deliverDetailContractItemDTO.setSfProductName(contractItemResAppDTO.getProductName());
            deliverDetailContractItemDTO.setPower(contractItemResAppDTO.getPower().doubleValue());
            deliverDetailContractItemDTO.setQuantityP(contractItemResAppDTO.getQuantityP());
            deliverDetailContractItemDTO.setQuantityMw(contractItemResAppDTO.getQuantityMw());
            deliverDetailContractItemDTO.setUnitPriceW(contractItemResAppDTO.getUnitPriceW());
            deliverDetailContractItemDTO.setContractBusinessItemId(contractItemResAppDTO.getContractBusinessItemId());
            deliverDetailContractItemDTO.setDeliverDetailContractItemDTOList(value);
            deliverDetailContractItemDTO.setToBeExecutedQuantityP(contractItemResAppDTO.getToBeExecutedQuantityP());
            deliverDetailContractItemDTO.setRemainingQuantity(contractItemResAppDTO.getRemainingQuantityP());
            deliverDetailContractItemDTO.setMultiPowerBegin(contractItemResAppDTO.getMultiPowerBegin());
            deliverDetailContractItemDTO.setMultiPowerEnd(contractItemResAppDTO.getMultiPowerEnd());
            deliverDetailContractItemDTO.setPbiProductId(contractItemResAppDTO.getPbiProductId());
            deliverDetailContractItemDTO.setProductIcon(value.get(0).getProductIcon());
            resultDetailContractItemDTOList.add(deliverDetailContractItemDTO);
        });

        return resultDetailContractItemDTOList;
    }

    public Result<PageResponse<DeliverListRespDTO>> getDeliveryList(PageRequest<DeliverListReqDTO> req,
                                                                    DeliverAuthListUserReqDTO para) {
        if (SysUserTypeEnum.INTERNAL.getType().equals(req.getQuery().getUserType())) {
            Result<PageResponse<DeliverListRespDTO>> result = deliverMapperService.getInternalDeliveryList(req, para);// 内部用户查询条件
            if (CollUtil.isNotEmpty(result.getData().getRecords())) {
                result.getData().getRecords().forEach(e -> {
                    e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
                });
            }
            return result;
        } else {
            Result<PageResponse<DeliverListRespDTO>> result = deliverMapperService.getExternalDeliveryPage(req);
            if (CollUtil.isNotEmpty(result.getData().getRecords())) {
                result.getData().getRecords().forEach(e -> {
                    e.setIncotermText(DeliverMethod.getDescByCode(e.getIncoterm()));
                    e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
                });

            }
            return result;// 外部用户查询条件
        }
    }


    /**
     * 权限校验，有数据权限返回true；无数据权限返回false
     *
     * @return
     */
    private boolean permissionCheck(DeliverPO deliver, DeliverDetailReqDTo reqDTO) {
        // 草稿状态数据只有自己查看
        if (DeliverStatus.UN_SUBMIT.getCode().equals(deliver.getDeliverStatus())) {
            return StringUtils.equals(deliver.getCreatedBy(), reqDTO.getUserId());
        }
        //非管理员区分内外部用户分别校验
        if (SysUserTypeEnum.INTERNAL.getType().equals(reqDTO.getUserType())) {
            String orgType = deliverAuthManager.getOrgTypeByUserId(reqDTO.getUserId());
            //用户组织类型为common时，查询所有类型为销售的业务组织编码
            if (SysOrganizationTypeEnum.COMMON.getCode().equals(orgType)) {
                List<String> subUserIdList = deliverAuthManager.getOperationSubUserList(reqDTO.getUserId());
                subUserIdList.add(reqDTO.getUserId());
                return subUserIdList.contains(deliver.getSalesInternalUserId())
                        || subUserIdList.contains(deliver.getOperationInternalUserId());
            } else if (SysOrganizationTypeEnum.SALES.getCode().equals(orgType)) {
                SubordinateQueryReqDTO subordinateQueryReqDTO = new SubordinateQueryReqDTO();
                subordinateQueryReqDTO.setUserId(reqDTO.getUserId());
                subordinateQueryReqDTO.setSysUserTypeEnum(SysUserTypeEnum.INTERNAL);
                List<SysSalesSubordinateRespDTO> result = sysUserFeign.getSalesSubordinate(subordinateQueryReqDTO).getData();
                if (ObjectUtils.isEmpty(result)) {
                    return StringUtils.equals(deliver.getSalesInternalUserId(), reqDTO.getUserId());
                }
                List<SysSalesSubordinateRespDTO> permissionList = result.stream()
                        .filter(e -> e.getUserId().equals(deliver.getSalesInternalUserId()) && e.getOrganizationCode().equals(deliver.getBizOrganizationCode()))
                        .collect(Collectors.toList());
                return !CollectionUtils.isEmpty(permissionList);
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(orgType)) {

                SubordinateQueryReqDTO subordinateQueryReqDTO = new SubordinateQueryReqDTO();
                subordinateQueryReqDTO.setUserId(reqDTO.getUserId());
                subordinateQueryReqDTO.setSysUserTypeEnum(SysUserTypeEnum.INTERNAL);
                List<SysUserRespDTO> result = sysUserFeign.getSubordinate(subordinateQueryReqDTO).getData();

                if (ObjectUtils.isEmpty(result)) {
                    return StringUtils.equals(deliver.getOperationInternalUserId(), reqDTO.getUserId());
                }
                List<String> userIds = result.stream().map(SysUserRespDTO::getUserId).collect(Collectors.toList());

                return userIds.contains(deliver.getOperationInternalUserId());
            } else {
                return false;
            }
        } else {

            List<SysSubDealerRespDTO> allUser = sysDealerSalesRelationFeign.getSubDealerInfo(reqDTO.getUserId()).getData();
            if (ObjectUtils.isEmpty(allUser)) {
                return StringUtils.equals(deliver.getExternalUserId(), reqDTO.getUserId());
            }
            List<SysSubDealerRespDTO> permissionList = allUser.stream()
                    .filter(e -> e.getDealerUserId().equals(deliver.getExternalUserId()) && e.getEnterpriseId().equals(deliver.getEnterpriseId()))
                    .collect(Collectors.toList());
            return !CollectionUtils.isEmpty(permissionList);
        }
    }

    /**
     * 用于处理搜索结果
     *
     * @param req req
     * @return 搜索结果
     */
    public Result<PageResponse<DeliverListRespDTO>> getSearchList(PageRequest<DeliverListReqDTO> req,
                                                                  DeliverAuthListUserReqDTO para
    ) {
        if (SysUserTypeEnum.INTERNAL.getType().equals(req.getQuery().getUserType())) {
            return deliverMapperService.searchInternalDeliveryList(req, para); // 内部搜索条件
        } else {
            return deliverMapperService.searchExternalDeliveryList(req);// 外部搜索条件
        }

    }

    /**
     * 获取远程合同详情信息
     */

    private ContractDeliveryDetailResDTO getRemoteContractDetailResList(List<DeliverContractPO> deliverContractPOList, List<DeliverContractItemPO> deliverContractItemPOS) {
        List<String> ids = deliverContractPOList.stream().map(DeliverContractPO::getContractId).collect(Collectors.toList());
        ContractQueryReqItemIdListDTO contractItemIdReq = new ContractQueryReqItemIdListDTO(
                deliverContractItemPOS.stream().map(DeliverContractItemPO::getContractBusinessItemId)
                        .collect(Collectors.toList()));
        return contractFeign.qryListByContractIds(new ContractDetailQueryReqIdListDTO(ids, contractItemIdReq.getContractBusinessItemIds())).getData();
    }


    /**
     * 获取所有的产品信息
     *
     * @param deliverContractItemPOS 表内合同行
     */
    private List<ProductSummaryDTO> getProdSummaryList(List<DeliverContractItemPO> deliverContractItemPOS) {
        List<String> allProductId = deliverContractItemPOS.stream().map(DeliverContractItemPO::getPbiProductId)
                .collect(Collectors.toList());
        return productFeign.getSummaryByProductIds(new ProductSummaryQueryReqDTO(allProductId)).getData();
    }

    /**
     * 根据合同获取对应的合同行
     *
     * @param contractPO             当前合同
     * @param deliverContractItemPOS 所有的合同行集合
     */
    private List<DeliverContractItemPO> getDeliverContractItem(DeliverContractPO contractPO,
                                                               List<DeliverContractItemPO> deliverContractItemPOS) {
        return deliverContractItemPOS.stream()
                .filter(e -> e.getDeliverContractId().equals(contractPO.getDeliverContractId()))
                .collect(Collectors.toList());
    }

    /**
     * 设置产品信息
     *
     * @param contractItemPOList 合同行信息
     * @param prodSummaryList    产品信息
     */
    private void setProdInfo(List<DeliverDetailContractItemDTO> contractItemPOList, List<ProductSummaryDTO> prodSummaryList) {
        // 产品名称
        contractItemPOList.forEach(i -> i.setSfProductName(
                prodSummaryList.stream().filter(s -> s.getProductId().equals(i.getPbiProductId()))
                        .findAny().orElse(new ProductSummaryDTO()).getProductName()
        ));

        // 添加产品小图
        contractItemPOList.forEach(i -> i.setProductIcon(
                prodSummaryList.stream().filter(s -> s.getProductId().equals(i.getPbiProductId()))
                        .findAny().orElse(new ProductSummaryDTO()).getIconUrl()
        ));
    }

    /**
     * 设置合同行信息
     *
     * @param contractItemPOList          表内合同行
     * @param delvierContractItemPOList   本次发货的合同行信息
     * @param contractDeliveryItemResList 本次发货的远程合同行信息 【远程】
     */
    private void setContractItemInfo(List<DeliverContractItemPO> contractItemPOList,
                                     List<DeliverDetailContractItemDTO> delvierContractItemPOList,
                                     List<ContractDeliveryItemResDTO> contractDeliveryItemResList) {
        // 根据发货合同行，找到所有的合同，再回去查所有的发货单，根据所有发货单的状态，再计算待执行数量

        // 获取该合同行，过去所有的发货单已经 SUBMIT SALES_CONFIRM的所有信息
        List<LockedQuantityListDTO> lockedQuantityList = deliverBizManager.getLockedQuantity(contractItemPOList);

        // 设置未执行量
        delvierContractItemPOList.forEach(localContract -> {
            // 远程合同
            ContractDeliveryItemResDTO remoteContract = contractDeliveryItemResList != null
                    ? contractDeliveryItemResList.stream()
                    .filter(s -> s.getContractBusinessItemId().equals(localContract.getContractBusinessItemId()))
                    .findAny().orElse(new ContractDeliveryItemResDTO())
                    : null;
            ContractDeliveryItemResDTO finalRemoteContract = Optional.ofNullable(remoteContract).orElse(new ContractDeliveryItemResDTO());

            localContract.setSfContractItemId(finalRemoteContract.getSfContractItemId());
            localContract.setSfParentItemId(finalRemoteContract.getSfParentItemId());
            int remainingQuantityP = Optional.ofNullable(finalRemoteContract.getRemainingQuantityP()).orElse(0);
            localContract.setRemainingQuantity(remainingQuantityP);
            // 锁定数量
            int locked = lockedQuantityList.isEmpty() ? 0 :
                    lockedQuantityList.stream()
                            .filter(e -> e.getContractBusinessItemId().equals(localContract.getContractBusinessItemId()))
                            .mapToInt(LockedQuantityListDTO::getQuantityP)
                            .sum();
            // 待执行数量
            if (remainingQuantityP - locked <= 0) {
                localContract.setToBeExecutedQuantityP(remainingQuantityP);
            }
            localContract.setToBeExecutedQuantityP(remainingQuantityP - locked);
            log.info("待执行数量:{} , 锁定数量:{} ,sf 合同剩余数量:{}", localContract.getToBeExecutedQuantityP(), locked, remainingQuantityP);
        });

        // 设置合同行合同数量
        delvierContractItemPOList.forEach(localContract -> localContract.setQuantityP(
                contractDeliveryItemResList != null
                        ? contractDeliveryItemResList.stream().filter(s -> s.getContractBusinessItemId().equals(localContract.getContractBusinessItemId()))
                        .findAny().orElse(new ContractDeliveryItemResDTO()).getQuantityP()
                        : null
        ));

        // QuantityW
        delvierContractItemPOList.forEach(localContract -> localContract.setQuantityW(
                contractDeliveryItemResList != null
                        ? contractDeliveryItemResList.stream().filter(s -> s.getContractBusinessItemId().equals(localContract.getContractBusinessItemId()))
                        .findAny().orElse(new ContractDeliveryItemResDTO()).getQuantityW()
                        : null
        ));


        //QuantityMw
        delvierContractItemPOList.forEach(localContract -> localContract.setQuantityMw(
                contractDeliveryItemResList != null
                        ? contractDeliveryItemResList.stream().filter(s -> s.getContractBusinessItemId().equals(localContract.getContractBusinessItemId()))
                        .findAny().orElse(new ContractDeliveryItemResDTO()).getQuantityMw()
                        : null
        ));
    }

    /**
     * 设置当前合同信息
     *
     * @param deliverContractDTO 当前合同
     * @param contractPO         表内合同
     */
    private void setContractInfo(DeliverDetailContractDTO deliverContractDTO,
                                 DeliverContractPO contractPO,
                                 List<ContractDeliveryResDTO> remoteContractResList) {

        ContractDeliveryResDTO remoteContract = remoteContractResList.stream()
                .filter(e -> e.getSfContractNo().equals(contractPO.getSfContractNo()))
                .findFirst().orElse(new ContractDeliveryResDTO());


        deliverContractDTO.setContractId(contractPO.getContractId());
        deliverContractDTO.setSfContractId(contractPO.getSfContractId());
        deliverContractDTO.setSfContractNo(contractPO.getSfContractNo());
        deliverContractDTO.setRebate(contractPO.getRebate());
        // 没有双签的就没有值
        deliverContractDTO.setCounterSignedDate(contractPO.getCounterSignedDate() == null
                ? null
                : contractPO.getCounterSignedDate().toLocalDate());
        deliverContractDTO.setTotalVolumeMW(remoteContract.getTotalVolumeMw());
        deliverContractDTO.setContractStatus(remoteContract.getContractStatus());
    }


    public Result<Map<String, DeliveredQuantityResDTO>> qryDeliveredQuantity(List<String> contractItemIds) {
        Map<String, DeliveredQuantityResDTO> result = new HashMap<>();
        if (ObjectUtils.isEmpty(contractItemIds)) {
            return Result.ok(result);
        }
        List<DeliveredItemDTO> deliveredItems = deliverContractItemMapperService.selDeliveredItems(contractItemIds);

        if (ObjectUtils.isEmpty(deliveredItems)) {
            return Result.ok(result);
        }

        deliveredItems.stream().forEach(a -> {
            DeliveredQuantityResDTO drRes = BeanUtil.copyProperties(a, DeliveredQuantityResDTO.class);
            result.put(a.getContractBusinessItemId(), drRes);
        });

        return Result.ok(result);

    }

    public Result<BigDecimal> trendChart(DeliverOrderCockpitReqDTO reqDTO) {
        LambdaQueryWrapper<DeliverPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeliverPO::getEnterpriseId, reqDTO.getEnterpriseId());
        queryWrapper.eq(DeliverPO::getDeliverStatus, DeliverStatus.OPERATION_CONFIRMED.getCode());
        queryWrapper.ge(ObjectUtil.isNotNull(reqDTO.getStartTime()), DeliverPO::getCreatedTime, reqDTO.getStartTime());
        queryWrapper.le(ObjectUtil.isNotNull(reqDTO.getEndTime()), DeliverPO::getCreatedTime, reqDTO.getEndTime());
        if (StringUtils.isNotEmpty(reqDTO.getCapitalEnterpriseId())){
            queryWrapper.eq(DeliverPO::getCapitalEnterpriseId, reqDTO.getCapitalEnterpriseId());
        }else {
            queryWrapper.and(wrapper -> wrapper.eq(DeliverPO::getCapitalEnterpriseId, "").or().isNull(DeliverPO::getCapitalEnterpriseId));
        }
        List<DeliverPO> deliverPOList=deliverMapperService.list(queryWrapper);
        if(CollectionUtils.isEmpty(deliverPOList)){
            return Result.ok(BigDecimal.ZERO);
        }
        return Result.ok(deliverPOList.stream().map(DeliverPO::getQuantityMw).filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
    }
}
