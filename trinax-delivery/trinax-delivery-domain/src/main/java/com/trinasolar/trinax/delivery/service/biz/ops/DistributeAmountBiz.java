package com.trinasolar.trinax.delivery.service.biz.ops;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.constants.enums.BpmStatusEnum;
import com.trinasolar.trinax.delivery.constants.enums.DistributeOperationTypeEnum;
import com.trinasolar.trinax.delivery.constants.enums.SyncStatusSfEnum;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DistributeAccountBillMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractItemPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverContractPO;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.repository.domain.DistributeAccountBillPO;
import com.trinasolar.trinax.delivery.service.manager.DeliverApprovalManger;
import com.trinasolar.trinax.integration.api.IntegrationDeliveryFeign;
import com.trinasolar.trinax.integration.dto.input.SfAccountBalanceEditReq;
import com.trinasolar.trinax.partner.dto.output.EnterpriseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created on 2025/6/4, 17:57
 * author: zhangyang
 */
@Component
@Slf4j
public class DistributeAmountBiz {

    @Value("${deliver.openSystemOrgCode}")
    String openSystemOrgCode;
    @Autowired
    private DistributeAccountBillMapperService distributeAccountBillMapperService;
    @Autowired
    private IntegrationDeliveryFeign integrationDeliveryFeign;
    @Autowired
    private DeliverApprovalManger deliverApprovalManger;
    @Autowired
    private DeliverContractMapperService deliverContractMapperService;


    /**
     * 分销金额回滚
     *
     * @param deliverPO
     * @param deliverContract
     */
    public void callbackDistributeAmount(DeliverPO deliverPO, DeliverContractPO deliverContract) {
        if(!openSystemOrgCode.contains(deliverPO.getBizOrganizationCode())){
            //非开放区域，暂时不用处理
            return;
        }

        DistributeAccountBillPO distributeAccountBillPO=distributeAccountBillMapperService.getOne(new LambdaQueryWrapper<DistributeAccountBillPO>()
                .eq(DistributeAccountBillPO::getDeliverContractId,deliverContract.getDeliverContractId())
                .eq(DistributeAccountBillPO::getOperationType, DistributeOperationTypeEnum.DEDUCTION.getCode())
                .orderByDesc(DistributeAccountBillPO::getCreatedTime).last("limit 1"));
        if (ObjectUtils.isNotEmpty(distributeAccountBillPO)){
            if(distributeAccountBillPO.getSfSyncStatus().equals(SyncStatusSfEnum.FAIL.getCode())){
                //由于历史充值并未成功，所以此处不用回滚，直接删除
                distributeAccountBillMapperService.removeById(distributeAccountBillPO.getId());
                return;
            }
            DistributeAccountBillPO callbackBill = new DistributeAccountBillPO();
            BeanUtils.copyProperties(distributeAccountBillPO,callbackBill);
            callbackBill.setId(null);
            callbackBill.setCreatedTime(LocalDateTime.now());
            callbackBill.setUpdatedTime(LocalDateTime.now());
            callbackBill.setOperationType(DistributeOperationTypeEnum.CALLBACK.getCode());


            Result<Object> result=remoteCallbackDistributeAmount(callbackBill);
            if (!result.getSuccess()){
                log.error("同步回滚分销余额到sf失败，sfContractNo="+callbackBill.getSfContractNo()+"原因：{}",result.getMessage());
                callbackBill.setSfSyncStatus(SyncStatusSfEnum.FAIL.getCode());
                callbackBill.setSfSyncMessage(result.getMessage());
            }else {
                log.info("同步分销余额到sf成功,sfContractNo="+callbackBill.getSfContractNo());
                callbackBill.setSfSyncStatus(SyncStatusSfEnum.SUCCESS.getCode());
            }
            distributeAccountBillMapperService.saveOrUpdate(callbackBill);
        }
    }

    /**
     * 分销金额回滚 远程调用
     * @param callbackBill
     * @return
     */
    private Result<Object> remoteCallbackDistributeAmount(DistributeAccountBillPO callbackBill) {
        SfAccountBalanceEditReq sfAccountBalanceEditReq=new SfAccountBalanceEditReq();
        sfAccountBalanceEditReq.setMdmCode(callbackBill.getMdmCode());
        sfAccountBalanceEditReq.setOperationType(DistributeOperationTypeEnum.CALLBACK.getCode());
        sfAccountBalanceEditReq.setTaskId(callbackBill.getBpmTaskId());
        sfAccountBalanceEditReq.setAmount(callbackBill.getAmount());

        return integrationDeliveryFeign.editBalance(sfAccountBalanceEditReq);
    }

    /**
     * 分销金额扣除
     * @param deliver
     * @param enterpriseInfo
     * @param deliverContractPO
     * @param itemPOList
     */
    public void deductDistributeAmount(DeliverPO deliver, EnterpriseDTO enterpriseInfo, DeliverContractPO deliverContractPO, List<DeliverContractItemPO> itemPOList) {
        if(!openSystemOrgCode.contains(deliver.getBizOrganizationCode())){
            //非开放区域，暂时不用处理
            return;
        }

        BigDecimal totalPrice=itemPOList.stream().map(a->a.getUnitPriceW().multiply(a.getQuantityW())).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2,BigDecimal.ROUND_HALF_UP);
        DistributeAccountBillPO distributeAccountBillPO=new DistributeAccountBillPO();
        distributeAccountBillPO.setAmount(totalPrice);
        distributeAccountBillPO.setDeliverContractId(deliverContractPO.getDeliverContractId());
        distributeAccountBillPO.setBpmTaskId(deliverContractPO.getBpmTaskId());
        distributeAccountBillPO.setMdmCode(enterpriseInfo.getMdmId());
        distributeAccountBillPO.setCreatedTime(LocalDateTime.now());
        distributeAccountBillPO.setUpdatedTime(LocalDateTime.now());
        distributeAccountBillPO.setDeliveryNo(deliverContractPO.getDeliverNo());
        distributeAccountBillPO.setSfContractNo(deliverContractPO.getSfContractNo());
        distributeAccountBillPO.setOperationType(DistributeOperationTypeEnum.DEDUCTION.getCode());

        Result<Object> result= remoteDeductDistributeAmount(distributeAccountBillPO);

        if (!result.getSuccess()){
            log.error("同步分销余额到sf失败，sfContractNo="+deliverContractPO.getSfContractNo()+"原因：{}",result.getMessage());
            distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.FAIL.getCode());
            distributeAccountBillPO.setSfSyncMessage(result.getMessage());
        }else{
            log.info("同步分销余额到sf成功,sfContractNo="+deliverContractPO.getSfContractNo());
            distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.SUCCESS.getCode());
        }
        distributeAccountBillMapperService.save(distributeAccountBillPO);
    }

    public Result<Object> remoteDeductDistributeAmount(DistributeAccountBillPO distributeAccountBillPO) {
        SfAccountBalanceEditReq sfAccountBalanceEditReq=new SfAccountBalanceEditReq();
        sfAccountBalanceEditReq.setAmount(distributeAccountBillPO.getAmount());
        sfAccountBalanceEditReq.setTaskId(distributeAccountBillPO.getBpmTaskId());
        sfAccountBalanceEditReq.setMdmCode(distributeAccountBillPO.getMdmCode());
        sfAccountBalanceEditReq.setOperationType(DistributeOperationTypeEnum.DEDUCTION.getCode());
        return integrationDeliveryFeign.editBalance(sfAccountBalanceEditReq);
    }


    /**
     * 重试分销余额同步
     * @param deliverContractId
     * @return
     */
    public Result<Object> retrySubmitDistributeAmount(String deliverContractId) {
        DeliverContractPO deliverContractPO = deliverContractMapperService.getOne(new LambdaQueryWrapper<DeliverContractPO>().eq(DeliverContractPO::getDeliverContractId, deliverContractId));
        Assert.notNull(deliverContractPO,"查询提货合同信息异常");
        if(deliverContractPO.getBpmStatus().equals(BpmStatusEnum.REJECT.getCode())){
            //如果是审批拒绝，则需要回滚
            DistributeAccountBillPO distributeAccountBillPO = distributeAccountBillMapperService.getOne(new LambdaQueryWrapper<DistributeAccountBillPO>().eq(DistributeAccountBillPO::getDeliverContractId, deliverContractId)
                    .eq(DistributeAccountBillPO::getOperationType,DistributeOperationTypeEnum.CALLBACK.getCode()).orderByDesc(DistributeAccountBillPO::getCreatedTime).last("limit 1"));
            Assert.notNull(distributeAccountBillPO,"查询回滚余额流水异常");
            Result<Object> result=remoteCallbackDistributeAmount(distributeAccountBillPO);
            if (!result.getSuccess()){
                log.error("同步回滚分销余额到sf失败，sfContractNo="+deliverContractPO.getSfContractNo()+"原因：{}",result.getMessage());
                distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.FAIL.getCode());
                distributeAccountBillPO.setSfSyncMessage(result.getMessage());
                distributeAccountBillMapperService.saveOrUpdate(distributeAccountBillPO);
                return Result.fail("同步回滚分销余额到sf失败");
            }else{
                log.info("同步回滚分销余额到sf成功,sfContractNo="+deliverContractPO.getSfContractNo());
                distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.SUCCESS.getCode());
                distributeAccountBillMapperService.saveOrUpdate(distributeAccountBillPO);
                return Result.ok();
            }
        }else if(deliverContractPO.getBpmStatus().equals(BpmStatusEnum.APPROVING.getCode())
                ||deliverContractPO.getBpmStatus().equals(BpmStatusEnum.APPROVED.getCode())){
            DistributeAccountBillPO distributeAccountBillPO = distributeAccountBillMapperService.getOne(new LambdaQueryWrapper<DistributeAccountBillPO>().eq(DistributeAccountBillPO::getDeliverContractId, deliverContractId)
                    .eq(DistributeAccountBillPO::getOperationType,DistributeOperationTypeEnum.DEDUCTION.getCode()).orderByDesc(DistributeAccountBillPO::getCreatedTime).last("limit 1"));

            Assert.notNull(distributeAccountBillPO,"查询余额同步流水异常");
            Result<Object> result=remoteDeductDistributeAmount(distributeAccountBillPO);
            if (!result.getSuccess()){
                log.error("同步分销余额到sf失败，sfContractNo="+deliverContractPO.getSfContractNo()+"原因：{}",result.getMessage());
                distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.FAIL.getCode());
                distributeAccountBillPO.setSfSyncMessage(result.getMessage());
                distributeAccountBillMapperService.saveOrUpdate(distributeAccountBillPO);
                return Result.fail("同步分销余额到sf失败");
            }else{
                log.info("同步分销余额到sf成功,sfContractNo="+deliverContractPO.getSfContractNo());
                distributeAccountBillPO.setSfSyncStatus(SyncStatusSfEnum.SUCCESS.getCode());
                distributeAccountBillMapperService.saveOrUpdate(distributeAccountBillPO);
                return Result.ok();
            }
        }

        return Result.fail("bpm审批状态异常");
    }

}
