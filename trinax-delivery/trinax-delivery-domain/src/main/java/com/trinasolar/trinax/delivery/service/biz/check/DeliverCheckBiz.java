package com.trinasolar.trinax.delivery.service.biz.check;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.dto.output.delivery.check.DetailCheckResDTO;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.repository.domain.DeliverPO;
import com.trinasolar.trinax.delivery.service.manager.DeliverCheckManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverCheckBiz {
    final DeliverCheckManager deliverCheckManager;
    final DeliverMapperService deliverMapperService;

    public Result<DetailCheckResDTO> deliverDetailCheck(String deliverNo) {
        if (StringUtils.isBlank(deliverNo)) {
            return Result.ok();
        }

        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, deliverNo).one();

        return deliverCheckManager.deliverDetailCheck(deliverNo, deliver);
    }
}
