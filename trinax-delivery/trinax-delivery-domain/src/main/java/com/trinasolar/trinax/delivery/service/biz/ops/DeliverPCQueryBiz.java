package com.trinasolar.trinax.delivery.service.biz.ops;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.cart.dto.input.ContractQueryReqItemIdListDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.api.ContractFeign;
import com.trinasolar.trinax.contract.constants.ContractConstants;
import com.trinasolar.trinax.contract.constants.enums.OpenContractEnum;
import com.trinasolar.trinax.contract.dto.output.ContractDeliveryItemResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CoBusinessResDTO;
import com.trinasolar.trinax.contract.dto.output.ContractDetailResAppDTO;
import com.trinasolar.trinax.contract.dto.output.ContractDetailResAppDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CoBusinessResDTO;
import com.trinasolar.trinax.delivery.constants.enums.*;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverAuthListSalesUserDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverAuthListUserReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsDetailReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.query.DeliverOpsListReqDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverApprovalFile;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverDetailOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverInfoOpsRespDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.detail.DeliverItem;
import com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverDetailResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.file.DeliverExportResDTO;
import com.trinasolar.trinax.delivery.dto.output.delivery.list.DeliverOpsListRespDTO;
import com.trinasolar.trinax.delivery.manager.UserManager;
import com.trinasolar.trinax.delivery.repository.atomicservice.*;
import com.trinasolar.trinax.delivery.repository.dao.DeliverDetailCheckDTO;
import com.trinasolar.trinax.delivery.repository.domain.*;
import com.trinasolar.trinax.delivery.repository.mapper.DeliverMapper;
import com.trinasolar.trinax.delivery.service.manager.DeliverAuthManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.delivery.service.manager.OrganizationManager;
import com.trinasolar.trinax.intentorder.api.IntentOrderFeign;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderPCQueryResDTO;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import com.trinasolar.trinax.masterdata.dto.output.ReleaseProductImageDTO;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.api.MarketingAccountFeign;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.dto.input.ErpOrderTypeQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.ErpOrderTypeResDTO;
import com.trinasolar.trinax.partner.dto.output.enterprise.EnterpriseAddressRespDTO;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysEnterpriseUserRespDTO;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverPCQueryBiz {
    final DeliverMapperService deliverMapperService;
    final DeliverMapper deliverMapper;
    final EnterpriseFeign enterpriseFeign;
    final DeliverAuthManager deliverAuthManager;
    final SysUserFeign sysUserFeign;
    final UserManager userManager;
    final OrganizationManager organizationManager;
    final MarketingAccountFeign marketingAccountFeign;
    final DeliverBizManager deliverBizManager;
    final SysOrganizationFeign sysOrganizationFeign;
    private final DeliverContractMapperService deliverContractMapperService;
    private final DeliverContractItemMapperService deliverContractItemMapperService;
    final ProductFeign productFeign;
    private final ContractFeign contractFeign;
    private final DeliverFileRelationMapperService deliverFileRelationMapperService;
    private final DistributeAccountBillMapperService distributeAccountBillService;
    private final com.trinasolar.trinax.intentorder.api.IntentOrderFeign intentOrderFeign;

    public Result<PageResponse<DeliverOpsListRespDTO>> listDeliverOps(PageRequest<DeliverOpsListReqDTO> req) {
        String userId = req.getQuery().getUserId();

//        if (StringUtils.isNotBlank(req.getQuery().getEnterpriseId())) {
//            List<String> coList = new ArrayList<>();
//            coList.add(req.getQuery().getEnterpriseId());
//            if (req.getQuery().isQueryRelCo()) {
//                List<String> relCoList = enterpriseFeign.qryRelCo(req.getQuery().getEnterpriseId()).getData();
//                coList.addAll(relCoList);
//            }
//        }
        // 用户权限查询参数
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(userId, SysUserTypeEnum.INTERNAL.getType());
        Page<DeliverOpsListRespDTO> page = new Page<>(req.getIndex(), req.getSize());
        IPage<DeliverOpsListRespDTO> orderIPage = deliverMapperService.listDeliverOps(page, req.getQuery(), para);
        List<DeliverOpsListRespDTO> pcDeliverList = orderIPage.getRecords();
        //战区数据填充
        Result<List<SysOrganizationRespDTO>> sysOrganizationResult = sysOrganizationFeign.getOrgListByType(SysOrganizationTypeEnum.SALES.getCode());
        Map<String, String> organizationInfoMap = sysOrganizationResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));

        pcDeliverList.forEach(e -> {
            e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
            e.setBizOrganizationName(organizationInfoMap.get(e.getBizOrganizationCode()));
            e.setCustomerCategoryText(EnterpriseTypeEnum.getDescByCode(e.getCustomerCategory()));
        });
        composeDeliverOpsList(pcDeliverList);
        PageResponse<DeliverOpsListRespDTO> result = PageResponse.toResult(
                req.getIndex(),
                req.getSize(),
                (int) orderIPage.getTotal(),
                pcDeliverList);
        return Result.ok((result));
    }

    public Result<List<DeliverDetailResDTO>> exportList(DeliverOpsListReqDTO req) {
        String userId = req.getUserId();
        // 用户权限查询参数
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(userId, SysUserTypeEnum.INTERNAL.getType());
        List<DeliverExportResDTO> respDTOS = deliverMapper.opsExportDeliverList(req, para);
        deliverOpsExportList(respDTOS);

        List<DeliverDetailResDTO> result = BeanUtil.copyToList(respDTOS, DeliverDetailResDTO.class, CopyOptions.create().setFieldValueEditor((fieldName, fieldValue) -> {
            if (ContractConstants.CONTRACT_REBATE.equals(fieldName)) {
                if (Boolean.parseBoolean((String) fieldValue)) {
                    return "是";
                } else {
                    return "否";
                }

            } else {
                return fieldValue;
            }
        }));

        return Result.ok(result);

    }

    private void deliverOpsExportList(List<DeliverExportResDTO> deliverOpsListRespDTOS) {
        if (deliverOpsListRespDTOS.isEmpty()) {
            return;
        }
        // 填充联系人信息
        List<String> userIds = deliverOpsListRespDTOS.stream()
                .map(DeliverExportResDTO::getContactId).toList();
        Map<String, SysEnterpriseUserRespDTO> contactsMap = sysUserFeign.userListByUserIds(userIds).getData()
                .stream()
                .collect(Collectors.toMap(SysEnterpriseUserRespDTO::getUserId, Function.identity()));

        //战区数据填充
        Result<List<SysOrganizationRespDTO>> sysOrganizationResult = sysOrganizationFeign.getOrgListByType(SysOrganizationTypeEnum.SALES.getCode());
        Map<String, String> organizationInfoMap = sysOrganizationResult.getData().stream().collect(Collectors.toMap(SysOrganizationRespDTO::getOrganizationCode, SysOrganizationRespDTO::getOrganizationName));
        deliverOpsListRespDTOS.forEach(e -> {
            e.setContactName(contactsMap.containsKey(e.getContactId()) ? contactsMap.get(e.getContactId()).getUserName() : null);
            e.setSignEntity(StringUtils.isEmpty(e.getCapitalEnterpriseName()) ? e.getEnterpriseName() : e.getCapitalEnterpriseName());
            e.setBizOrganizationName(organizationInfoMap.get(e.getBizOrganizationCode()));
        });
        //  所有的地址id
        List<String> addressIdList = deliverOpsListRespDTOS.stream().map(DeliverExportResDTO::getAddressId)
                .toList();
        List<EnterpriseAddressRespDTO> addressList = enterpriseFeign.getAddressListByAddressIdList(addressIdList).getData();

        // 填充地址信息
        for (DeliverExportResDTO resp : deliverOpsListRespDTOS) {
            EnterpriseAddressRespDTO addResp = deliverBizManager.getAddressVO(resp.getAddressId(), addressList);
            resp.setAddress(deliverBizManager.getFullAddress(addResp));
            resp.setRecipientName(addResp.getRecipientName());
            resp.setRecipientPhone(addResp.getRecipientPhone());
            String desc = DeliverStatus.getDescByCode(resp.getStatus());
            resp.setStatusText(desc);
            resp.setDeliverMethod(DeliverMethod.getDescByCode(resp.getDeliverMethod()));
            resp.setCustomerCategoryText(EnterpriseTypeEnum.getDescByCode(resp.getCustomerCategory()));
            if (StringUtils.isNotBlank(resp.getOpenContract())) {
                String[] openContract = resp.getOpenContract().split(";");
                StringJoiner joiner = new StringJoiner(";");
                for (int i = 0; i < openContract.length; i++) {
                    joiner.add(OpenContractEnum.getDescByCode(openContract[i]));
                }
                resp.setOpenContractText(joiner.toString());
            }
        }

    }

    private void composeDeliverOpsList(List<DeliverOpsListRespDTO> deliverOpsListRespDTOS) {
        if (deliverOpsListRespDTOS.isEmpty()) {
            return;
        }
        // 填充联系人信息
        List<String> userIds = deliverOpsListRespDTOS.stream()
                .map(DeliverOpsListRespDTO::getContactId).toList();

        Map<String, SysEnterpriseUserRespDTO> contactsMap = sysUserFeign.userListByUserIds(userIds).getData()
                .stream()
                .collect(Collectors.toMap(SysEnterpriseUserRespDTO::getUserId, Function.identity()));
        log.info("查询联系人信息： userIds: {} 信息 {}", userIds, contactsMap);
        deliverOpsListRespDTOS.forEach(e -> e.setContactName(contactsMap.containsKey(e.getContactId()) ? contactsMap.get(e.getContactId()).getUserName() : null));

        //  所有的地址id
        List<String> addressIdList = deliverOpsListRespDTOS.stream().map(DeliverOpsListRespDTO::getAddressId)
                .toList();
        List<EnterpriseAddressRespDTO> addressList = enterpriseFeign.getAddressListByAddressIdList(addressIdList).getData();

        // 填充地址信息
        for (DeliverOpsListRespDTO resp : deliverOpsListRespDTOS) {
            EnterpriseAddressRespDTO addResp = deliverBizManager.getAddressVO(resp.getAddressId(), addressList);
            resp.setAddress(deliverBizManager.getFullAddress(addResp));
            resp.setRecipientName(addResp.getRecipientName());
            resp.setRecipientPhone(addResp.getRecipientPhone());
            String desc = DeliverStatus.getDescByCode(resp.getStatus());
            resp.setStatusText(desc);
            resp.setDeliverMethodCode(resp.getDeliverMethod());
            resp.setDeliverMethod(DeliverMethod.getDescByCode(resp.getDeliverMethod()));
        }

    }

    public Result<PageResponse<DeliverDetailOpsRespDTO>> detailOpsDeliver(PageRequest<DeliverOpsDetailReqDTO> req) {

        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, req.getQuery().getDeliverNo()).one();
        if (ObjectUtils.isEmpty(deliver)) {
            return Result.ok();
        }
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(req.getQuery().getUserId(), req.getQuery().getUserType());
        if (!permissionCheck(deliver, req.getQuery(), para)) {
            //权限校验失败
            return Result.ok();
        }

        Page<DeliverDetailOpsRespDTO> page = new Page<>(req.getIndex(), req.getSize());
        String deliverNo = req.getQuery().getDeliverNo();
        IPage<DeliverDetailOpsRespDTO> opsDetail = deliverMapperService.detailOpsDeliver(page, deliverNo);
        List<DeliverDetailOpsRespDTO> opsDetails = opsDetail.getRecords();

        List<String> userIds = opsDetails.stream()
                .map(DeliverDetailOpsRespDTO::getContactId).toList();
        Map<String, SysEnterpriseUserRespDTO> contactsMap = sysUserFeign.userListByUserIds(userIds).getData()
                .stream()
                .collect(Collectors.toMap(SysEnterpriseUserRespDTO::getUserId, Function.identity()));

        //获取 erp 订单的属性
        ErpOrderTypeQueryReqDTO queryReq = ErpOrderTypeQueryReqDTO.builder().setIntercompanyOrderType("");
        List<ErpOrderTypeResDTO> erpOrderTypeList = marketingAccountFeign.getErpOrderType(queryReq).getData();
        HashMap<String, String> erpOrderTypeMap = new HashMap<>();
        for (int i = 0; i < erpOrderTypeList.size(); i++) {
            erpOrderTypeMap.put(erpOrderTypeList.get(i).getErpOrderTypeId(), erpOrderTypeList.get(i).getErpOrderTypeName());
        }

        //  所有的地址id
        List<String> addressIdList = opsDetails.stream().map(DeliverDetailOpsRespDTO::getAddressId)
                .toList();
        List<EnterpriseAddressRespDTO> addressList = enterpriseFeign.getAddressListByAddressIdList(addressIdList).getData();

        opsDetails.forEach(e -> {
            if (ObjectUtils.isNotEmpty(contactsMap.get(e.getContactId()))) {
                e.setContactName(contactsMap.get(e.getContactId()).getUserName());
            }
            e.setErpOrderTypeName(erpOrderTypeMap.get(e.getErpOrderTypeId()));
            e.setAddress(deliverBizManager.getAddressById(e.getAddressId(), addressList));
        });


        PageResponse<DeliverDetailOpsRespDTO> result = PageResponse.toResult(
                req.getIndex(),
                req.getSize(),
                (int) opsDetail.getTotal(),
                opsDetails);
        return Result.ok((result));
    }


    /**
     * 权限校验，有数据权限返回true；无数据权限返回false
     *
     * @return
     */
    private boolean permissionCheck(DeliverPO deliverPO, DeliverOpsDetailReqDTO req, DeliverAuthListUserReqDTO para) {
        // 如果是超级管理员，
        if (userManager.isAdmin(req.getUserId())) {
            return true;
        }

        //非管理员区分内外部用户分别校验
        if (SysUserTypeEnum.INTERNAL.getType().equals(req.getUserType())) {
            if (StringUtils.equals(para.getSysUserTypeEnum(), SysOrganizationTypeEnum.COMMON.getCode())) {
                return para.getCommonOrganizationCodes().contains(deliverPO.getBizOrganizationCode());
            } else if (StringUtils.equals(SysOrganizationTypeEnum.SALES.getCode(), (para.getSysUserTypeEnum()))) {
                List<String> saleidList = para.getSalesUserList().stream().map(DeliverAuthListSalesUserDTO::getUserId).toList();
                return saleidList.contains(deliverPO.getSalesInternalUserId());
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(para.getSysUserTypeEnum())) {
                return para.getOperationUserIdList().contains(deliverPO.getOperationInternalUserId());
            } else {
                return false;
            }
        } else {
            List<SysSubDealerRespDTO> subDealerList = deliverAuthManager.getDealerSubordinate(req.getUserId());
            long count = subDealerList.stream().filter(a -> a.getDealerUserId().equals(deliverPO.getExternalUserId()) &&
                    a.getEnterpriseId().equals(deliverPO.getEnterpriseId())).count();
            return count > 0;
        }
    }


    public Result<List<DeliverInfoOpsRespDTO>> deliverDetailByNo(DeliverOpsDetailReqDTO req) {


        DeliverPO deliver = deliverMapperService.lambdaQuery().eq(DeliverPO::getDeliverNo, req.getDeliverNo()).one();
        if (ObjectUtils.isEmpty(deliver)) {
            return Result.ok();
        }
        DeliverAuthListUserReqDTO para = deliverAuthManager.getQueryAuthPara(req.getUserId(), req.getUserType());
        if (!permissionCheck(deliver, req, para)) {
            //权限校验失败
            return Result.ok();
        }

        String deliverNo = req.getDeliverNo();

        List<DeliverContractPO> drContractList = deliverContractMapperService.lambdaQuery().eq(DeliverContractPO::getDeliverNo, deliverNo).list();
        if (ObjectUtils.isEmpty(drContractList)) {
            return Result.ok();
        }

        List<String> drContractIds = drContractList.stream().map(DeliverContractPO::getContractId).toList();

        Result<List<CoBusinessResDTO>> busContractResult=contractFeign.qryContractBusiness(drContractIds);
        Assert.isTrue(busContractResult.getSuccess(),"合同信息请求出错！");
        List<String> intentOrderNoList=busContractResult.getData().stream().map(CoBusinessResDTO::getIntentOrderNo).toList();
        Result<List<IntentOrderPCQueryResDTO>> intentOrderList=intentOrderFeign.findIntentOrderByOrderNo(intentOrderNoList);
        Assert.isTrue(busContractResult.getSuccess(),"订单信息请求出错！");
        List<IntentOrderPCQueryResDTO> intentOrderListData = intentOrderList.getData();
        Map<String, Integer> orderNoTypeMap = intentOrderListData.stream()
                .collect(Collectors.toMap(IntentOrderPCQueryResDTO::getIntentOrderNo, IntentOrderPCQueryResDTO::getOrderType));

        Map<String, Integer> contractTypeMap = busContractResult.getData().stream()
                .collect(Collectors.toMap(
                        CoBusinessResDTO::getContractId,
                        item -> orderNoTypeMap.getOrDefault(item.getIntentOrderNo(), 0)
                ));
        //drItem 数据
        List<DeliverContractItemPO> drItems = deliverContractItemMapperService.lambdaQuery().eq(DeliverContractItemPO::getDeliverNo, deliverNo).list();
        Map<String, List<DeliverContractItemPO>> drItemMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(drItems)) {
            drItemMap = drItems.stream().collect(Collectors.groupingBy(DeliverContractItemPO::getDeliverContractId));
        }

        //获取合同信息
        List<String> contractItemIds = drItems.stream().map(DeliverContractItemPO::getContractBusinessItemId).toList();
        ContractQueryReqItemIdListDTO reqDTO = new ContractQueryReqItemIdListDTO();
        reqDTO.setContractBusinessItemIds(contractItemIds);
        final List<ContractDeliveryItemResDTO> contractDrItems = contractFeign.deliveryListItemByContractItemIds(reqDTO).getData();
//        if (ObjectUtils.isEmpty(contractDrItems)) {
//            contractDrItems = new ArrayList<>();
//        }
        Map<String, ContractDeliveryItemResDTO> contractItemMap = contractDrItems.stream().collect(Collectors.toMap(ContractDeliveryItemResDTO::getContractBusinessItemId, Function.identity()));
        List<DeliverInfoOpsRespDTO> result = new ArrayList<>();
        Map<String, List<DeliverContractItemPO>> finalDrItemMap = drItemMap;

        //获取 erp 订单的属性
        ErpOrderTypeQueryReqDTO queryReq = ErpOrderTypeQueryReqDTO.builder().setIntercompanyOrderType("");
        List<ErpOrderTypeResDTO> erpOrderTypeList = marketingAccountFeign.getErpOrderType(queryReq).getData();

        HashMap<String, String> erpOrderTypeMap = new HashMap<>();
        for (int i = 0; i < erpOrderTypeList.size(); i++) {
            erpOrderTypeMap.put(erpOrderTypeList.get(i).getErpOrderTypeId(), erpOrderTypeList.get(i).getErpOrderTypeName());
        }

        //获取产品缩略图
        List<String> productList = drItems.stream()
                .map(DeliverContractItemPO::getSfProductName).distinct().toList();
        Result<List<ReleaseProductImageDTO>> imageResult = productFeign.getReleaseProductImageDTO(productList);
        Map<String, List<ReleaseProductImageDTO>> imageMap = imageResult.getData().stream()
                .collect(Collectors.groupingBy(ReleaseProductImageDTO::getProductName));
        List<DeliverDetailCheckDTO> items = deliverContractItemMapperService.selSubmitItems(req.getDeliverNo());


        HashMap<String, Integer> itemMap = new HashMap<>();

        items.forEach(a -> {
            Integer count = itemMap.get(a.getContractBusinessItemId());
            if (ObjectUtils.isEmpty(count)) {
                itemMap.put(a.getContractBusinessItemId(), a.getQuantityP());
            } else {
                itemMap.put(a.getContractBusinessItemId(), a.getQuantityP() + count);
            }
        });

        //获取特批文件数据
        List<DeliverFileRelationPO> fileRelations = deliverFileRelationMapperService.lambdaQuery().eq(DeliverFileRelationPO::getDeliverNo, deliverNo).list();

        Map<String, List<DeliverFileRelationPO>> fileRelMap = fileRelations.stream().collect(Collectors.groupingBy(DeliverFileRelationPO::getSfContractNo));
        if (ObjectUtils.isEmpty(fileRelMap)) {
            fileRelMap = new HashMap<>();
        }

        Map<String, List<DeliverFileRelationPO>> finalFileRelMap = fileRelMap;
//        Map<String, ContractDetailResAppDTO> contractRemoteInfoMap=new HashMap<>();
//        for(String contractId:contractIds){
//            Result<ContractDetailResAppDTO> contractDetailResAppDTOResult=contractFeign.contractDetailResApp(contractId);
//            contractRemoteInfoMap.put(contractId,contractDetailResAppDTOResult.getData());
//        }

        drContractList.forEach(a -> {
            List<DeliverItem> deliverItems = BeanUtil.copyToList(finalDrItemMap.get(a.getDeliverContractId()), DeliverItem.class);
            Map<String,List<DeliverItem>> deliverItemByContractBusinessItemIdMap = deliverItems.stream().collect(Collectors.groupingBy(DeliverItem::getContractBusinessItemId));
            Map<String,BigDecimal> deliverItemQuantityByContractBusinessItemIdMap = deliverItemByContractBusinessItemIdMap.entrySet().stream().collect(Collectors.toMap(entry->entry.getKey(), entry -> entry.getValue().stream().map(DeliverItem::getQuantityP).reduce(BigDecimal.ZERO,BigDecimal::add)));
            if (ObjectUtils.isNotEmpty(deliverItems)) {
                deliverItems.forEach(b -> {
                    ContractDeliveryItemResDTO contractInfo = contractItemMap.get(b.getContractBusinessItemId());
                    int drRemainingQuantity = 0;
                    if (ObjectUtils.isNotEmpty(contractInfo)) {
                        b.setMultiPowerBegin(contractInfo.getMultiPowerBegin());
                        b.setMultiPowerEnd(contractInfo.getMultiPowerEnd());
                        b.setContractProductQuantity(contractInfo.getQuantityP());
                        b.setContractProductQuantityMw(contractInfo.getQuantityMw());
                        drRemainingQuantity = ObjectUtils.isEmpty(contractInfo.getRemainingQuantityP()) ? 0 : contractInfo.getRemainingQuantityP();
                    }
                    b.setErpOrderTypeName(erpOrderTypeMap.get(b.getErpOrderTypeId()));
                    b.setProductIconUrl(imageMap.get(b.getSfProductName()).get(0).getImageUrl());
                    int currentQuantity = ObjectUtils.isEmpty(itemMap.get(b.getContractBusinessItemId())) ? 0 : itemMap.get(b.getContractBusinessItemId());

                    int quantitySyncSf = 1;

                    if (ObjectUtils.isEmpty(a.getQuantitySyncSf())) {
                        quantitySyncSf = 2;
                    } else {
                        quantitySyncSf = a.getQuantitySyncSf();
                    }

                    if (DeliverStatus.OPERATION_CONFIRMED.getCode().equals(deliver.getDeliverStatus())
                            && quantitySyncSf == 1) {
                        b.setToBeExecute(drRemainingQuantity - currentQuantity);
                    } else {
                        //此处调整为
//                        b.setToBeExecute(drRemainingQuantity - currentQuantity + b.getQuantityP().intValue());
                        b.setToBeExecute(drRemainingQuantity - currentQuantity + deliverItemQuantityByContractBusinessItemIdMap.get(b.getContractBusinessItemId()).intValue());
                    }
                    log.info("运营查看发货申请详情 drRemainingQuantity：{}, currentQuantity:{} , quantityP:{}", drRemainingQuantity, currentQuantity, b.getQuantityP().intValue());
                    BigDecimal amount = new BigDecimal(0);
                    boolean isZero = ObjectUtils.isEmpty(b.getQuantityP()) || ObjectUtils.isEmpty(b.getPower()) || ObjectUtils.isEmpty(b.getUnitPriceW());
                    if (!isZero) {
                        amount = b.getQuantityP().multiply(b.getPower()).multiply(b.getUnitPriceW());
                    }

                    b.setAmount(amount);

                });
                //设置可选的功率
                fillMultiPowerList(deliverItems);
            }

            String openContractText = "";
            if (StringUtils.isNotBlank(a.getOpenContract())) {
                String[] openContract = a.getOpenContract().split(";");
                StringBuilder openContractDesc = new StringBuilder();
                for (int i = 0; i < openContract.length; i++) {
                    openContractDesc.append(OpenContractEnum.getDescByCode(openContract[i])).append(";");
                }
                openContractText = openContractDesc.substring(0, openContractDesc.length() - 1).toString();

            }
            String distributeAmountSfStatus=getDistributeAmountSfStatus(a);
            List<DeliverFileRelationPO> fileRel = finalFileRelMap.get(a.getSfContractNo());
            List<DeliverApprovalFile> approvalFiles = new ArrayList<>();
            if (ObjectUtils.isNotEmpty(fileRel)) {
                approvalFiles = BeanUtil.copyToList(fileRel, DeliverApprovalFile.class);
            }
            DeliverInfoOpsRespDTO deliverInfoOpsRespDTO = DeliverInfoOpsRespDTO
                    .builder()
                    .setDeliverContractId(a.getDeliverContractId())
                    .setContractId(a.getContractId())
                    .setRebate(a.getRebate())
                    .setSfContractNo(a.getSfContractNo())
                    .setApprovalCreatedTime(ObjectUtils.isEmpty(a.getApprovalCreatedTime()) ? null : a.getApprovalCreatedTime().toLocalDate())
                    .setApprovalType(a.getApprovalType())
                    .setApprover(a.getApprover())
                    .setNextApprover(a.getNextApprover())
                    .setApprovalTypeText(ApprovalTypeEnum.getDescByCode(a.getApprovalType()))
                    .setBpmTaskId(a.getBpmTaskId())
                    .setBpmApprovalOpinions(a.getBpmApprovalOpinions())
                    .setBpmStatus(a.getBpmStatus())
                    .setBpmStatusText(BpmStatusEnum.getDescByCode(a.getBpmStatus()))
                    .setSyncStatusSf(a.getSyncStatusSf())
                    .setSyncStatusSfText(SyncStatusSfEnum.getDescByCode(a.getSyncStatusSf()))
                    .setSyncDescriptionSf(a.getSyncDescriptionSf())
                    .setIsOpenContract(a.getIsOpenContract())
                    .setSignEntity(StringUtils.isEmpty(deliver.getCapitalEnterpriseName()) ? deliver.getEnterpriseName() : deliver.getCapitalEnterpriseName())
                    .setDeliverApprovalFiles(approvalFiles)
                    .setOpenContract(a.getOpenContract())
                    .setOpenContractText(openContractText)
                    .setSalesInternalUserName(deliver.getSalesInternalUserName())
                    .setSalesInternalUserId(deliver.getSalesInternalUserId())
                    .setDistributeAmountSfStatus(distributeAmountSfStatus)
                    .setDistributeAmountSfStatusText(SyncStatusSfEnum.getDescByCode(distributeAmountSfStatus))
                    .setDeliverItemList(deliverItems)
                    .setOrderType(contractTypeMap.get(a.getContractId()))
                    .setOperationInternalUserId(deliver.getOperationInternalUserId())
                    .setOperationInternalUserName(deliver.getOperationInternalUserName());
            result.add(deliverInfoOpsRespDTO);
        });

        return Result.ok((result));
    }

    private void fillMultiPowerList(List<DeliverItem> contractDrItems) {
        ProductModulePowerQueryReqDTO productModulePowerQueryReqDTO=new ProductModulePowerQueryReqDTO();
        productModulePowerQueryReqDTO.setProductIds(contractDrItems.stream().map(DeliverItem::getPbiProductId).collect(Collectors.toSet()));
        Result<List<ProductModulePowerDTO>> powerResult=productFeign.queryProductModulePowerList(productModulePowerQueryReqDTO);
        Assert.isTrue(powerResult.getSuccess(),"查询产品功率范围出错");

        List<ProductModulePowerDTO> productModulePowerDTOS=powerResult.getData();
        Map<String, List<ProductModulePowerDTO>> productModulePowerDTOMap=productModulePowerDTOS.stream().collect(Collectors.groupingBy(ProductModulePowerDTO::getProductId));

        contractDrItems.forEach(item->{
            item.setMultiPowerList(calcMultiPowerList(item, productModulePowerDTOMap.get(item.getPbiProductId())));
        });
    }

    private List<Integer> calcMultiPowerList(DeliverItem item, List<ProductModulePowerDTO> productModulePowerDTOS) {
        if(StringUtils.isNotBlank(item.getMultiPowerBegin())&& StringUtils.isNotBlank(item.getMultiPowerEnd())){
            Integer begin=Integer.parseInt(item.getMultiPowerBegin());
            Integer end=Integer.parseInt(item.getMultiPowerEnd());

            return productModulePowerDTOS.stream().map(ProductModulePowerDTO::getOutputPower).map(Integer::parseInt).
                    filter(power->power<=end&&power>=begin).toList();
        }else{
            if(item.getPower()!=null){
                return Arrays.asList(item.getPower().intValue());
            }else{
                return Collections.emptyList();
            }
        }
    }

    private String getDistributeAmountSfStatus(DeliverContractPO deliverContractPO) {
        List<DistributeAccountBillPO> distributeAccountBillPOList=distributeAccountBillService.list(new QueryWrapper<DistributeAccountBillPO>().lambda().eq(DistributeAccountBillPO::getDeliverContractId,deliverContractPO.getDeliverContractId()));
        if(ObjectUtils.isEmpty(distributeAccountBillPOList)){
            return "";
        }else{
            Optional optional= distributeAccountBillPOList.stream().filter(item->item.getSfSyncStatus().equals(SyncStatusSfEnum.FAIL.getCode())).findAny();
            if(optional.isPresent()){
                return SyncStatusSfEnum.FAIL.getCode();
            }else{
                return SyncStatusSfEnum.SUCCESS.getCode();
            }
        }
    }
}
