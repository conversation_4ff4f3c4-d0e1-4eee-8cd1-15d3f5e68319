package com.trinasolar.trinax.delivery.service.biz.ops;

import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.delivery.dto.input.delivery.cancel.CancelDeliverAfterConfirmReqDTO;
import com.trinasolar.trinax.delivery.dto.input.delivery.reverse.ReverseDeliverReqDTO;
import com.trinasolar.trinax.delivery.manager.DeliveryTodoListManger;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverContractItemMapperService;
import com.trinasolar.trinax.delivery.repository.atomicservice.DeliverMapperService;
import com.trinasolar.trinax.delivery.service.biz.lock.business.DeliverBusinessLock;
import com.trinasolar.trinax.delivery.service.manager.DeliverBizManager;
import com.trinasolar.trinax.delivery.service.manager.DeliverManager;
import com.trinasolar.trinax.user.api.SysUserFeign;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class DeliverOpsCancelBiz implements DeliverBusinessLock {
    final DeliverMapperService deliverMapperService;
    final DeliverContractItemMapperService deliverContractItemMapperService;
    final DeliverBizManager deliverBizManager;
    final SysUserFeign sysUserFeign;
    final DeliverManager deliverManager;
    final MqManager mqManager;
    final TodoListFeign todoListFeign;
    final RedissonClient redissonClient;
    final DeliveryTodoListManger deliveryTodoListManger;


    public Result<Object> cancelDeliver(CancelDeliverAfterConfirmReqDTO req) {
        List<String> lockList = new ArrayList<>();
        lockList.add(req.getDeliverNo());
        return this.lockBusiness(JacksonUtil.bean2Json(req), lockList, ReverseDeliverReqDTO.class, redissonClient);
    }

    @Override
    public Result<Object> doBusiness(Object reqBody) {
        CancelDeliverAfterConfirmReqDTO req = (CancelDeliverAfterConfirmReqDTO) reqBody;
        String deliverNo = req.getDeliverNo();
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isEmpty(req.getCancelItems())) {
            return Result.ok(deliverNo);
        }
//        for () {
//
//        }
        return Result.ok(deliverNo);
    }

    @Override
    public Result<Object> lockBusiness(String req, List lockKeyListStr, Class beanType, RedissonClient redissonClient) {
        return DeliverBusinessLock.super.lockBusiness(req, lockKeyListStr, beanType, redissonClient);
    }
}
