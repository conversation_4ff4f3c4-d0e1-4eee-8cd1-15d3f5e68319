package com.trinasolar.trinax.delivery.service.biz.lock.business;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.delivery.constants.LockConstants;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.List;

import static com.trinasolar.trinax.delivery.constants.DeliverResultCode.DELIVER_BUSY_ERROR;

@FunctionalInterface
public interface DeliverBusinessLock<T> {
    Result<Object> doBusiness(T reqBody);

    /**
     * 发货默认根据 前缀："DELIVER_SHARE_LOCK_" + delivery_no
     */
    default Result<Object> lockBusiness(String req, List<String> lockKeyListStr, Class<T> beanType, RedissonClient redissonClient) {
        T reqBody = JacksonUtil.json2Bean(req, beanType);
        RLock[] lockKeyList = new RLock[lockKeyListStr.size()];
        for (int i = 0; i < lockKeyListStr.size(); i++) {
            lockKeyList[i] = redissonClient.getLock(LockConstants.LOCK_PREFIX_DELIVER + lockKeyListStr.get(i));
        }
//        RLock cacheLock = redissonClient.getLock(LockConstants.LOCK_PREFIX_DELIVER + lockKey);
        RLock cacheLock = redissonClient.getMultiLock(lockKeyList);
        Result<Object> result;
        boolean isGetLock = cacheLock.tryLock();
        if (!isGetLock) {
            return Result.fail(DELIVER_BUSY_ERROR.getCode(), DELIVER_BUSY_ERROR.getMessage());
        }

        try {
            result = this.doBusiness(reqBody);
        } catch (Exception e) {
            throw new BizException(ResultCode.FAIL.getCode(), e.getMessage(), e);
        } finally {
            try {
                cacheLock.unlock();
            } catch (Exception e) {
                if (!(e instanceof IllegalMonitorStateException)) {
                    //补偿机制，强制解锁
                    cacheLock.forceUnlock();
                }
            }
        }
        return result;
    }
}
