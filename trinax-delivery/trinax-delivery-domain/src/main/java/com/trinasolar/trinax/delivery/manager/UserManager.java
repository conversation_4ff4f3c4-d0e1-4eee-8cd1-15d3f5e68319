package com.trinasolar.trinax.delivery.manager;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;


@Service
@Slf4j
public class UserManager {

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    RedisUtil redisUtil;

    //用户数据缓存
    private static final String USER_INFO_CACHE = "USER:INFO:CACHE:PREFIX:USERID:";
    /**
     * 用户是否为管理员角色（admin）
     * @return
     */
    public boolean isAdmin(String userId) {
        String redisKey = USER_INFO_CACHE + userId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            SysUserRespDTO respDTO = JacksonUtil.json2Bean(redisVal, SysUserRespDTO.class);
            return !CollectionUtils.isEmpty(respDTO.getRoleIds()) && respDTO.getRoleIds().contains(UserConstant.ADMIN_ROLE_ID);
        }else{
            Result<SysUserRespDTO> userResult = sysUserFeign.getUserByUserId(userId);
            if(Boolean.TRUE.equals(!userResult.getSuccess()) || ObjectUtils.isEmpty(userResult.getData())){
                throw new BizException(ResultCode.FAIL.getCode(), "获取当前用户信息失败");
            }else{
                redisUtil.set(redisKey, JacksonUtil.bean2Json(userResult.getData()), UserConstant.DATA_PERMISSION_CACHE_TIME);
                Collection<String> roleIds = userResult.getData().getRoleIds();
                return !CollectionUtils.isEmpty(roleIds) && roleIds.contains(UserConstant.ADMIN_ROLE_ID);
            }
        }
    }
}
