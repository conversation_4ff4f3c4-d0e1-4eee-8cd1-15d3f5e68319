package com.trinasolar.trinax.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 *  线程池配置类，根据k8s机器配置自动设置IO密集型任务的线程池大小
 */
@Configuration
public class CompletableFutureConfig {

    private static final int CPU_CORES = Runtime.getRuntime().availableProcessors();
    private static final int IO_SCALE = 4;
    private static final int POOL_SIZE = CPU_CORES * IO_SCALE;
    private static final int QUEUE_CAPACITY = 1000;
    private static final String THREAD_NAME_PREFIX = "io-task-";

    private ThreadPoolTaskExecutor ioTaskExecutor;

    @Bean("ioTaskExecutor")
    public Executor ioTaskExecutor() {
        ioTaskExecutor = new ThreadPoolTaskExecutor();
        ioTaskExecutor.setCorePoolSize(POOL_SIZE);
        ioTaskExecutor.setMaxPoolSize(POOL_SIZE);
        ioTaskExecutor.setQueueCapacity(QUEUE_CAPACITY);
        ioTaskExecutor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        ioTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        ioTaskExecutor.setAwaitTerminationSeconds(60);
        ioTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        ioTaskExecutor.initialize();
        return ioTaskExecutor;
    }

    /**
     * 应用关闭时关闭线程池
     */
    @PreDestroy
    public void destroy() {
        if (ioTaskExecutor != null) {
            ioTaskExecutor.shutdown();
        }
    }
}