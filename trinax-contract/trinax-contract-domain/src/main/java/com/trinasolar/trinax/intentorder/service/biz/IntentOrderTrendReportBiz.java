package com.trinasolar.trinax.intentorder.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderReportTrendReqDTO;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderReportTrendResDTO;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class IntentOrderTrendReportBiz {

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;


    /**
     * 意向单趋势统计报表
     *
     * @param reqDTO
     * @return
     */
    public List<IntentOrderReportTrendResDTO> trendList(IntentOrderReportTrendReqDTO reqDTO) {
        log.info("意向单趋势报表trendList，入参：{}", JacksonUtil.bean2Json(reqDTO));
        //处理请求参数
        initParam(reqDTO);

        //满足条件总的意向单记录
        List<IntentOrderPO> totalList = queryData(reqDTO,reqDTO.getStatus());
        if(CollUtil.isEmpty(totalList)){
            return Collections.emptyList();
        }
        log.info("意向单趋势报表totalList数据量：{}", totalList.size());
        //满足条件的确认意向单记录(此时是已确认的意向单)
        List<IntentOrderPO> confirmedList = queryData(reqDTO,RequireOrderStatusEnum.CONFIRMED.getCode());
        log.info("意向单趋势报表confirmedList数据量：{}", confirmedList.size());
        //返回
        List<IntentOrderReportTrendResDTO> resultList = new ArrayList<>();
        //直接查询年份时，处理逻辑会不同,需要特殊处理头尾
        if (ObjectUtil.isNotNull(reqDTO.getYear()) && reqDTO.getYear() != 0) {
            //传年份条件时处理
            yearConditionDeal(totalList,confirmedList,resultList,reqDTO);
        } else {
            //传日期时，日期都是一周为单位，天数一共是7的整数倍；注意需要+1
            int dayRange = reqDTO.getEndTime().getDayOfYear() - reqDTO.getStartTime().getDayOfYear() + 1;
            log.info("意向单趋势报表:传整周日期时,dayRange:{}", dayRange);
            dataRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime(), dayRange);
        }
        List<IntentOrderReportTrendResDTO> filterResult = dataFilter(resultList);

        //再对resultList进行时间排序处理
        return filterResult;
    }

    private List<IntentOrderReportTrendResDTO> dataFilter(List<IntentOrderReportTrendResDTO> resultList) {
        List<IntentOrderReportTrendResDTO> filterResult = new ArrayList<>();
        List<IntentOrderReportTrendResDTO> resultDeal = resultList.stream().
                sorted(Comparator.comparing(IntentOrderReportTrendResDTO::getDate))
                .collect(Collectors.toList());
        //是否第一个有值的节点
        boolean isFirstValue = false;
        for (int i = 0; i < resultDeal.size(); i++) {
            if (isFirstValue) {
                filterResult.add(resultDeal.get(i));
            } else {
                IntentOrderReportTrendResDTO intent = resultDeal.get(i);
                if (intent.getTotalAmount() == 0
                        && intent.getTotalIncreaseAmount() == 0
                        && intent.getConfirmedAmount() == 0
                        && intent.getConfirmedIncreaseAmount() == 0
                        && intent.getTotalPower().compareTo(BigDecimal.ZERO) == 0
                        && intent.getTotalIncreasePower().compareTo(BigDecimal.ZERO) == 0
                        && intent.getConfirmedIncreasePower().compareTo(BigDecimal.ZERO) == 0
                        && intent.getConfirmedPower().compareTo(BigDecimal.ZERO) == 0) {
                } else {
                    isFirstValue = true;
                    filterResult.add(resultDeal.get(i));
                }
            }
        }
        return filterResult;
    }

    /**
     * 处理查询条件是年时的数据
     */
    private void yearConditionDeal(List<IntentOrderPO> totalList,List<IntentOrderPO> confirmedList
            ,List<IntentOrderReportTrendResDTO> resultList,IntentOrderReportTrendReqDTO reqDTO){
        //所选年的天数
        int yearDays;
        LocalDateTime now = LocalDateTime.now();
        if(now.getYear() == reqDTO.getYear()){
            reqDTO.setEndTime(LocalDateTime.of(now.getYear(),now.getMonth(),now.getDayOfMonth(),23,59,59));
            yearDays = now.getDayOfYear();
        }else{
            yearDays = reqDTO.getEndTime().getDayOfYear();
        }
        // 一年的第一天 & 最后一天，分别是一个周的第几天
        LocalDate startLocalDate = reqDTO.getStartTime().toLocalDate();
        LocalDate endLocalDate = reqDTO.getEndTime().toLocalDate();
        int firstWeekDay = startLocalDate.getDayOfWeek().getValue();
        int endWeekDay = endLocalDate.getDayOfWeek().getValue();
        //需要处理以下三种情况：
        //1、如果第一天是周一，那么最后一天一定不是周末（一年365天或366天），尾部需要特殊处理
        //2、如果第一天不是周一，最后一天不是周末，头部尾部都需要特殊处理
        //3、如果第一天不是周一，最后一天是周末，则头部需要特殊处理
        log.info("意向单趋势报表:yearDays:{},firstWeekDay:{},endWeekDay:{}", yearDays,firstWeekDay,endWeekDay);
        if (firstWeekDay == 1) {
            //整周的天数
            int dayRange = yearDays - endWeekDay;
            log.info("意向单趋势报表:firstWeekDay == 1时,dayRange:{}", dayRange);
            dataRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime(), dayRange);
            dataUnRegularDeal(totalList, confirmedList, resultList, reqDTO.getEndTime().minusDays(endWeekDay), reqDTO.getEndTime());
        } else {
            int dayRange;
            if (endWeekDay == 7) {
                dayRange = yearDays - firstWeekDay + 1;
                log.info("意向单趋势报表:endWeekDay == 7时,dayRange:{}", dayRange);
                long fd = (long)firstWeekDay - 1;
                long sd = (long)8 - firstWeekDay;
                dataUnRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime().minusDays(fd), reqDTO.getStartTime().plusDays(sd));
                dataRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime().plusDays(sd), dayRange);
            } else {
                dayRange = yearDays - firstWeekDay - endWeekDay;
                log.info("意向单趋势报表:firstWeekDay = 1 & endWeekDay != 7,时,dayRange:{}", dayRange);
                dataRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime(), dayRange);
                dataUnRegularDeal(totalList, confirmedList, resultList, reqDTO.getEndTime().minusDays(endWeekDay), reqDTO.getEndTime());
                dataUnRegularDeal(totalList, confirmedList, resultList, reqDTO.getStartTime(), reqDTO.getStartTime().plusDays(firstWeekDay));
            }
        }
    }
    /**
     * 查询数据
     * @param reqDTO
     * @param status
     */
    private List<IntentOrderPO> queryData(IntentOrderReportTrendReqDTO reqDTO,String status){
        //满足条件总的意向单记录
        return intentOrderMapperService.lambdaQuery()
                .eq(StringUtils.isNotBlank(reqDTO.getBizOrganizationCode()), IntentOrderPO::getBizOrganizationCode, reqDTO.getBizOrganizationCode())
                .eq(StringUtils.isNotBlank(status), IntentOrderPO::getStatus, status)
                .eq(StringUtils.isNotBlank(reqDTO.getCustomerCategory()), IntentOrderPO::getCustomerCategory, reqDTO.getCustomerCategory())
                .ge(ObjectUtil.isNotNull(reqDTO.getStartTime()), IntentOrderPO::getCreatedTime, reqDTO.getSearchStartTime())
                .le(ObjectUtil.isNotNull(reqDTO.getEndTime()), IntentOrderPO::getCreatedTime, reqDTO.getEndTime())
                .list();
    }

    /**
     *处理请求参数，传年份时，处理查询时间范围为：当年的1月1日-当年12月31日
     * @param reqDTO
     */
    private void initParam(IntentOrderReportTrendReqDTO reqDTO){

        int currentYear = LocalDateTime.now().getYear();
        if (ObjectUtil.isNotEmpty(reqDTO.getYear())) {
            if (currentYear < reqDTO.getYear()) {
                throw new BizException(ResultCode.FAIL.getCode(), "所选年份大于当前年");
            }
        }
        if (ObjectUtil.isNotNull(reqDTO.getYear()) && reqDTO.getYear() != 0) {
            reqDTO.setStartTime(LocalDateTime.of(reqDTO.getYear(), 1, 1, 0, 0, 0));
            reqDTO.setEndTime(LocalDateTime.of(reqDTO.getYear(), 12, 31, 23, 59, 59));
        }
        LocalDateTime searchStartTime = LocalDateTime.of(reqDTO.getStartTime().getYear(), 1, 1, 0, 0, 0);
        reqDTO.setSearchStartTime(searchStartTime);
    }
    /**
     * 处理头尾的不规则数据
     *
     * @param totalList
     * @param confirmedList
     * @param resultList
     * @param startDateTime
     */
    private void dataUnRegularDeal(List<IntentOrderPO> totalList, List<IntentOrderPO> confirmedList
            , List<IntentOrderReportTrendResDTO> resultList, LocalDateTime startDateTime, LocalDateTime endDateTime) {

        IntentOrderReportTrendResDTO resDTO = new IntentOrderReportTrendResDTO();
        //时间处理
        resDTO.setDateStr(endDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("M月d日")));
        resDTO.setDate(endDateTime.toLocalDate());
        log.info("dataUnRegularDeal:endDateTime：{}", endDateTime);

        //截止时间-总数：意向单记录 & 确认意向单记录
        List<IntentOrderPO> totalConditionList = totalList.stream().filter(e -> e.getCreatedTime().isBefore(endDateTime))
                .collect(Collectors.toList());
        log.info("dataUnRegularDeal:totalConditionList.size：{}", totalConditionList.size());
        List<IntentOrderPO> confirmedConditionList = confirmedList.stream().filter(e -> e.getCreatedTime().isBefore(endDateTime))
                .collect(Collectors.toList());
        log.info("dataUnRegularDeal:confirmedConditionList.size：{}", confirmedConditionList.size());
        //数据不存在时，不记录
        if(CollUtil.isEmpty(totalConditionList)){
            return;
        }
        resDTO.setTotalAmount(totalConditionList.size());
        resDTO.setTotalPower(totalConditionList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        resDTO.setConfirmedAmount(confirmedConditionList.size());
        resDTO.setConfirmedPower(confirmedConditionList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        //间隔时间-新增：意向单记录 & 确认意向单记录
        List<IntentOrderPO> totalConditionIncrList = totalList.stream()
                .filter(e -> e.getCreatedTime().isAfter(startDateTime) && e.getCreatedTime().isBefore(endDateTime))
                .collect(Collectors.toList());
        log.info("dataUnRegularDeal:totalConditionIncrList.size：{}", totalConditionIncrList.size());
        List<IntentOrderPO> confirmedConditionIncrList = confirmedList.stream()
                .filter(e -> e.getCreatedTime().isAfter(startDateTime) && e.getCreatedTime().isBefore(endDateTime))
                .collect(Collectors.toList());
        log.info("dataUnRegularDeal:confirmedConditionIncrList.size：{}", confirmedConditionIncrList.size());
        resDTO.setTotalIncreaseAmount(totalConditionIncrList.size());
        resDTO.setTotalIncreasePower(totalConditionIncrList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        resDTO.setConfirmedIncreaseAmount(confirmedConditionIncrList.size());
        resDTO.setConfirmedIncreasePower(confirmedConditionIncrList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        resultList.add(resDTO);
    }

    /**
     * 处理整周的规则数据
     * @param totalList
     * @param confirmedList
     * @param resultList
     * @param startDateTime
     * @param dayRange
     */
    private void dataRegularDeal(List<IntentOrderPO> totalList, List<IntentOrderPO> confirmedList
            , List<IntentOrderReportTrendResDTO> resultList, LocalDateTime startDateTime, int dayRange) {
        //传日期时，日期都是一周为单位，天数一共是7的整数倍；注意需要+1
        int weeks = dayRange / 7;
        LocalDateTime increaseStartTime = startDateTime;
        LocalDateTime increaseEndTime = LocalDateTime.of(startDateTime.getYear(),startDateTime.getMonth(),startDateTime.getDayOfMonth(),23,59,59);
        increaseEndTime = increaseEndTime.plusDays(6);
        for (int i = 0; i < weeks; i++) {
            IntentOrderReportTrendResDTO resDTO = new IntentOrderReportTrendResDTO();
            //时间处理
            log.info("dataRegularDeal:increaseStartTime：{}", increaseStartTime);
            log.info("dataRegularDeal:increaseEndTime：{}", increaseEndTime);
            resDTO.setDate(increaseEndTime.toLocalDate());
            resDTO.setDateStr(increaseEndTime.toLocalDate().format(DateTimeFormatter.ofPattern("M月d日")));

            //截止时间-总数：意向单记录 & 确认意向单记录
            //截止时间-总数：意向单记录 & 确认意向单记录
            LocalDateTime finalIncreaseEndTime = increaseEndTime;
            List<IntentOrderPO> totalConditionList = totalList.stream().filter(e -> e.getCreatedTime().isBefore(finalIncreaseEndTime))
                    .collect(Collectors.toList());
            log.info("dataRegularDeal:totalConditionList.size：{}", totalConditionList.size());
            List<IntentOrderPO> confirmedConditionList = confirmedList.stream().filter(e -> e.getCreatedTime().isBefore(finalIncreaseEndTime))
                    .collect(Collectors.toList());
            log.info("dataRegularDeal:confirmedConditionList.size：{}", confirmedConditionList.size());

            resDTO.setTotalAmount(totalConditionList.size());
            resDTO.setTotalPower(totalConditionList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            resDTO.setConfirmedAmount(confirmedConditionList.size());
            resDTO.setConfirmedPower(confirmedConditionList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            //间隔时间-新增：意向单记录 & 确认意向单记录
            LocalDateTime finalIncreaseDateTime = increaseStartTime;
            List<IntentOrderPO> totalConditionIncrList = totalList.stream()
                    .filter(e -> e.getCreatedTime().isAfter(finalIncreaseDateTime) && e.getCreatedTime().isBefore(finalIncreaseEndTime))
                    .collect(Collectors.toList());
            log.info("dataRegularDeal:totalConditionIncrList.size：{}", totalConditionIncrList.size());
            List<IntentOrderPO> confirmedConditionIncrList = confirmedList.stream()
                    .filter(e -> e.getCreatedTime().isAfter(finalIncreaseDateTime) && e.getCreatedTime().isBefore(finalIncreaseEndTime))
                    .collect(Collectors.toList());
            log.info("dataRegularDeal:confirmedConditionIncrList.size：{}", confirmedConditionIncrList.size());
            resDTO.setTotalIncreaseAmount(totalConditionIncrList.size());
            resDTO.setTotalIncreasePower(totalConditionIncrList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            resDTO.setConfirmedIncreaseAmount(confirmedConditionIncrList.size());
            resDTO.setConfirmedIncreasePower(confirmedConditionIncrList.stream().map(IntentOrderPO::getPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            //开始时间也需要增加
            increaseStartTime = increaseStartTime.plusDays(7);
            increaseEndTime = increaseEndTime.plusDays(7);

            resultList.add(resDTO);
        }
    }
}
