package com.trinasolar.trinax.intentorder.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessMapperService;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessPO;
import com.trinasolar.trinax.intentorder.constants.IntentOrderConstant;
import com.trinasolar.trinax.intentorder.constants.IntentOrderResultCode;
import com.trinasolar.trinax.intentorder.constants.enums.*;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderItemSaveReqDTO;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderSaveReqDTO;
import com.trinasolar.trinax.intentorder.dto.output.ProductPowerResDTO;
import com.trinasolar.trinax.intentorder.dto.output.SubmitAndConfirmResDTO;
import com.trinasolar.trinax.intentorder.manager.MoneyCheckManager;
import com.trinasolar.trinax.intentorder.manager.ProductPowerCheckManager;
import com.trinasolar.trinax.intentorder.manager.caffeine.CacheMessageManager;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderItemMapperService;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.mapper.IntentOrderItemMapper;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.intentorder.utils.IntentOrderNoGenerator;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.partner.dto.output.MarketingTaxRespDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.EnterpriseUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.EnterpriseUserQueryReqDTO;
import com.trinasolar.trinax.user.dto.input.SysSalesOperationRelationReqDTO;
import com.trinasolar.trinax.user.dto.output.SysEnterpriseUserRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;


@Service
@Slf4j
public class IntentOrderEditBiz {

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    private IntentOrderItemMapperService intentOrderItemMapperService;

    @Autowired
    private ProductPowerCheckManager productPowerCheckManager;

    @Autowired
    private EnterpriseFeign partnerEnterpriseFeign;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private IntentOrderNoGenerator intentOrderNoGenerator;

    @Autowired
    private ContractBusinessMapperService contractBusinessMapperService;

    @Autowired
    private IntentOrderItemMapper intentOrderItemMapper;

    @Autowired
    private MoneyCheckManager moneyCheckManager;

    @Autowired
    private CacheMessageManager cacheMessageManager;

    @Autowired
    private ExecutorService executorService;

    /**
     * 编辑保存意向单：只做修改保存操作
     * 1、内部用户修改未提及状态意向单--意向单行信息直接删除重新插入，意向单休息直接计算修改。
     * 2、内部用户修改提交状态意向单（修改由外部用户提交的意向单）--同上
     * 3、内部用户修改已确认状态意向单（修改已确认状态意向单：不会删除行）--新增和修改的意向单行信息需区分处理，意向单信息直接修改。
     * <p>
     * 4、外部用户修改未提交状态意向单
     *
     * @param reqDTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Result<SubmitAndConfirmResDTO> updateIntentOrder(IntentOrderSaveReqDTO reqDTO) {
        log.info("updateIntentOrder() 编辑意向单信息入参，reqDTO：{}", reqDTO);

        IntentOrderPO intentOrderPO = BeanUtil.toBean(reqDTO, IntentOrderPO.class);
        List<IntentOrderItemSaveReqDTO> itemPOList = reqDTO.getItemList();
        List<IntentOrderItemPO> itemList = BeanUtil.copyToList(itemPOList, IntentOrderItemPO.class);
        if (ObjectUtils.isEmpty(intentOrderPO) && CollectionUtils.isEmpty(itemPOList)) {
            return Result.fail("修改意向单时，意向单信息为空");
        }
        //0、校验意向单是否有合同记录，有合同记录不允许编辑（针对同步后再次编辑的情况）
        validContract(reqDTO.getIntentOrderNo());

        //1、校验意向单状态
        log.info("updateIntentOrder() 校验意向单状态");
        validIfCanEdit(reqDTO.getIntentOrderNo());

        //2、当账套信息不为空时，需要调接口通过套账id查询税分类查询设置
        log.info("updateIntentOrder() 通过选择的套账，获取配置的税分类税率信息");
        if (StringUtils.isNotBlank(reqDTO.getRequestedOu())) {
            setTaxInfo(intentOrderPO,reqDTO.getRequestedOu());
        }

        //3、客户自行下单时，内部销售人员编号不为空时，需要重新设置销售用户信息
        // 外部用户创建编辑意向单时，前端选中销售信息
//        if (OrderMethodEnum.ORDER_BY_CUSTOMER.getCode().equals(intentOrderPO.getIsSurrogateOrder())
//                && StringUtils.isNotBlank(intentOrderPO.getEnterpriseId())) {
//            //外部用户客户自行下单时，需要通过外部用户userId，和企业id查询销售用户信息
//            setInternalUser(intentOrderPO);
//        }

        //4、设置bizOrganization and Operation
        if (StringUtils.isNotBlank(intentOrderPO.getEnterpriseId())) {
            setBizOrgAndOperation(intentOrderPO);
        }

        //5、当传赠品信息时，校验赠品的groupId与产品groupId一致性
        log.info("updateIntentOrder() 当传赠品信息时，校验赠品的groupId与产品groupId一致性");
        if (!CollectionUtils.isEmpty(itemList)) {
            validGitProduct(itemList);
        }
        SubmitAndConfirmResDTO resDTO = new SubmitAndConfirmResDTO();
        resDTO.setIntentOrderNo(reqDTO.getIntentOrderNo());
        //6、已确认状态的意向单，只有新的意向单行中没有确认过的，“非备件”产品才需要校验上下架
        if (IntentOrderStatusEnum.CONFIRMED.getCode().equals(reqDTO.getStatus())) {
            List<IntentOrderItemPO> powerCheckList = itemList.stream()
                    .filter(e -> !IntentOrderItemStatusEnum.CONFIRMED.getCode().equals(e.getStatus())
                            && !ModuleTypeEnum.PART.getCode().equals(e.getModuleType()))
                    .toList();
            if (!CollectionUtils.isEmpty(powerCheckList)) {
                //校验意向单产品功率上下架
                List<ProductPowerResDTO> downList = productPowerCheckManager.checkProductPowerInfo(powerCheckList);
                if (!CollectionUtils.isEmpty(downList)) {
                    resDTO.setProductPowerList(downList);
                    return Result.result(IntentOrderResultCode.PRODUCT_POWER_DOWN.getCode(), "存在产品功率已经下架", resDTO);
                }
            }
            editConfirmedRecord(itemList, intentOrderPO);
        } else {
            if (!CollectionUtils.isEmpty(itemList)) {
                log.info("updateIntentOrder() 非CONFIRM状态的意向单，校验上下架信息");
                //校验意向单产品功率上下架，只是校验组件产品
                List<IntentOrderItemPO> powerCheckList = itemList.stream().filter(e -> !ModuleTypeEnum.PART.getCode().equals(e.getModuleType())).toList();
                List<ProductPowerResDTO> downList = productPowerCheckManager.checkProductPowerInfo(powerCheckList);
                if (!CollectionUtils.isEmpty(downList)) {
                    resDTO.setProductPowerList(downList);
                    return Result.result(IntentOrderResultCode.PRODUCT_POWER_DOWN.getCode(), "存在产品功率已经下架", resDTO);
                }
            }
            editOtherRecord(itemList, intentOrderPO);
        }
        executorService.execute(()->cacheMessageManager.clearMessage(intentOrderPO));
        return Result.ok(resDTO);

    }

    /**
     * 设置税分类，税率等信息
     */
    private void setTaxInfo(IntentOrderPO intentOrderPO, String requestedOu){
        Result<String> taxIdResult = partnerEnterpriseFeign.getEnterpriseTaxId(requestedOu);
        if (Boolean.TRUE.equals(!taxIdResult.getSuccess()) || StringUtils.isBlank(taxIdResult.getData())) {
            throw new BizException(ResultCode.FAIL.getCode(), "根据套账信息没有查找到对应的税分类信息");
        } else {
            intentOrderPO.setTaxClassification(taxIdResult.getData());
        }
        //取税率信息（前端需要展示）
        log.info("updateIntentOrder() 通过税分类信息，获取税率信息");
        if (StringUtils.isNotBlank(intentOrderPO.getTaxClassification())) {
            Result<MarketingTaxRespDTO> marketingTaxResult = partnerEnterpriseFeign.getMarketingTaxByTaxId(intentOrderPO.getTaxClassification());
            log.info("marketingTaxResult:{}", marketingTaxResult);
            if (Boolean.TRUE.equals(marketingTaxResult.getSuccess()) && !ObjectUtils.isEmpty(marketingTaxResult.getData())) {
                intentOrderPO.setTaxName(marketingTaxResult.getData().getTaxName());
            }
        }
    }

    /**
     * 存在赠品信息时，校验赠品信息一致性
     *
     * @param itemList
     */
    private void validGitProduct(List<IntentOrderItemPO> itemList) {
        List<String> giftGroupIds = itemList.stream()
                .filter(e -> ItemTypeEnum.COMPLIMENTARY.getCode().equals(e.getItemType())
                        && StringUtils.isNotBlank(e.getGroupId()))
                .map(IntentOrderItemPO::getGroupId).distinct().toList();

        if (!CollectionUtils.isEmpty(giftGroupIds)) {
            List<String> productGroupIds = itemList.stream()
                    .filter(e -> ItemTypeEnum.PRODUCT.getCode().equals(e.getItemType()))
                    .map(IntentOrderItemPO::getGroupId).distinct().toList();
            boolean isEqual = productGroupIds.containsAll(giftGroupIds);
            if (!isEqual) {
                throw new BizException(ResultCode.FAIL.getCode(), "编辑意向单时，赠品信息与产品信息不一致");
            }
        }
    }

    /**
     * 设置服务组织与运营
     *
     * @param intentOrderPO
     */
    private void setBizOrgAndOperation(IntentOrderPO intentOrderPO) {
        String enterpriseId = intentOrderPO.getEnterpriseId();
        Result<List<EnterpriseBizRelationResDTO>> bizRelationResult = partnerEnterpriseFeign.getEnterpriseBizRelationByEnterpriseId(enterpriseId);
        if (Boolean.TRUE.equals(bizRelationResult.getSuccess()) && !CollectionUtils.isEmpty(bizRelationResult.getData())) {
            intentOrderPO.setBizOrganizationCode(bizRelationResult.getData().get(0).getBizOrganizationCode());
        } else {
            throw new BizException(ResultCode.FAIL.getCode(), "通过企业信息查找业务所属组织编码失败");
        }
        if (StringUtils.isNotBlank(intentOrderPO.getSalesInternalUserId()) && StringUtils.isNotBlank(intentOrderPO.getBizOrganizationCode())) {
            SysSalesOperationRelationReqDTO relationReqDTO = new SysSalesOperationRelationReqDTO();
            relationReqDTO.setBizOrganizationCode(intentOrderPO.getBizOrganizationCode());
            relationReqDTO.setSalesUserId(intentOrderPO.getSalesInternalUserId());
            Result<SysSalesOperationRelationRespDTO> relationResult = sysUserFeign.getOperationBySalesUserId(relationReqDTO);
            if (Boolean.TRUE.equals(relationResult.getSuccess()) && !ObjectUtils.isEmpty(relationResult.getData())) {
                intentOrderPO.setOperationInternalUserId(relationResult.getData().getOperationUserId());
                intentOrderPO.setOriginOperationInternalUserId(relationResult.getData().getOperationUserId());
                intentOrderPO.setOperationInternalUserName(relationResult.getData().getOperationUserName());
            } else {
                throw new BizException(ResultCode.FAIL.getCode(), "通过销售和组织编码查找运营人员信息失败");
            }
        }
    }

    /**
     * 设置内部人员信息
     *
     * @param intentOrderPO
     */
    private void setInternalUser(IntentOrderPO intentOrderPO) {
        EnterpriseUserQueryReqDTO userReqDTO = new EnterpriseUserQueryReqDTO();
        userReqDTO.setEnterpriseUserType(EnterpriseUserTypeEnum.SALES.getType());
        userReqDTO.setDealerUserId(intentOrderPO.getCreatedBy());
        //有关联方时，以关联方企业id查询
        if (StringUtils.isNotBlank(intentOrderPO.getCapitalEnterpriseId())) {
            userReqDTO.setEnterpriseId(intentOrderPO.getCapitalEnterpriseId());
        } else {
            userReqDTO.setEnterpriseId(intentOrderPO.getEnterpriseId());
        }
    }

    /**
     * 编辑保存：已确认状态的意向单 再次编辑
     *
     * @param itemList
     * @param intentOrderPO
     */
    private void editConfirmedRecord(List<IntentOrderItemPO> itemList, IntentOrderPO intentOrderPO) {
        log.info("updateIntentOrder() CONFIRM状态的意向单，修改信息start");
        //1、过滤新增的意向单行 & 修改的意向单行
        List<IntentOrderItemPO> newItemList = itemList.stream().filter(e -> !IntentOrderItemStatusEnum.CONFIRMED.getCode().equals(e.getStatus())).toList();
        List<IntentOrderItemPO> updateItemList = itemList.stream().filter(e -> IntentOrderItemStatusEnum.CONFIRMED.getCode().equals(e.getStatus())).toList();

        //2、数据库中没有确认过的意向单行全部物理删除，重新插入新数据
        LambdaUpdateWrapper<IntentOrderItemPO> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(IntentOrderItemPO::getIntentOrderNo, intentOrderPO.getIntentOrderNo());
        deleteWrapper.ne(IntentOrderItemPO::getStatus, IntentOrderItemStatusEnum.CONFIRMED.getCode());
        intentOrderItemMapper.delete(deleteWrapper);

        //3、数据库已存在的确认过的意向单行
        List<IntentOrderItemPO> databaseItemList = intentOrderItemMapperService.lambdaQuery()
                .eq(IntentOrderItemPO::getIntentOrderNo, intentOrderPO.getIntentOrderNo())
                .eq(IntentOrderItemPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode())
                .eq(IntentOrderItemPO::getStatus, IntentOrderItemStatusEnum.CONFIRMED.getCode())
                .list();

        //4、过滤校验 数据库中确认过的意向单行号 & 更新的意向单行号
        List<String> databaseItemNos = databaseItemList.stream().map(IntentOrderItemPO::getIntentOrderItemNo).distinct().toList();
        List<String> updateItemNos = updateItemList.stream().map(IntentOrderItemPO::getIntentOrderItemNo).distinct().toList();
        boolean isEqual = databaseItemNos.containsAll(updateItemNos) && updateItemNos.containsAll(databaseItemNos);
        if (!isEqual) {
            throw new BizException(ResultCode.FAIL.getCode(), "更新的意向单行信息不一致");
        }
        for (IntentOrderItemPO intentOrderItemPO : newItemList) {
            intentOrderItemPO.setInstallation(InstallationTypeEnum.PORTRAIT.getCode());
            intentOrderItemPO.setCreatedTime(LocalDateTime.now());
            intentOrderItemPO.setIsDeleted(DeleteFlagEnum.NOT_DELETE.getCode());
            intentOrderItemPO.setCreatedBy(intentOrderPO.getUpdatedBy());
            intentOrderItemPO.setCreatedName(intentOrderPO.getUpdatedName());
            intentOrderItemPO.setIntentOrderNo(intentOrderPO.getIntentOrderNo());
            intentOrderItemPO.setIntentOrderItemNo(intentOrderNoGenerator.getIntentOrderItemNo());
            intentOrderItemPO.setOpType(OpTypeEnum.CREATE.getCode());
            intentOrderItemPO.setPlugConnector(IntentOrderConstant.INTENT_ORDER_PLUG_CONNECTOR);
        }
        //5、填充修改的意向单行处理
        log.info("databaseItemList 处理前:{}",databaseItemList);
        log.info("updateItemList 处理前:{}",updateItemList);
        fillUpdateItem(databaseItemList, updateItemList);
        log.info("databaseItemList 处理后:{}",databaseItemList);
        log.info("updateItemList 处理前后:{}",updateItemList);
        //6、计算更新意向单信息
        StringJoiner productNameJoiner = new StringJoiner(",");
        BigDecimal salePrice = new BigDecimal(0);
        BigDecimal guidePrice = new BigDecimal(0);
        BigDecimal requirementW = new BigDecimal(0);
        BigDecimal requirementMw = new BigDecimal(0);
        BigDecimal giftRequirementMw = new BigDecimal(0);
        long pcsAmount = 0;
        long giftAmount = 0;
        for (int i = 0; i < itemList.size(); i++) {
            if (StringUtils.isNotBlank(itemList.get(i).getProductName())) {
                productNameJoiner.add(itemList.get(i).getProductName());
            }
            if (!ObjectUtils.isEmpty(itemList.get(i).getSaleTotalPrice())) {
                salePrice = salePrice.add(itemList.get(i).getSaleTotalPrice());
            }
            if (!ObjectUtils.isEmpty(itemList.get(i).getGuideTotalPrice())) {
                guidePrice = guidePrice.add(itemList.get(i).getGuideTotalPrice());
            }
            if (!ObjectUtils.isEmpty(itemList.get(i).getQuantityW())) {
                requirementW = requirementW.add(itemList.get(i).getQuantityW());
            }
            if(!ObjectUtils.isEmpty(itemList.get(i).getQuantityMw())){
                requirementMw = requirementMw.add(itemList.get(i).getQuantityMw());
                if(ItemTypeEnum.COMPLIMENTARY.getCode().equals(itemList.get(i).getItemType())){
                    giftRequirementMw = giftRequirementMw.add(itemList.get(i).getQuantityMw());
                }
            }
            //产品片数与赠品片数分开计算
            if(!ObjectUtils.isEmpty(itemList.get(i).getQuantityP())){
                pcsAmount += itemList.get(i).getQuantityP();
                if(ItemTypeEnum.COMPLIMENTARY.getCode().equals(itemList.get(i).getItemType())){
                    giftAmount += itemList.get(i).getQuantityP();
                }
            }
        }
        //校验销售总金额，参考总金额长度
        moneyCheckManager.verifyMoneySize(salePrice,guidePrice);

        intentOrderPO.setProductName(productNameJoiner.toString());
        intentOrderPO.setPotentialSalesVolume(requirementMw);
        intentOrderPO.setGiftPotentialSalesVolume(giftRequirementMw);
        intentOrderPO.setRequirements(requirementW);
        intentOrderPO.setSaleTotalAmount(salePrice);
        intentOrderPO.setGuideTotalAmount(guidePrice);
        intentOrderPO.setProductPcsCount(new BigDecimal(pcsAmount));
        intentOrderPO.setGiftPcsCount(new BigDecimal(giftAmount));
        intentOrderPO.setUpdatedTime(LocalDateTime.now());
        intentOrderPO.setOpType(OpTypeEnum.UPDATE.getCode());

        LambdaUpdateWrapper<IntentOrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IntentOrderPO::getIntentOrderNo, intentOrderPO.getIntentOrderNo());
        intentOrderMapperService.update(intentOrderPO, updateWrapper);
        intentOrderItemMapperService.saveBatch(newItemList);
        intentOrderItemMapperService.updateBatchById(databaseItemList);
        log.info("updateIntentOrder() CONFIRM状态的意向单，修改信息end");

    }

    /**
     * 编辑保存：未提交，已提交 状态的意向单
     *
     * @param itemList
     * @param intentOrderPO
     */
    private void editOtherRecord(List<IntentOrderItemPO> itemList, IntentOrderPO intentOrderPO) {
        log.info("updateIntentOrder() 非CONFIRM状态的意向单，修改信息start");
        //1、物理删除原有所有意向单行，全部重新插入
        LambdaUpdateWrapper<IntentOrderItemPO> deleteWrapper = new LambdaUpdateWrapper<>();
        deleteWrapper.eq(IntentOrderItemPO::getIntentOrderNo, intentOrderPO.getIntentOrderNo());
        intentOrderItemMapper.delete(deleteWrapper);

        //2、重新计算并设置意向单需要更新的信息
        StringJoiner productNameJoiner = new StringJoiner(",");
        BigDecimal salePrice = new BigDecimal(0);
        BigDecimal guidePrice = new BigDecimal(0);
        BigDecimal requirementW = new BigDecimal(0);
        BigDecimal requirementMw = new BigDecimal(0);
        BigDecimal giftRequirementMw = new BigDecimal(0);
        long pcsAmount = 0;
        long giftAmount = 0;
        if (!CollectionUtils.isEmpty(itemList)) {
            for (int i = 0; i < itemList.size(); i++) {
                itemList.get(i).setInstallation(InstallationTypeEnum.PORTRAIT.getCode());
                itemList.get(i).setCreatedTime(LocalDateTime.now());
                itemList.get(i).setIsDeleted(DeleteFlagEnum.NOT_DELETE.getCode());
                itemList.get(i).setCreatedBy(intentOrderPO.getUpdatedBy());
                itemList.get(i).setCreatedName(intentOrderPO.getUpdatedName());
                itemList.get(i).setIntentOrderNo(intentOrderPO.getIntentOrderNo());
                itemList.get(i).setIntentOrderItemNo(intentOrderNoGenerator.getIntentOrderItemNo());
                itemList.get(i).setOpType(OpTypeEnum.CREATE.getCode());
                itemList.get(i).setPlugConnector(IntentOrderConstant.INTENT_ORDER_PLUG_CONNECTOR);
                if (StringUtils.isNotBlank(itemList.get(i).getProductName())) {
                    productNameJoiner.add(itemList.get(i).getProductName());
                }
                if (!ObjectUtils.isEmpty(itemList.get(i).getSaleTotalPrice())) {
                    salePrice = salePrice.add(itemList.get(i).getSaleTotalPrice());
                }
                if (!ObjectUtils.isEmpty(itemList.get(i).getGuideTotalPrice())) {
                    guidePrice = guidePrice.add(itemList.get(i).getGuideTotalPrice());
                }
                if (!ObjectUtils.isEmpty(itemList.get(i).getQuantityW())) {
                    requirementW = requirementW.add(itemList.get(i).getQuantityW());
                }
                if(!ObjectUtils.isEmpty(itemList.get(i).getQuantityMw())){
                    requirementMw = requirementMw.add(itemList.get(i).getQuantityMw());
                    if(ItemTypeEnum.COMPLIMENTARY.getCode().equals(itemList.get(i).getItemType())){
                        giftRequirementMw = giftRequirementMw.add(itemList.get(i).getQuantityMw());
                    }
                }
                //产品片数与赠品片数分开计算
                if(!ObjectUtils.isEmpty(itemList.get(i).getQuantityP())){
                    pcsAmount += itemList.get(i).getQuantityP();
                    if(ItemTypeEnum.COMPLIMENTARY.getCode().equals(itemList.get(i).getItemType())){
                        giftAmount = itemList.get(i).getQuantityP();
                    }
                }
            }
            intentOrderItemMapperService.saveBatch(itemList);
        }
        //校验销售总金额，参考总金额长度
        moneyCheckManager.verifyMoneySize(salePrice,guidePrice);

        intentOrderPO.setProductName(productNameJoiner.toString());
        intentOrderPO.setPotentialSalesVolume(requirementMw);
        intentOrderPO.setGiftPotentialSalesVolume(giftRequirementMw);
        intentOrderPO.setRequirements(requirementW);
        intentOrderPO.setSaleTotalAmount(salePrice);
        intentOrderPO.setGuideTotalAmount(guidePrice);
        intentOrderPO.setProductPcsCount(new BigDecimal(pcsAmount));
        intentOrderPO.setGiftPcsCount(new BigDecimal(giftAmount));
        intentOrderPO.setUpdatedTime(LocalDateTime.now());
        if(StringUtils.isNotBlank(intentOrderPO.getSalesInternalUserId())){
            intentOrderPO.setOriginSalesInternalUserId(intentOrderPO.getSalesInternalUserId());
        }
        if(StringUtils.isNotBlank(intentOrderPO.getExternalUserId())){
            intentOrderPO.setOriginExternalUserId(intentOrderPO.getExternalUserId());
        }

        LambdaUpdateWrapper<IntentOrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(IntentOrderPO::getIntentOrderNo, intentOrderPO.getIntentOrderNo());
        intentOrderMapperService.update(intentOrderPO, updateWrapper);
        log.info("updateIntentOrder() 非CONFIRM状态的意向单，修改信息end");
    }


    /**
     * 校验意向单是否在至尊宝已经有合同记录，有则不允许删除（合同记录存在，说明有非草稿状态的合同）
     * @param intentOrderNo
     */
    private void validContract(String intentOrderNo) {
        List<ContractBusinessPO> contractBusinessPOS = contractBusinessMapperService.listByIntentOrderNos(Lists.newArrayList(intentOrderNo));
        if (!contractBusinessPOS.isEmpty()) {
            throw new BizException(ResultCode.FAIL.getCode(), "该意向单已经存在非草稿状态的合同，不允许编辑");
        }
    }

    /**
     * 校验意向单能否被编辑修改，不满足条件直接抛出异常
     *
     * @param intentOrderNo
     */
    private void validIfCanEdit(String intentOrderNo) {

        List<IntentOrderPO> existsList = intentOrderMapperService.lambdaQuery()
                .eq(IntentOrderPO::getIntentOrderNo, intentOrderNo)
                .eq(IntentOrderPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode()).list();
        if (CollectionUtils.isEmpty(existsList) || existsList.size() != 1) {
            throw new BizException(ResultCode.FAIL.getCode(), "意向单状态异常，操作失败");
        }
        if (IntentOrderStatusEnum.CANCELED.getCode().equals(existsList.get(0).getStatus())) {
            throw new BizException(IntentOrderResultCode.INTENT_ORDER_CANCELED.getCode(), "该意向单已被取消");
        }
    }

    /**
     * 填充更新的意向单行数据
     *
     * @param existsList
     * @param updateList
     * @return
     */
    private void fillUpdateItem(List<IntentOrderItemPO> existsList, List<IntentOrderItemPO> updateList) {
        Map<String, List<IntentOrderItemPO>> itemMap = updateList.stream().collect(Collectors.groupingBy(IntentOrderItemPO::getIntentOrderItemNo));
        existsList.forEach(exist->{
            IntentOrderItemPO update = itemMap.get(exist.getIntentOrderItemNo()).get(0);
            exist.setEstimatedDeliveryDate(update.getEstimatedDeliveryDate());
            exist.setBusBar(update.getBusBar());
            exist.setCableLengthCathode(update.getCableLengthCathode());
            exist.setCableLengthPositive(update.getCableLengthPositive());
            exist.setGuidePrice(update.getGuidePrice());
            exist.setGuideTotalPrice(update.getGuideTotalPrice());
            exist.setInputType(update.getInputType());
            exist.setInstallation(update.getInstallation());
            exist.setItemType(update.getItemType());
            exist.setMaxPower(update.getMaxPower());
            exist.setMinPower(update.getMinPower());
            exist.setPlugConnector(update.getPlugConnector());
            exist.setPlugConnectorQuantity(update.getPlugConnectorQuantity());
            exist.setModuleLength(update.getModuleLength());
            exist.setModuleWidth(update.getModuleWidth());
            exist.setPower(update.getPower());
            exist.setQuantityW(update.getQuantityW());
            exist.setSaleTotalPrice(update.getSaleTotalPrice());
            exist.setSalePrice(update.getSalePrice());
            exist.setNetPrice(update.getNetPrice());
            exist.setOtherCostPrice(update.getOtherCostPrice());
            exist.setQuantityMw(update.getQuantityMw());
            exist.setProductVersion(update.getProductVersion());
            exist.setProductImgUrl(update.getProductImgUrl());
            exist.setProductCategory(update.getProductCategory());
            exist.setModuleType(update.getModuleType());
            exist.setProductName(update.getProductName());
            exist.setProductId(update.getProductId());
            exist.setQuantityP(update.getQuantityP());
            //OpType 传DELETE 时，需要先将数据标记删除
            if(StringUtils.isNotBlank(update.getOpType()) && OpTypeEnum.DELETE.getCode().equals(update.getOpType())){
                exist.setOpType(OpTypeEnum.DELETE.getCode());
                exist.setIsDeleted(DeleteFlagEnum.DELETED.getCode());
            }else{
                exist.setOpType(OpTypeEnum.UPDATE.getCode());
                exist.setIsDeleted(DeleteFlagEnum.NOT_DELETE.getCode());
            }
            exist.setUpdatedTime(LocalDateTime.now());
            exist.setPlugConnector(IntentOrderConstant.INTENT_ORDER_PLUG_CONNECTOR);
            if(ObjectUtil.isNotEmpty(update.getRelationId())){
                exist.setRelationId(update.getRelationId());
            }
        });
    }


}
