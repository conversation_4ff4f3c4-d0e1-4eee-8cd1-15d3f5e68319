package com.trinasolar.trinax.intentorder.repository.dao.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 意向单
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
public class IntentOrderAppQueryDAO {
    /**
     * 客户名称/产品姓名
     */
    private String param;
    //状态
    private List<String> status;
    //组件尺寸
    private BigDecimal size;

    //admin标识，传值表示是admin
    private String adminFlag;

    //当前用户id
    private String currentUserId;
    //当前用户为内部用户，且组织类型不为COMMON时，通过<sales_internal_user_id,biz_organization_code>列表查询
    private List<IntentOrderQueryObjectDAO> saleBizOrgList;

    //当前用户为内部用户，且orgType为“COMMON”时，需要通过bizOrgCode查询--该逻辑暂时已经弃用
    private List<String> bizOrgCodeList;

    //当前用户为内部用户，且orgType为“COMMON”时，需要查询所有下级人员对应的数据（不区分销售，运营）
    private List<String> commonUserList;

    //当前用户为外部用户，通过<external_user_id,enterprise_id>列表查询
    private List<IntentOrderQueryObjectDAO> dealerEnterpriseList;

    //运营人员List
    private List<String> operationUserIdList;

    /**
     * 排除企业:该企业的数据不能被非管理员查看
     */
    private String excludeEnterpriseId;

    /**
     * 组装类型为COMMON类型的用户，需要排除企业数据权限
     */
    private String userOrgType;


    @Schema(description = "意向单号")
    private String intentOrderNo;

    @Schema(description = "公司")
    private String enterpriseName;

    @Schema(description = "关联方企业名称")
    private String capitalEnterpriseName;
    
    @Schema(description = "创建开始时间：PC端需求新增入参")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建结束时间：PC端需求新增入参")
    private LocalDateTime createdTimeEnd;

    @Schema(description = "期望交货开始时间：PC端需求新增入参")
    private LocalDateTime expectDeliveryDateStart;

    @Schema(description = "期望交货结束时间：PC端需求新增入参")
    private LocalDateTime expectDeliveryDateEnd;

    @Schema(description = "产品名称：PC端需求新增入参")
    private String productName;

    @Schema(description = "创建人：PC端需求新增入参")
    private String createdBy;

    @Schema(description = "需求量Start：PC端需求新增入参")
    private BigDecimal potentialSalesVolumeStart;

    @Schema(description = "需求量End：PC端需求新增入参")
    private BigDecimal potentialSalesVolumeEnd;

    @Schema(description = "意向单类型 1:普通意向单 2:快速订单")
    private Integer orderType;


}
