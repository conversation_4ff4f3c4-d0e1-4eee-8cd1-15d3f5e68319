package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.cart.dto.input.ContractFrameQueryAuthDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.dto.input.contract.frame.ContactFrameAppReqDTO;
import com.trinasolar.trinax.partner.api.EnterpriseFeign;
import com.trinasolar.trinax.partner.dto.input.EnterpriseQueryReqDTO;
import com.trinasolar.trinax.partner.dto.output.EnterpriseResPcExcelDTO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SubordinateQueryReqDTO;
import com.trinasolar.trinax.user.dto.input.relation.CoBindInfReqDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import com.trinasolar.trinax.user.dto.output.relation.ContactorSyncResDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class ContractFrameAuthBiz {

    private final SysUserFeign sysUserFeign;
    private final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;
    private final EnterpriseFeign enterpriseFeign;


    /**
     * 处理pc端框架合同列表的权限查询
     */
    public boolean handlePcAuth(ContractFrameQueryAuthDTO query) {
        String loginUserId = query.getUserID();
        Result<SysUserRespDTO> sysUserRespDTOResult = sysUserFeign.getUserByUserId(loginUserId);
        SysUserRespDTO sysUserRespDTO = sysUserRespDTOResult.getData();
        // 如果是超级管理员，那么直接可以看所有
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.ADMIN_ROLE_ID)) {
            return true;
        }
        if (SysOrganizationTypeEnum.OPERATION.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 运营用户
            EnterpriseQueryReqDTO reqDTO = new EnterpriseQueryReqDTO();
            reqDTO.setLoginUserId(loginUserId);
            List<EnterpriseResPcExcelDTO> enterpriseResPcExcelDTOS = enterpriseFeign.listEnterpriseResPcExcel(reqDTO).getData();
            if (ObjectUtil.isEmpty(enterpriseResPcExcelDTOS)) {
                return false;
            }
            query.setOperationEnterpriseIds(enterpriseResPcExcelDTOS.stream().map(EnterpriseResPcExcelDTO::getEnterpriseId).toList());
        } else if (SysOrganizationTypeEnum.COMMON.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 常规用户
            Result<List<SysUserRespDTO>> sysUserFeignSubordinateResult = sysUserFeign.getSubordinate(SubordinateQueryReqDTO.builder().userId(loginUserId).sysUserTypeEnum(SysUserTypeEnum.INTERNAL).build());
            if (ObjectUtil.isEmpty(sysUserFeignSubordinateResult.getData())) {
                return false;
            }
            query.setCommonUserIdList(sysUserFeignSubordinateResult.getData().stream().map(SysUserRespDTO::getUserId).toList());
        } else {
            // 其它组织类型的用户
            // 无任何数据权限，直接返回空
            return false;
        }
        return true;
    }

    /**
     * 处理app端框架合同列表的权限查询
     */
    public boolean handleAppAuth(ContactFrameAppReqDTO query) {
        // app端查看框架合同是从我的客户列表点进来的，只要能够看见有框架合同按钮，就可以请求此接口，所以这里不用过滤权限

        if (SysUserTypeEnum.INTERNAL.getType().equals(query.getLoginUserType())) {
            return false;
        }
        List<String> userIds = new ArrayList<>();

        userIds.add(query.getLoginUserId());
        List<ContactorSyncResDTO> contactorList = sysDealerSalesRelationFeign.qyrBindInfoByCondition(CoBindInfReqDTO.builder().setUserIds(userIds));

        List<String> enterpriseIds = new ArrayList<>();
        contactorList.forEach(a -> {
            enterpriseIds.add(a.getEnterpriseId());
        });
        if (ObjectUtil.isEmpty(enterpriseIds)) {
            //权限校验失败
            return true;
        }
        query.setEnterpriseIds(enterpriseIds);
        return false;
    }

}
