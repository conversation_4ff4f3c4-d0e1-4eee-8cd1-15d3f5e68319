package com.trinasolar.trinax.intentorder.manager;

import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.intentorder.repository.dao.input.IntentOrderQueryObjectDAO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysSalesOperationRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SubordinateQueryReqDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesOperationRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesSubordinateRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class GetSubordinateManager {

    @Autowired
    private SysSalesOperationRelationFeign sysSalesOperationRelationFeign;

    @Autowired
    private SysDealerSalesRelationFeign sysDealerSalesRelationFeign;

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    RedisUtil redisUtil;

    /**
     * 获取运营下级用户
     */
    public List<String> getOperationSubUserList(String operationUserId){
        String redisKey = UserConstant.DATA_PERMISSION_USER_CACHE_PREFIX + operationUserId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            List<SysUserRespDTO> list = JacksonUtil.json2List(redisVal, SysUserRespDTO.class);
            return list.stream().map(SysUserRespDTO::getUserId).distinct().collect(Collectors.toList());
        }else{
            SubordinateQueryReqDTO reqDTO = new SubordinateQueryReqDTO();
            reqDTO.setUserId(operationUserId);
            reqDTO.setSysUserTypeEnum(SysUserTypeEnum.INTERNAL);
            Result<List<SysUserRespDTO>> result= sysUserFeign.getSubordinate(reqDTO);
            if(Boolean.TRUE.equals(!result.getSuccess()) || CollectionUtils.isEmpty(result.getData())){
                return Collections.emptyList();
            }
            return result.getData().stream().map(SysUserRespDTO::getUserId).distinct().collect(Collectors.toList());
        }
    }



    /**
     * 获取运营下级用户用户信息
     */
    public List<IntentOrderQueryObjectDAO> getOperationSubordinate(String operationUserId){
        Result<List<SysSalesOperationRelationRespDTO>> result= sysSalesOperationRelationFeign.getSysSalesListByOps(operationUserId);
        if(Boolean.TRUE.equals(!result.getSuccess()) || CollectionUtils.isEmpty(result.getData())){
            return Collections.emptyList();
        }
        List<SysSalesOperationRelationRespDTO> list  = result.getData();
        List<IntentOrderQueryObjectDAO> userList = new ArrayList<>();
        list.forEach(e->{
            IntentOrderQueryObjectDAO objectDAO = new IntentOrderQueryObjectDAO();
            objectDAO.setUserId(e.getSalesUserId());
            objectDAO.setBizOrganizationCode(e.getBizOrganizationCode());
            userList.add(objectDAO);
        });
        return userList;
    }


    /**
     * 获取销售下级用户用户信息
     * @param salesUserId
     * @return
     */
    public List<IntentOrderQueryObjectDAO> getSalesSubordinate(String salesUserId){
        List<IntentOrderQueryObjectDAO> userList = new ArrayList<>();

        String redisKey = UserConstant.DATA_PERMISSION_SALES_CACHE_PREFIX + salesUserId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            List<SysSalesSubordinateRespDTO> result = JacksonUtil.json2List(redisVal, SysSalesSubordinateRespDTO.class);
            result.forEach(e->{
                IntentOrderQueryObjectDAO objectDAO = new IntentOrderQueryObjectDAO();
                objectDAO.setUserId(e.getUserId());
                objectDAO.setBizOrganizationCode(e.getOrganizationCode());
                userList.add(objectDAO);
            });
            return userList;
        }else{
            SubordinateQueryReqDTO reqDTO = new SubordinateQueryReqDTO();
            reqDTO.setUserId(salesUserId);
            reqDTO.setSysUserTypeEnum(SysUserTypeEnum.INTERNAL);
            Result<List<SysSalesSubordinateRespDTO>> result= sysUserFeign.getSalesSubordinate(reqDTO);
            if(Boolean.TRUE.equals(!result.getSuccess()) || CollectionUtils.isEmpty(result.getData())){
                return Collections.emptyList();
            }
            List<SysSalesSubordinateRespDTO> list  = result.getData();
            list.forEach(e->{
                IntentOrderQueryObjectDAO objectDAO = new IntentOrderQueryObjectDAO();
                objectDAO.setUserId(e.getUserId());
                objectDAO.setBizOrganizationCode(e.getOrganizationCode());
                userList.add(objectDAO);
            });
            return userList;
        }
    }

    /**
     * 获取经销商下级用户用户信息
     * @param dealerUserId
     * @return
     */
    public List<IntentOrderQueryObjectDAO> getDealerSubordinate(String dealerUserId){
        List<IntentOrderQueryObjectDAO> userList = new ArrayList<>();

        String cache = redisUtil.get(UserConstant.DATA_PERMISSION_DEALER_CACHE_PREFIX + dealerUserId);
        if(StringUtils.isNotBlank(cache)){
            List<SysSubDealerRespDTO> result = JacksonUtil.json2List(cache,SysSubDealerRespDTO.class);
            result.forEach(e->{
                IntentOrderQueryObjectDAO objectDAO = new IntentOrderQueryObjectDAO();
                objectDAO.setUserId(e.getDealerUserId());
                objectDAO.setEnterpriseId(e.getEnterpriseId());
                userList.add(objectDAO);
            });
            return userList;
        }else{
            Result<List<SysSubDealerRespDTO>> result= sysDealerSalesRelationFeign.getSubDealerInfo(dealerUserId);
            if(Boolean.TRUE.equals(!result.getSuccess()) || CollectionUtils.isEmpty(result.getData())){
                return Collections.emptyList();
            }
            List<SysSubDealerRespDTO> list  = result.getData();

            list.forEach(e->{
                IntentOrderQueryObjectDAO objectDAO = new IntentOrderQueryObjectDAO();
                objectDAO.setUserId(e.getDealerUserId());
                objectDAO.setEnterpriseId(e.getEnterpriseId());
                userList.add(objectDAO);
            });
            return userList;
        }
    }


}
