package com.trinasolar.trinax.requireorder.repository.dao.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 意向单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
public class RequireOrderAppQueryDAO {

    //admin标识，传值表示是admin
    private String adminFlag;

    @Schema(description = "需求单状态")
    private String requireOrderStatus;

    @Schema(description = "输入框查询条件")
    private String keyword;

    @Schema(description = "当前用户id")
    private String currentUserId;

    @Schema(description = "外部用户id：当外部用户查询时，需要传该参数")
    private String externalUserId;

    @Schema(description = "common下级UserIdList")
    private List<String> commonUserList;

    @Schema(description = "运营下级UserIdList")
    private List<String> operationUserIdList;

    @Schema(description = "组织类型为销售时，通过<sales_internal_user_id,biz_organization_code>列表查询")
    private List<RequireOrderQueryObjectDAO> saleBizOrgList;

    @Schema(description = "外部用户，通过<businessExternalUserId,enterprise_id>列表查询")
    private List<RequireOrderQueryObjectDAO> externalEnterpriseList;

    @Schema(description = "客户名称：PC端需求新增入参")
    private String enterpriseName;

    @Schema(description = "需求单编号：PC端需求新增入参")
    private String requireOrderNo;

    @Schema(description = "创建开始时间：PC端需求新增入参")
    private LocalDateTime createdTimeStart;

    @Schema(description = "创建结束时间：PC端需求新增入参")
    private LocalDateTime createdTimeEnd;

    @Schema(description = "需求所有人：PC端需求新增入参")
    private String requireOrderOwnerName;
}
