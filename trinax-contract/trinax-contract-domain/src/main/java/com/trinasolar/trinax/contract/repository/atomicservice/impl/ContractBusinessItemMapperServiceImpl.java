package com.trinasolar.trinax.contract.repository.atomicservice.impl;


import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessItemMapperService;
import com.trinasolar.trinax.contract.repository.mapper.ContractBusinessItemMapper;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessItemPO;
import com.trinasolar.trinax.intentorder.constants.enums.ModuleTypeEnum;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class ContractBusinessItemMapperServiceImpl extends ServiceImpl<ContractBusinessItemMapper, ContractBusinessItemPO> implements ContractBusinessItemMapperService {
    @Override
    public List<ContractBusinessItemPO> listByContractBusinessItemIds(List<String> contractBusinessItemIds) {
        if (ObjectUtil.isEmpty(contractBusinessItemIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<ContractBusinessItemPO>().in(ContractBusinessItemPO::getContractBusinessItemId, contractBusinessItemIds));
    }

    @Override
    public List<ContractBusinessItemPO> listByContractIds(List<String> contractIds) {
        if (ObjectUtil.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<ContractBusinessItemPO>().in(ContractBusinessItemPO::getContractId, contractIds));
    }

    @Override
    public List<ContractBusinessItemPO> listByContractId(String contractId) {
        return list(new LambdaQueryWrapper<ContractBusinessItemPO>().eq(ContractBusinessItemPO::getContractId, contractId));
    }

    @Override
    public List<ContractBusinessItemPO> listByContractIdsAndModuleType(List<String> contractIds, ModuleTypeEnum moduleTypeEnum) {
        if (ObjectUtil.isEmpty(contractIds) || ObjectUtil.isNull(moduleTypeEnum)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapper<ContractBusinessItemPO>()
                .in(ContractBusinessItemPO::getContractId, contractIds)
                .eq(ContractBusinessItemPO::getModuleType, moduleTypeEnum.getCode()));
    }

}
