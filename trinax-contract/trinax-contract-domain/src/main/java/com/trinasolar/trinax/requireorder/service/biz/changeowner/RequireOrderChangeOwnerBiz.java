package com.trinasolar.trinax.requireorder.service.biz.changeowner;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.basic.api.ChangeOwnerFeign;
import com.trinasolar.trinax.basic.api.TodoListFeign;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeTaskStatusEnum;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeUserTypeEnum;
import com.trinasolar.trinax.basic.dto.input.TodoListChangeOwnerReqDTO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerRecordMqDTO;
import com.trinasolar.trinax.basic.dto.input.changeOwner.ChangeOwnerStatusUpdateReqDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.requireorder.dto.input.changeowner.RequireOrderChangeOwnerReqDTO;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.mapper.RequireOrderMapper;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RequireOrderChangeOwnerBiz {

    @Autowired
    private RequireOrderMapper requireOrderMapper;
    @Autowired
    private RequireOrderMapperService requireOrderMapperService;
    @Autowired
    private MqManager mqManager;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private ChangeOwnerFeign changeOwnerFeign;
    @Autowired
    private TodoListFeign todoListFeign;


    /**
     * changeOwner
     * @param reqDTO
     * @return
     */
    public Result<String> changeOwner(RequireOrderChangeOwnerReqDTO reqDTO) {
        log.info("RequireOrder change owner 入参RequireOrderChangeOwnerReqDTO：{}", reqDTO);
        List<RequireOrderPO> requireOrderPOList = requireOrderMapper.selectChangeOwnerRecord(reqDTO);

        if(CollUtil.isEmpty(requireOrderPOList)){
            //不存在变更数据时，
            // 只需要修改任务状态
            updateTaskStatus(reqDTO,true,0,"");
            //添加任务记录
            sendChangeMq(reqDTO);
        }else{
            //填充ChangeOwner数据
            fillRequireOrderData(requireOrderPOList,reqDTO);
            List<String> bizNoList= requireOrderPOList.stream().map(RequireOrderPO::getRequireOrderNo).toList();

            boolean changed = false;
            String errMessage = "";
            //修改业务数据
            try{
                changed = requireOrderMapperService.updateBatchById(requireOrderPOList);
            }catch (Exception e){
                errMessage = e.getMessage();
                log.error("Exception message:",e);
            }finally {
                //修改任务状态
                updateTaskStatus(reqDTO,changed,requireOrderPOList.size(),errMessage);
                //修改待办状态
                if(changed){
                    updateTodoList(reqDTO,bizNoList);
                }
                //发送任务记录MQ
                sendChangeMq(reqDTO);
            }
        }
        return Result.ok();

    }
    private void updateTodoList(RequireOrderChangeOwnerReqDTO reqDTO,List<String> bizNoList){
        TodoListChangeOwnerReqDTO todoChangeReq = BeanUtil.toBean(reqDTO,TodoListChangeOwnerReqDTO.class);
        todoChangeReq.setBizNoList(bizNoList);
        todoChangeReq.setBizPrefix("RQ_");
        todoListFeign.changeOwner(todoChangeReq);
    }

    private void fillRequireOrderData(List<RequireOrderPO> requireOrderPOList,RequireOrderChangeOwnerReqDTO reqDTO){
        if(ChangeUserTypeEnum.SALES_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            requireOrderPOList.forEach(e->{
                if(reqDTO.getOriginUserId().equals(e.getSalesInternalUserId())
                        && reqDTO.getOriginUserId().equals(e.getRequireOrderOwnerId())) {
                    e.setSalesInternalUserId(reqDTO.getNewUserId());
                    e.setSalesInternalUserName(reqDTO.getNewUserName());
                    e.setRequireOrderOwnerId(reqDTO.getNewUserId());
                    e.setRequireOrderOwnerName(reqDTO.getNewUserName());
                }else if(reqDTO.getOriginUserId().equals(e.getSalesInternalUserId())){
                    e.setSalesInternalUserId(reqDTO.getNewUserId());
                    e.setSalesInternalUserName(reqDTO.getNewUserName());
                }else if(reqDTO.getOriginUserId().equals(e.getRequireOrderOwnerId())){
                    e.setRequireOrderOwnerId(reqDTO.getNewUserId());
                    e.setRequireOrderOwnerName(reqDTO.getNewUserName());
                }
            });
        }else if(ChangeUserTypeEnum.OPERATION_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            requireOrderPOList.forEach(e->{
                e.setOperationInternalUserId(reqDTO.getNewUserId());
                e.setOperationInternalUserName(reqDTO.getNewUserName());
            });
        }else if(ChangeUserTypeEnum.EXTERNAL_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            requireOrderPOList.forEach(e->{
                e.setBusinessExternalUserId(reqDTO.getNewUserId());
                e.setBusinessExternalUserName(reqDTO.getNewUserName());
                if(reqDTO.getOriginUserId().equals(e.getCreatedBy())){
                    e.setCreatedBy(reqDTO.getNewUserId());
                    e.setCreatedName(reqDTO.getNewUserName());
                    e.setUpdatedTime(LocalDateTime.now());
                }
            });
        }
    }

    private void updateTaskStatus(RequireOrderChangeOwnerReqDTO reqDTO, boolean changed,int amount,String errMessage){
        ChangeOwnerStatusUpdateReqDTO updateReqDTO =  new ChangeOwnerStatusUpdateReqDTO();
        updateReqDTO.setTaskNo(reqDTO.getTaskNo());
        updateReqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        updateReqDTO.setUpdateUserId(reqDTO.getUpdatedBy());
        updateReqDTO.setUpdateUserName(reqDTO.getUpdatedName());
        updateReqDTO.setSubTaskStatus(changed ? ChangeTaskStatusEnum.SUCCESS.getCode():ChangeTaskStatusEnum.FAIL.getCode());
        updateReqDTO.setOriginUserId(reqDTO.getOriginUserId());
        updateReqDTO.setNewUserId(reqDTO.getNewUserId());
        updateReqDTO.setChangeAmount(amount);
        updateReqDTO.setErrMessage(errMessage);
        changeOwnerFeign.updateTaskStatus(updateReqDTO);
    }

    private void sendChangeMq(RequireOrderChangeOwnerReqDTO reqDTO){
        ChangeOwnerRecordMqDTO mqDTO = new ChangeOwnerRecordMqDTO();
        mqDTO.setTaskNo(reqDTO.getTaskNo());
        mqDTO.setSubTaskNo(reqDTO.getSubTaskNo());
        mqDTO.setSituationCode(reqDTO.getSituationCode());
        mqDTO.setOldValue(reqDTO.getOriginUserId() + "," + reqDTO.getOriginUserName());
        mqDTO.setNewValue(reqDTO.getNewUserId() + "," + reqDTO.getNewUserName());
        mqDTO.setChangeTable("trinax_contract.requireOrder");
        if(ChangeUserTypeEnum.SALES_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            mqDTO.setChangeField("sales_internal_user_id,sales_internal_user_name");
        }else if(ChangeUserTypeEnum.OPERATION_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            mqDTO.setChangeField("operation_internal_user_id,operation_internal_user_name");
        }else if(ChangeUserTypeEnum.EXTERNAL_USER_CHANGE.getCode().equals(reqDTO.getChangeType())){
            mqDTO.setOldValue(reqDTO.getOriginUserId() + "," + reqDTO.getOriginUserName() + "," + reqDTO.getOriginUserPhone());
            mqDTO.setNewValue(reqDTO.getNewUserId() + "," + reqDTO.getNewUserName() + "," + reqDTO.getNewUserPhone());
            mqDTO.setChangeField("business_external_user_id,business_external_user_name");
        }
        mqDTO.setCurrentUserId(reqDTO.getUpdatedBy());
        mqDTO.setCurrentUserName(reqDTO.getUpdatedName());
        log.info("requireOrder ChangeOwnerRecord mqDTO：{}", reqDTO);
        mqManager.sendTopic(BasicCommonConstant.CHANGE_OWNER_RECORD_TOPIC, JacksonUtil.bean2Json(mqDTO));
    }


}
