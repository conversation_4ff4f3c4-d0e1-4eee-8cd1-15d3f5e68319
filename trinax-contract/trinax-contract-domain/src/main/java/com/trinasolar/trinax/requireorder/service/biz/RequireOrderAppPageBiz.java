package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.requireorder.constants.enums.OpportunitySegmentEnum;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderStatusEnum;
import com.trinasolar.trinax.requireorder.constants.enums.TrackerTypeEnum;
import com.trinasolar.trinax.requireorder.dto.input.RequireOrderAppPageReqDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderRespDTO;
import com.trinasolar.trinax.requireorder.manager.RequireGetSubordinateManager;
import com.trinasolar.trinax.requireorder.manager.RequireOrganizationManager;
import com.trinasolar.trinax.requireorder.manager.RequireUserManager;
import com.trinasolar.trinax.requireorder.repository.dao.input.RequireOrderAppQueryDAO;
import com.trinasolar.trinax.requireorder.repository.mapper.RequireOrderMapper;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserStatusEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.input.SysSalesDealerRelationReqDTO;
import com.trinasolar.trinax.user.dto.input.SysUserQueryDTO;
import com.trinasolar.trinax.user.dto.output.SysDealerSalesRelationRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RequireOrderAppPageBiz {

    @Autowired
    private RequireOrderMapper requireOrderMapper;

    @Autowired
    private RequireUserManager requireUserManager;

    @Autowired
    private RequireOrganizationManager requireOrganizationManager;

    @Autowired
    private RequireGetSubordinateManager requireGetSubordinateManager;

    @Autowired
    private SysDealerSalesRelationFeign sysDealerSalesRelationFeign;

    @Autowired
    private SysUserFeign sysUserFeign;
    /**
     * PC端分页接口，支持菜单列表 & 用户详情页查询分页
     *
     * @param reqDTO
     * @return
     */
    public PageResponse<RequireOrderRespDTO> requireOrderAppPage(PageRequest<RequireOrderAppPageReqDTO> reqDTO) {

        RequireOrderAppQueryDAO queryDAO = BeanUtil.toBean(reqDTO.getQuery(), RequireOrderAppQueryDAO.class);
        //处理权限参数
        initPermissionParam(queryDAO, reqDTO.getQuery());

        Page<RequireOrderPO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        IPage<RequireOrderPO> orderPOPage = requireOrderMapper.findAppPage(page, queryDAO);
        //返回内容转义封装
        List<RequireOrderRespDTO> resultList = new ArrayList<>();
        List<SysSalesDealerRelationReqDTO> reqDTOList = new ArrayList<>();
        orderPOPage.getRecords().forEach(e -> {
            RequireOrderRespDTO resDTO = BeanUtil.toBean(e, RequireOrderRespDTO.class);
            resDTO.setRequireOrderStatusText(RequireOrderStatusEnum.getDescByCode(resDTO.getRequireOrderStatus()));
            resDTO.setOpportunitySegmentText(OpportunitySegmentEnum.getDescByCode(resDTO.getOpportunitySegment()));
            resultList.add(resDTO);

            SysSalesDealerRelationReqDTO relationReqDTO = new SysSalesDealerRelationReqDTO();
            relationReqDTO.setEnterpriseId(e.getEnterpriseId());
            relationReqDTO.setDealerUserId(e.getBusinessExternalUserId());
            reqDTOList.add(relationReqDTO);
        });
        //需要同步将需求单创建用户和企业的绑定关系返回
        if(CollUtil.isNotEmpty(reqDTOList)){
            Result<List<SysDealerSalesRelationRespDTO>>  listResult = sysDealerSalesRelationFeign.listByEnterpriseIdAndDealerUserIdBatch(reqDTOList);
            if(Boolean.TRUE.equals(listResult.getSuccess()) && CollUtil.isNotEmpty(listResult.getData())){
                Map<String,List<SysDealerSalesRelationRespDTO>> map =  listResult.getData().stream().collect(Collectors.groupingBy(e->e.getEnterpriseId() + e.getDealerUserId()));
                resultList.forEach(e->{
                    if(CollUtil.isNotEmpty(map.get(e.getEnterpriseId() + e.getBusinessExternalUserId()))){
                        e.setAuthStatus(map.get(e.getEnterpriseId() + e.getBusinessExternalUserId()).get(0).getAuthStatus());
                    }
                    if(TrackerTypeEnum.PARTNER.getCode() .equals(e.getTrackerUserType())){
                        e.setTrackerUserId(e.getPartnerUserId());
                        e.setTrackerUserName(e.getPartnerUserName());
                    }else{
                        e.setTrackerUserId(e.getSalesInternalUserId());
                        e.setTrackerUserName(e.getSalesInternalUserName());
                    }
                });
            }
        }
        //填充跟进人信息
        fillTrackerUserInfo(resultList);

        return PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) orderPOPage.getTotal(),
                resultList);
    }

    /**
     * 处理权限参数：管理员时，直接传一个标识《只要传参就会当作管理员》，
     */
    private void initPermissionParam(RequireOrderAppQueryDAO queryDAO, RequireOrderAppPageReqDTO reqDTO) {
        if (SysUserTypeEnum.INTERNAL.getType().equals(reqDTO.getCurrentUserType())) {
            if (!requireUserManager.isAdmin(reqDTO.getCurrentUserId())) {
                String orgType = requireOrganizationManager.getOrgTypeByUserId(reqDTO.getCurrentUserId());
                if (SysOrganizationTypeEnum.COMMON.getCode().equals(orgType)) {
                    queryDAO.setCommonUserList(requireGetSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId()));
                } else if (SysOrganizationTypeEnum.SALES.getCode().equals(orgType)) {
                    queryDAO.setSaleBizOrgList(requireGetSubordinateManager.getSalesSubordinate(reqDTO.getCurrentUserId()));
                } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(orgType)) {
                    queryDAO.setOperationUserIdList(requireGetSubordinateManager.getOperationSubUserList(reqDTO.getCurrentUserId()));
                }
            } else {
                queryDAO.setAdminFlag("admin");
            }
        }else{
            queryDAO.setExternalUserId(reqDTO.getCurrentUserId());
            queryDAO.setExternalEnterpriseList(requireGetSubordinateManager.getDealerSubordinate(reqDTO.getCurrentUserId()));
        }
    }

    public List<RequireOrderRespDTO> requireOrderAppList(RequireOrderAppPageReqDTO reqDTO) {

        RequireOrderAppQueryDAO queryDAO = BeanUtil.toBean(reqDTO, RequireOrderAppQueryDAO.class);
        //处理权限参数
        initPermissionParam(queryDAO, reqDTO);
        //查询数据
        List<RequireOrderPO> orderPOList = requireOrderMapper.findAppList(queryDAO);
        //返回内容转义封装
        List<RequireOrderRespDTO> resultList = BeanUtil.copyToList(orderPOList,RequireOrderRespDTO.class);
        resultList.forEach(e->{
            e.setRequireOrderStatusText(RequireOrderStatusEnum.getDescByCode(e.getRequireOrderStatus()));
            if(TrackerTypeEnum.PARTNER.getCode() .equals(e.getTrackerUserType())){
                e.setTrackerUserId(e.getPartnerUserId());
                e.setTrackerUserName(e.getPartnerUserName());
            }else{
                e.setTrackerUserId(e.getSalesInternalUserId());
                e.setTrackerUserName(e.getSalesInternalUserName());
            }
        });
        //填充跟进人信息
        fillTrackerUserInfo(resultList);
        return resultList;
    }

    /**
     *填充跟进人信息
     * @param resultList
     */
    private void fillTrackerUserInfo(List<RequireOrderRespDTO> resultList){
        List<String> trackerUserIdList = resultList.stream().map(RequireOrderRespDTO::getTrackerUserId).distinct().toList();
        Result<List<SysUserRespDTO>> trackerUserResult = sysUserFeign.listUser(SysUserQueryDTO.builder().userIdList(trackerUserIdList).build());
        if(Boolean.TRUE.equals(trackerUserResult.getSuccess()) && CollUtil.isNotEmpty(trackerUserResult.getData())){
            Map<String,List<SysUserRespDTO>> trackerUserRespMap = trackerUserResult.getData().stream().collect(Collectors.groupingBy(SysUserRespDTO::getUserId));
            resultList.forEach(e->{
                List<SysUserRespDTO> userList = trackerUserRespMap.get(e.getTrackerUserId());
                if(CollUtil.isNotEmpty(userList)){
                    if(!SysUserStatusEnum.WITHDRAW.getCode().equals(userList.get(0).getStatus())){
                        e.setTrackerUserName(userList.get(0).getUserName());
                        e.setTrackerUserPhone(userList.get(0).getMobile());
                    }
                }
            });
        }
    }


}
