package com.trinasolar.trinax.intentorder.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendJPushReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderTypeEnum;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.manager.IntentOrderMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class ExternalSubmitMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private IntentOrderMessageManager intentOrderMessageManager;

    @Value("${message.template.externalSubmitPush}")
    private String pushTemplateCode;

    @Value("${message.template.qoExternalSubmitPush}")
    private String qoPushTemplateCode;

    @Value("${message.template.externalSubmitTodo}")
    private String todoTemplateCode;

    @Value("${message.template.qoExternalSubmitTodo}")
    private String qoTodoTemplateCode;

    public void execute(IntentOrderMessageMqDTO reqDTO) {
        log.info("生态伙伴创建意向单,给销售用户发送极光消息和待办消息。入参：{}", reqDTO);
        MessageSendCommonReqDTO pushMessageReqDTO = new MessageSendCommonReqDTO();
        pushMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.JPUSH.getCode());
        List<MessageSendJPushReqDTO> jPushList = new ArrayList<>();

        Map<String, String> content = new HashMap<>();
        content.put("enterpriseName", reqDTO.getEnterpriseName());
        content.put("externalUserName", reqDTO.getExternalUserName());
        content.put("potentialSalesVolume", reqDTO.getPotentialSalesVolume().stripTrailingZeros().toPlainString());
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());

        if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(reqDTO.getOrderType())){
            MessageSendJPushReqDTO pushReqDTO = intentOrderMessageManager.generateJPush(qoPushTemplateCode,
                    Collections.singletonList(reqDTO.getSalesInternalUserId()), content);
            jPushList.add(pushReqDTO);
        }else{
            MessageSendJPushReqDTO pushReqDTO = intentOrderMessageManager.generateJPush(pushTemplateCode,
                    Collections.singletonList(reqDTO.getSalesInternalUserId()), content);
            jPushList.add(pushReqDTO);
        }

        pushMessageReqDTO.setJPushList(jPushList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(pushMessageReqDTO));
        log.info("生态伙伴创建意向单，给分销销售发送极光消息完成");

        MessageSendCommonReqDTO todoMessageReqDTO = new MessageSendCommonReqDTO();
        todoMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());
        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();

        if(IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(reqDTO.getOrderType())){
            MessageSendTodoReqDTO todoReqDTO = intentOrderMessageManager.generateTodo(reqDTO,
                    qoTodoTemplateCode, TodoBizCodeEnum.QO_EXTERNAL_CREATE.getCode(),
                    Collections.singletonList(reqDTO.getSalesInternalUserId()),content, TodoOrganizationTypeEnum.SALES.getCode());
            todoList.add(todoReqDTO);
        }else{
            MessageSendTodoReqDTO todoReqDTO = intentOrderMessageManager.generateTodo(reqDTO,
                    todoTemplateCode, TodoBizCodeEnum.IO_EXTERNAL_CREATE.getCode(),
                    Collections.singletonList(reqDTO.getSalesInternalUserId()),content, TodoOrganizationTypeEnum.SALES.getCode());
            todoList.add(todoReqDTO);
        }

        todoMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(todoMessageReqDTO));
        log.info("生态伙伴创建意向单，给分销销售发送待办消息完成，param:{}", JacksonUtil.bean2Json(todoMessageReqDTO));
    }

}
