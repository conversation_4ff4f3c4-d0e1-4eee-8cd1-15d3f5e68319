package com.trinasolar.trinax.contract.repository.atomicservice.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.contract.constants.enums.ContractFileSourceEnum;
import com.trinasolar.trinax.contract.dto.output.ContractFileResDTO;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractFileMapperService;
import com.trinasolar.trinax.contract.repository.mapper.ContractFileMapper;
import com.trinasolar.trinax.contract.repository.po.ContractFilePO;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class ContractFileMapperServiceImpl extends ServiceImpl<ContractFileMapper, ContractFilePO> implements ContractFileMapperService {
    @Override
    public List<ContractFileResDTO> listByContractIds(List<String> contractIds) {
        if (ObjectUtil.isEmpty(contractIds)) {
            return Collections.emptyList();
        }
        List<ContractFilePO> contractFilePOS = list(new LambdaQueryWrapper<ContractFilePO>()
                .select(ContractFilePO::getContractId,
                        ContractFilePO::getFileName,
                        ContractFilePO::getFileUrl,
                        ContractFilePO::getFileSource)
                .in(ContractFilePO::getContractId, contractIds));
        List<ContractFileResDTO> contractFileResDTOS = BeanUtil.copyToList(contractFilePOS, ContractFileResDTO.class);
        contractFileResDTOS.forEach(e -> e.setFileSourceText(ContractFileSourceEnum.getDescByCode(e.getFileSource())));
        return contractFileResDTOS;
    }

    @Override
    public List<ContractFilePO> listByContractId(String contractId) {
        return lambdaQuery().eq(ContractFilePO::getContractId, contractId).list();
    }

    @Override
    public void deleteByContractId(String contractId) {
        remove(new LambdaQueryWrapper<ContractFilePO>().eq(ContractFilePO::getContractId, contractId));
    }
}
