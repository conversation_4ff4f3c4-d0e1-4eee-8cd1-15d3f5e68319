package com.trinasolar.trinax.intentorder.service.biz.message;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendJPushReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderTypeEnum;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.manager.IntentOrderMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class ExternalConfirmInternalCreateMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private IntentOrderMessageManager intentOrderMessageManager;

    @Value("${message.template.qoExternalConfirmInternalCreateNoticeSales}")
    private String qoExternalConfirmInternalCreateNoticeSales;

    public void execute(IntentOrderMessageMqDTO reqDTO){
        log.info("客户确认分销销售自建的渠道订单,给分销销售发送系统通知,入参：{}", reqDTO);
        Map<String,String> content = new HashMap<>();
        content.put("externalUserName",reqDTO.getExternalUserName());
        content.put("potentialSalesVolume",reqDTO.getPotentialSalesVolume().stripTrailingZeros().toPlainString());
        content.put("intentOrderNo",reqDTO.getIntentOrderNo());
        content.put("enterpriseName",reqDTO.getEnterpriseName());

        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());
        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();


        MessageSendNoticeReqDTO noticeReqDTO = intentOrderMessageManager.generateNotice(reqDTO,
                "", MessageBizCodeEnum.QO_EXTERNAL_CONFIRM.getValue(), qoExternalConfirmInternalCreateNoticeSales,
                Collections.singletonList(reqDTO.getExternalUserId()),content);
        noticeList.add(noticeReqDTO);

        MessageSendNoticeReqDTO operationNotice = BeanUtil.toBean(noticeReqDTO,MessageSendNoticeReqDTO.class);
        content.put("enterpriseName",reqDTO.getEnterpriseName());
        operationNotice.setTemplateCode(qoExternalConfirmInternalCreateNoticeSales);
        operationNotice.setUserIdList(Collections.singletonList(reqDTO.getOperationInternalUserId()));
        operationNotice.setContent(content);

        noticeList.add(operationNotice);
        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("客户确认分销销售自建的渠道订单,给分销销售发送系统通知完成，param:{}",JacksonUtil.bean2Json(noticeMessageReqDTO));

    }

}
