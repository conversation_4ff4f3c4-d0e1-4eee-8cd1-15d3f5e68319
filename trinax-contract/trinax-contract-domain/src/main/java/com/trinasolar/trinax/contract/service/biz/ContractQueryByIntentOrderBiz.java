package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.basic.api.OptionFeign;
import com.trinasolar.trinax.basic.constants.enums.OptionGroupConstant;
import com.trinasolar.trinax.basic.dto.output.OptionItemResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.contract.constants.enums.ContractStatusEnum;
import com.trinasolar.trinax.contract.dto.output.ContractIntentOrderQueryResDTO;
import com.trinasolar.trinax.contract.repository.mapper.ContractMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ContractQueryByIntentOrderBiz {

    @Autowired
    private ContractMapper contractMapper;

    @Autowired
    private OptionFeign optionFeign;


    /**
     *
     * @param intentOrderNo
     * @return
     */
    public List<ContractIntentOrderQueryResDTO> getContractByIntentOrderNo(String intentOrderNo) {
        List<ContractIntentOrderQueryResDTO> result = contractMapper.getContractByIntentOrderNo(intentOrderNo);
        if(CollUtil.isEmpty(result)){
            return Collections.emptyList();
        }
        List<String> paymentTerms = result.stream().map(ContractIntentOrderQueryResDTO::getPaymentTerms).distinct().toList();
        Map<String, String> optionMap = getOptionValue2desc(OptionGroupConstant.PAYMENT_TERM, paymentTerms);
        result.forEach(e->{
            e.setTax(getTaxText(e.getTax()));
            e.setPaymentTermsText(optionMap.get(e.getPaymentTerms()));
            e.setContractStatusText(ContractStatusEnum.getDescByCode(e.getContractStatus()));
        });
        return result;
    }

    private String getTaxText(String tax) {
        if (ObjectUtil.isNotEmpty(tax)) {
            return tax + "%";
        }
        return null;
    }

    private Map<String, String> getOptionValue2desc(String optionGroup, List<String> values) {
        if (ObjectUtil.isEmpty(values)) {
            return Collections.emptyMap();
        }
        Result<List<OptionItemResDTO>> batchOptionItemByValueResult = optionFeign.getBatchOptionItemByValue(values);
        if (ObjectUtil.isEmpty(batchOptionItemByValueResult.getData())) {
            return Collections.emptyMap();
        }
        return batchOptionItemByValueResult.getData().stream().filter(e -> e.getOptionGroup().equals(optionGroup)).collect(Collectors.toMap(OptionItemResDTO::getOptionValue, OptionItemResDTO::getOptionDesc));
    }

}
