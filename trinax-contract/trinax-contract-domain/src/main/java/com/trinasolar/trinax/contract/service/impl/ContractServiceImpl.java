package com.trinasolar.trinax.contract.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.trinasolar.trinax.basic.api.EmailFeign;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.dto.input.email.EmailReqDTO;
import com.trinasolar.trinax.basic.dto.input.email.SendEmailByExchangeDTO;
import com.trinasolar.trinax.basic.dto.output.email.EmailResDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.cart.dto.input.*;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.contract.constants.ContractConstants;
import com.trinasolar.trinax.contract.constants.enums.*;
import com.trinasolar.trinax.contract.dto.input.*;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverCancelContractDTO;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverCancelDTO;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverCancelItemDTO;
import com.trinasolar.trinax.contract.dto.input.contract.DeliverContractItemDTO;
import com.trinasolar.trinax.contract.dto.input.contract.sales.*;
import com.trinasolar.trinax.contract.dto.input.statistic.ApprovedUnCountersignedContractReqDTO;
import com.trinasolar.trinax.contract.dto.input.statistic.StatisticContractMonthMoneyReqDTO;
import com.trinasolar.trinax.contract.dto.input.statistic.StatisticContractMwReqDTO;
import com.trinasolar.trinax.contract.dto.mq.ContractMessageMqDTO;
import com.trinasolar.trinax.contract.dto.output.*;
import com.trinasolar.trinax.contract.dto.output.contract.ContractAppPcCommonBusinessItemDTO;
import com.trinasolar.trinax.contract.dto.output.contract.DeliverChangeItemResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.DeliverChangeItemResult;
import com.trinasolar.trinax.contract.dto.output.contract.DeliverItemContractItemDTO;
import com.trinasolar.trinax.contract.dto.output.contract.frame.file.SalesContractExResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ApprovedUnCountersignedContractDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CoBusinessResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.ContractTrendResDTO;
import com.trinasolar.trinax.contract.dto.output.contract.sales.CountersignedUnDeliveryContractDTO;
import com.trinasolar.trinax.contract.dto.output.file.ContractFile;
import com.trinasolar.trinax.contract.dto.output.statistic.*;
import com.trinasolar.trinax.contract.manager.ContractCustomerSigningStatusManager;
import com.trinasolar.trinax.contract.manager.caffeine.ContractCache;
import com.trinasolar.trinax.contract.repository.atomicservice.*;
import com.trinasolar.trinax.contract.repository.mapper.ContractBusinessItemMapper;
import com.trinasolar.trinax.contract.repository.mapper.ContractDeliveryAgreementMapper;
import com.trinasolar.trinax.contract.repository.mapper.ContractMapper;
import com.trinasolar.trinax.contract.repository.po.*;
import com.trinasolar.trinax.contract.service.ContractBusinessItemService;
import com.trinasolar.trinax.contract.service.ContractFrameService;
import com.trinasolar.trinax.contract.service.ContractService;
import com.trinasolar.trinax.contract.service.biz.*;
import com.trinasolar.trinax.contract.service.biz.distributeTask.DistributeTaskInfoBiz;
import com.trinasolar.trinax.contract.service.biz.file.SignFileBiz;
import com.trinasolar.trinax.contract.service.biz.message.*;
import com.trinasolar.trinax.contract.service.biz.sales.ContractTrendBiz;
import com.trinasolar.trinax.contract.service.biz.sales.ItemChangeBiz;
import com.trinasolar.trinax.contract.utils.AESUtil;
import com.trinasolar.trinax.contract.utils.IdGenerator;
import com.trinasolar.trinax.delivery.constants.enums.BpmStatusEnum;
import com.trinasolar.trinax.integration.api.IntegrationContractFeign;
import com.trinasolar.trinax.integration.dto.input.contract.SfSyncContractReqDTO;
import com.trinasolar.trinax.integration.dto.input.oa.SaleContractToOaReqDTO;
import com.trinasolar.trinax.integration.dto.output.contract.ContractChangeItemResDTO;
import com.trinasolar.trinax.integration.dto.output.contract.SfSyncContractDTO;
import com.trinasolar.trinax.intentorder.constants.IntentOrderConstant;
import com.trinasolar.trinax.intentorder.constants.enums.BusinessChanceChannelEnum;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderMessageSituationEnum;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderStatusEnum;
import com.trinasolar.trinax.intentorder.dto.input.IntentOrderPCQueryReqDTO;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.dto.output.IntentOrderDetailResDTO;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.intentorder.service.IntentOrderService;
import com.trinasolar.trinax.intentorder.service.biz.message.ContractPartnerOperateMessageBiz;
import com.trinasolar.trinax.partner.api.EnterpriseBizRelationFeign;
import com.trinasolar.trinax.partner.constants.enums.EnterpriseTypeEnum;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.requireorder.manager.TodoCompleteManager;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 合同基础信息 服务实现类
 * </p>
 */
@Slf4j
@Service
public class ContractServiceImpl implements ContractService {


    @Autowired
    ContractStaModifyBiz contractStaModifyBiz;

    @Autowired
    ContractAddBiz contractAddBiz;

    @Autowired
    private ContractBusinessItemMapperService contractBusinessItemMapperService;

    @Autowired
    private ContractBusinessItemSnapshotMapperService contractBusinessItemSnapshotMapperService;

    @Autowired
    private ContractFileMapperService contractFileMapperService;

    @Autowired
    private ContractMapperService contractMapperService;

    @Autowired
    private ContractMapper contractMapper;


    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private LockDoubleSignedMessageBiz lockDoubleSignedMessageBiz;

    @Autowired
    private FrameLockDoubleMessageBiz frameLockDoubleMessageBiz;

    @Autowired
    private FrameLockSingleMessageBiz frameLockSingleMessageBiz;

    @Autowired
    private FrameUnApproveMessageBiz frameUnApproveMessageBiz;

    @Autowired
    private SingleSignedMessageBiz singleSignedMessageBiz;

    @Autowired
    private UnApproveMessageBiz unApproveMessageBiz;

    @Autowired
    private ContractFillBiz contractFillBiz;

    @Autowired
    private ContractDeliveryFillBiz contractDeliveryFillBiz;

    @Autowired
    private ContractAuthBiz contractAuthBiz;

    @Autowired
    private ContractShipmentBiz contractShipmentBiz;

    @Autowired
    private ContractBusinessMapperService contractBusinessMapperService;

    @Autowired
    private ContractCustomerSigningStatusManager contractCustomerSigningStatusManager;

    @Autowired
    private ContractCache contractCache;

    @Autowired
    private ContractFrameMapperService contractFrameMapperService;

    @Autowired
    private ContractFrameService contractFrameService;

    @Autowired
    private StatisticQueryBiz statisticQueryBiz;

    @Autowired
    private ContractBusinessItemMapper contractBusinessItemMapper;

    @Autowired
    private StatisticSummaryBiz statisticSummaryBiz;

    @Autowired
    private ContractReportBiz contractReportBiz;

    @Autowired
    private StatisticFillBiz statisticFillBiz;
    @Autowired
    ItemChangeBiz itemChangeBiz;
    @Autowired
    SignFileBiz signFileBiz;

    @Autowired
    EnterpriseBizRelationFeign enterpriseBizRelationFeign;

    @Autowired
    private ContractQueryByIntentOrderBiz contractQueryByIntentOrderBiz;
    @Autowired
    ContractTrendBiz contractTrendBiz;
    @Autowired
    ContractDeliveryAgreementMapper contractDeliveryAgreementMapper;

    @Autowired
    private EmailFeign emailFeign;

    @Autowired
    private ContractDeliveryAgreementMapperService contractDeliveryAgreementMapperService;

    @Autowired
    IntentOrderService intentOrderService;

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    QuickOrderContractBiz quickOrderContractBiz;

    @Autowired
    private IntegrationContractFeign integrationContractFeign;

    @Autowired
    private MqManager mqManager;
    @Autowired
    private ContractBpmLogMapperService contractBpmlogMapperService;

    @Autowired
    private TodoCompleteManager todoCompleteManager;

    @Autowired
    private ContractPartnerOperateMessageBiz contractPartnerOperateMessageBiz;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IdGenerator idGenerator;

    @Autowired
    DistributeTaskInfoBiz distributeTaskInfoBiz;

    @Autowired
    DistributeTaskInfoMapperService distributeTaskInfoMapperService;

    @Autowired
    ContractBusinessItemService contractBusinessItemService;

    @Autowired
    private Executor ioTaskExecutor;

    /**
     * 发货接口需要从合同接口批量拿合同信息
     *
     * @param contractIds 合同id列表
     */
    @Override
    public List<ContractDeliveryResDTO> deliveryListByContractIds(List<String> contractIds) {
        if (ObjectUtil.isEmpty(contractIds)) {
            // 不传合同id就返回空
            return Collections.emptyList();
        }
        // 查询contract信息
        List<ContractPO> contractPOS = contractMapperService.listByContractIds(contractIds);
        return contractFillBiz.convert2ContractDeliveryResDTO(contractPOS);
    }

    public ContractDeliveryDetailResDTO qryListByContractIds(List<String> contractIds, List<String> contractBusinessItemIds) {
        CompletableFuture<List<ContractDeliveryResDTO>> deliveryListFuture = CompletableFuture.supplyAsync(() ->
                deliveryListByContractIds(contractIds), ioTaskExecutor);

        CompletableFuture<List<ContractDeliveryItemResDTO>> itemListFuture = CompletableFuture.supplyAsync(() ->
                contractBusinessItemService.deliveryListItemByContractItemIds(contractBusinessItemIds), ioTaskExecutor);

        CompletableFuture<List<ContractDetailResAppDTO>> detailListFuture = CompletableFuture.supplyAsync(() ->
                contractIds.stream().map(id -> contractDetailResApp(id, true)).toList(), ioTaskExecutor);

        // 等待所有任务完成并设置结果
        return CompletableFuture.allOf(deliveryListFuture, itemListFuture, detailListFuture)
                .thenApply(v -> {
                    ContractDeliveryDetailResDTO dto = new ContractDeliveryDetailResDTO();
                    dto.setContractDeliveryResDTOs(deliveryListFuture.join());
                    dto.setContractDeliveryItemResDTOs(itemListFuture.join());
                    dto.setContractDetailResAppDTOs(detailListFuture.join());
                    return dto;
                }).join();
    }

    @Override
    public Result<String> modifyContractSta(List<ContactStaModifyReqDTO> reqDTO) {
        if (ObjectUtil.isNotEmpty(reqDTO)) {
            ContactStaModifyReqDTO contactStaModifyReqDTO = reqDTO.get(0);
            ContractFramePO contractFramePO = contractFrameMapperService.getByBpmTaskId(contactStaModifyReqDTO.getSn());
            if (ObjectUtil.isNotNull(contractFramePO)) {
                // 走框架合同逻辑
                contractFrameService.modifyContractStatus(contactStaModifyReqDTO);
                return Result.ok();
            }
        }
        // 走销售合同逻辑
        return contractStaModifyBiz.modifyContractSta(reqDTO);
    }

    @Override
    public Result<String> addContract(ContractInfoReqDTO reqDTO) {
        return contractAddBiz.addContract(reqDTO);
    }

    /**
     * 合同列表-app端分页（权限过滤）
     */
    @Override
    public PageResponse<ContractResAppDTO> pageContractResAppAuth(PageRequest<ContractQueryAppReqDTO> pageReqDTO) {
        ContractQueryAppReqDTO query = pageReqDTO.getQuery();

        //加载权限缓存
        ContractQueryAuthCacheDTO authCache = authInit(query);
        if (!authCache.getAuth()) {
            return PageResponse.empty(pageReqDTO);
        }
        return pageContractResApp(pageReqDTO);

    }

    @Override
    public List<SalesContractExResDTO> downSalesContract(ContractQueryAppReqDTO req) {

        //加载权限缓存
        ContractQueryAuthCacheDTO authCache = authInit(req);
        if (!authCache.getAuth()) {
            return new ArrayList<>();
        }

        List<ContractResAppDTO> contractResAppDTOS = contractMapper.downSalesContract(req);

        if (ObjectUtil.isNotEmpty(contractResAppDTOS)) {
            contractFillBiz.fillContractAppDTOWithAsync(contractResAppDTOS);
        }
        return BeanUtil.copyToList(contractResAppDTOS, SalesContractExResDTO.class);
    }

    private ContractQueryAuthCacheDTO authInit(ContractQueryAppReqDTO req) {

        String key = ContractConstants.USER_CONTRACT_AUTH + req.getUserID();
        // 先从缓存中取 权限缓存
        ContractQueryAuthCacheDTO cacheVal = contractAuthBiz.setToQuery(req, key);
        if (ObjectUtil.isNull(cacheVal)) {
            // 没有就从数据库查询
            boolean auth = contractAuthBiz.handleAuth(req);
            cacheVal = new ContractQueryAuthCacheDTO();
            cacheVal.setAuth(auth);
            // 将权限结果设置到缓存中
            contractAuthBiz.setToCache(req, cacheVal, key);
        }
        return cacheVal;
    }

    /**
     * 合同列表-app端分页（权限过滤+缓存）
     */
    @Override
    public PageResponse<ContractResAppDTO> pageContractResAppAuthWithCache(PageRequest<ContractQueryAppReqDTO> pageReqDTO) {
        String loginUserId = pageReqDTO.getQuery().getUserID();
        PageResponse<ContractResAppDTO> pageResponse = contractCache.getPage(loginUserId, pageReqDTO);
        if (ObjectUtil.isNotNull(pageResponse)) {
            return pageResponse;
        }
//        log.info("contract分页请求未命中缓存");
        pageResponse = pageContractResAppAuth(pageReqDTO);
        contractCache.putPage(loginUserId, pageReqDTO, pageResponse);
        return pageResponse;
    }

    private PageResponse<ContractResAppDTO> pageContractResApp(PageRequest<ContractQueryAppReqDTO> pageReqDTO) {
        LocalDateTime start = LocalDateTime.now();
        ContractQueryAppReqDTO query = pageReqDTO.getQuery();
        if(!CollectionUtils.isEmpty(query.getContractStatusList())&&query.getContractStatusList().contains(ContractStatusEnum.APPROVING.getCode())){
            //审批中的增加提交OA失败的状态
            query.getContractStatusList().add(ContractStatusEnum.OA_FAILED.getCode());
        }
        IPage<ContractResAppDTO> contractResAppDTOIPage = contractMapper.pageContractResApp(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), query);
        log.info("合同列表-分页查询耗时{}毫秒", Duration.between(start, LocalDateTime.now()).toMillis());

        List<ContractResAppDTO> contractResAppDTOS = contractResAppDTOIPage.getRecords();
        if (ObjectUtil.isNotEmpty(contractResAppDTOS)) {
            contractFillBiz.fillContractAppDTOWithAsync(contractResAppDTOS);
        }
        contractResAppDTOS.forEach(contractResAppDTO -> {
            contractFillBiz.mergeContractBusinessItem(contractResAppDTO.getContractItemResAppDTOS(),null);
            List<ContractItemResAppDTO>  parentContractItems=contractResAppDTO.getContractItemResAppDTOS().stream().filter(item->ObjectUtil.equals(item.getSfParentItemId(),"0")).toList();
            //查询一级节点的快照信息，如果存在快照，需要使用快照来替换片数和MW数
            contractFillBiz.fillParentItemsBySnapshot(parentContractItems);
            fillContractSummary(contractResAppDTO,parentContractItems);
            //只保留一级节点
            contractResAppDTO.setContractItemResAppDTOS(parentContractItems);
        });

        return PageResponse.toResult(
                pageReqDTO,
                (int) contractResAppDTOIPage.getTotal(),
                contractResAppDTOS);
    }


    /**
     * 对MW和总价重新计算
     * @param contractDetailResAppDTO
     * @param parentContractItems
     */
    private void fillContractSummary(ContractResAppDTO contractDetailResAppDTO, List<ContractItemResAppDTO> parentContractItems) {
        contractDetailResAppDTO.setTotalVolumeMw(parentContractItems.stream().map(item->item.getQuantityMw()).reduce(BigDecimal.ZERO,BigDecimal::add));
        contractDetailResAppDTO.setTotalAmountRmb(parentContractItems.stream().map(item->item.getUnitPriceW().multiply(item.getQuantityMw()).multiply(BigDecimal.valueOf(1e6))).reduce(BigDecimal.ZERO,BigDecimal::add));
    }

    /**
     * 合同列表-pc端分页（权限过滤）
     */
    @Override
    public PageResponse<ContractResPcDTO> pageContractResPcAuth(PageRequest<ContractQueryPcReqDTO> pageReqDTO) {
        ContractQueryPcReqDTO query = pageReqDTO.getQuery();
        boolean auth = contractAuthBiz.handleAuth(query);
        if (!auth) {
            return PageResponse.empty(pageReqDTO);
        }
        return pageContractResPc(pageReqDTO);
    }

    /**
     * 合同列表-pc端分页
     */
    @Override
    public PageResponse<ContractResPcDTO> pageContractResPc(PageRequest<ContractQueryPcReqDTO> pageReqDTO) {
        IPage<ContractResPcDTO> contractResPcDTOIPage = contractMapper.pageContractResPc(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), pageReqDTO.getQuery());
        List<ContractResPcDTO> contractResPcDTOS = contractResPcDTOIPage.getRecords();
        if (ObjectUtil.isNotEmpty(contractResPcDTOS)) {
            contractFillBiz.fillContractPcDTO(contractResPcDTOS);
        }
        return PageResponse.toResult(
                pageReqDTO,
                (int) contractResPcDTOIPage.getTotal(),
                contractResPcDTOS);
    }

    @Override
    public PageResponse<ContractResAppDTO> pageChangeContractResApp(PageRequest<ChangeContractQueryAppReqDTO> pageReqDTO) {
        ChangeContractQueryAppReqDTO query = pageReqDTO.getQuery();
        // contractId和intentOrderNo如果都没传就返回空
        if (ObjectUtil.isEmpty(query.getContractId()) && ObjectUtil.isEmpty(query.getIntentOrderNo())) {
            return PageResponse.empty(pageReqDTO);
        }
        ContractQueryAppReqDTO contractQueryAppReqDTO = BeanUtil.toBean(query, ContractQueryAppReqDTO.class);
        // 查询子合同（变更合同）-查询变更合同列表
        if (ObjectUtil.isNotEmpty(query.getContractId())) {
            ContractPO contractPO = contractMapperService.findByContractId(query.getContractId());
            List<ContractPO> contractPOS = contractMapperService.listBySfLastContractNos(Lists.newArrayList(contractPO.getSfContractNo()));
            if (ObjectUtil.isEmpty(contractPOS)) {
                return PageResponse.empty(pageReqDTO);
            }
            List<String> contractIds = contractPOS.stream().map(ContractPO::getContractId).toList();
            contractQueryAppReqDTO.setContractIdList(contractIds);
        }
        PageRequest<ContractQueryAppReqDTO> pageReqDTO2 = new PageRequest<>();
        pageReqDTO2.setIndex(pageReqDTO.getIndex());
        pageReqDTO2.setSize(pageReqDTO.getSize());
        pageReqDTO2.setQuery(contractQueryAppReqDTO);
        return pageContractResApp(pageReqDTO2);
    }

    @Override
    public ContractDetailResAppDTO contractDetailResApp(String contractId,Boolean isDeliver) {
        ContractResAppDTO contractResAppDTO = contractMapper.findResAppByContractId(contractId);
        // 填充属性
        return contractFillBiz.convert2ContractDetailResAppDTO(contractResAppDTO,isDeliver);
    }


    /**
     * 查询合同列表
     */
    @Override
    public List<ContractFileResDTO> queryContractFile(String contractId) {
        return contractFileMapperService.listByContractIds(Collections.singletonList(contractId));
    }

    @Override
    public ContractDetailResPcDTO contractDetailResPc(ContractQueryPcDetailReqDTO reqDTO) {
        boolean auth = contractAuthBiz.handleAuth(reqDTO);
        if (!auth) {
            throw new BizException(ResultCode.FAIL.getCode(), "数据不存在或无权限");
        }
        ContractDetailResPcDTO contractDetailResPcDTO = contractMapper.contractDetailResPc(reqDTO);
        if (Objects.isNull(contractDetailResPcDTO)) {
            throw new BizException(ResultCode.FAIL.getCode(), "数据不存在或无权限");
        }
        contractFillBiz.fillContractPcDTO(Lists.newArrayList(contractDetailResPcDTO));

        List<ContractFileResDTO> contractFileResDTOS = contractFileMapperService.listByContractIds(Lists.newArrayList(reqDTO.getContractId()));
        List<ContractBusinessItemPO> contractBusinessItemPOS = contractBusinessItemMapperService.listByContractId(reqDTO.getContractId());

        List<ContractItemResPcDTO> contractItemResPcDTOS = contractFillBiz.convert2ContractItemResPcDTO(contractBusinessItemPOS, contractDetailResPcDTO.getSfRecordId());
        contractFillBiz.fillContractDetailResPcDTO(contractDetailResPcDTO);
        contractDetailResPcDTO.setContractFileResDTOS(contractFileResDTOS);
        if (StringUtils.isNotBlank(contractDetailResPcDTO.getOpenContract())) {
            String[] openContract = contractDetailResPcDTO.getOpenContract().split(";");

            StringBuilder openContractDesc = new StringBuilder();
            for (int i = 0; i < openContract.length; i++) {
                openContractDesc.append(OpenContractEnum.getDescByCode(openContract[i])).append(";");
            }
            String openContractText = openContractDesc.substring(0, openContractDesc.length() - 1).toString();

            contractDetailResPcDTO.setOpenContractText(openContractText);
        }
        contractDetailResPcDTO.setCustomerCategoryText(EnterpriseTypeEnum.getDescByCode(contractDetailResPcDTO.getCustomerCategory()));

        contractDetailResPcDTO.setContractItemResPcDTOS(contractItemResPcDTOS);

        List<ContractBusinessItemSnapshotPO> snapshotPOS = contractBusinessItemSnapshotMapperService.listByContractId(reqDTO.getContractId());
        List<ContractItemResPcDTO> snapshotDtos = contractFillBiz.convert2ContractItemResPcDTO2(snapshotPOS, contractDetailResPcDTO.getSfRecordId());
        contractDetailResPcDTO.setContractItemSnapResPcDTOS(snapshotDtos);

        contractDetailResPcDTO.setSignEntity(StringUtils.isEmpty(contractDetailResPcDTO.getCapitalEnterpriseName()) ? contractDetailResPcDTO.getEnterpriseName() : contractDetailResPcDTO.getCapitalEnterpriseName());
        return contractDetailResPcDTO;
    }

    @Override
    public Result<ContractResPcDTO> queryContactById(String contractId) {
        ContractPO contract = contractMapperService.lambdaQuery()
                .eq(ContractPO::getContractId, contractId).one();
        return Result.ok(BeanUtil.toBean(contract, ContractResPcDTO.class));
    }


    @Override
    public void sendMessageForContract(ContractMessageMqDTO req) {
        if (ContractSituationEnum.CONTRACT_SINGLE_SIGNED.getCode().equals(req.getSituationType())) {
            singleSignedMessageBiz.execute(req);
        } else if (ContractSituationEnum.CONTRACT_UN_APPROVE.getCode().equals(req.getSituationType())) {
            unApproveMessageBiz.execute(req);
        } else if (ContractSituationEnum.CONTRACT_LOCK_SIGNED.getCode().equals(req.getSituationType())) {
            lockDoubleSignedMessageBiz.execute(req);
        } else if (ContractSituationEnum.FRAME_CONTRACT_LOCK_SINGLE.getCode().equals(req.getSituationType())) {
            frameLockSingleMessageBiz.execute(req);
        } else if (ContractSituationEnum.FRAME_CONTRACT_LOCK_DOUBLE.getCode().equals(req.getSituationType())) {
            frameLockDoubleMessageBiz.execute(req);
        } else if (ContractSituationEnum.FRAME_CONTRACT_UN_APPROVE.getCode().equals(req.getSituationType())) {
            frameUnApproveMessageBiz.execute(req);
        } else if (ContractSituationEnum.CONTRACT_CONFIG_MULTI_POWER.getCode().equals(req.getSituationType())) {
            singleSignedMessageBiz.executeConfig(req);
        } else {
            log.info("合同消息场景类型不存在");
        }
    }

    @Override
    public List<ContractResPcExcelDTO> listContractResPcExcelAuth(ContractQueryPcReqDTO reqDTO) {
        boolean auth = contractAuthBiz.handleAuth(reqDTO);
        if (!auth) {
            return Collections.emptyList();
        }
        return listContractResPcExcel(reqDTO);
    }

    @Override
    public List<ContractResPcExcelDTO> listContractResPcExcel(ContractQueryPcReqDTO reqDTO) {
        List<ContractResPcDTO> contractResPcDTOS = contractMapper.listContractResPc(reqDTO);
        if (ObjectUtil.isNotEmpty(contractResPcDTOS)) {
            contractFillBiz.fillContractPcDTO(contractResPcDTOS);
        }
        return BeanUtil.copyToList(contractResPcDTOS, ContractResPcExcelDTO.class, CopyOptions.create().setFieldValueEditor((fieldName, fieldValue) -> {
            if (ContractConstants.CONTRACT_REBATE.equals(fieldName)) {
                if (Boolean.parseBoolean((String) fieldValue)) {
                    return "是";
                } else {
                    return "否";
                }

            } else {
                return fieldValue;
            }
        }));
    }

    @Override
    public ContractShipmentResDTO contractReleaseRes(String contractId) {
        ContractPO contractPO = contractMapperService.findByContractId(contractId);
        List<ContractReleaseItemResDTO> contractReleaseItemResDTOS = contractShipmentBiz.getContractReleaseItemResDTO(contractPO.getSfContractFamilyNo());
        ContractBusinessPO contractBusinessPO = contractBusinessMapperService.findByContractId(contractId);
        return ContractShipmentResDTO.builder()
                .sfContractNo(contractPO.getSfContractNo())
                .contractId(contractPO.getContractId())
                .counterSignedDate(contractPO.getCounterSignedDate())
                .counterSignedDate02(ObjectUtil.isNotNull(contractPO.getCounterSignedDate()) ? contractPO.getCounterSignedDate().toLocalDate() : null)
                .enterpriseName(contractPO.getEnterpriseName())
                .totalVolumeMw(contractBusinessPO.getTotalVolumeMw())
                .contractReleaseItemResDTOS(contractReleaseItemResDTOS)
                .incoterm(contractBusinessPO.getIncoterm())
                .build();
    }

    @Override
    public List<ContractShipmentTransportResDTO> contractShipmentTransportRes(String contractId, String erpSo) {
        ContractPO contractPO = contractMapperService.findByContractId(contractId);
        return contractShipmentBiz.contractShipmentTransportRes(contractPO.getSfContractFamilyNo(), erpSo);
    }

    /**
     * 根据意向单号获取客户签收状态
     */
    @Override
    public Double getCustomerSigningStatus(String intentOrderNo) {
        return contractCustomerSigningStatusManager.getCustomerSigningStatus(intentOrderNo);
    }

    /**
     * 合同列表查询-发货申请需要
     */
    @Override
    public List<ContractDeliveryResAppDTO> deliveryContractResApp(ContractDeliveryQueryReqDTO reqDTO) {
        if(CollUtil.isEmpty(reqDTO.getContractIds())){
            //如果合同id为空，则说明是bff直接请求数据，需要鉴权
            boolean auth = contractAuthBiz.handleDeliveryAuth(reqDTO);
            if (!auth) {
                log.info("deliveryContractResApp auth false");
                return Collections.emptyList();
            }
        }

        List<ContractDeliveryResAppDTO> contractDeliveryResAppDTOS = contractMapper.listDeliveryContractResApp(reqDTO);
        log.info("contractDeliveryResAppDTOS: {}", contractDeliveryResAppDTOS);
        if (ObjectUtil.isNotEmpty(contractDeliveryResAppDTOS)) {
            contractDeliveryFillBiz.fillContractDeliveryResAppDTO(contractDeliveryResAppDTOS, reqDTO.getDeliverNo());
            contractDeliveryResAppDTOS.forEach(contractDeliveryResAppDTO -> {
                contractFillBiz.mergeContractBusinessItem(contractDeliveryResAppDTO.getContractItemDeliveryResAppDTOS(),null);
                //只保留一级节点
                contractDeliveryResAppDTO.setContractItemDeliveryResAppDTOS(contractDeliveryResAppDTO.getContractItemDeliveryResAppDTOS().stream().filter(item->ObjectUtil.equals(item.getSfParentItemId(),"0")).toList());
            });

            log.info("发货接口合同列表查询—合同数量: {}", contractDeliveryResAppDTOS.size());
            List<ContractDeliveryResAppDTO> result = Lists.newArrayList();
            contractDeliveryResAppDTOS.forEach(e -> {
                boolean notEmpty = ObjectUtil.isNotEmpty(e.getContractItemDeliveryResAppDTOS());
                if (notEmpty) {
                    // 如果查询参数不满足则过滤掉
                    if (ObjectUtil.isNotEmpty(reqDTO.getQueryP())) {
                        // 转成小写
                        String lowerCaseQueryP = reqDTO.getQueryP().toLowerCase();
                        if (!(e.getSfContractNo().toLowerCase().contains(lowerCaseQueryP) || e.getContractItemDeliveryResAppDTOS().stream().anyMatch(i -> i.getProductName().toLowerCase().contains(lowerCaseQueryP)))) {
                            log.info("发货接口合同列表查询—未查到符合查询参数的产品，合同id: {}", e.getContractId());
                            return;
                        }
                    }
                    result.add(e);
                } else {
                    log.info("发货接口合同列表查询—未查到符合条件的产品，合同id: {}", e.getContractId());
                }
            });
            return result;
        }
        return Collections.emptyList();
    }

    /**
     * 合同列表查询-发货申请需要
     */
    @Override
    public List<ContractDeliveryResAppDTO> deliveryContractForManagement(ContractDeliveryQueryReqDTO reqDTO) {
//        boolean auth = contractAuthBiz.handleManagementAuth(reqDTO);
        boolean auth = contractAuthBiz.handleDeliveryAuth(reqDTO);
        if (!auth) {
            return Collections.emptyList();
        }
//        List<ContractDeliveryResAppDTO> contractDeliveryResAppDTOS = contractMapper.listDeliveryContractForManagement(reqDTO);
        List<ContractDeliveryResAppDTO> contractDeliveryResAppDTOS = contractMapper.listDeliveryContractResApp(reqDTO);
        log.info("管理端-contractDeliveryResAppDTOS {}", contractDeliveryResAppDTOS);
        if (ObjectUtil.isNotEmpty(contractDeliveryResAppDTOS)) {
            contractDeliveryFillBiz.fillContractDeliveryResAppDTO(contractDeliveryResAppDTOS, reqDTO.getDeliverNo());
            contractDeliveryResAppDTOS.forEach(contractDeliveryResAppDTO -> {
                contractFillBiz.mergeContractBusinessItem(contractDeliveryResAppDTO.getContractItemDeliveryResAppDTOS(),null);
                //只保留一级节点
                contractDeliveryResAppDTO.setContractItemDeliveryResAppDTOS(contractDeliveryResAppDTO.getContractItemDeliveryResAppDTOS().stream().filter(item->ObjectUtil.equals(item.getSfParentItemId(),"0")).toList());
            });

            log.info("管理端-发货接口合同列表查询—合同数量: {}", contractDeliveryResAppDTOS.size());
            List<ContractDeliveryResAppDTO> result = Lists.newArrayList();
            contractDeliveryResAppDTOS.forEach(e -> {
                boolean notEmpty = ObjectUtil.isNotEmpty(e.getContractItemDeliveryResAppDTOS());
                if (notEmpty) {
                    // 如果查询参数不满足则过滤掉
                    if (ObjectUtil.isNotEmpty(reqDTO.getQueryP())) {
                        // 转成小写
                        String lowerCaseQueryP = reqDTO.getQueryP().toLowerCase();
                        if (!(e.getSfContractNo().toLowerCase().contains(lowerCaseQueryP) || e.getContractItemDeliveryResAppDTOS().stream().anyMatch(i -> i.getProductName().toLowerCase().contains(lowerCaseQueryP)))) {
                            log.info("管理端-发货接口合同列表查询—未查到符合查询参数的产品，合同id: {}", e.getContractId());
                            return;
                        }
                    }
                    result.add(e);
                } else {
                    log.info("管理端-发货接口合同列表查询—未查到符合条件的产品，合同id: {}", e.getContractId());
                }
            });
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public List<ContractResPc02DTO> listContractByIntentOrderNo(String intentOrderNo) {
        List<ContractResPc02DTO> contractResPc02DTOS = contractMapper.listContractByIntentOrderNo(intentOrderNo);
        if (ObjectUtil.isNotEmpty(contractResPc02DTOS)) {
            contractFillBiz.fillContractResPc02DTO(contractResPc02DTOS);
        }
        return contractResPc02DTOS;
    }

    @Override
    public void cleanCaffeineCache(ContractMessageMqDTO req) {
        if (Objects.isNull(req)) {
            return;
        }
        if (StringUtils.isNotBlank(req.getExternalUserId())) {
            contractCache.clearByUserId(req.getExternalUserId());
        }
//        if (StringUtils.isNotBlank(req.getSalesInternalUserId())) {
//            contractCache.clearByUserId(req.getSalesInternalUserId());
//        }
//        if (StringUtils.isNotBlank(req.getOperationInternalUserId())) {
//            contractCache.clearByUserId(req.getOperationInternalUserId());
//        }
        List<String> userIds = sysUserFeign.listClearUserIds(Stream.of(req.getSalesInternalUserId(), req.getOperationInternalUserId()).filter(ObjectUtil::isNotEmpty).toList());
        contractCache.clearByUserIds(userIds);
    }

    @Override
    public List<StatisticContractMwRespDTO> statisticContractMw(StatisticContractMwReqDTO reqDTO) {
        statisticQueryBiz.handleStatisticContractMwReqDTO(reqDTO);
        List<StatisticContractMwRespDTO> statisticContractMwRespDTOS;
        if (ObjectUtil.isNotEmpty(reqDTO.getCustomerCategory())) {
            statisticContractMwRespDTOS = statisticQueryBiz.statisticContractMwWithCustomerCategory(reqDTO);
        } else {
            statisticContractMwRespDTOS = contractBusinessItemMapper.statisticContractMw(reqDTO);
        }

        return statisticContractMwRespDTOS.stream().sorted(Comparator.comparing(StatisticContractMwRespDTO::getNum).reversed()).toList();
    }

    @Override
    public List<StatisticContractMwListDTO> statisticContractMwList(StatisticContractMwReqDTO reqDTO) {
        statisticQueryBiz.handleStatisticContractMwReqDTO(reqDTO);
        List<StatisticContractMwItem> statisticContractMwItems;
        if (ObjectUtil.isNotEmpty(reqDTO.getCustomerCategory())) {
            statisticContractMwItems = statisticQueryBiz.statisticContractMwExportWithCustomerCategory(reqDTO);
        } else {
            statisticContractMwItems = contractBusinessItemMapper.statisticContractMwExport(reqDTO);
        }
        return statisticSummaryBiz.summaryStatisticContractMwListDTO(statisticContractMwItems);
    }

    @Override
    public List<StatisticContractMonthMoneyRespDTO> statisticContractMonthMoney(StatisticContractMonthMoneyReqDTO reqDTO) {
        statisticQueryBiz.handleStatisticContractMonthMoneyReqDTO(reqDTO);
        List<StatisticContractMonthMoneyDbItem> statisticContractMonthMoneyDbItems;
        if (ObjectUtil.isNotEmpty(reqDTO.getCustomerCategory())) {
            statisticContractMonthMoneyDbItems = statisticQueryBiz.statisticContractMonthMoneyWithCustomerCategory(reqDTO);
        } else {
            statisticContractMonthMoneyDbItems = contractBusinessItemMapper.statisticContractMonthMoney(reqDTO);
        }
        List<StatisticContractMonthMoneyRespDTO> result = statisticSummaryBiz.summaryStatisticContractMonthMwRespDTO(reqDTO, statisticContractMonthMoneyDbItems);

        return result.stream()
                .sorted(Comparator.comparing(StatisticContractMonthMoneyRespDTO::getBizOrganizationName))
                .toList();
    }

    @Override
    public List<StatisticContractMonthMoneyListDTO> statisticContractMonthMoneyList(StatisticContractMonthMoneyReqDTO reqDTO) {
        statisticQueryBiz.handleStatisticContractMonthMoneyReqDTO(reqDTO);
        List<StatisticContractMonthMoneyDbItem> statisticContractMonthMoneyDbItems;
        if (ObjectUtil.isNotEmpty(reqDTO.getCustomerCategory())) {
            statisticContractMonthMoneyDbItems = statisticQueryBiz.statisticContractMonthMoneyWithCustomerCategory(reqDTO);
        } else {
            statisticContractMonthMoneyDbItems = contractBusinessItemMapper.statisticContractMonthMoney(reqDTO);
        }
        List<StatisticContractMonthMoneyListDTO> result = statisticSummaryBiz.summaryStatisticContractMonthMwListDTO(reqDTO, statisticContractMonthMoneyDbItems);
        return result.stream()
                .sorted(Comparator.comparing(StatisticContractMonthMoneyListDTO::getBizOrganizationName)
                        .thenComparing(StatisticContractMonthMoneyListDTO::getMonth))
                .toList();
    }

    @Override
    public Result<List<ContractReportResDTO>> reportPowerList(ContractReportReqDTO reqDTO) {
        return Result.ok(contractReportBiz.reportPowerList(reqDTO));
    }

    @Override
    public Result<Map<String, List<ContractReportResDTO>>> reportPowerChart(ContractReportReqDTO reqDTO) {
        return Result.ok(contractReportBiz.reportPowerChart(reqDTO));
    }

    @Override
    public PageResponse<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractPage(PageRequest<ApprovedUnCountersignedContractReqDTO> pageReqDTO) {
        ApprovedUnCountersignedContractReqDTO query = pageReqDTO.getQuery();
        statisticQueryBiz.handleApprovedUnCountersignedContractReqDTO(query);
        IPage<ApprovedUnCountersignedContractDTO> ipage = contractMapper.approvedUnCountersignedContractPage(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), query);
        statisticFillBiz.fillApprovedUnCountersignedContractDTO(ipage.getRecords());
        return PageResponse.toResult(
                pageReqDTO,
                (int) ipage.getTotal(),
                ipage.getRecords());
    }

    @Override
    public List<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractList(ApprovedUnCountersignedContractReqDTO reqDTO) {
        statisticQueryBiz.handleApprovedUnCountersignedContractReqDTO(reqDTO);
        List<ApprovedUnCountersignedContractDTO> approvedUnCountersignedContractDTOS = contractMapper.approvedUnCountersignedContractList(reqDTO);
        statisticFillBiz.fillApprovedUnCountersignedContractDTO(approvedUnCountersignedContractDTOS);
        return approvedUnCountersignedContractDTOS;
    }

    @Override
    public PageResponse<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractPage(PageRequest<Integer> pageReqDTO) {
        LocalDate counterSignedDateEnd = LocalDate.now().minusDays(pageReqDTO.getQuery());
        IPage<CountersignedUnDeliveryContractDTO> ipage = contractMapper.countersignedUnDeliveryContractPage(new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize()), counterSignedDateEnd);
        statisticFillBiz.fillCountersignedUnDeliveryContractDTO(ipage.getRecords());
        return PageResponse.toResult(
                pageReqDTO,
                (int) ipage.getTotal(),
                ipage.getRecords());
    }

    @Override
    public List<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractList(int day) {
        LocalDate counterSignedDateEnd = LocalDate.now().minusDays(day);
        List<CountersignedUnDeliveryContractDTO> countersignedUnDeliveryContractDTOS = contractMapper.countersignedUnDeliveryContractList(counterSignedDateEnd);
        statisticFillBiz.fillCountersignedUnDeliveryContractDTO(countersignedUnDeliveryContractDTOS);
        return countersignedUnDeliveryContractDTOS;
    }

    @Override
    public Result<List<ContractFile>> contractFiles(String contractId) {
        return signFileBiz.contractFiles(contractId);
    }

    @Override
    public Result<List<ContractChangeItemResDTO>> contractItemsModify(ContractItemsModifyReqDTO req) {
        return itemChangeBiz.contractItemsModify(req);
    }

    @Override
    public Result<List<ContractIntentOrderQueryResDTO>> getContractByIntentOrderNo(String intentOrderNo) {
        return Result.ok(contractQueryByIntentOrderBiz.getContractByIntentOrderNo(intentOrderNo));
    }



    @Override
    public Result<List<CoBusinessResDTO>> qryContractBusiness(List<String> contractIds) {
        if (ObjectUtil.isEmpty(contractIds)) {
            return Result.ok();
        }

        List<ContractBusinessPO> contractBusinessPOS = contractBusinessMapperService.lambdaQuery().in(ContractBusinessPO::getContractId, contractIds).list();
        if (ObjectUtil.isEmpty(contractBusinessPOS)) {
            contractBusinessPOS = new ArrayList<>();
        }
        return Result.ok(BeanUtil.copyToList(contractBusinessPOS, CoBusinessResDTO.class));
    }

    @Override
    public Result<Void> updateDrSfId(List<DrSfIdUpdateReqDTO> req) {
        req.stream().forEach(a -> {
            contractBusinessMapperService.lambdaUpdate()
                    .set(ContractBusinessPO::getDrSfId,a.getDrSfId())
                    .eq(ContractBusinessPO::getContractId,a.getContractId())
                    .update();
        });
        return Result.ok();
    }

    @Override
    public Result<PageResponse<ContractItemReportResDTO>> reportContractItem(PageRequest<ContractItemReportQueryDTO> pageRequest) {
        ContractItemReportQueryDTO contractItemReportQueryDTO= pageRequest.getQuery();
        if(CollUtil.isNotEmpty(contractItemReportQueryDTO.getBizOrganizationCode())){
            //根据战区查询下属的企业
            Result<List<EnterpriseBizRelationResDTO>> enterPriseResult=enterpriseBizRelationFeign.listByBizOrganizationCodes(contractItemReportQueryDTO.getBizOrganizationCode());
            Assert.isTrue(enterPriseResult.getSuccess(),"根据战区查询企业异常！");
            if(CollUtil.isEmpty(enterPriseResult.getData())){
                PageResponse<ContractItemReportResDTO> pageResponse= PageResponse.toResult(
                        pageRequest,
                        0,
                        new ArrayList<>());
                return Result.ok(pageResponse);
            }else{
                contractItemReportQueryDTO.setEnterpriseIds(enterPriseResult.getData().stream().map(EnterpriseBizRelationResDTO::getEnterpriseId).toList());
            }
        }

        IPage<ContractItemReportResDTO> ipage = contractMapper.reportContractItem(new Page<>(pageRequest.getIndex(), pageRequest.getSize()), contractItemReportQueryDTO);
        statisticFillBiz.fillReportInfoContractDTO(ipage.getRecords());
        PageResponse<ContractItemReportResDTO> pageResponse= PageResponse.toResult(
                pageRequest,
                (int) ipage.getTotal(),
                ipage.getRecords());
        return Result.ok(pageResponse);
    }

    @Override
    public Result<List<ContractTrendResDTO>> contractTrendList(ContractTrendReqDTO req) {

        return contractTrendBiz.contractTrendList(req);
    }

    @Override
    public PageResponse<DeliveryAgreementRespDTO> pageDeliveryAgreement(PageRequest<DeliveryAgreementQueryDTO> pageReqDTO) {
        DeliveryAgreementQueryDTO deliveryAgreementQueryDTO=pageReqDTO.getQuery();
        LambdaQueryWrapper<ContractDeliveryAgreementPO> queryWrapper = new LambdaQueryWrapper<ContractDeliveryAgreementPO>();
        queryWrapper.ge(StringUtils.isNotBlank(deliveryAgreementQueryDTO.getDeliveryDateBegin()),ContractDeliveryAgreementPO::getDeliveryDate, deliveryAgreementQueryDTO.getDeliveryDateBegin())
                .le(StringUtils.isNotBlank(deliveryAgreementQueryDTO.getDeliveryDateEnd()),ContractDeliveryAgreementPO::getDeliveryDate, deliveryAgreementQueryDTO.getDeliveryDateEnd())
                .eq(deliveryAgreementQueryDTO.getIsCancelled()!=null,ContractDeliveryAgreementPO::getIsCancelled,deliveryAgreementQueryDTO.getIsCancelled());
        LambdaQueryWrapper<ContractDeliveryAgreementPO> orQueryWrapper = new LambdaQueryWrapper<ContractDeliveryAgreementPO>();
        if(CollUtil.isNotEmpty(deliveryAgreementQueryDTO.getSaleContractNos())){
            queryWrapper.and(qw->{
                for(String saleContractNo:deliveryAgreementQueryDTO.getSaleContractNos()){
                    qw.or().like(ContractDeliveryAgreementPO::getSaleContractNo,saleContractNo);
                }
            });
        }
        queryWrapper.orderByDesc(ContractDeliveryAgreementPO::getCreatedTime);
        Page<ContractDeliveryAgreementPO> page = new Page<>(pageReqDTO.getIndex(), pageReqDTO.getSize());
        IPage<ContractDeliveryAgreementPO> deliveryPages = contractDeliveryAgreementMapper.selectPage(page, queryWrapper);
        List<DeliveryAgreementRespDTO> list=BeanUtil.copyToList(deliveryPages.getRecords(),DeliveryAgreementRespDTO.class);
        return PageResponse.toResult(pageReqDTO.getIndex(), pageReqDTO.getSize(), (int)deliveryPages.getTotal(), list);
    }

    @Override
    public List<DeliveryAgreementRespDTO> exportDeliveryAgreement(DeliveryAgreementQueryDTO deliveryAgreementQueryDTO) {
        LambdaQueryWrapper<ContractDeliveryAgreementPO> queryWrapper = new LambdaQueryWrapper<ContractDeliveryAgreementPO>();
        queryWrapper.ge(StringUtils.isNotBlank(deliveryAgreementQueryDTO.getDeliveryDateBegin()),ContractDeliveryAgreementPO::getDeliveryDate, deliveryAgreementQueryDTO.getDeliveryDateBegin())
                .le(StringUtils.isNotBlank(deliveryAgreementQueryDTO.getDeliveryDateEnd()),ContractDeliveryAgreementPO::getDeliveryDate, deliveryAgreementQueryDTO.getDeliveryDateEnd())
                .eq(deliveryAgreementQueryDTO.getIsCancelled()!=null,ContractDeliveryAgreementPO::getIsCancelled,deliveryAgreementQueryDTO.getIsCancelled());
        LambdaQueryWrapper<ContractDeliveryAgreementPO> orQueryWrapper = new LambdaQueryWrapper<ContractDeliveryAgreementPO>();
        if(CollUtil.isNotEmpty(deliveryAgreementQueryDTO.getSaleContractNos())){
            queryWrapper.and(qw->{
                for(String saleContractNo:deliveryAgreementQueryDTO.getSaleContractNos()){
                    qw.or().like(ContractDeliveryAgreementPO::getSaleContractNo,saleContractNo);
                }
            });
        }
        List<ContractDeliveryAgreementPO> deliveryList = contractDeliveryAgreementMapper.selectList( queryWrapper);
        List<DeliveryAgreementRespDTO> list=BeanUtil.copyToList(deliveryList,DeliveryAgreementRespDTO.class);
        return list;
    }

    @Override
    public Boolean cancelDeliveryAgreement(List<String> contractIds) {
        if(CollUtil.isEmpty(contractIds)){
            return false;
        }
        LambdaUpdateWrapper<ContractDeliveryAgreementPO> lambdaUpdateWrapper=new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(ContractDeliveryAgreementPO::getContractId,contractIds)
                .set(ContractDeliveryAgreementPO::getIsCancelled,1)
                .set(ContractDeliveryAgreementPO::getUpdatedTime,LocalDateTime.now());
        contractDeliveryAgreementMapper.update(null,lambdaUpdateWrapper);
        return true;
    }

    @Override
    public PageResponse<EmailResDTOByContract> getEmail(PageRequest<EmailReqDTOByContract> req) {
        PageRequest<EmailReqDTO> inputEmailReqDTO = new PageRequest<>();
        inputEmailReqDTO.setIndex(req.getIndex());
        inputEmailReqDTO.setSize(req.getSize());
        EmailReqDTO emailReqDTO = BeanUtil.copyProperties(req.getQuery(), EmailReqDTO.class);
        Result<SysUserRespDTO> userByUserId = sysUserFeign.getUserByUserId(req.getQuery().getUserId());
        emailReqDTO.setUserEmail(userByUserId.getData().getUserEmail());
        emailReqDTO.setUserEmailPassword(AESUtil.decryptOut(userByUserId.getData().getUserEmailPassword()));
        inputEmailReqDTO.setQuery(emailReqDTO);
        Result<PageResponse<EmailResDTO>> email = emailFeign.getEmail(inputEmailReqDTO);
        return PageResponse.toResult(email.getData().getIndex(),email.getData().getSize(),email.getData().getTotal(),BeanUtil.copyToList(email.getData().getRecords(), EmailResDTOByContract.class));
    }

    @Override
    public Boolean sendEmailByExchange(SendEmailByExchangeDTOByContract req) {
        SendEmailByExchangeDTO sendEmailByExchangeDTO = BeanUtil.copyProperties(req, SendEmailByExchangeDTO.class);
        Result<SysUserRespDTO> userByUserId = sysUserFeign.getUserByUserId(req.getUserId());
        sendEmailByExchangeDTO.setUserEmail(userByUserId.getData().getUserEmail());
        sendEmailByExchangeDTO.setUserEmailPassword(AESUtil.decryptOut(userByUserId.getData().getUserEmailPassword()));
        Result<Void> voidResult = emailFeign.sendEmailByExchange(sendEmailByExchangeDTO);
        if (Boolean.TRUE.equals(voidResult.getSuccess())) {
            contractDeliveryAgreementMapperService.lambdaUpdate()
                    .set(ContractDeliveryAgreementPO::getIsSendEmail, 1)
                    .set(ContractDeliveryAgreementPO::getUpdatedTime, LocalDateTime.now())
                    .eq(ContractDeliveryAgreementPO::getContractId, req.getContractId())
                    .update();
        }
        return Boolean.TRUE;
    }

    @Override
    public SendEmailInfoDTO getSendEmailInfo(SendEmailInfoDTO req) {
        SendEmailInfoDTO result = new SendEmailInfoDTO();
        List<String> list = new ArrayList();
        //老发件人
        String senderOld = req.getSender();
        //收件人
        List<String> recipientList = Arrays.stream(req.getRecipient().split(";")).toList();
        //抄送人
        List<String> ccList = Arrays.stream(req.getCc().split(";")).toList();

        //获取发件人完整邮箱地址
        list.add(senderOld);
        list.addAll(recipientList);
        list.addAll(ccList);
        List<String> filteredList = list.stream().distinct().filter(email -> email.contains(req.getLoginUserEmail())).toList();
        String loginUserEmail = filteredList.get(0);
        result.setSender(loginUserEmail);

        //登录人是收件人
        if (recipientList.contains(loginUserEmail)) {
            recipientList.add(senderOld);
            result.setRecipient(recipientList.stream().filter(email -> !Objects.equals(email, loginUserEmail)).collect(Collectors.joining(";")));
            result.setCc(req.getCc());
            return result;
        }
        //登录人回复自己
        if (Objects.equals(senderOld, loginUserEmail)) {
            result.setRecipient(req.getRecipient());
            result.setCc(req.getCc());
            return result;
        }
        //登陆人是抄送人人
        if (ccList.contains(loginUserEmail)) {
            //老的发件人放到收件人里面
            recipientList.add(senderOld);
            result.setRecipient(recipientList.stream().collect(Collectors.joining(";")));
            ccList.removeIf(email -> Objects.equals(email, loginUserEmail));
            result.setCc(ccList.stream().collect(Collectors.joining(";")));
            return result;
        }
        return null;
    }

    @Override
    public Map<String, List<ContractBusinessItemResDTO>> queryBusinessItem(List<String> contractIds) {
        if (CollUtil.isNotEmpty(contractIds)) {
            List<ContractBusinessItemPO> contractBusinessItemPOList = contractBusinessItemMapperService.lambdaQuery()
                    .in(ContractBusinessItemPO::getContractId, contractIds)
                    .list();
            if (CollUtil.isNotEmpty(contractBusinessItemPOList)) {
                Map<String, List<ContractBusinessItemPO>> collect = contractBusinessItemPOList.stream().collect(Collectors.groupingBy(ContractBusinessItemPO::getContractId));
                return collect.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> BeanUtil.copyToList(entry.getValue(), ContractBusinessItemResDTO.class)));
            }
        }
        return null;
    }

    @Override
    public QuickOrderContractReviewDTO reviewContract(IntentOrderPCQueryReqDTO intentOrderPCQueryReqDTO) {
        //查询意向单详情
        Result<IntentOrderDetailResDTO> result=intentOrderService.findIntentOrderDetail(intentOrderPCQueryReqDTO);
        Assert.isTrue(result.getSuccess(),"查询意向单详情出错");
        IntentOrderDetailResDTO intentOrderDetailResDTO=result.getData();
        Assert.notNull(intentOrderDetailResDTO,"查询意向单详情出错");
        QuickOrderContractReviewDTO quickOrderContractReviewDTO= BeanUtil.copyProperties(intentOrderDetailResDTO,QuickOrderContractReviewDTO.class);
//        quickOrderContractReviewDTO.setSalesTeam("");

        return quickOrderContractReviewDTO;
    }

    @Override
    public QuickOrderContractPreviewResDTO previewContractDoc(QuickOrderContractSubmitReqDTO quickOrderContractSubmitReqDTO) {
        return quickOrderContractBiz.genContract(quickOrderContractSubmitReqDTO);
    }

    @Override
    public Boolean submitQuickOrderContract(QuickOrderContractSubmitReqDTO reqDTO) {
        SfSyncContractReqDTO sfReqDTO = transReq(reqDTO);
        Result<SysUserRespDTO> userInfo = sysUserFeign.getUserByUserId(reqDTO.getCurrentUserId());

        Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfReqDTO);
        if (sfResp == null || sfResp.getData() == null) {
            if (sfResp != null) {
                throw new BizException(ResultCode.FAIL.getCode(), sfResp.getMessage());
            } else {
                throw new BizException(ResultCode.FAIL.getCode(), "向SF同步合同失败");
            }
        }
        todoCompleteManager.completeBatch(reqDTO.getIntentOrderNo(), TodoBizCodeEnum.QO_EXTERNAL_INTERNAL_CONFIRM.getCode(),
                reqDTO.getCurrentUserId(), userInfo.getData().getUserName());
        contractAddBiz.addContractDraft(reqDTO, sfResp.getData());
        return Boolean.TRUE;
    }

    @Override
    public Boolean bpmCallBack(BPMContractReqDTO bpmContractReqDTO) {
        RLock cacheLock = redissonClient.getLock(ContractConstants.CONTRACT_BPM_CALLBACK_LOCK + bpmContractReqDTO.getBpmTaskID());
        Boolean result = Boolean.FALSE;
        try {
            // 设定一下2s超时
            if (cacheLock.tryLock(2, 2, TimeUnit.SECONDS)) {
                try {
                    return doBusiness(bpmContractReqDTO);
                } finally {
                    cacheLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重新设置中断状态
            log.error("BPM回调线程被中断: ", e);
            throw new BizException(ResultCode.FAIL.getCode(), "操作被中断");
        } catch (Exception e) {
            log.error("BPM回调更新异常: ", e);
            throw new BizException(ResultCode.FAIL.getCode(), ":业务异常：" + e.getMessage());
        }
        return result;
    }

    private boolean doBusiness(BPMContractReqDTO bpmContractReqDTO) {
        ContractPO contract = contractMapperService.lambdaQuery()
                .eq(ContractPO::getBpmTaskId, bpmContractReqDTO.getBpmTaskID())
                .eq(ContractPO::getIsDeleted, 0).one();
        if (contract == null) {
            return Boolean.FALSE;
        }
        // 根据审批结果，更新
        // pending表示是下次审批，其他则为当前审批结果
        ContractBusinessPO businessPO = contractBusinessMapperService.findByContractId(contract.getContractId());
        if (BpmStatusEnum.APPROVED.getCode().equals(businessPO.getBpmStatus())
                || BpmStatusEnum.REJECT.getCode().equals(businessPO.getBpmStatus())) {
            log.info("合同[{}]审批状态已完结，跳过状态更新", contract.getContractId());
            return Boolean.TRUE;
        }
        processPendingUsers(businessPO, bpmContractReqDTO);
        boolean approveEnd = ("归档".equals(bpmContractReqDTO.getApprovalNodeDescription())
                || "申请人最终确认".equals(bpmContractReqDTO.getApprovalNodeDescription()) || "申请人指定盖章类型".equals(bpmContractReqDTO.getApprovalNodeDescription()))
                && StringUtils.equals("approved", bpmContractReqDTO.getApprovalResult());
        boolean rejectEnd = StringUtils.equals("rejected", bpmContractReqDTO.getFinalApprovalResult());
        IntentOrderPO intentOrderPO = intentOrderMapperService.findByIntentOrderNo(businessPO.getIntentOrderNo());
        if ("pending".equals(bpmContractReqDTO.getApprovalResult())) {
//            businessPO.setNextApprover(bpmContractReqDTO.getApprover());
        } else {
            businessPO.setBpmApprovalOpinions(bpmContractReqDTO.getApprovalResult());
            businessPO.setApprover(bpmContractReqDTO.getApprover());
            if ("创建".equals(bpmContractReqDTO.getApprovalNodeDescription())) {
                businessPO.setNextApprover(bpmContractReqDTO.getApprover());
            }
            businessPO.setBpmApprovedTime(bpmContractReqDTO.getApprovalCompletionTime());
            businessPO.setApprovalNodeDescription(bpmContractReqDTO.getComments());
            if (approveEnd) {
                businessPO.setBpmStatus(BpmStatusEnum.APPROVED.getCode());
                contract.setContractStatus(ContractStatusEnum.SINGLE_SIGNED.getCode());
            } else if (rejectEnd) {
                businessPO.setBpmStatus(BpmStatusEnum.REJECT.getCode());
                contract.setContractStatus(ContractStatusEnum.REJECTED.getCode());
                intentOrderPO.setStatus(IntentOrderStatusEnum.CONFIRMED.getCode());
            }
        }
        contractMapperService.saveOrUpdate(contract);
        contractBusinessMapperService.saveOrUpdate(businessPO);

        // 审批结束
        if (approveEnd || rejectEnd) {
//            contractBusinessMapperService.saveOrUpdate(businessPO);
            intentOrderMapperService.saveOrUpdate(intentOrderPO);
            if (approveEnd) {
                //更新sf合同状态
                SfSyncContractReqDTO sfSyncContractReqDTO = new SfSyncContractReqDTO();
                sfSyncContractReqDTO.setSfContractId(contract.getSfRecordId());
                sfSyncContractReqDTO.setIntentOrderNo(businessPO.getIntentOrderNo());
                sfSyncContractReqDTO.setContractStatus(ContractStatusEnum.SINGLE_SIGNED.getCode());
                Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfSyncContractReqDTO);
                if (sfResp == null || sfResp.getData() == null) {
                    if (sfResp != null) {
                        log.error("向SF同步合同失败,sfContractNo="+contract.getSfContractNo()+",message="+sfResp.getMessage());
                    } else {
                        log.error("向SF同步合同失败，返回为空，,sfContractNo="+contract.getSfContractNo());
                    }
                }
                sendNotifyAfterCallBack(intentOrderPO, contract.getContractId(),null, null, bpmContractReqDTO.getComments(), IntentOrderMessageSituationEnum.QO_CONTRACT_BPM_APPROVED);
            } else if (rejectEnd) {
                //更新sf合同状态
                SfSyncContractReqDTO sfSyncContractReqDTO = new SfSyncContractReqDTO();
                sfSyncContractReqDTO.setSfContractId(contract.getSfRecordId());
                sfSyncContractReqDTO.setIntentOrderNo(businessPO.getIntentOrderNo());
                sfSyncContractReqDTO.setContractStatus(ContractStatusEnum.REJECTED.getCode());
                Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfSyncContractReqDTO);
                if (sfResp == null || sfResp.getData() == null) {
                    if (sfResp != null) {
                        log.error("向SF同步合同失败,sfContractNo="+contract.getSfContractNo()+",message="+sfResp.getMessage());
                    } else {
                        log.error("向SF同步合同失败，返回为空，,sfContractNo="+contract.getSfContractNo());
                    }
                }
                sendNotifyAfterCallBack(intentOrderPO, contract.getContractId(),null, null, bpmContractReqDTO.getComments(), IntentOrderMessageSituationEnum.QO_CONTRACT_BPM_REJECT);
            }
        }
        return Boolean.TRUE;
    }

    private void processPendingUsers(ContractBusinessPO businessPO, BPMContractReqDTO reqDTO) {
        String pendingUsers = businessPO.getBpmPendingUsers();
        JSONObject obj = JSON.parseObject(pendingUsers);
        if (obj == null) {
            obj = new JSONObject();
        }
        String node = reqDTO.getApprovalNodeDescription();
        String approver = reqDTO.getApprover();
        // pending表示下一次需要审批的人
        if ("pending".equals(reqDTO.getApprovalResult())) {
            if (obj.containsKey(node)) {
                JSONArray temp = obj.getJSONArray(node);
                temp.add(approver);
                obj.put(node, temp);
            } else {
                obj.put(node, new JSONArray(Lists.newArrayList(approver)));
            }
        } else if ("approved".equals(reqDTO.getApprovalResult())) {
            // 审批通过
            if (obj.containsKey(node)) {
                obj.remove(node);
            }
        } else {
            // 其他情况清空下个审批人
            obj = new JSONObject();
        }
        Set<String> users = Sets.newHashSet();
        for (String key : obj.keySet()) {
            JSONArray values = obj.getJSONArray(key);
            Iterator<Object> it = values.iterator();
            while (it.hasNext()) {
                users.add((String) it.next());
            }
        }
        businessPO.setBpmPendingUsers(obj.toJSONString());
        businessPO.setNextApprover(StringUtils.join(users, ","));
    }

    @Override
    @Transactional
    public Boolean retryQuickOrderContractToBPM(String contractId) {
        ContractBpmLogPO contractBpmLogPO=contractBpmlogMapperService.lambdaQuery()
                .eq(ContractBpmLogPO::getContractId, contractId)
                .last("limit 1")
                .one();
        Assert.notNull(contractBpmLogPO,"查询BPM审批记录出错");
        Assert.isTrue(contractBpmLogPO.getStatus()==0,"BPM审批提交状态正常，无法重试");
        SaleContractToOaReqDTO saleContractToOaReqDTO= JSON.parseObject(contractBpmLogPO.getConvertedMsg(), SaleContractToOaReqDTO.class);
        Result<String> result = integrationContractFeign.sendContractToOa(saleContractToOaReqDTO);
        if(result.getSuccess()){
            contractBpmLogPO.setStatus(1);
            contractBpmLogPO.setBpmTaskId(result.getData());
            contractBpmlogMapperService.saveOrUpdate(contractBpmLogPO);
            ContractPO contractPO = contractMapperService.getOne( Wrappers.lambdaQuery(ContractPO.class).eq(ContractPO::getContractId,contractId));
            ContractBusinessPO contractBusinessPO = contractBusinessMapperService.findByContractId(contractId);
            //更新合同状态
            contractPO.setContractStatus(ContractStatusEnum.APPROVING.getCode());
            contractPO.setBpmTaskId(result.getData());
            contractBusinessPO.setBpmStatus(BpmStatusEnum.APPROVING.getCode());
            contractMapperService.saveOrUpdate(contractPO);
            contractBusinessMapperService.saveOrUpdate(contractBusinessPO);
            return Boolean.TRUE;
        }else{
            contractBpmLogPO.setRemark(result.getMessage());
            contractBpmlogMapperService.saveOrUpdate(contractBpmLogPO);
            throw new RuntimeException(result.getMessage());
        }
    }

    @Override
    @Transactional
    public void confirmOrder(ContractModifyReqDTO contractModifyReqDTO) {
        ContractPO contract = contractMapperService.lambdaQuery()
                .eq(ContractPO::getContractId, contractModifyReqDTO.getContractId())
                .eq(ContractPO::getIsDeleted, 0).one();
        if (contract == null || ContractStatusEnum.COUNTERSIGNED.getCode().equals(contract.getContractStatus())) {
            return;
        }
        ContractBusinessPO businessPO = contractBusinessMapperService.findByContractId(contract.getContractId());
        IntentOrderPO intentOrderPO = intentOrderMapperService.findByIntentOrderNo(businessPO.getIntentOrderNo());
        contract.setContractStatus(ContractStatusEnum.COUNTERSIGNED.getCode());
        contractMapperService.saveOrUpdate(contract);

        //更新sf合同状态
        SfSyncContractReqDTO sfSyncContractReqDTO = new SfSyncContractReqDTO();
        sfSyncContractReqDTO.setSfContractId(contract.getSfRecordId());
        sfSyncContractReqDTO.setIntentOrderNo(businessPO.getIntentOrderNo());
        sfSyncContractReqDTO.setContractStatus(ContractStatusEnum.COUNTERSIGNED.getCode());

        Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfSyncContractReqDTO);
        if (sfResp == null || sfResp.getData() == null) {
            if (sfResp != null) {
                log.error("向SF同步合同失败,sfContractNo="+contract.getSfContractNo()+",message="+sfResp.getMessage());
                throw new BizException(ResultCode.FAIL.getCode(), "系统异常，请联系管理员！");
            } else {
                log.error("向SF同步合同失败，返回为空，,sfContractNo="+contract.getSfContractNo());
                throw new BizException(ResultCode.FAIL.getCode(), "系统异常，请联系管理员！");
            }
        }

        // 完成一下待办
        todoCompleteManager.completeBatch(contract.getContractId(), MessageBizCodeEnum.CONTRACT_BPM_APPROVED_TODO.getValue()
                , contractModifyReqDTO.getCurrentUserId(), contractModifyReqDTO.getCurrentUserName());


        contractPartnerOperateMessageBiz.executeApproved(contract, intentOrderPO, contractModifyReqDTO.getCurrentUserId(), contractModifyReqDTO.getCurrentUserName());
    }

    @Override
    @Transactional
    public void cancelOrder(ContractModifyReqDTO contractModifyReqDTO) {
        ContractPO contract = contractMapperService.lambdaQuery()
                .eq(ContractPO::getContractId, contractModifyReqDTO.getContractId())
                .eq(ContractPO::getIsDeleted, 0).one();
        if (contract == null || ContractStatusEnum.CONTRACT_DISCARDED.getCode().equals(contract.getContractStatus())) {
            return;
        }
        ContractBusinessPO businessPO = contractBusinessMapperService.findByContractId(contract.getContractId());
        IntentOrderPO intentOrderPO = intentOrderMapperService.findByIntentOrderNo(businessPO.getIntentOrderNo());
        contract.setContractStatus(ContractStatusEnum.CONTRACT_DISCARDED.getCode());
        contractMapperService.saveOrUpdate(contract);
        intentOrderPO.setStatus(IntentOrderStatusEnum.CANCELED.getCode());
        intentOrderMapperService.saveOrUpdate(intentOrderPO);

        //更新sf合同状态
        SfSyncContractReqDTO sfSyncContractReqDTO = new SfSyncContractReqDTO();
        sfSyncContractReqDTO.setSfContractId(contract.getSfRecordId());
        sfSyncContractReqDTO.setIntentOrderNo(businessPO.getIntentOrderNo());
        sfSyncContractReqDTO.setContractStatus(ContractStatusEnum.CONTRACT_DISCARDED.getCode());

        Result<SfSyncContractDTO> sfResp = integrationContractFeign.syncContractStatusToSFDC(sfSyncContractReqDTO);
        if (sfResp == null || sfResp.getData() == null) {
            if (sfResp != null) {
                log.error("向SF同步合同失败,sfContractNo="+contract.getSfContractNo()+",message="+sfResp.getMessage());
                throw new BizException(ResultCode.FAIL.getCode(), "系统异常，请联系管理员！");
            } else {
                log.error("向SF同步合同失败，返回为空，,sfContractNo="+contract.getSfContractNo());
                throw new BizException(ResultCode.FAIL.getCode(), "系统异常，请联系管理员！");
            }
        }
        // 完成一下待办
        todoCompleteManager.completeBatch(contract.getContractId(), MessageBizCodeEnum.CONTRACT_BPM_APPROVED_TODO.getValue()
                , contractModifyReqDTO.getCurrentUserId(), contractModifyReqDTO.getCurrentUserName());
        contractPartnerOperateMessageBiz.executeReject(contract, intentOrderPO, contractModifyReqDTO.getCurrentUserId(), contractModifyReqDTO.getCurrentUserName());
    }

    /**
     * TODO:需要做幂等性改造
     * @param itemDTOS
     * @return
     */
    @Override
    public DeliverChangeItemResDTO deliverContractItemsChange(List<DeliverContractItemDTO> itemDTOS) {
        DeliverChangeItemResDTO resDTO = new DeliverChangeItemResDTO();
        resDTO.setNeedChange(false);
        if(CollUtil.isEmpty(itemDTOS)){
            resDTO.setChangeResultMsg("传递的发货产品行为空，直接返回！");
            return resDTO;
        }
        List<String> contractIds=itemDTOS.stream().map(DeliverContractItemDTO::getContractId).toList();
        List<ContractBusinessPO> contractBusinessPOs=contractBusinessMapperService.lambdaQuery()
                .in(ContractBusinessPO::getContractId,contractIds)
                .eq(ContractBusinessPO::getIsDeleted,0)
                .list();
        List<ContractPO> contractPOS=contractMapperService.lambdaQuery()
                .in(ContractPO::getContractId,contractIds)
                .eq(ContractPO::getIsDeleted,0)
                .list();
        Map<String,ContractPO> contractPOMap=contractPOS.stream().collect(Collectors.toMap(ContractPO::getContractId,contractPO -> contractPO));
        List<ContractBusinessPO> openContractBusinessPOS=contractBusinessPOs.stream().filter(contractBusinessPO -> OpenContractEnum.PCS_POWER.getCode().equals(contractBusinessPO.getOpenContract())).toList();
        if(CollUtil.isEmpty(openContractBusinessPOS)){
            resDTO.setChangeResultMsg("发货的合同都不是开口合同，直接返回！");
            return resDTO;
        }
        Map<String,ContractBusinessPO> contractBusinessPOMap=openContractBusinessPOS.stream().collect(Collectors.toMap(ContractBusinessPO::getContractId,contractBusinessPO -> contractBusinessPO));
        Set<String> openContractIds=openContractBusinessPOS.stream().map(ContractBusinessPO::getContractId).collect(Collectors.toSet());

        //把开口合同下面的所有产品行都查出来并分组
        List<ContractBusinessItemPO> contractBusinessItemPOs=contractBusinessItemMapperService.lambdaQuery()
                .in(ContractBusinessItemPO::getContractId,openContractIds)
                .list();

        //基于contractId再做分组
        Map<String,List<ContractBusinessItemPO>> conBusItemByContractId=contractBusinessItemPOs.stream()
                .collect(Collectors.groupingBy(ContractBusinessItemPO::getContractId));

        Map<String,List<DeliverContractItemDTO>> itemsByContractMap=itemDTOS.stream()
                .filter(item->openContractIds.contains(item.getContractId()))
                .collect(Collectors.groupingBy(DeliverContractItemDTO::getContractId));
        //合同行变更项目容器
        Map<String,List<ItemChange>> itemChangeMap=new HashMap<>();
        //changeItemId和发货产品行id的映射
        Map<String,String> changeItemIdDeliverItemIdMap=new HashMap<>();
        Map<String,List<String>> businessItemIdDeliverItemIdMap=new HashMap<>();
        //进行比对筛选,拼装出需要更新和新增的合同产品行
        for (Map.Entry<String, List<DeliverContractItemDTO>> entry : itemsByContractMap.entrySet()) {
            List<ContractBusinessItemPO> contractBusinessItemPOS=conBusItemByContractId.get(entry.getKey());
            Map<String,ContractBusinessItemPO> contractBusinessItemPOMap=contractBusinessItemPOS.stream()
                    .collect(Collectors.toMap(ContractBusinessItemPO::getContractBusinessItemId,contractBusinessItemPO->contractBusinessItemPO));

            Map<String,Integer> itemDeductAmount=new HashMap<>();
            Map<String,Integer> itemAdditionAmount=new HashMap<>();
            List<ItemChange> needAddItemChangeList=new ArrayList<>();
            entry.getValue().forEach(item->{
                ContractBusinessItemPO contractBusinessItemPO=contractBusinessItemPOMap.get(item.getContractBusinessItemId());
                //判断是否为原功率发货
                if(contractBusinessItemPO.getPower().compareTo(item.getPower())!=0){
                    itemDeductAmount.computeIfAbsent(item.getContractBusinessItemId(),k->0);
                    itemDeductAmount.computeIfPresent(item.getContractBusinessItemId(),(k,v)->v+item.getQuantityP());
                    //寻找是否已有该父产品行拆出来同功率产品行
                    Optional<ContractBusinessItemPO> samePowerBusinessItemPOOpt= contractBusinessItemPOS.stream().filter(businessItemPO->contractBusinessItemPO.getSfContractItemId().equals(businessItemPO.getSfParentItemId())&&businessItemPO.getPower().compareTo(item.getPower())==0).findAny();
                    if(samePowerBusinessItemPOOpt.isPresent()){
                        //需要更新新拆出的产品行数量
                        ContractBusinessItemPO samePowerBusinessItemPO=samePowerBusinessItemPOOpt.get();
                        businessItemIdDeliverItemIdMap.computeIfAbsent(samePowerBusinessItemPO.getContractBusinessItemId(),k->new ArrayList<>());
                        businessItemIdDeliverItemIdMap.get(samePowerBusinessItemPO.getContractBusinessItemId()).add(item.getDeliverContractItemId());
                        itemAdditionAmount.computeIfAbsent(samePowerBusinessItemPO.getContractBusinessItemId(),k->0);
                        itemAdditionAmount.computeIfPresent(samePowerBusinessItemPO.getContractBusinessItemId(),(k,v)->v+item.getQuantityP());
                        //说明需要重新挂产品合同行
                        resDTO.setNeedChange(true);
                    }else{
                        //否则需要新增一行产品行
                        ItemChange newItemChange=buildAddItemChange(contractPOMap.get(item.getContractId()),contractBusinessItemPO,item);
                        changeItemIdDeliverItemIdMap.put(newItemChange.getItemChangeId(),item.getDeliverContractItemId());
                        needAddItemChangeList.add(newItemChange);
                        //说明需要重新挂产品合同行
                        resDTO.setNeedChange(true);
                    }
                }
            });
            //需拆出来的合同行
            needAddItemChangeList.addAll(buildDeductItemChange(contractPOMap.get(entry.getKey()),itemDeductAmount,contractBusinessItemPOMap));
            needAddItemChangeList.addAll(buildAdditionItemChange(contractPOMap.get(entry.getKey()),itemAdditionAmount,contractBusinessItemPOMap,
                    businessItemIdDeliverItemIdMap,changeItemIdDeliverItemIdMap));
            itemChangeMap.put(entry.getKey(),needAddItemChangeList);
        }

        //进行SF合同产品行变更的调用
        List<DeliverChangeItemResult> resultList=new ArrayList<>();
        for (Map.Entry<String, List<ItemChange>> entry : itemChangeMap.entrySet()) {
            ContractItemsModifyReqDTO req =new ContractItemsModifyReqDTO();
            req.setItemChangeList(entry.getValue());
            ContractBusinessPO contractBusinessPO= contractBusinessPOMap.get(entry.getKey());
            if(contractBusinessPO!=null){
                req.setLoginUserId(contractBusinessPO.getOperationInternalUserId());
                req.setLoginUserName(contractBusinessPO.getOperationInternalUserName());
                req.setLoginUserType(SysUserTypeEnum.INTERNAL.getType());
            }

            Result<List<ContractChangeItemResDTO>>  changeContractResult=itemChangeBiz.contractItemsModify(req);
            //重新查询一下合同产品行，讲新增行也查询出来
            contractBusinessItemPOs=contractBusinessItemMapperService.lambdaQuery()
                    .eq(ContractBusinessItemPO::getContractId,entry.getKey())
                    .list();
            Map<String,ContractBusinessItemPO> nowContractBusinessItemPOMap=contractBusinessItemPOs.stream().collect(Collectors.toMap(ContractBusinessItemPO::getSfContractItemId,contractBusinessItemPO->contractBusinessItemPO));
            DeliverChangeItemResult deliverChangeItemResult=new DeliverChangeItemResult();
            if(changeContractResult.getSuccess()){
                deliverChangeItemResult.setSuccess(true);
                deliverChangeItemResult.setContractId(entry.getKey());
                List<DeliverItemContractItemDTO> list=changeContractResult.getData().stream().map(
                        a -> {
                            ContractBusinessItemPO contractBusinessItemPO=nowContractBusinessItemPOMap.getOrDefault(a.getSfContractItemId(),new ContractBusinessItemPO());
                            DeliverItemContractItemDTO deliverItemContractItemDTO = new DeliverItemContractItemDTO();
                            deliverItemContractItemDTO.setDeliverContractItemId(changeItemIdDeliverItemIdMap.get(a.getZzbItemChangeId()));
                            deliverItemContractItemDTO.setContractBusinessItemId(contractBusinessItemPO.getContractBusinessItemId());
                            deliverItemContractItemDTO.setSfContractItemId(a.getSfContractItemId());
                            return deliverItemContractItemDTO;
                        }).toList();
                deliverChangeItemResult.setItems(list);

            }else{
                deliverChangeItemResult.setSuccess(false);
                deliverChangeItemResult.setMessage("SF合同产品行变更失败:"+changeContractResult.getMessage());
            }
            resultList.add(deliverChangeItemResult);
        }
        resDTO.setChangeResultList(resultList);
        return resDTO;
    }

    @Override
    public void deliverCancelContractItemsChange(DeliverCancelDTO deliverCancelDTO) {
        Assert.notEmpty(deliverCancelDTO.getDeliverCancelContractDTOList(),"需要取消的合同为空！");
        //先做幂等性校验
        List<DeliverCancelContractDTO> deliverCancelContractDTOList=getUnSuccessTaskResult(deliverCancelDTO);
        if(CollUtil.isEmpty(deliverCancelContractDTOList)){
            //说明所有任务都已完成，不需要做处理
            return;
        }

        Map<String,List<ItemChange>> contractItemChangeMap=new HashMap<>();
        Map<String,ContractBusinessPO> contractBusinessPOMap=new HashMap<>();
        deliverCancelContractDTOList.forEach(deliverCancelContractDTO -> {
            if(CollUtil.isNotEmpty(deliverCancelContractDTO.getCancelItemDTOList())){
                ContractBusinessPO contractBusinessPO=contractBusinessMapperService.lambdaQuery()
                        .eq(ContractBusinessPO::getContractId,deliverCancelContractDTO.getContractId())
                        .one();
                contractBusinessPOMap.put(deliverCancelContractDTO.getContractId(),contractBusinessPO);

                ContractPO contractPO=contractMapperService.lambdaQuery()
                        .eq(ContractPO::getContractId,deliverCancelContractDTO.getContractId())
                        .one();

                if(!StringUtils.equals(OpenContractEnum.PCS_POWER.getCode(),contractBusinessPO.getOpenContract())){
                    //非开口合同直接返回
                    return;
                }

                List<ContractBusinessItemPO> contractBusinessItemPOS=contractBusinessItemMapperService.lambdaQuery()
                        .eq(ContractBusinessItemPO::getContractId,deliverCancelContractDTO.getContractId())
//                        .in(ContractBusinessItemPO::getContractBusinessItemId,deliverCancelContractDTO.getCancelItemDTOList().stream().map(DeliverCancelItemDTO::getContractBusinessItemId).toList())
                        .list();

                Map<String,ContractBusinessItemPO> contractBusinessItemPOMap=contractBusinessItemPOS.stream().collect(Collectors.toMap(ContractBusinessItemPO::getContractBusinessItemId,contractBusinessItemPO->contractBusinessItemPO));
                Map<String,ContractBusinessItemPO> contractBusItemBySfIdMap=contractBusinessItemPOS.stream().collect(Collectors.toMap(ContractBusinessItemPO::getSfContractItemId,contractBusinessItemPO->contractBusinessItemPO));


                List<ItemChange> itemChangeList=new ArrayList<>();
                Map<String,BigDecimal> parentItemAdditionQuantityMap=new HashMap<>();
                deliverCancelContractDTO.getCancelItemDTOList().forEach(cancelItemDTO -> {
                    ContractBusinessItemPO contractBusinessItemPO=contractBusinessItemPOMap.get(cancelItemDTO.getContractBusinessItemId());
                    if(StringUtils.isBlank(contractBusinessItemPO.getSfParentItemId())){
                        return;
                    }
                    //只有拆出来的合同产品行需要做数量的退还
                    contractBusinessItemPO.setQuantityP(contractBusinessItemPO.getQuantityP().subtract(new BigDecimal(cancelItemDTO.getCancelP())));
                    if(contractBusinessItemPO.getQuantityP().compareTo(BigDecimal.ZERO)==0){
                        //如果退完后该行已无数量可退，则删除该行
                        ItemChange itemChange=buildCancelDeleteItemchange(contractPO,deliverCancelDTO,contractBusinessItemPO);
                        itemChangeList.add(itemChange);
                    }else{
                        //否则只做原行的数量减少
                        ItemChange itemChange=buildCancelDeductionItemchange(contractPO,deliverCancelDTO,contractBusinessItemPO);
                        itemChangeList.add(itemChange);
                    }

                    parentItemAdditionQuantityMap.putIfAbsent(contractBusinessItemPO.getSfParentItemId(),BigDecimal.ZERO);
                    parentItemAdditionQuantityMap.put(contractBusinessItemPO.getSfParentItemId(),parentItemAdditionQuantityMap.get(contractBusinessItemPO.getSfParentItemId()).add(new BigDecimal(cancelItemDTO.getCancelP())));
                });

                if(CollUtil.isNotEmpty(parentItemAdditionQuantityMap)){
                    parentItemAdditionQuantityMap.forEach((parentItemId,additionQuantity)->{
                        //母行做更新，做片数新增
                        ContractBusinessItemPO parentBusinessItemPo=contractBusItemBySfIdMap.get(parentItemId);
                        ItemChange itemChange=buildCancelAdditionItemchange(contractPO,deliverCancelDTO,parentBusinessItemPo,additionQuantity);
                        itemChangeList.add(itemChange);
                    });
                }
                contractItemChangeMap.put(deliverCancelContractDTO.getContractId(),itemChangeList);
            }
        });

        Map<String,DeliverChangeItemResult> contractItemChangeResultMap=new HashMap<>();
        //真正发起合同变更
        if(CollUtil.isNotEmpty(contractItemChangeMap)){
            contractItemChangeMap.forEach((contractId,itemChangeList)->{
                ContractItemsModifyReqDTO req =new ContractItemsModifyReqDTO();
                req.setItemChangeList(itemChangeList);
                ContractBusinessPO contractBusinessPO= contractBusinessPOMap.get(contractId);
                if(contractBusinessPO!=null){
                    req.setLoginUserId(contractBusinessPO.getOperationInternalUserId());
                    req.setLoginUserName(contractBusinessPO.getOperationInternalUserName());
                    req.setLoginUserType(SysUserTypeEnum.INTERNAL.getType());
                }
                Result<List<ContractChangeItemResDTO>>  changeContractResult=itemChangeBiz.contractItemsModify(req);
                contractItemChangeResultMap.put(contractId,buildDeliverChangeItemResult(changeContractResult));
            });
        }

        //记录task结果用于后续校验幂等性
        saveTaskResult(deliverCancelDTO,contractItemChangeResultMap);
        //失败需要抛给调用方，让调用方重试
        contractItemChangeResultMap.forEach((contractId,deliverChangeItemResult)->{
            Assert.isTrue(deliverChangeItemResult.isSuccess(),contractId+"合同行变更失败:"+deliverChangeItemResult.getMessage());
        });
    }

    private ItemChange buildCancelAdditionItemchange(ContractPO contractPO, DeliverCancelDTO deliverCancelDTO, ContractBusinessItemPO parentBusinessItemPo, BigDecimal additionQuantity) {
        ItemChange itemChange=new ItemChange();
        itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));
        itemChange.setContractNo(contractPO.getSfContractNo());
        itemChange.setOriginalItemId(parentBusinessItemPo.getContractBusinessItemId());
        itemChange.setContractItemId(parentBusinessItemPo.getContractBusinessItemId());
        itemChange.setExpectedDeliverDate(parentBusinessItemPo.getExpectedDeliverDate().toLocalDate());
        itemChange.setInputType("PCs/ASP");
        itemChange.setQuantityW(parentBusinessItemPo.getPower());
        itemChange.setQuantityP(String.valueOf(parentBusinessItemPo.getQuantityP().add(additionQuantity)));
        itemChange.setQuantityMw(new BigDecimal(itemChange.getQuantityP()).multiply(itemChange.getQuantityW()).divide(BigDecimal.valueOf(1e6), 6,RoundingMode.FLOOR));
        itemChange.setComment(deliverCancelDTO.getDeliverNo()+"取消发货");
        return itemChange;
    }

    private ItemChange buildCancelDeductionItemchange(ContractPO contractPO, DeliverCancelDTO deliverCancelDTO, ContractBusinessItemPO contractBusinessItemPO) {
        ItemChange itemChange=new ItemChange();
        itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));
        itemChange.setContractNo(contractPO.getSfContractNo());
        itemChange.setOriginalItemId(contractBusinessItemPO.getContractBusinessItemId());
        itemChange.setContractItemId(contractBusinessItemPO.getContractBusinessItemId());
        itemChange.setExpectedDeliverDate(contractBusinessItemPO.getExpectedDeliverDate().toLocalDate());
        itemChange.setInputType("PCs/ASP");
        itemChange.setQuantityW(contractBusinessItemPO.getPower());
        itemChange.setQuantityP(String.valueOf(contractBusinessItemPO.getQuantityP()));
        itemChange.setQuantityMw(new BigDecimal(itemChange.getQuantityP()).multiply(itemChange.getQuantityW()).divide(BigDecimal.valueOf(1e6), 6,RoundingMode.FLOOR));
        itemChange.setComment(deliverCancelDTO.getDeliverNo()+"取消发货");
        return itemChange;
    }

    private ItemChange buildCancelDeleteItemchange(ContractPO contractPO, DeliverCancelDTO deliverCancelDTO, ContractBusinessItemPO contractBusinessItemPO) {
        ItemChange itemChange=new ItemChange();
        itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));
        itemChange.setContractNo(contractPO.getSfContractNo());
        itemChange.setOriginalItemId(contractBusinessItemPO.getContractBusinessItemId());
        itemChange.setContractItemId(contractBusinessItemPO.getContractBusinessItemId());
        itemChange.setExpectedDeliverDate(contractBusinessItemPO.getExpectedDeliverDate().toLocalDate());
        itemChange.setInputType("PCs/ASP");
        itemChange.setQuantityW(contractBusinessItemPO.getPower());
        itemChange.setQuantityP("0");
        itemChange.setQuantityMw(BigDecimal.ZERO);
        itemChange.setAction(ItemChangeType.DELETE);
        itemChange.setComment(deliverCancelDTO.getDeliverNo()+"取消发货");
        return itemChange;
    }

    private void saveTaskResult(DeliverCancelDTO deliverCancelDTO, Map<String, DeliverChangeItemResult> contractItemChangeResultMap) {
        DistributeTaskInfoPO distributeTaskInfoPO=distributeTaskInfoBiz.getDistributeTaskInfo(deliverCancelDTO.getTaskNo());
        if(distributeTaskInfoPO==null){
            distributeTaskInfoPO=new DistributeTaskInfoPO();
            distributeTaskInfoPO.setTaskNo(deliverCancelDTO.getTaskNo());
            distributeTaskInfoPO.setTaskType(DistributeTaskTypeEnum.DELIVER_CANCEL_CONTRACT_CHANGE.getCode());
            distributeTaskInfoPO.setTaskParam(JSON.toJSONString(deliverCancelDTO));
            distributeTaskInfoPO.setTaskResult(JSON.toJSONString(contractItemChangeResultMap));
            distributeTaskInfoPO.setCreateTime(LocalDateTime.now());
            distributeTaskInfoPO.setUpdateTime(LocalDateTime.now());
            distributeTaskInfoMapperService.save(distributeTaskInfoPO);
        }else{
            Map<String,DeliverChangeItemResult> resultMap=JSON.parseObject(distributeTaskInfoPO.getTaskResult(),new TypeReference<Map<String,DeliverChangeItemResult>>(){});
            resultMap.putAll(contractItemChangeResultMap);
            distributeTaskInfoPO.setTaskResult(JSON.toJSONString(resultMap));
            distributeTaskInfoMapperService.updateById(distributeTaskInfoPO);
        }
    }

    /**
     * 构建结果
     * @param changeContractResult
     * @return
     */
    private DeliverChangeItemResult buildDeliverChangeItemResult(Result<List<ContractChangeItemResDTO>> changeContractResult) {
        DeliverChangeItemResult deliverChangeItemResult=new DeliverChangeItemResult();
        deliverChangeItemResult.setSuccess(changeContractResult.getSuccess());
        deliverChangeItemResult.setMessage(changeContractResult.getMessage());
        return deliverChangeItemResult;
    }

    /**
     * 获取未执行合同变更成功的合同任务
     * @param deliverCancelDTO
     * @return
     */
    private List<DeliverCancelContractDTO> getUnSuccessTaskResult(DeliverCancelDTO deliverCancelDTO) {
        DistributeTaskInfoPO distributeTaskInfoPO=distributeTaskInfoBiz.getDistributeTaskInfo(deliverCancelDTO.getTaskNo());
        if(distributeTaskInfoPO==null){
            return deliverCancelDTO.getDeliverCancelContractDTOList();
        }
        String taskResult=distributeTaskInfoPO.getTaskResult();
        Map<String,DeliverChangeItemResult> resultMap=JSON.parseObject(taskResult,new TypeReference<Map<String,DeliverChangeItemResult>>(){});
        if(CollUtil.isEmpty(resultMap)){
            return deliverCancelDTO.getDeliverCancelContractDTOList();
        }

        //将所有未成功实施变更的合同过滤返回
        return deliverCancelDTO.getDeliverCancelContractDTOList().stream()
                .filter(deliverCancelContractDTO -> !resultMap.get(deliverCancelContractDTO.getContractId()).isSuccess())
                .toList();

    }

    /**
     * 构建数量增加产品行
     *
     * @param contractPO
     * @param itemAdditionAmount
     * @param contractBusinessItemPOMap
     * @param businessItemIdDeliverItemIdMap
     * @param changeItemIdDeliverItemIdMap
     * @return
     */
    private Collection<? extends ItemChange> buildAdditionItemChange(ContractPO contractPO, Map<String, Integer> itemAdditionAmount,
                                                                     Map<String, ContractBusinessItemPO> contractBusinessItemPOMap,
                                                                     Map<String, List<String>> businessItemIdDeliverItemIdMap,
                                                                     Map<String, String> changeItemIdDeliverItemIdMap) {
        List<ItemChange> list=new ArrayList<>();
        for (Map.Entry<String, Integer> itemDeuctEntry : itemAdditionAmount.entrySet()) {
            ItemChange itemChange=new ItemChange();
            itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));
            itemChange.setContractNo(contractPO.getSfContractNo());
            itemChange.setOriginalItemId(itemDeuctEntry.getKey());
            itemChange.setContractItemId(itemDeuctEntry.getKey());
            //查询原合同产品行
            ContractBusinessItemPO contractBusinessItemPO=contractBusinessItemPOMap.get(itemDeuctEntry.getKey());
            //对于原拆出来的同功率产品行，直接挂在原拆出来的同功率产品行
            List<String> deliverItemIdList=businessItemIdDeliverItemIdMap.get(itemDeuctEntry.getKey());
            deliverItemIdList.forEach(item->{
                changeItemIdDeliverItemIdMap.put(itemChange.getItemChangeId(),item);
            });
            itemChange.setExpectedDeliverDate(contractBusinessItemPO.getExpectedDeliverDate().toLocalDate());
            itemChange.setInputType("PCs/ASP");
            itemChange.setQuantityW(contractBusinessItemPO.getPower());
            itemChange.setQuantityP(String.valueOf(contractBusinessItemPO.getQuantityP().add(new BigDecimal(itemDeuctEntry.getValue()))));
            itemChange.setQuantityMw(new BigDecimal(itemChange.getQuantityP()).multiply(itemChange.getQuantityW()).divide(BigDecimal.valueOf(1e6),6, RoundingMode.FLOOR));

            list.add(itemChange);
        }
        return list;
    }

    /**
     * 构建数量扣减合同产品行
     *
     * @param contractPO
     * @param itemDeductAmount
     * @param contractBusinessItemPOMap
     */
    private List<ItemChange> buildDeductItemChange(ContractPO contractPO, Map<String, Integer> itemDeductAmount, Map<String, ContractBusinessItemPO> contractBusinessItemPOMap) {
        List<ItemChange> list=new ArrayList<>();
        for (Map.Entry<String, Integer> itemDeuctEntry : itemDeductAmount.entrySet()) {
            ItemChange itemChange=new ItemChange();
            itemChange.setContractNo(contractPO.getSfContractNo());
            itemChange.setOriginalItemId(itemDeuctEntry.getKey());
            itemChange.setContractItemId(itemDeuctEntry.getKey());
            //查询原合同产品行
            ContractBusinessItemPO contractBusinessItemPO=contractBusinessItemPOMap.get(itemDeuctEntry.getKey());
            itemChange.setExpectedDeliverDate(contractBusinessItemPO.getExpectedDeliverDate().toLocalDate());
            itemChange.setInputType("PCs/ASP");
            itemChange.setQuantityW(contractBusinessItemPO.getPower());
            itemChange.setQuantityP(String.valueOf(contractBusinessItemPO.getQuantityP().subtract(new BigDecimal(itemDeuctEntry.getValue()))));
            itemChange.setQuantityMw(new BigDecimal(itemChange.getQuantityP()).multiply(itemChange.getQuantityW()).divide(BigDecimal.valueOf(1e6), 6,RoundingMode.FLOOR));
            itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));

            list.add(itemChange);
        }
        return list;
    }

    /**
     * 构建需要拆分出来的新增合同产品行
     * @param contractPO
     * @param contractBusinessItemPO
     * @param item
     * @return
     */
    private ItemChange buildAddItemChange(ContractPO contractPO, ContractBusinessItemPO contractBusinessItemPO, DeliverContractItemDTO item) {
        ItemChange itemChange=new ItemChange();
        itemChange.setContractNo(contractPO.getSfContractNo());
        itemChange.setOriginalItemId(contractBusinessItemPO.getContractBusinessItemId());
        itemChange.setContractItemId("");
        itemChange.setExpectedDeliverDate(contractBusinessItemPO.getExpectedDeliverDate().toLocalDate());
        itemChange.setInputType("PCs/ASP");
        itemChange.setQuantityMw(item.getPower().multiply(new BigDecimal(item.getQuantityP())).divide(BigDecimal.valueOf(1e6),6, RoundingMode.FLOOR));
        itemChange.setQuantityW(item.getPower());
        itemChange.setQuantityP(String.valueOf(item.getQuantityP()));
        itemChange.setItemChangeId(idGenerator.uniqueIdInit("CHG", "item-change-id"));
        itemChange.setComment(contractBusinessItemPO.getNotes());

        return itemChange;
    }

    private void sendNotifyAfterCallBack(IntentOrderPO po, String contractId, String currentUserId, String currentUserName,
                                         String approvalDesc, IntentOrderMessageSituationEnum situation) {
        IntentOrderMessageMqDTO messageMqDTO = BeanUtil.toBean(po, IntentOrderMessageMqDTO.class);
        messageMqDTO.setCurrentUserId(currentUserId);
        messageMqDTO.setCurrentUserName(currentUserName);
        messageMqDTO.setSituationType(situation.getCode());
        messageMqDTO.setApprovalDesc(approvalDesc);
        messageMqDTO.setContractId(contractId);
        mqManager.sendTopic(IntentOrderConstant.INTENT_ORDER_CHANGED, JSONUtil.toJsonStr(messageMqDTO),true);
    }

    private SfSyncContractReqDTO transReq(QuickOrderContractSubmitReqDTO reqDTO) {
        SfSyncContractReqDTO sfReqDTO = new SfSyncContractReqDTO();
        sfReqDTO.setChannel(BusinessChanceChannelEnum.getDescByCode(reqDTO.getChannel()));
        sfReqDTO.setIntentOrderNo(reqDTO.getIntentOrderNo());
        sfReqDTO.setOpenContractType(reqDTO.getOpenContractType());
        sfReqDTO.setEnterpriseType(reqDTO.getEnterpriseType());
        sfReqDTO.setStartDate(reqDTO.getContractEffectDateBegin());
        sfReqDTO.setEndDate(reqDTO.getContractEffectDateEnd());
        sfReqDTO.setIncludeRebate(reqDTO.getIsRebate());
        sfReqDTO.setRebateUsedAmount(reqDTO.getRebateMoney() == null ? null : String.valueOf(reqDTO.getRebateMoney()));
        sfReqDTO.setContractSummary(reqDTO.getContractSummary());
        //发起审批后再提交合同附件
//        SfContractFileItemReqDTO fileDto = new SfContractFileItemReqDTO();
//        String fileUrl = reqDTO.getContractDocUrl();
//        fileDto.setFileId(fileUrl.substring(fileUrl.lastIndexOf("/")+1));
//        fileDto.setFileUrl(fileUrl);
//        sfReqDTO.setContractFileItem(Lists.newArrayList(fileDto));
        sfReqDTO.setContractRemark("满足《框架协议》返利条件的前提下，本合同可涉及："+reqDTO.getContractComment()+" 其他：无");
        sfReqDTO.setDeliveryDate(DateUtils.format(reqDTO.getDeliveryDate(),"yyyy-MM-dd"));
        sfReqDTO.setContractStatus(ContractStatusEnum.APPROVING.getCode());
        sfReqDTO.setPaymentNotes(reqDTO.getPaymentNotes());
        sfReqDTO.setShippingNotes(reqDTO.getShippingNotes());

        return sfReqDTO;
    }
}
