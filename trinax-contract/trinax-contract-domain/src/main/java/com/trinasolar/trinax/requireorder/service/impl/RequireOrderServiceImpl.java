package com.trinasolar.trinax.requireorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.basic.constants.enums.changeowner.ChangeSituationEnum;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderMessageSituationEnum;
import com.trinasolar.trinax.requireorder.dto.input.*;
import com.trinasolar.trinax.requireorder.dto.input.changeowner.RequireOrderChangeOwnerReqDTO;
import com.trinasolar.trinax.requireorder.dto.mq.RequireOrderMessageMqDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderDetailRespDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderExcelRespDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderReportResDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderRespDTO;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import com.trinasolar.trinax.requireorder.service.RequireOrderService;
import com.trinasolar.trinax.requireorder.service.biz.*;
import com.trinasolar.trinax.requireorder.service.biz.changeowner.RequireOrderChangeOwnerBiz;
import com.trinasolar.trinax.requireorder.service.biz.changeowner.RequireOrderManagerChangeOwnerBiz;
import com.trinasolar.trinax.requireorder.service.biz.message.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class RequireOrderServiceImpl implements RequireOrderService {

    @Autowired
    private RequireOrderSubmitBiz requireOrderSubmitBiz;

    @Autowired
    private RequireOrderMapperService requireOrderMapperService;

    @Autowired
    private RequireOrderDetailBiz requireOrderDetailBiz;

    @Autowired
    private RequireOrderPCQueryBiz requireOrderPCQueryBiz;

    @Autowired
    private RequireOrderAppPageBiz requireOrderAppPageBiz;

    @Autowired
    private RequireOrderCancelBiz requireOrderCancelBiz;

    @Autowired
    private RequireOrderEditBiz requireOrderEditBiz;

    @Autowired
    private RequireOrderAllocationPartnerBiz requireOrderAllocationPartnerBiz;

    @Autowired
    private RequireOrderAllocationSaleBiz requireOrderAllocationSaleBiz;

    @Autowired
    private LoginUserRoleBiz loginUserRoleBiz;

    @Autowired
    private InternalCancelBiz internalCancelBiz;

    @Autowired
    private ConfirmToIntentOrderBiz confirmToIntentOrderBiz;

    @Autowired
    private AllocationToSaleBiz allocationToSaleBiz;

    @Autowired
    private AllocationToPartnerBiz allocationToPartnerBiz;

    @Autowired
    private AreaManagerAllocationToAnotherBiz areaManagerAllocationToAnotherBiz;

    @Autowired
    private ExternalCancelBiz externalCancelBiz;

    @Autowired
    private ExternalCreatePersonalOrderBiz externalCreatePersonalOrderBiz;

    @Autowired
    private ExternalCreateEnterpriseOrderBiz externalCreateEnterpriseOrderBiz;

    @Autowired
    private RequireOrderReportBiz requireOrderReportBiz;

    @Autowired
    private RequireOrderChangeOwnerBiz requireOrderChangeOwnerBiz;

    @Autowired
    private RequireOrderManagerChangeOwnerBiz requireOrderManagerChangeOwnerBiz;


    @Override
    public Result<String> submit(RequireOrderSubmitReqDTO reqDTO) {
        return requireOrderSubmitBiz.submitRequireOrder(reqDTO);
    }

    @Override
    public RequireOrderRespDTO getByRequireOrderNo(String requireOrderNo) {
        RequireOrderPO requireOrderPO = requireOrderMapperService.getByRequireOrderNo(requireOrderNo);
        return BeanUtil.copyProperties(requireOrderPO, RequireOrderRespDTO.class);
    }

    @Override
    public Result<RequireOrderDetailRespDTO> findRequireOrderDetail(RequireOrderDetailReqDTO reqDTO) {
        return Result.ok(requireOrderDetailBiz.findRequireOrderDetail(reqDTO));
    }

    @Override
    public Result<List<RequireOrderExcelRespDTO>> findRequireOrderPCList(RequireOrderPCPageReqDTO reqDTO) {
        return Result.ok(requireOrderPCQueryBiz.requireOrderPCList(reqDTO));
    }

    @Override
    public Result<PageResponse<RequireOrderRespDTO>> findRequireOrderPCPage(PageRequest<RequireOrderPCPageReqDTO> reqDTO) {
        return Result.ok(requireOrderPCQueryBiz.requireOrderPCPage(reqDTO));
    }

    @Override
    public Result<PageResponse<RequireOrderRespDTO>> findRequireOrderAppPage(PageRequest<RequireOrderAppPageReqDTO> reqDTO) {
        return Result.ok(requireOrderAppPageBiz.requireOrderAppPage(reqDTO));
    }

    @Override
    public Result<List<RequireOrderRespDTO>> findRequireOrderAppList(RequireOrderAppPageReqDTO reqDTO) {
        return Result.ok(requireOrderAppPageBiz.requireOrderAppList(reqDTO));
    }

    @Override
    public Result<String> cancelRequireOrder(RequireOrderCancelReqDTO reqDTO) {
        requireOrderCancelBiz.cancelRequireOrder(reqDTO);
        return Result.ok("取消成功");
    }

    @Override
    public Result<String> allocationPartner(RequireOrderAllocationPartnerReqDTO reqDTO) {
        requireOrderAllocationPartnerBiz.allocationPartner(reqDTO);
        return Result.ok("生态伙伴分配成功");
    }

    @Override
    public Result<String> allocationSale(RequireOrderAllocationSaleReqDTO reqDTO) {
        requireOrderAllocationSaleBiz.allocationSale(reqDTO);
        return Result.ok("分配成功");
    }

    @Override
    public Result<String> editRequireOrder(RequireOrderEditReqDTO reqDTO) {
        requireOrderEditBiz.editRequireOrder(reqDTO);
        return Result.ok("编辑成功");
    }

    @Override
    public Result<String> loginUserRole(LoginUserRoleReqDTO reqDTO) {
        return Result.ok(loginUserRoleBiz.loginUserRole(reqDTO));
    }

    @Override
    public void sendMessageForRequireOrder(RequireOrderMessageMqDTO req) {
        if(RequireOrderMessageSituationEnum.RQ_EXTERNAL_CREATE_PERSONAL_ORDER.getCode().equals(req.getSituationType())){
            externalCreatePersonalOrderBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_EXTERNAL_CREATE_ENTERPRISE_ORDER.getCode().equals(req.getSituationType())){
            externalCreateEnterpriseOrderBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_AREA_MANAGER_ALLOCATION_TO_ANOTHER.getCode().equals(req.getSituationType())){
            areaManagerAllocationToAnotherBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_ALLOCATION_TO_SALE.getCode().equals(req.getSituationType())){
            allocationToSaleBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_ALLOCATION_TO_PARTNER.getCode().equals(req.getSituationType())){
            allocationToPartnerBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_CONFIRM_TO_INTENT_ORDER.getCode().equals(req.getSituationType())){
            confirmToIntentOrderBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_EXTERNAL_CANCEL.getCode().equals(req.getSituationType())){
            externalCancelBiz.execute(req);
        }else if(RequireOrderMessageSituationEnum.RQ_INTERNAL_CANCEL.getCode().equals(req.getSituationType())){
            internalCancelBiz.execute(req);
        }else{
            log.info("意向单消息场景类型不存在");
        }
    }

    @Override
    public Result<Map<String, List<RequireOrderReportResDTO>>> reportAmountChart(RequireOrderReportReqDTO reqDTO) {
        return Result.ok(requireOrderReportBiz.reportAmountChart(reqDTO));
    }

    @Override
    public Result<List<RequireOrderReportResDTO>> reportAmountList(RequireOrderReportReqDTO reqDTO) {
        return Result.ok(requireOrderReportBiz.reportAmountList(reqDTO));
    }

    @Override
    public Result<String> requireOrderChangeOwner(RequireOrderChangeOwnerReqDTO reqDTO) {
        if(ChangeSituationEnum.AREA_MANAGER_REQUIRE_ORDER.getCode().equals(reqDTO.getSituationCode())){
            return requireOrderManagerChangeOwnerBiz.changeOwner(reqDTO);
        }else{
            return requireOrderChangeOwnerBiz.changeOwner(reqDTO);
        }
    }


}
