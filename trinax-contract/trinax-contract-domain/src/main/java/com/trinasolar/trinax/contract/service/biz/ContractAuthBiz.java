package com.trinasolar.trinax.contract.service.biz;

import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.cart.dto.input.*;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.manager.AuthSubordinateManager;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.SysOrganizationTypeEnum;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.SubordinateQueryReqDTO;
import com.trinasolar.trinax.user.dto.output.SysSalesSubordinateRespDTO;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import com.trinasolar.trinax.user.dto.output.SysUserRespDTO;
import dtt.cache.redisclient.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ContractAuthBiz {

    @Autowired
    private SysUserFeign sysUserFeign;

    @Autowired
    private SysDealerSalesRelationFeign sysDealerSalesRelationFeign;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private AuthSubordinateManager authSubordinateManager;


    //用户数据缓存
    private static final String USER_INFO_CACHE = "USER:INFO:CACHE:PREFIX:USERID:";


    /**
     * 处理pc端、app端合同列表的权限查询
     */
    public boolean handleAuth(ContractQueryAuthDTO query) {
        String loginUserId = query.getUserID();
        Result<SysUserRespDTO> sysUserRespDTOResult = sysUserFeign.getUserByUserId(loginUserId);
        SysUserRespDTO sysUserRespDTO = sysUserRespDTOResult.getData();
        // 如果是超级管理员，那么直接可以看所有
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.ADMIN_ROLE_ID)) {
            return true;
        }
        if (sysUserRespDTO.getUserType().equals(SysUserTypeEnum.EXTERNAL.getType())) {
            // 经销商用户（外部用户）
            Result<List<SysSubDealerRespDTO>> subDealerInfoResult = sysDealerSalesRelationFeign.getSubDealerInfo(loginUserId);
            if (ObjectUtil.isEmpty(subDealerInfoResult.getData())) {
                return false;
            }
            List<ContractQueryAuthDealerDTO> contractQueryAuthDealerDTOS = subDealerInfoResult.getData().stream().map(e -> new ContractQueryAuthDealerDTO(e.getDealerUserId(), e.getEnterpriseId())).toList();
            query.setContractQueryAuthDealerDTOS(contractQueryAuthDealerDTOS);
        } else {
            if (SysOrganizationTypeEnum.SALES.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                // 销售用户
                Result<List<SysSalesSubordinateRespDTO>> salesSubordinateRespDTOResult = sysUserFeign.getSalesSubordinate(new SubordinateQueryReqDTO(sysUserRespDTO.getUserId(), SysUserTypeEnum.EXTERNAL));
                if (ObjectUtil.isEmpty(salesSubordinateRespDTOResult.getData())) {
                    return false;
                }
                List<ContractQueryAuthSalesDTO> contractQueryAuthSalesDTOS = salesSubordinateRespDTOResult.getData().stream().map(e -> new ContractQueryAuthSalesDTO(e.getUserId(), e.getOrganizationCode())).toList();
                query.setContractQueryAuthSalesDTOS(contractQueryAuthSalesDTOS);
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                // 运营用户
                Result<List<SysUserRespDTO>> sysUserFeignSubordinateResult = sysUserFeign.getSubordinate(SubordinateQueryReqDTO.builder().userId(loginUserId).sysUserTypeEnum(SysUserTypeEnum.INTERNAL).build());
                if (ObjectUtil.isEmpty(sysUserFeignSubordinateResult.getData())) {
                    return false;
                }
                query.setOperationInternalUserIdList(sysUserFeignSubordinateResult.getData().stream().map(SysUserRespDTO::getUserId).toList());
            } else if (SysOrganizationTypeEnum.COMMON.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                // 常规用户
                Result<List<SysUserRespDTO>> sysUserFeignSubordinateResult = sysUserFeign.getSubordinate(SubordinateQueryReqDTO.builder().userId(loginUserId).sysUserTypeEnum(SysUserTypeEnum.INTERNAL).build());
                if (ObjectUtil.isEmpty(sysUserFeignSubordinateResult.getData())) {
                    return false;
                }
                query.setCommonUserIdList(sysUserFeignSubordinateResult.getData().stream().map(SysUserRespDTO::getUserId).toList());
            } else {
                // 其它组织类型的用户
                // 无任何数据权限，直接返回空
                return false;
            }
        }
        return true;
    }

    // 设置到缓存中
    public void setToCache(ContractQueryAuthDTO query, ContractQueryAuthCacheDTO val, String key) {
        val.setOperationInternalUserIdList(query.getOperationInternalUserIdList());
        val.setContractQueryAuthSalesDTOS(query.getContractQueryAuthSalesDTOS());
        val.setContractQueryAuthDealerDTOS(query.getContractQueryAuthDealerDTOS());
        redisUtil.set(key, JacksonUtil.bean2Json(val), 300);
    }

    // 从缓存中查出的数据设置到query中
    public ContractQueryAuthCacheDTO setToQuery(ContractQueryAuthDTO query, String key) {
        String valStr = redisUtil.get(key);
        if (ObjectUtil.isEmpty(valStr)) {
            return null;
        }
        ContractQueryAuthCacheDTO val = JacksonUtil.json2Bean(valStr, ContractQueryAuthCacheDTO.class);
        query.setOperationInternalUserIdList(val.getOperationInternalUserIdList());
        query.setContractQueryAuthSalesDTOS(val.getContractQueryAuthSalesDTOS());
        query.setContractQueryAuthDealerDTOS(val.getContractQueryAuthDealerDTOS());
        return val;
    }

    /**
     * 处理创建发货申请的合同列表权限查询
     */
    public boolean handleDeliveryAuth(ContractDeliveryQueryReqDTO query) {
        String loginUserId = query.getUserID();
        Result<SysUserRespDTO> sysUserRespDTOResult = sysUserFeign.getUserByUserId(loginUserId);
        SysUserRespDTO sysUserRespDTO = sysUserRespDTOResult.getData();
        if (SysOrganizationTypeEnum.COMMON.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 常规用户
            // 不做限制，可以看到所选客户的所有合同
            return true;
        }
        if (SysOrganizationTypeEnum.SERVICE.getCode().equals(sysUserRespDTO.getOrganizationType())) {
            // 技术用户
            // 无任何数据权限
            return false;
        }
        if (sysUserRespDTO.getUserType().equals(SysUserTypeEnum.EXTERNAL.getType())) {
            // 经销商用户（外部用户）
            query.setExternalUserId(loginUserId);
        } else {
            //authSubordinateManager
            if (SysOrganizationTypeEnum.SALES.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                // 销售用户
                query.setSalesInternalUserId(loginUserId);
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                // 运营用户
                query.setOperationInternalUserId(loginUserId);
            } else {
                // 如果出现了其他类型的登录用户，暂时直接无数据权限
                return false;
            }
        }
        return true;
    }

    /**
     * 处理管理端查询发货申请合同产品时，权限问题
     * @param query
     * @return
     */
    public boolean handleManagementAuth(ContractDeliveryQueryReqDTO query) {
        String loginUserId = query.getUserID();
        if(isAdmin(loginUserId)){
            return true;
        }
        Result<SysUserRespDTO> sysUserRespDTOResult = sysUserFeign.getUserByUserId(loginUserId);
        SysUserRespDTO sysUserRespDTO = sysUserRespDTOResult.getData();
        //管理端使用的接口，不存在外部用户情况
        if (sysUserRespDTO.getUserType().equals(SysUserTypeEnum.EXTERNAL.getType())) {
            return false;
        } else {
            if (SysOrganizationTypeEnum.COMMON.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                query.setCommonUserList(authSubordinateManager.getSubUserIdList(loginUserId));
            } else if (SysOrganizationTypeEnum.SALES.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                query.setSaleBizOrgList(authSubordinateManager.getSalesSubordinate(loginUserId));
            } else if (SysOrganizationTypeEnum.OPERATION.getCode().equals(sysUserRespDTO.getOrganizationType())) {
                query.setOperationUserIdList(authSubordinateManager.getSubUserIdList(loginUserId));
            } else {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否管理员
     * @param userId
     * @return
     */
    public boolean isAdmin(String userId) {
        String redisKey = USER_INFO_CACHE + userId;
        String redisVal = redisUtil.get(redisKey);
        if (StringUtils.isNotBlank(redisVal)) {
            SysUserRespDTO respDTO = JacksonUtil.json2Bean(redisVal, SysUserRespDTO.class);
            return !CollectionUtils.isEmpty(respDTO.getRoleIds()) && respDTO.getRoleIds().contains(UserConstant.ADMIN_ROLE_ID);
        }else{
            Result<SysUserRespDTO> userResult = sysUserFeign.getUserByUserId(userId);
            if(Boolean.TRUE.equals(!userResult.getSuccess()) || ObjectUtils.isEmpty(userResult.getData())){
                throw new BizException(ResultCode.FAIL.getCode(), "获取当前用户信息失败");
            }else{
                redisUtil.set(redisKey, JacksonUtil.bean2Json(userResult.getData()), UserConstant.DATA_PERMISSION_CACHE_TIME);
                Collection<String> roleIds = userResult.getData().getRoleIds();
                return !CollectionUtils.isEmpty(roleIds) && roleIds.contains(UserConstant.ADMIN_ROLE_ID);
            }
        }
    }
}
