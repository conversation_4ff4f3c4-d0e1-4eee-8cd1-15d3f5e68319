package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.core.collection.CollUtil;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.delivery.dto.output.delivery.report.DeliveryReportResDTO;
import com.trinasolar.trinax.requireorder.dto.input.RequireOrderReportReqDTO;
import com.trinasolar.trinax.requireorder.dto.output.RequireOrderReportResDTO;
import com.trinasolar.trinax.requireorder.repository.mapper.RequireOrderMapper;
import com.trinasolar.trinax.user.api.SysOrganizationFeign;
import com.trinasolar.trinax.user.dto.output.SysOrganizationRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class RequireOrderReportBiz {

    @Autowired
    private RequireOrderMapper requireOrderMapper;

    @Autowired
    private SysOrganizationFeign sysOrganizationFeign;


    /**
     * 数量统计报表
     * @param reqDTO
     * @return
     */
    public Map<String,List<RequireOrderReportResDTO>> reportAmountChart(RequireOrderReportReqDTO reqDTO) {
        List<RequireOrderReportResDTO> result = reportAmountList(reqDTO);
        if(CollUtil.isNotEmpty(result)){
            Map<String,List<RequireOrderReportResDTO>> map = result.stream().collect(Collectors.groupingBy(RequireOrderReportResDTO::getBizOrganizationName));
            return map.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            (oldValue, newValue) -> oldValue,
                            TreeMap::new
                    ));
        }else{
            return Collections.emptyMap();
        }
    }

    /**
     * 数量统计列表
     * @param reqDTO
     * @return
     */
    public List<RequireOrderReportResDTO> reportAmountList(RequireOrderReportReqDTO reqDTO) {
        //处理搜索时间范围
        prepareReportParam(reqDTO);

        List<RequireOrderReportResDTO> result = requireOrderMapper.selectReportAmountList(reqDTO);
        if(CollUtil.isEmpty(result)){
            return Collections.emptyList();
        }
        //填充信息
        fillContent(result, reqDTO.getYear());
        if ("2".equals(reqDTO.getApiType()) && CollUtil.isNotEmpty(result)) {
            Map<String, List<RequireOrderReportResDTO>> dataMap = result.stream().collect(Collectors.groupingBy(RequireOrderReportResDTO::getBizOrganizationCode));
            for (Map.Entry<String, List<RequireOrderReportResDTO>> entry : dataMap.entrySet()) {
                List<RequireOrderReportResDTO> temp = entry.getValue();
                temp = temp.stream().sorted(Comparator.comparing(RequireOrderReportResDTO::getFullMonth)).toList();
                long amountCount = 0;
                for (RequireOrderReportResDTO resDTO : temp) {
                    amountCount += resDTO.getAmount();
                    resDTO.setAmount(amountCount);
                }
            }
        }
        return result.stream()
                .sorted(Comparator.comparing(RequireOrderReportResDTO::getBizOrganizationName).thenComparing(RequireOrderReportResDTO::getSingleMonth))
                .toList();
    }


    /**
     * 准备参数
     * @param reqDTO
     */
    private void prepareReportParam(RequireOrderReportReqDTO reqDTO){
        if(ObjectUtils.isEmpty(reqDTO.getYear())){
            reqDTO.setYear(LocalDateTime.now().getYear());
        }
        //查询新增时，查询该年的数据;查询累计时，查询这一年每个月的累计加
        reqDTO.setStartTime(LocalDateTime.of(reqDTO.getYear(), 1,1,0,0,0));
        reqDTO.setEndTime(LocalDateTime.of(reqDTO.getYear(), 12,31,23,59,59));
    }

    private void fillContent(List<RequireOrderReportResDTO> result,int year){
        List<String> orgList = result.stream().map(RequireOrderReportResDTO::getBizOrganizationCode).toList();
        Result<List<SysOrganizationRespDTO>> orgResult = sysOrganizationFeign.listOrgByOrganizationCodes(orgList);
        if(Boolean.TRUE.equals(orgResult.getSuccess()) && CollUtil.isNotEmpty(orgResult.getData())){
            Map<String,List<SysOrganizationRespDTO>> orgMap = orgResult.getData().stream().collect(Collectors.groupingBy(SysOrganizationRespDTO::getOrganizationCode));
            result.forEach(e->{
                if(CollUtil.isNotEmpty(orgMap.get(e.getBizOrganizationCode()))){
                    e.setBizOrganizationName(orgMap.get(e.getBizOrganizationCode()).get(0).getOrganizationName());
                }
            });
        }
        //如果是往年，需要构造12个月份数据；如果是当前年，需要构造已到月份数据
        List<String> totalFullMonth = new ArrayList<>();
        if(LocalDate.now().getYear() > year){
            for (int i = 1; i <= 12; i++) {
                totalFullMonth.add(year + String.format("%02d",i));
            }
        }else{
            int monthNumber = LocalDate.now().getMonthValue();
            for (int i = 1; i <= monthNumber; i++) {
                totalFullMonth.add(year + String.format("%02d",i));
            }
        }

        Map<String,List<RequireOrderReportResDTO>> dataMap = result.stream().collect(Collectors.groupingBy(RequireOrderReportResDTO::getBizOrganizationCode));
        for (Map.Entry<String, List<RequireOrderReportResDTO>> entry : dataMap.entrySet()) {
            List<RequireOrderReportResDTO> temp =  entry.getValue();
            List<String> existFullMonth = temp.stream().map(RequireOrderReportResDTO::getFullMonth).toList();
            List<String> diffFullMonth = totalFullMonth.stream()
                    .filter(element -> !existFullMonth.contains(element))
                    .toList();
            diffFullMonth.forEach(e->{
                RequireOrderReportResDTO resDTO = new RequireOrderReportResDTO();
                resDTO.setAmount(0);
                resDTO.setBizOrganizationCode(entry.getKey());
                resDTO.setBizOrganizationName(entry.getValue().get(0).getBizOrganizationName());
                resDTO.setFullMonth(e);
                resDTO.setSingleYear(year);
                resDTO.setSingleMonth(Integer.valueOf(e.substring(4)));
                result.add(resDTO);
            });
        }
    }

}
