package com.trinasolar.trinax.intentorder.service.biz.message;

import com.google.common.collect.Lists;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.contract.repository.atomicservice.ContractBusinessMapperService;
import com.trinasolar.trinax.contract.repository.po.ContractBusinessPO;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.manager.IntentOrderMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class ContractBpmCallBackMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private IntentOrderMessageManager intentOrderMessageManager;

    @Autowired
    private ContractBusinessMapperService contractBusinessMapperService;

    public void executeApproved(IntentOrderMessageMqDTO reqDTO) {
        log.info("bpm审批合同通过,入参：{}", reqDTO);
        Map<String, String> content = new HashMap<>();
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());
        content.put("customerName", reqDTO.getExternalUserName());
        content.put("bpmTaskId", reqDTO.getBpmTaskId());

        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());
        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();

        MessageSendNoticeReqDTO noticeReqDTO = intentOrderMessageManager.generateNotice2(reqDTO, reqDTO.getContractId(),
                "", MessageBizCodeEnum.CONTRACT_BPM_APPROVED_NOTICE.getValue(), MessageBizCodeEnum.CONTRACT_BPM_APPROVED_NOTICE.getValue(),
                Lists.newArrayList(reqDTO.getSalesInternalUserId(), reqDTO.getOperationInternalUserId()), content);
        noticeList.add(noticeReqDTO);

        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("bpm审批合同通过, 给销售，运营发送消息通知成功，param:{}", JacksonUtil.bean2Json(noticeMessageReqDTO));


        MessageSendCommonReqDTO toMessage = new MessageSendCommonReqDTO();
        toMessage.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());
        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();

        MessageSendTodoReqDTO todoReqDTO = intentOrderMessageManager.generateTodo2(reqDTO, reqDTO.getContractId(),
                MessageBizCodeEnum.CONTRACT_BPM_APPROVED_TODO.getValue(), MessageBizCodeEnum.CONTRACT_BPM_APPROVED_TODO.getValue(),
                Lists.newArrayList(reqDTO.getExternalUserId()), content, TodoOrganizationTypeEnum.PARTNER.getCode());
        todoList.add(todoReqDTO);

        toMessage.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(toMessage));
        log.info("bpm审批合同通过, 给外部客户发送待办通知成功，param:{}", JacksonUtil.bean2Json(toMessage));
    }

    public void executeReject(IntentOrderMessageMqDTO reqDTO) {
        log.info("bpm审批合同拒绝,入参：{}", reqDTO);
        Map<String, String> content = new HashMap<>();
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());
        content.put("customerName", reqDTO.getExternalUserName());
        content.put("approvalDesc", reqDTO.getApprovalDesc());

        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());
        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();

        MessageSendNoticeReqDTO noticeReqDTO = intentOrderMessageManager.generateNotice2(reqDTO, reqDTO.getContractId(),
                "", MessageBizCodeEnum.CONTRACT_BPM_REJECT_NOTICE.getValue(), MessageBizCodeEnum.CONTRACT_BPM_REJECT_NOTICE.getValue(),
                Lists.newArrayList(reqDTO.getSalesInternalUserId(), reqDTO.getOperationInternalUserId()), content);
        noticeList.add(noticeReqDTO);

        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("bpm审批合同拒绝, 给销售，运营发送消息通知成功，param:{}", JacksonUtil.bean2Json(noticeMessageReqDTO));

    }
}
