package com.trinasolar.trinax.requireorder.service.biz;

import cn.hutool.json.JSONUtil;
import com.trinasolar.trinax.common.dto.output.ResultCode;
import com.trinasolar.trinax.common.exception.BizException;
import com.trinasolar.trinax.requireorder.constants.RequireOrderConstant;
import com.trinasolar.trinax.requireorder.constants.RequireOrderResultCode;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderMessageSituationEnum;
import com.trinasolar.trinax.requireorder.constants.enums.RequireOrderStatusEnum;
import com.trinasolar.trinax.requireorder.constants.enums.TrackerTypeEnum;
import com.trinasolar.trinax.requireorder.dto.input.RequireOrderAllocationPartnerReqDTO;
import com.trinasolar.trinax.requireorder.dto.mq.RequireOrderMessageMqDTO;
import com.trinasolar.trinax.requireorder.manager.RequireBizTransferManager;
import com.trinasolar.trinax.requireorder.manager.TodoCompleteManager;
import com.trinasolar.trinax.requireorder.repository.atomicservice.RequireOrderMapperService;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderPO;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;


@Service
@Slf4j
public class RequireOrderAllocationPartnerBiz {

    @Autowired
    private RequireOrderMapperService requireOrderMapperService;

    @Autowired
    private MqManager mqManager;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private TodoCompleteManager todoCompleteManager;

    @Autowired
    private RequireBizTransferManager requireBizTransferManager;


    public void allocationPartner(RequireOrderAllocationPartnerReqDTO reqDTO) {
        //校验并获取数据
        RequireOrderPO requireOrderPO = check(reqDTO.getRequireOrderNo());
        //分配需求单
        doAllocation(requireOrderPO,reqDTO);
        //分配需求单后完成待办并发通知消息
        executorService.execute(()->sendAllocationMsg(requireOrderPO,reqDTO));
        //保存添加操作记录
        requireBizTransferManager.saveTransferRecord(reqDTO.getRequireOrderNo(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName(),reqDTO.getPartnerUserId());
    }

    private RequireOrderPO check(String requireOrderNo){
        RequireOrderPO requireOrderPO = requireOrderMapperService.getByRequireOrderNo(requireOrderNo);
        if(ObjectUtils.isEmpty(requireOrderPO)){
            throw new BizException(ResultCode.FAIL.getCode(),"需求单分配给生态伙伴失败：需求单不存在");
        }
        if(RequireOrderStatusEnum.CANCELED.getCode().equals(requireOrderPO.getRequireOrderStatus())){
            throw new BizException(RequireOrderResultCode.REQUIRE_ORDER_CANCELED.getCode(),RequireOrderResultCode.REQUIRE_ORDER_CANCELED.getMessage());
        }
        return requireOrderPO;
    }

    /**
     * 分配需求单
     * @param reqDTO
     */
    private void doAllocation(RequireOrderPO requireOrderPO,RequireOrderAllocationPartnerReqDTO reqDTO){
        requireOrderPO.setPartnerUserId(reqDTO.getPartnerUserId());
        requireOrderPO.setPartnerUserName(reqDTO.getPartnerUserName());
        requireOrderPO.setPartnerEnterpriseId(reqDTO.getPartnerEnterpriseId());
        requireOrderPO.setPartnerEnterpriseName(reqDTO.getPartnerEnterpriseName());
        requireOrderPO.setUpdatedBy(reqDTO.getCurrentUserId());
        requireOrderPO.setUpdatedName(reqDTO.getCurrentUserName());
        requireOrderPO.setUpdatedTime(LocalDateTime.now());
        requireOrderPO.setTrackerUserType(TrackerTypeEnum.PARTNER.getCode());
        requireOrderPO.setSalesInternalUserId("");
        requireOrderPO.setOriginSalesInternalUserId("");
        requireOrderPO.setSalesInternalUserName("");
        requireOrderPO.setOperationInternalUserId("");
        requireOrderPO.setOperationInternalUserName("");
        boolean isUpdated = requireOrderMapperService.updateById(requireOrderPO);
        if(!isUpdated){
            throw new BizException(ResultCode.FAIL.getCode(),reqDTO.getCurrentUserName() + "分配需求单" + reqDTO.getRequireOrderNo() + "失败");
        }
    }

    /**
     * 分配需求单后需要做的事情
     * @param reqDTO
     */
    private void sendAllocationMsg(RequireOrderPO po,RequireOrderAllocationPartnerReqDTO reqDTO){
        log.info("分配生态伙伴时，完成已有待办，并给生态伙伴发送通知消息");
        //完成已有待办《此处只能完成待办，不能删除待办，后续可能需要通过待办进入详情页》
        todoCompleteManager.completeTodo(reqDTO.getRequireOrderNo(),reqDTO.getCurrentUserId(),reqDTO.getCurrentUserName());

        //发送需求单变化MQ
        RequireOrderMessageMqDTO messageMqDTO = new RequireOrderMessageMqDTO();
        messageMqDTO.setAreaManagerUserId(po.getSalesInternalUserId());
        messageMqDTO.setSituationType(RequireOrderMessageSituationEnum.RQ_ALLOCATION_TO_PARTNER.getCode());
        messageMqDTO.setCurrentUserId(reqDTO.getCurrentUserId());
        messageMqDTO.setCurrentUserName(reqDTO.getCurrentUserName());
        messageMqDTO.setRequireOrderNo(po.getRequireOrderNo());
        messageMqDTO.setEnterpriseName(po.getEnterpriseName());
        messageMqDTO.setExternalUserId(po.getBusinessExternalUserId());
        messageMqDTO.setExternalUserName(po.getBusinessExternalUserName());
        messageMqDTO.setPartnerUserId(reqDTO.getPartnerUserId());
        mqManager.sendTopic(RequireOrderConstant.REQUIRE_ORDER_CHANGED, JSONUtil.toJsonStr(messageMqDTO),true);
    }

}
