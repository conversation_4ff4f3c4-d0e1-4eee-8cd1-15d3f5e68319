package com.trinasolar.trinax.requireorder.repository.dao.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 意向单
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-24
 */
@Data
public class RequireOrderQueryObjectDAO {

    @Schema(description = "用户userId")
    private String userId;

    @Schema(description = "用户对应企业id：外部用户查询数据时，该字段有值")
    private String enterpriseId;

    @Schema(description = "用户对服务组织：内部销售用户查询数据时，该字段有值")
    private String bizOrganizationCode;

}
