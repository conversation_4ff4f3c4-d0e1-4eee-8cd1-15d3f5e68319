package com.trinasolar.trinax.requireorder.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.requireorder.repository.po.RequireOrderFilePO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 需求单文件 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-22
 */
@Mapper
public interface RequireOrderFileMapper extends BaseMapper<RequireOrderFilePO> {

}
