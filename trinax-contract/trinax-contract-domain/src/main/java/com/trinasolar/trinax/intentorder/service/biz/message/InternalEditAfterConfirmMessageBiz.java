package com.trinasolar.trinax.intentorder.service.biz.message;

import cn.hutool.core.bean.BeanUtil;
import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoBizCodeEnum;
import com.trinasolar.trinax.basic.constants.enums.todolist.TodoOrganizationTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendTodoReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import com.trinasolar.trinax.intentorder.constants.enums.IntentOrderTypeEnum;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.manager.IntentOrderMessageManager;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class InternalEditAfterConfirmMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Autowired
    private IntentOrderMessageManager intentOrderMessageManager;

    @Value("${message.template.repeatConfirmNoticeExternal}")
    private String noticeExternalTemplate;

    @Value("${message.template.repeatConfirmNoticeOperation}")
    private String noticeOperationTemplate;

    @Value("${message.template.qoRepeatConfirmNoticeExternal}")
    private String qoNoticeExternalTemplate;

    @Value("${message.template.qoRepeatConfirmNoticeOperation}")
    private String qoNoticeOperationTemplate;

    public void execute(IntentOrderMessageMqDTO reqDTO) {
        log.info("分销销售确认后，编辑意向单,给生态伙伴发送系统通知消息，还需给运营发送通知消息，入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());

        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();
        Map<String, String> content = new HashMap<>();
        content.put("internalUserName", reqDTO.getSalesInternalUserName());
        content.put("potentialSalesVolume", reqDTO.getPotentialSalesVolume().stripTrailingZeros().toPlainString());
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());

        if (IntentOrderTypeEnum.QUICK_ORDER.getCode().equals(reqDTO.getOrderType())) {
            MessageSendNoticeReqDTO noticeReqDTO = intentOrderMessageManager.generateNotice(reqDTO
                    , "", MessageBizCodeEnum.QO_INTERNAL_EDIT_AFTER_CONFIRM.getValue(),
                    noticeExternalTemplate, Collections.singletonList(reqDTO.getExternalUserId()), content);
            noticeList.add(noticeReqDTO);
            //运营
            MessageSendNoticeReqDTO operationNotice = BeanUtil.toBean(noticeReqDTO, MessageSendNoticeReqDTO.class);
            operationNotice.setTemplateCode(qoNoticeOperationTemplate);
            operationNotice.setUserIdList(Collections.singletonList(reqDTO.getOperationInternalUserId()));
            noticeList.add(operationNotice);
        } else {
            MessageSendNoticeReqDTO noticeReqDTO = intentOrderMessageManager.generateNotice(reqDTO
                    , "", MessageBizCodeEnum.IO_INTERNAL_EDIT_AFTER_CONFIRM.getValue(),
                    noticeExternalTemplate, Collections.singletonList(reqDTO.getExternalUserId()), content);
            noticeList.add(noticeReqDTO);
            //运营
            MessageSendNoticeReqDTO operationNotice = BeanUtil.toBean(noticeReqDTO, MessageSendNoticeReqDTO.class);
            operationNotice.setTemplateCode(noticeOperationTemplate);
            operationNotice.setUserIdList(Collections.singletonList(reqDTO.getOperationInternalUserId()));
            noticeList.add(operationNotice);
        }

        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("分销销售确认后，编辑意向单,给生态伙伴发送系统通知消息，还需给运营发送通知消息完成，param：{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }

    public void executeToConfig(IntentOrderMessageMqDTO reqDTO) {
        log.info("多功率发货需求的意向单，分销销售确认后，需给运营发送待办，入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());

        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();
        Map<String, String> content = new HashMap<>();
        content.put("enterpriseName", reqDTO.getEnterpriseName());
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());
        MessageSendTodoReqDTO todoReqDTO = intentOrderMessageManager.generateTodo(reqDTO,
                TodoBizCodeEnum.IO_INTERNAL_EDIT_AFTER_CONFIRM_TO_CONFIG.getCode(), TodoBizCodeEnum.IO_INTERNAL_EDIT_AFTER_CONFIRM_TO_CONFIG.getCode(),
                Collections.singletonList(reqDTO.getOperationInternalUserId()), content, TodoOrganizationTypeEnum.OPERATION.getCode());
        todoList.add(todoReqDTO);

        noticeMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("多功率发货需求的意向单，分销销售确认后，需给运营发送待办完成，param：{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }

    public void executeRefuse(IntentOrderMessageMqDTO reqDTO) {
        log.info("多功率发货需求的意向单，运营拒绝配置，回退到销售，入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.TODO.getCode());

        List<MessageSendTodoReqDTO> todoList = new ArrayList<>();
        Map<String, String> content = new HashMap<>();
        content.put("userName", reqDTO.getCurrentUserName());
        content.put("intentOrderNo", reqDTO.getIntentOrderNo());
        content.put("reason", reqDTO.getApprovalDesc());
        MessageSendTodoReqDTO todoReqDTO = intentOrderMessageManager.generateTodo(reqDTO,
                TodoBizCodeEnum.IO_INTERNAL_EDIT_REFUSE_CONFIG.getCode(), TodoBizCodeEnum.IO_INTERNAL_EDIT_REFUSE_CONFIG.getCode(),
                Collections.singletonList(reqDTO.getSalesInternalUserId()), content, TodoOrganizationTypeEnum.SALES.getCode());
        todoList.add(todoReqDTO);

        noticeMessageReqDTO.setTodoList(todoList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC, JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("多功率发货需求的意向单，运营拒绝配置，回退到销售，param：{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }


}
