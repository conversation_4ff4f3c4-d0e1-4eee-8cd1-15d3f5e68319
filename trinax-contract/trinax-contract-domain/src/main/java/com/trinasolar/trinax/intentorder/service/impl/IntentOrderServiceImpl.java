package com.trinasolar.trinax.intentorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.intentorder.constants.enums.*;
import com.trinasolar.trinax.intentorder.dto.IntentOrderPCExcelResDTO;
import com.trinasolar.trinax.intentorder.dto.input.*;
import com.trinasolar.trinax.intentorder.dto.input.changeowner.IntentOrderChangeOwnerReqDTO;
import com.trinasolar.trinax.intentorder.dto.mq.IntentOrderMessageMqDTO;
import com.trinasolar.trinax.intentorder.dto.output.*;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderItemMapperService;
import com.trinasolar.trinax.intentorder.repository.atomicservice.IntentOrderMapperService;
import com.trinasolar.trinax.intentorder.repository.mapper.IntentOrderItemMapper;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderItemPO;
import com.trinasolar.trinax.intentorder.repository.po.IntentOrderPO;
import com.trinasolar.trinax.intentorder.service.IntentOrderService;
import com.trinasolar.trinax.intentorder.service.biz.*;
import com.trinasolar.trinax.intentorder.service.biz.changeowner.IntentOrderChangeOwnerBiz;
import com.trinasolar.trinax.intentorder.service.biz.message.*;
import com.trinasolar.trinax.masterdata.api.ProductFeign;
import com.trinasolar.trinax.masterdata.dto.input.ProductModulePowerQueryReqDTO;
import com.trinasolar.trinax.masterdata.dto.output.ProductModulePowerDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IntentOrderServiceImpl implements IntentOrderService {

    @Autowired
    private IntentOrderItemMapper intentOrderItemMapper;

    @Autowired
    private IntentOrderMapperService intentOrderMapperService;

    @Autowired
    private IntentOrderItemMapperService intentOrderItemMapperService;

    @Autowired
    private IntentOrderAppPageBiz intentOrderAppPageBiz;

    @Autowired
    private IntentOrderSaveBiz intentOrderSaveBiz;

    @Autowired
    private IntentOrderEditBiz intentOrderUpdateBiz;

    @Autowired
    private IntentOrderCancelBiz intentOrderCancelBiz;
    
    @Autowired
    private IntentOrderSubmitBiz intentOrderSubmitBiz;

    @Autowired
    private IntentOrderConfirmBiz intentOrderConfirmBiz;

    @Autowired
    private IntentOrderCheckBiz intentOrderCheckBiz;

    @Autowired
    private IntentOrderDetailBiz intentOrderDetailBiz;

    @Autowired
    private ExternalSubmitMessageBiz externalSubmitMessageBiz;

    @Autowired
    private ExternalCancelMessageBiz externalCancelMessageBiz;

    @Autowired
    private InternalCancelMessageBiz internalCancelMessageBiz;

    @Autowired
    private InternalConfirmExternalCreateMessageBiz internalConfirmExternalCreateMessageBiz;

    @Autowired
    private InternalCreateMessageBiz internalCreateMessageBiz;

    @Autowired
    private InternalEditAfterConfirmMessageBiz internalEditAfterConfirmMessageBiz;

    @Autowired
    private IntentOrderPcPageBiz intentOrderPcPageBiz;

    @Autowired
    private IntentOrderPcListBiz intentOrderPcListBiz;

    @Autowired
    private CaffeineCacheClearBiz caffeineCacheClearBiz;

    @Autowired
    private IntentOrderReportBiz intentOrderReportBiz;

    @Autowired
    private IntentOrderChangeOwnerBiz intentOrderChangeOwnerBiz;

    @Autowired
    private IntentOrderTrendReportBiz intentOrderTrendReportBiz;

    @Autowired
    ExternalConfirmInternalCreateMessageBiz externalConfirmInternalCreateMessageBiz;

    @Autowired
    ContractBpmCallBackMessageBiz contractBpmCallBackMessageBiz;

    @Autowired
    private ProductFeign productFeign;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<String> deleteIntentOrder(String intentOrderNo, String userId) {
        //内外部用户都只能删除自己创建的，未提交的意向单数据
        List<IntentOrderPO> intentOrderPOList = intentOrderMapperService.lambdaQuery()
                .eq(IntentOrderPO::getIntentOrderNo, intentOrderNo)
                .eq(IntentOrderPO::getCreatedBy, userId)
                .eq(IntentOrderPO::getStatus, IntentOrderStatusEnum.UN_SUBMIT.getCode())
                .eq(IntentOrderPO::getIsDeleted, DeleteFlagEnum.NOT_DELETE.getCode())
                .list();
        if (CollectionUtils.isEmpty(intentOrderPOList) || intentOrderPOList.size() != 1) {
            return Result.fail("意向单删除失败");
        }
        boolean bool = intentOrderMapperService.lambdaUpdate()
                .eq(IntentOrderPO::getIntentOrderNo, intentOrderNo)
                .eq(IntentOrderPO::getCreatedBy, userId)
                .eq(IntentOrderPO::getStatus, IntentOrderStatusEnum.UN_SUBMIT.getCode())
                .set(IntentOrderPO::getIsDeleted, DeleteFlagEnum.DELETED.getCode())
                .set(IntentOrderPO::getUpdatedBy, userId)
                .set(IntentOrderPO::getUpdatedTime, LocalDateTime.now())
                .update();
        intentOrderItemMapperService.lambdaUpdate()
                .eq(IntentOrderItemPO::getIntentOrderNo, intentOrderNo)
                .eq(IntentOrderItemPO::getCreatedBy, userId)
                .set(IntentOrderItemPO::getIsDeleted, DeleteFlagEnum.DELETED.getCode())
                .set(IntentOrderItemPO::getUpdatedBy, userId)
                .set(IntentOrderItemPO::getUpdatedTime, LocalDateTime.now())
                .update();
        return bool ? Result.ok("意向单删除成功") : Result.fail("意向单删除失败");

    }

    @Override
    public Result<SubmitAndConfirmResDTO> saveIntentOrder(IntentOrderSaveReqDTO reqDTO) {
        return intentOrderSaveBiz.saveIntentOrder(reqDTO);
    }

    @Override
    public Result<SubmitAndConfirmResDTO> updateIntentOrder(IntentOrderSaveReqDTO reqDTO) {
        return intentOrderUpdateBiz.updateIntentOrder(reqDTO);
    }

    @Override
    public Result<SubmitAndConfirmResDTO> checkIntentOrder(IntentOrderConfirmReqDTO reqDTO) {
        return intentOrderCheckBiz.checkIntentOrder(reqDTO);
    }

    /**
     * 只有外部用户会提交意向单，给分销销售  新意向单 站内通知
     *
     * @param reqDTO
     * @return
     */
    @Override
    public Result<SubmitAndConfirmResDTO> submitIntentOrder(IntentOrderConfirmReqDTO reqDTO) {
        return intentOrderSubmitBiz.submitIntentOrder(reqDTO);
    }

    @Override
    public Result<SubmitAndConfirmResDTO> confirmPartIntentOrder(IntentOrderConfirmReqDTO reqDTO) {
        return intentOrderConfirmBiz.confirmPartIntentOrder(reqDTO);
    }

    /**
     * 三种情况
     * 1、代客下单，然后确认意向单：意向单状态为未提交  && 意向单创建人 与 意向单销售用户一致时     （新意向单通知）
     * 2、确认外部用户自建的意向单：意向单状态为已提交 && 意向单记录创建人 与 意向单所属人一致时    （确认意向单通知）
     * 3、更新编辑意向单    ：意向单状态为已经确认时                                        （更新意向单通知）
     *
     * @param reqDTO
     * @return
     */
    @Override
    public Result<SubmitAndConfirmResDTO> confirmIntentOrder(IntentOrderConfirmReqDTO reqDTO) {
        return intentOrderConfirmBiz.confirmIntentOrder(reqDTO);
    }

    @Override
    public Result<PageResponse<IntentOrderPCQueryResDTO>> findIntentOrderPCPage(PageRequest<IntentOrderPCQueryReqDTO> reqDTO) {
        return Result.ok(intentOrderPcPageBiz.findIntentOrderPCPage(reqDTO));
    }


    @Override
    public Result<List<IntentOrderPCExcelResDTO>> findIntentOrderPCPageList(IntentOrderPCQueryReqDTO reqDTO) {
        return Result.ok(intentOrderPcListBiz.findIntentOrderPCList(reqDTO));
    }


    @Override
    public Result<PageResponse<IntentOrderItemResDTO>> findIntentOrderItemPCPage(PageRequest<IntentOrderItemPCQueryReqDTO> reqDTO) {
        IntentOrderPO intentOrderPO = intentOrderMapperService.lambdaQuery().eq(IntentOrderPO::getIntentOrderNo,reqDTO.getQuery().getIntentOrderNo())
                .one();

        LambdaQueryWrapper<IntentOrderItemPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotBlank(reqDTO.getQuery().getIntentOrderNo()), IntentOrderItemPO::getIntentOrderNo, reqDTO.getQuery().getIntentOrderNo())
                .eq(IntentOrderItemPO::getIsDeleted,DeleteFlagEnum.NOT_DELETE.getCode())
                .orderByDesc(IntentOrderItemPO::getId);
        Page<IntentOrderItemPO> page = new Page<>(reqDTO.getIndex(), reqDTO.getSize());
        IPage<IntentOrderItemPO> orderIPage = intentOrderItemMapper.selectPage(page, queryWrapper);

        List<IntentOrderItemPO> pageList = orderIPage.getRecords();
        Set<String> productIds = pageList.stream().map(IntentOrderItemPO::getProductId).collect(Collectors.toSet());
        ProductModulePowerQueryReqDTO req = new ProductModulePowerQueryReqDTO();
        req.setProductIds(productIds);
        Result<List<ProductModulePowerDTO>> dtoResult = productFeign.queryProductModulePowerList(req);
        Map<String, List<ProductModulePowerDTO>> powerMap = dtoResult.getData().stream().collect(Collectors.groupingBy(ProductModulePowerDTO::getProductId));
        List<IntentOrderItemResDTO> resultList = new ArrayList<>();
        for (IntentOrderItemPO po : pageList) {
            IntentOrderItemResDTO resDTO = BeanUtil.toBean(po,IntentOrderItemResDTO.class);
            resDTO.setInputType(BuyTypeEnum.getDescByCode(resDTO.getInputType()));
            resDTO.setModuleType(ModuleTypeEnum.getDescByCode(resDTO.getModuleType()));
            if(ObjectUtils.isNotEmpty(intentOrderPO)){
                resDTO.setEfcText(intentOrderPO.isEfc()?"是":"否");
            }
            resDTO.setInstallationText(InstallationTypeEnum.getDescByCode(resDTO.getInstallation()));
            if (powerMap.containsKey(resDTO.getProductId())) {
                List<ProductModulePowerDTO> dtos = powerMap.get(resDTO.getProductId())
                        .stream().sorted(Comparator.comparing(ProductModulePowerDTO::getOutputPower)).toList();
                resDTO.setModulePowerDTOS(dtos);
            }
            resultList.add(resDTO);
        }
        PageResponse<IntentOrderItemResDTO> result = PageResponse.toResult(
                reqDTO.getIndex(),
                reqDTO.getSize(),
                (int) orderIPage.getTotal(),
                resultList);

        return Result.ok(result);
    }

    @Override
    public Result<PageResponse<IntentOrderAppQueryResDTO>> findIntentOrderAppPage(PageRequest<IntentOrderAppQueryReqDTO> reqDTO) {
        return Result.ok(intentOrderAppPageBiz.findIntentOrderAppPage(reqDTO));
    }

    @Override
    public Result<List<IntentOrderAppQueryResDTO>> findIntentOrderAppList(IntentOrderAppQueryReqDTO reqDTO) {
        return Result.ok(intentOrderAppPageBiz.findIntentOrderAppList(reqDTO));
    }

    @Override
    public Result<IntentOrderDetailResDTO> findIntentOrderDetail(IntentOrderPCQueryReqDTO reqDTO) {
        return Result.ok(intentOrderDetailBiz.findIntentOrderDetail(reqDTO));
    }

    @Override
    public Result<String> cancelStaffIntentOrder(IntentOrderCancelReqDTO reqDTO) {
        return intentOrderCancelBiz.cancelStaffIntentOrder(reqDTO);
    }

    @Override
    public Result<String> cancelPartnerIntentOrder(IntentOrderCancelReqDTO req) {
        return intentOrderCancelBiz.cancelPartnerIntentOrder(req);
    }

    @Override
    public void sendMessageForIntentOrder(IntentOrderMessageMqDTO req) {
       if(IntentOrderMessageSituationEnum.IO_EXTERNAL_CREATE.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_EXTERNAL_CREATE.getCode().equals(req.getSituationType())){
           externalSubmitMessageBiz.execute(req);
       }else if(IntentOrderMessageSituationEnum.IO_INTERNAL_CONFIRM_EXTERNAL_CREATE.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_INTERNAL_CONFIRM_EXTERNAL_CREATE.getCode().equals(req.getSituationType())){
           internalConfirmExternalCreateMessageBiz.execute(req);
       }else if(IntentOrderMessageSituationEnum.IO_EXTERNAL_CANCEL.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_EXTERNAL_CANCEL.getCode().equals(req.getSituationType())){
           externalCancelMessageBiz.execute(req);
       }else if(IntentOrderMessageSituationEnum.IO_INTERNAL_CANCEL.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_INTERNAL_CANCEL.getCode().equals(req.getSituationType())){
           internalCancelMessageBiz.execute(req);
       }else if(IntentOrderMessageSituationEnum.IO_INTERNAL_EDIT_AFTER_CONFIRM.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_INTERNAL_EDIT_AFTER_CONFIRM.getCode().equals(req.getSituationType())){
           internalEditAfterConfirmMessageBiz.execute(req);
       }else if (IntentOrderMessageSituationEnum.IO_INTERNAL_EDIT_AFTER_CONFIRM_TO_CONFIG.getCode().equals(req.getSituationType())) {
           internalEditAfterConfirmMessageBiz.executeToConfig(req);
       }else if (IntentOrderMessageSituationEnum.IO_INTERNAL_EDIT_REFUSE_CONFIG.getCode().equals(req.getSituationType())) {
           internalEditAfterConfirmMessageBiz.executeRefuse(req);
       }else if(IntentOrderMessageSituationEnum.IO_INTERNAL_CREATE.getCode().equals(req.getSituationType())||
               IntentOrderMessageSituationEnum.QO_INTERNAL_CREATE.getCode().equals(req.getSituationType())){
           internalCreateMessageBiz.execute(req);
       }else if(IntentOrderMessageSituationEnum.QO_EXTERNAL_CONFIRM_NOTICE.getCode().equals(req.getSituationType())){
           externalConfirmInternalCreateMessageBiz.execute(req);
       } else if (IntentOrderMessageSituationEnum.QO_CONTRACT_BPM_APPROVED.getCode().equals(req.getSituationType())) {
           contractBpmCallBackMessageBiz.executeApproved(req);
       } else if (IntentOrderMessageSituationEnum.QO_CONTRACT_BPM_REJECT.getCode().equals(req.getSituationType())) {
           contractBpmCallBackMessageBiz.executeReject(req);
       } else {
           log.info("意向单消息场景类型不存在");
       }
    }

    @Override
    public Result<IntentOrderSummaryResDTO> getSummaryData(IntentOrderPCQueryReqDTO reqDTO) {
        List<IntentOrderPCExcelResDTO> result = intentOrderPcListBiz.findIntentOrderPCList(reqDTO);
        IntentOrderSummaryResDTO summaryResDTO = new IntentOrderSummaryResDTO();
        if(!CollectionUtils.isEmpty(result)){
            BigDecimal totalPotentialSalesVolume = result.stream().map(IntentOrderPCExcelResDTO::getPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalProductPcsCount = result.stream().map(IntentOrderPCExcelResDTO::getProductPcsCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftPotentialSalesVolume = result.stream().map(IntentOrderPCExcelResDTO::getGiftPotentialSalesVolume)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal giftPcsCount = result.stream().map(IntentOrderPCExcelResDTO::getGiftPcsCount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            summaryResDTO.setTotalPotentialSalesVolume(totalPotentialSalesVolume);
            summaryResDTO.setTotalProductPcsCount(totalProductPcsCount);
            summaryResDTO.setGiftPotentialSalesVolume(giftPotentialSalesVolume);
            summaryResDTO.setGiftProductPcsCount(giftPcsCount);
        }
        return Result.ok(summaryResDTO);

    }

    @Override
    public void cleanCaffeineCache(IntentOrderMessageMqDTO req) {
        caffeineCacheClearBiz.clearCache(req);
    }

    @Override
    public Result<Map<String,List<IntentOrderReportResDTO>>> reportAmountChart(IntentOrderReportReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportAmountChart(reqDTO));
    }

    @Override
    public Result<List<IntentOrderReportResDTO>> reportAmountList(IntentOrderReportReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportAmountList(reqDTO));
    }

    @Override
    public Result<Map<String, List<IntentOrderReportResDTO>>> reportPowerChart(IntentOrderReportReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportPowerChart(reqDTO));
    }

    @Override
    public Result<List<IntentOrderReportResDTO>> reportPowerList(IntentOrderReportReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportPowerList(reqDTO));
    }

    @Override
    public Result<List<IntentOrderReportProductResDTO>> reportProductList(IntentOrderReportProductReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportProductList(reqDTO));
    }

    @Override
    public Result<List<IntentOrderReportProductResDTO>> reportProductChart(IntentOrderReportProductReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportProductChart(reqDTO));
    }

    @Override
    public Result<IntentOrderReportEnterpriseSummaryResDTO> reportEnterpriseAmountChart(IntentOrderReportEnterpriseReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportEnterpriseAmountChart(reqDTO));
    }

    @Override
    public Result<IntentOrderReportEnterpriseSummaryResDTO> reportEnterprisePowerChart(IntentOrderReportEnterpriseReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportEnterprisePowerChart(reqDTO));
    }

    @Override
    public Result<List<IntentOrderReportEnterpriseResDTO>> reportEnterpriseList(IntentOrderReportEnterpriseReqDTO reqDTO) {
        return Result.ok(intentOrderReportBiz.reportEnterpriseList(reqDTO));
    }

    @Override
    public Result<String> intentOrderChangeOwner(IntentOrderChangeOwnerReqDTO reqDTO) {
        return intentOrderChangeOwnerBiz.changeOwner(reqDTO);
    }

    @Override
    public Result<List<IntentOrderReportTrendResDTO>> trendList(IntentOrderReportTrendReqDTO reqDTO) {
        return Result.ok(intentOrderTrendReportBiz.trendList(reqDTO));
    }

    @Override
    public Result<IntentOrderCockpitResDTO> trendChart(IntentOrderCockpitReqDTO reqDTO) {
        return Result.ok(intentOrderAppPageBiz.trendChart(reqDTO));
    }

    @Override
    public Result<List<IntentOrderCockpitDetailsResDTO>> trendChartDetails(IntentOrderCockpitReqDTO reqDTO) {
        return Result.ok(intentOrderAppPageBiz.trendChartDetails(reqDTO));
    }

    @Override
    public Result<List<IntentOrderlistExternalEnterpriseDTO>> listExternalEnterprise(PageExternalEnterpriseQueryDTOCockpit reqDTO) {
        return Result.ok(intentOrderAppPageBiz.listExternalEnterprise(reqDTO));
    }

    @Override
    public SubmitAndConfirmResDTO externalConfirmIntentOrder(ExternalIntentOrderConfirmReqDTO reqDTO) {
        return intentOrderConfirmBiz.externalConfirmIntentOrder(reqDTO);
    }

    @Override
    public Result<SubmitAndConfirmResDTO> configIntentOrder(IntentOrderConfigReqDTO reqDTO) {
        return intentOrderConfirmBiz.configIntentOrder(reqDTO);
    }

    @Override
    public void refuseIntentOrder(IntentOrderConfigReqDTO reqDTO) {
        intentOrderConfirmBiz.refuseIntentOrder(reqDTO);
    }
    @Override
    public List<IntentOrderPCQueryResDTO> findIntentOrderByOrderNo(List<String> intentOrderNoList) {
        if(!CollectionUtils.isEmpty(intentOrderNoList)){
            LambdaQueryWrapper<IntentOrderPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(IntentOrderPO::getIntentOrderNo, intentOrderNoList);
            return intentOrderMapperService.list(queryWrapper).stream().map(item->{
                IntentOrderPCQueryResDTO intentOrderPCQueryResDTO=  new IntentOrderPCQueryResDTO();
                BeanUtils.copyProperties(item, intentOrderPCQueryResDTO);
                return intentOrderPCQueryResDTO;
            }).toList();
        }
        return Collections.emptyList();
    }
}
