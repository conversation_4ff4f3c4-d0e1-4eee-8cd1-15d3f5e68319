<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.trinasolar</groupId>
		<artifactId>trinax-shared</artifactId>
		<version>0.0.2.RELEASE</version>
		<relativePath>../trinax-shared</relativePath>
	</parent>

	<packaging>pom</packaging>
	<artifactId>trinax-contract-root</artifactId>
	<description>Demo project for Spring Boot</description>
	<version>0.0.4-SNAPSHOT</version>
	<properties>
		<java.version>17</java.version>
		<dtt-xxljob.version>0.0.2.RELEASE</dtt-xxljob.version>
	</properties>
	<modules>
		<module>trinax-contract-api</module>
		<module>trinax-contract-domain</module>
		<module>trinax-contract-boot</module>
		<module>trinax-billing-domain</module>
	</modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>4.0.1</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>5.2.2</version>
			</dependency>
			<dependency>
				<groupId>com.deepoove</groupId>
				<artifactId>poi-tl</artifactId>
				<version>1.12.2</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-contract-api</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-contract-domain</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-billing-domain</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-common</artifactId>
				<version>0.0.2.RELEASE</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-user-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-basic-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-delivery-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-integration-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-partner-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.trinasolar</groupId>
				<artifactId>trinax-masterdata-api</artifactId>
				<version>0.0.4-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>dtt.asset</groupId>
				<artifactId>dtt-framework-xxljob</artifactId>
				<version>${dtt-xxljob.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
