package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractQueryAuthSalesDTO {

    @Schema(description = "销售人员ID")
    private String salesInternalUserId;

    @Schema(description = "业务数据所属组织CODE")
    private String bizOrganizationCode;

}
