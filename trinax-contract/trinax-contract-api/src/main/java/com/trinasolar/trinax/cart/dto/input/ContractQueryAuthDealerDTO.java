package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractQueryAuthDealerDTO {

    @Schema(description = "外部人员ID")
    private String externalUserId;

    @Schema(description = "企业id")
    private String enterpriseId;

}
