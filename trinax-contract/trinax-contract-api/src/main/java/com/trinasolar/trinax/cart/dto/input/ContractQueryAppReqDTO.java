package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "合同-app查询")
public class ContractQueryAppReqDTO extends ContractQueryAuthDTO {

    @Schema(description = "德勤通合同主键ID（列表）", hidden = true)
    private List<String> contractIdList;

    @Schema(description ="查询参数")
    private String queryP;

    @Schema(description ="合同状态（列表）")
    private List<String> contractStatusList;

    @Schema(description = "意向单编号(意向单列表查询需要)")
    private String intentOrderNo;

    @Schema(description = "公司名称")
    private String enterpriseName;

    @Schema(description = "合同编号")
    private String sfContractNo;

    @Schema(description = "原始合同编号")
    private String sfContractFamilyNo;

    @Schema(description = "申请开始时间")
    private LocalDateTime applyDateStart;

    @Schema(description = "申请结束时间")
    private LocalDateTime applyDateEnd;

    @Schema(description = "销售人员")
    private String salesInternalUserName;

    @Schema(description = "签约主体")
    private String signEntity;

}
