package com.trinasolar.trinax.requireorder.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RequireOrderFileResDTO{
    /**
     * 自增主键ID
     */
    private Long id;

    @Schema(description ="需求单号")
    private String requireOrderNo;

    @Schema(description ="文件路径")
    private String fileUrl;

    @Schema(description ="文件类型")
    private String fileType;

    @Schema(description ="文件名")
    private String fileName;

    @Schema(description ="删除标识：0-未删除，1-删除")
    private Integer isDeleted;

    @Schema(description ="创建人ID")
    private String createdBy;

    @Schema(description ="创建人名称")
    private String createdName;

    @Schema(description ="创建时间")
    private LocalDateTime createdTime;

    @Schema(description ="更新人ID")
    private String updatedBy;

    @Schema(description ="更新人名称")
    private String updatedName;

    @Schema(description ="更新时间")
    private LocalDateTime updatedTime;

}
