package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(description = "购物车-添加")
public class CartAddReqDTO {
    @Schema(description ="购物车业务主键ID，修改时传入")
    private String cartId;
    @Schema(description ="产品ID，修改时可不传")
    private String productId;
    @Schema(description ="功率")
    private String power;
    @Schema(description ="购买方式：片-P，瓦-W")
    private String purchaseType;
    @Schema(description ="数量（片）")
    private Long quantityP;
    @Schema(description ="数量（瓦）")
    private BigDecimal quantityW;
    @Schema(description ="数量（MW）")
    private BigDecimal quantityMw;
    @Schema(description ="瓦单价")
    private BigDecimal avgPrice;
    @Schema(description ="净价")
    private BigDecimal netPrice;
    @Schema(description ="其他成本价")
    private BigDecimal otherCostPrice;

    @Schema(description = "创建人，前端不传值，后端默认赋值当前登录用户")
    private String createdBy;
    @Schema(description = "创建人，前端不传值，后端默认赋值当前登录用户")
    private String createdName;
}
