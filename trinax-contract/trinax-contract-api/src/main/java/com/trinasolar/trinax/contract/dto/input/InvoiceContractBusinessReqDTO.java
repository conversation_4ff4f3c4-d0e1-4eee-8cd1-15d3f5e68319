package com.trinasolar.trinax.contract.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "builder")
@Schema(description = "开票申请-合同族编号列表")
public class InvoiceContractBusinessReqDTO {

    @Schema(description = "合同族编号列表")
    private List<String> contractFamilyNoList;

}
