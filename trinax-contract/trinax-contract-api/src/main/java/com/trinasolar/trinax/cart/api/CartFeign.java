package com.trinasolar.trinax.cart.api;

import com.trinasolar.trinax.cart.constants.ServiceIds;
import com.trinasolar.trinax.cart.dto.input.CartAddReqDTO;
import com.trinasolar.trinax.cart.dto.input.CartDeleteReqDTO;
import com.trinasolar.trinax.cart.dto.input.CartDetailQueryReqDTO;
import com.trinasolar.trinax.cart.dto.input.CartQueryReqDTO;
import com.trinasolar.trinax.cart.dto.output.CartQueryResDTO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(tags = "购物车API")
@FeignClient(name = ServiceIds.CART_SERVICE)
public interface CartFeign {

    @PostMapping("/cart/queryCartByPage")
    @ApiOperation("购物车-列表查询")
    Result<PageResponse<CartQueryResDTO>> queryCartByPage(@RequestBody PageRequest<CartQueryReqDTO> req);

    @PostMapping("/cart/queryByCartIds")
    @ApiOperation("购物车-根据cartId获取对应的购物车明细信息（支持批量）")
    Result<List<CartQueryResDTO>> queryByCartIds(@RequestBody CartDetailQueryReqDTO req);

    @PostMapping("/cart/getCartCount")
    @ApiOperation("购物车-获取用户购物车数量")
    Result<Integer> getCartCount(@RequestBody CartQueryReqDTO reqDTO);

    @PostMapping("/cart/addCart")
    @ApiOperation("购物车-添加")
    Result<Boolean> addCart(@RequestBody CartAddReqDTO reqDTO);

    @PostMapping("/cart/delCart")
    @ApiOperation("购物车-移除（支持批量）")
    Result<Boolean> delCart(@RequestBody CartDeleteReqDTO reqDTO);

}
