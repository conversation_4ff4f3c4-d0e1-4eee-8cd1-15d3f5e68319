package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合同-pc查询")
public class ContractQueryPcReqDTO extends ContractQueryAuthDTO {

    @Schema(description = "申请日期开始（使用的是合同的创建日期）")
    private LocalDateTime applyDateStart;

    @Schema(description = "申请日期开始（使用的是合同的创建日期）")
    private LocalDateTime applyDateEnd;

    @Schema(description = "合同号（模糊）")
    private String sfContractNo;

    @Schema(description = "合同状态")
    private String contractStatus;

    @Schema(description = "销售人员姓名（模糊）")
    private String salesInternalUserName;

    @Schema(description = "客户名称（模糊）")
    private String enterpriseName;

    @Schema(description = "合同族编号（模糊）")
    private String sfContractFamilyNo;

    @Schema(description = "销售人员ID")
    private String salesInternalUserId;

    @Schema(description = "外部用户id")
    private String externalUserId;

    @Schema(description = "客户ID")
    private String enterpriseId;

    @Schema(description = "签约方客户类型")
    private String customerCategory;

    @Schema(description = "关联方企业名称")
    private String capitalEnterpriseName;

    @Schema(description = "签约主体")
    private String signEntity;

    @Schema(description = "业务所属战区编码")
    private String bizOrganizationCode;

    @Schema(description = "合同订单类型")
    private Integer orderType;
}
