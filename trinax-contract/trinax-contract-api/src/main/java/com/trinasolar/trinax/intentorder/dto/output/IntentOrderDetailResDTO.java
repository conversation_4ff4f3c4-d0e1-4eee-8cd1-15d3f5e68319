package com.trinasolar.trinax.intentorder.dto.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Schema(description = "意向单详情返回结果")
public class IntentOrderDetailResDTO {

    private List<IntentOrderItemResDTO> itemList;

    /**
     * 自增id
     */
    private Long id;

    @Schema(description = "意向单单号")
    private String intentOrderNo;

    @Schema(description = "SF商机编号")
    private String opportunityId;

    @Schema(description = "企业业务主键ID")
    private String enterpriseId;

    @Schema(description = "客户角色")
    private String accountRole;

    @Schema(description = "客户角色")
    private String accountRoleText;

    @Schema(description = "销售账套Id")
    private String requestedOu;

    @Schema(description = "账套名称")
    private String requestedOuName;

    @Schema(description = "税分类")
    private String taxClassification;

    @Schema(description = "税分类-名称")
    private String taxName;

    @Schema(description = "应用场景：业务机会市场分类")
    private String opportunitySegment;

    @Schema(description = "应用场景Text")
    private String opportunitySegmentText;

    @Schema(description = "业务机会记录分类")
    private String opportunityRecordType;

    @Schema(description = "业务机会渠道分类")
    private String opportunityChannelType;

    @Schema(description = "业务机会渠道分类")
    private String opportunityChannelTypeText;

    @Schema(description = "交货区域")
    private String goodsArrivalRegion;

    @Schema(description = "交货区域-文本")
    private String goodsArrivalRegionText;

    @Schema(description = "交货子区域")
    private String goodsArrivalSubRegion;

    @Schema(description = "交货子区域-文本呢")
    private String goodsArrivalSubRegionText;

    @Schema(description = "地区")
    private String area;

    @Schema(description = "地区-文本")
    private String areaText;

    @Schema(description = "需求量汇总（MW）")
    private BigDecimal potentialSalesVolume;

    @Schema(description = "产品需求总量（W）")
    private BigDecimal requirements;

    @Schema(description = "预计发货日期")
    private LocalDate estimatedDeliveryDate;

    @Schema(description = "商机币种")
    private String opportunityCurrency;

    @Schema(description = "商机币种")
    private String opportunityCurrencyText;

    @Schema(description = "内部销售用户userId")
    private String salesInternalUserId;

    @Schema(description = "内部销售用户姓名")
    private String salesInternalUserName;

    @Schema(description = "销售用户手机号")
    private String salesInternalUserPhone;

    @Schema(description = "业务所属战区编码")
    private String bizOrganizationCode;

    @Schema(description = "业务所属战区")
    private String bizOrganizationName;

    @Schema(description = "意向单所属用户名称：外部用户名")
    private String externalUserName;

    @Schema(description = "意向单所属用户userId：外部用户userId")
    private String externalUserId;

    @Schema(description = "是否关键用户")
    private Boolean regionalKeyAccount;

    @Schema(description = "是否关键用户")
    private String regionalKeyAccountText;

    @Schema(description = "贸易术语-提货类型：自提/非自提")
    private String incoterm;

    @Schema(description = "配送方式")
    private String incotermText;

    @Schema(description = "中国/海外工厂")
    private String factoryRegionType;

    @Schema(description = "中国/海外工厂-文本")
    private String factoryRegionTypeText;

    @Schema(description = "付款条款")
    private String paymentTerm;

    @Schema(description = "付款条款-文本")
    private String paymentTermText;

    @Schema(description = "行业属性")
    private String industryAttributes;

    @Schema(description = "行业属性-中文")
    private String industryAttributesText;

    @Schema(description = "操作类型")
    private String opType;

    @Schema(description = "取消/丢单原因类型：选填")
    private String cancelReasonType;

    @Schema(description = "其他取消原因：客户自己填")
    private String otherCancelReason;

    @Schema(description = "竞争对手")
    private String competitorInfo;

    @Schema(description = "其他竞争对手信息-json")
    private String otherCompetitorInfo;

    @Schema(description = "意向单状态")
    private String status;

    @Schema(description = "意向单状态-文本")
    private String statusText;

    @Schema(description = "产品名称，逗号分割，冗余用于搜索")
    private String productName;

    @Schema(description = "是否代客下单")
    private String isSurrogateOrder;

    @Schema(description = "是否代客下单-文本")
    private String isSurrogateOrderText;

    @Schema(description = "合计金额（参考价）")
    private BigDecimal guideTotalAmount;

    @Schema(description = "合计金额（销售价）")
    private BigDecimal saleTotalAmount;

    @Schema(description = "片数汇总（片）")
    private BigDecimal productPcsCount;

    @Schema(description = "赠品片数汇总（片）")
    private BigDecimal giftPcsCount;

    @Schema(description = "意向单备注")
    private String remark;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "创建人姓名")
    private String createdName;

    @Schema(description = "创建时间")
    private LocalDateTime createdTime;

    @Schema(description = "更新人")
    private String updatedBy;

    @Schema(description = "更新人姓名")
    private String updatedName;

    @Schema(description = "更新时间")
    private LocalDateTime updatedTime;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "删除标识：1-删除，0-未删除")
    private String isDeleted;

    @Schema(description = "创建人联系方式")
    private String createdPhone;

    @Schema(description = "efc")
    private boolean efc;

    @Schema(description = "efc-文本")
    private String efcText;

    @Schema(description = "客户方联系方式：外部用户联系方式")
    private String externalUserPhone;

    @Schema(description = "期望交货日期")
    private LocalDate expectDeliveryDate;

    @Schema(description = "区域|子区域|地区")
    private String fullRegion;

    @Schema(description = "区域子区域地区")
    private String fullRegionText;

    @Schema(description = "关联的德勤通合同主键ID(列表)")
    private List<String> contractIds;

    @Schema(description = "关联方企业id")
    private String capitalEnterpriseId;

    @Schema(description = "关联方企业名称")
    private String capitalEnterpriseName;

    @Schema(description = "赠品需求量汇总（MW）")
    private BigDecimal giftPotentialSalesVolume;

    @Schema(description = "运营用户userName")
    private String operationInternalUserName;

    @Schema(description = "运营用户userId")
    private String operationInternalUserId;

    @Schema(description = "取消用户userId")
    private String cancelUserId;

    @Schema(description = "取消用户名称")
    private String cancelUserName;

    @Schema(description = "取消时间")
    private LocalDateTime cancelTime;

    @Schema(description = "取消用户类型")
    private String cancelUserType;

    @Schema(description = "来源业务单号：需求单转意向单时为需求单号")
    private String sourceBizNo;

    @Schema(description = "来源类型")
    private String sourceType;

    @Schema(description = "签约实体：存在关联方时为关联方，否则为大商")
    private String signEntity;

    @Schema(description = "签约实体的客户类型")
    private String customerCategory;

    @Schema(description = "签约实体的客户类型:描述")
    private String customerCategoryText;

    @Schema(description = "框架合同sf Id")
    private String contractFrameId;

    @Schema(description = "收货联系人")
    private String deliverContactor;

    @Schema(description = "收货联系电话")
    private String deliverContactPhone;

    @Schema(description = "收货地址")
    private String deliverAddress;

    @Schema(description = "是否有多功率发货需求 1:是，0:否")
    private Integer deliverMultiPower;

    @Schema(description = "1:普通意向单 2:快速订单")
    private Integer orderType;

}
