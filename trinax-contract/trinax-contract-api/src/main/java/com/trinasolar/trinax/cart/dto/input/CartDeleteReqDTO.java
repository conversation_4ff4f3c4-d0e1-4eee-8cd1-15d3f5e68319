package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Schema(description = "购物车-移除（支持批量）")
public class CartDeleteReqDTO {
    @NotNull(message = "购物车业务主键ID不能为空")
    @Schema(description ="购物车业务主键id")
    private List<String> cartIds;

    @Schema(description = "修改人，前端不传值，后端默认赋值当前登录用户")
    private String updatedBy;
    @Schema(description = "修改人，前端不传值，后端默认赋值当前登录用户")
    private String updatedName;
}
