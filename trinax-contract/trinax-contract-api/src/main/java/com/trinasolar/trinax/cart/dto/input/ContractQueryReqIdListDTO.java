package com.trinasolar.trinax.cart.dto.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "合同-根据合同id列表查询")
public class ContractQueryReqIdListDTO {
    @Schema(description ="德勤通合同主键ID列表")
    private List<String> contractIds;

}
