package com.trinasolar.trinax.billing.invoice.service.biz.message;

import com.trinasolar.trinax.basic.constants.BasicCommonConstant;
import com.trinasolar.trinax.basic.constants.enums.messagetemplate.MessageTemplateNoticeTypeEnum;
import com.trinasolar.trinax.basic.dto.input.MessageSendCommonReqDTO;
import com.trinasolar.trinax.basic.dto.input.MessageSendNoticeReqDTO;
import com.trinasolar.trinax.basic.event.MessageBizCodeEnum;
import com.trinasolar.trinax.billing.dto.mq.InvoiceMessageMqDTO;
import com.trinasolar.trinax.billing.invoice.manager.InvoiceMessageFillManager;
import com.trinasolar.trinax.common.utils.JacksonUtil;
import dtt.asset.dttframeworkmq.manager.MqManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class InvoiceExternalSubmitMessageBiz {

    @Autowired
    private MqManager mqManager;

    @Value("${message.template.invoice.externalSubmitNotice}")
    private String noticeTemplateCode;

    @Autowired
    private InvoiceMessageFillManager invoiceMessageFillManager;

    public void execute(InvoiceMessageMqDTO reqDTO) {
        log.info("外部用户提交开票申请,给分销销售发通知消息入参：{}", reqDTO);
        MessageSendCommonReqDTO noticeMessageReqDTO = new MessageSendCommonReqDTO();
        noticeMessageReqDTO.setNoticeType(MessageTemplateNoticeTypeEnum.NOTICE.getCode());

        List<MessageSendNoticeReqDTO> noticeList = new ArrayList<>();
        Map<String, String> content = new HashMap<>();
        content.put("enterpriseName", reqDTO.getEnterpriseName());
        content.put("externalUserName", reqDTO.getExternalUserName());
        content.put("invoiceNo", reqDTO.getInvoiceNo());

        MessageSendNoticeReqDTO noticeReqDTO = invoiceMessageFillManager.generateNotice(reqDTO
                ,"", MessageBizCodeEnum.INVOICE_EXTERNAL_SUBMIT.getValue(),
                noticeTemplateCode,Collections.singletonList(reqDTO.getSaleInternalUserId()),content);

        noticeList.add(noticeReqDTO);
        noticeMessageReqDTO.setNoticeList(noticeList);
        mqManager.sendTopic(BasicCommonConstant.BASIC_MESSAGE_TOPIC,JacksonUtil.bean2Json(noticeMessageReqDTO));
        log.info("外部用户提交开票申请,给分销销售发通知消息完成，param:{}", JacksonUtil.bean2Json(noticeMessageReqDTO));
    }
}
