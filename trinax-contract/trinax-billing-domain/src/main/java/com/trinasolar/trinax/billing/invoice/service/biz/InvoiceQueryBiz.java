package com.trinasolar.trinax.billing.invoice.service.biz;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.trinax.billing.constants.enums.ApplicationStatusEnum;
import com.trinasolar.trinax.billing.constants.enums.InvoiceTypeEnum;
import com.trinasolar.trinax.billing.constants.enums.InvoicingUnitEnum;
import com.trinasolar.trinax.billing.dto.input.InvoiceDetailQryReqDTO;
import com.trinasolar.trinax.billing.dto.input.InvoiceListReqDTO;
import com.trinasolar.trinax.billing.dto.input.SubDealer;
import com.trinasolar.trinax.billing.dto.output.InvoiceApplicationDetailResDTO;
import com.trinasolar.trinax.billing.dto.output.InvoiceResDTO;
import com.trinasolar.trinax.billing.invoice.manager.AuthManager;
import com.trinasolar.trinax.billing.invoice.manager.InvoiceQueryManager;
import com.trinasolar.trinax.billing.invoice.repository.atomicservice.InvoiceApplicationMapperService;
import com.trinasolar.trinax.billing.invoice.repository.atomicservice.InvoiceProductMapperService;
import com.trinasolar.trinax.billing.invoice.repository.mapper.InvoiceApplicationMapper;
import com.trinasolar.trinax.billing.invoice.repository.po.InvoiceApplicationPO;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.user.api.SysDealerSalesRelationFeign;
import com.trinasolar.trinax.user.constants.SysUserTypeEnum;
import com.trinasolar.trinax.user.dto.output.SysSubDealerRespDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class InvoiceQueryBiz {
    final InvoiceApplicationMapperService invoiceApplicationMapperService;
    final InvoiceProductMapperService invoiceProductMapperService;
    final InvoiceApplicationMapper invoiceApplicationMapper;
    final InvoiceQueryManager invoiceQueryManager;
    final AuthManager authManager;
    final SysDealerSalesRelationFeign sysDealerSalesRelationFeign;

    public Result<List<InvoiceResDTO>> invoiceApplicationList(InvoiceListReqDTO req) {


        InvoiceListReqDTO reqLoad = reqInit(req);

        List<InvoiceResDTO> result = invoiceApplicationMapper.qryInvoiceFromApp(reqLoad);

        if (ObjectUtils.isEmpty(result)) {
            return Result.ok();
        }

        result.forEach(a -> {
            a.setInvoicingUnitStr(InvoicingUnitEnum.getDescByCode(a.getInvoicingUnit()));
            a.setApplicationStatusStr(ApplicationStatusEnum.getDescByCode(a.getApplicationStatus()));
            a.setInvoiceTypeName(InvoiceTypeEnum.getDescByCode(a.getInvoiceType()));
        });

        return Result.ok(result);
    }

    private InvoiceListReqDTO reqInit(InvoiceListReqDTO req) {
        if (SysUserTypeEnum.INTERNAL.getType().equals(req.getCurrentUserType())) {
            List<String> statusList = new ArrayList<>();
            statusList.add(ApplicationStatusEnum.SUBMITTED.getCode());
            req.setStatusList(statusList);
            List<String> userList = authManager.userInternalRelation(req.getCurrentUserId(), "SALES");
            req.setUserList(userList);
        }
        if (SysUserTypeEnum.EXTERNAL.getType().equals(req.getCurrentUserType())) {
            List<SysSubDealerRespDTO> userList = sysDealerSalesRelationFeign.getSubDealerInfo(req.getCurrentUserId()).getData();
            if (ObjectUtil.isEmpty(userList)) {
                userList = new ArrayList<>();
                SysSubDealerRespDTO user = new SysSubDealerRespDTO();
                user.setDealerUserId(req.getCurrentUserId());
                userList.add(user);
            }
            req.setSubDealerRel(BeanUtil.copyToList(userList, SubDealer.class));
            req.setCurrentUserType(SysUserTypeEnum.EXTERNAL.getType());
        }
        return req;
    }


    public Result<InvoiceApplicationDetailResDTO> invoiceApplicationDetail(InvoiceDetailQryReqDTO req) {
        InvoiceApplicationPO invoiceApplication = invoiceApplicationMapperService.lambdaQuery()
                .eq(InvoiceApplicationPO::getInvoiceApplicationId, req.getInvoiceApplicationId()).one();
        if (ObjectUtils.isEmpty(invoiceApplication)) {
            return Result.ok();
        }

        InvoiceApplicationDetailResDTO result = BeanUtil.copyProperties(invoiceApplication, InvoiceApplicationDetailResDTO.class);

        result.setApplicationStatusStr(ApplicationStatusEnum.getDescByCode(result.getApplicationStatus()));
        result.setInvoiceTypeName(InvoiceTypeEnum.getDescByCode(result.getInvoiceType()));
        result.setInvoicingUnitStr(InvoicingUnitEnum.getDescByCode(result.getInvoicingUnit()));
        return Result.ok(result);
    }

    public Result<PageResponse<InvoiceResDTO>> invoiceApplicationPage(PageRequest<InvoiceListReqDTO> req) {
        InvoiceListReqDTO reqLoad = reqInit(req.getQuery());

        IPage<InvoiceResDTO> qryResult = invoiceApplicationMapper.qryInvoicePageFromApp(new Page<>(req.getIndex(), req.getSize()), reqLoad);
        qryResult.getRecords().forEach(a -> {
            a.setInvoicingUnitStr(InvoicingUnitEnum.getDescByCode(a.getInvoicingUnit()));
            a.setApplicationStatusStr(ApplicationStatusEnum.getDescByCode(a.getApplicationStatus()));
            a.setInvoiceTypeName(InvoiceTypeEnum.getDescByCode(a.getInvoiceType()));
        });

        return Result.ok(PageResponse.toResult(
                req.getIndex(),
                req.getSize(),
                (int) qryResult.getTotal(),
                qryResult.getRecords()));
    }
}
