package com.trinasolar.trinax.billing.invoice.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trinasolar.trinax.billing.dto.input.contract.InvoiceContractListReqDTO;
import com.trinasolar.trinax.billing.dto.output.contract.InvoiceContractResDTO;
import com.trinasolar.trinax.billing.invoice.repository.po.InvoiceContractMainPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 开票OA合同主信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Mapper
public interface InvoiceContractMainMapper extends BaseMapper<InvoiceContractMainPO> {

    List<InvoiceContractResDTO> getList(@Param("req") InvoiceContractListReqDTO queryDTO);

}
