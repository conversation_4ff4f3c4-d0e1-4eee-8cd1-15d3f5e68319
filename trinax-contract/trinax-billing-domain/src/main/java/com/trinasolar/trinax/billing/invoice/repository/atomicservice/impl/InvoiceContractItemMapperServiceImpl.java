package com.trinasolar.trinax.billing.invoice.repository.atomicservice.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.trinax.billing.invoice.repository.atomicservice.InvoiceContractItemMapperService;
import com.trinasolar.trinax.billing.invoice.repository.mapper.InvoiceContractItemMapper;
import com.trinasolar.trinax.billing.invoice.repository.po.InvoiceContractItemPO;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 开票OA合同产品行信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-28
 */
@Service
public class InvoiceContractItemMapperServiceImpl extends ServiceImpl<InvoiceContractItemMapper, InvoiceContractItemPO> implements InvoiceContractItemMapperService {

}
