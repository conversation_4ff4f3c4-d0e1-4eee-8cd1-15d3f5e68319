FROM openjdk:17-jdk-slim

# 设置时区
RUN /bin/cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' > /etc/timezone

WORKDIR /data/app

#ARG JAR_PATH
COPY ./target/trinax-sharedapi.jar /data/app/trinax-sharedapi.jar

##EXPOSE ${EXPOSE_PORT}

ENV SPRING_PROFILES_ACTIVE="local"

CMD ["sh", "-c", "java $JAVA_OPTS -jar /data/app/trinax-sharedapi.jar --spring.profiles.active=$SPRING_PROFILES_ACTIVE --add-opens java.base/java.util=ALL-UNNAMED"]
