package com.trinasolar.trinax.sharedapi.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.trinasolar.trinax.auth.core.details.AuthUserDetails;
import com.trinasolar.trinax.auth.core.security.AuthUserHelper;
import com.trinasolar.trinax.common.dto.input.PageRequest;
import com.trinasolar.trinax.common.dto.output.PageResponse;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.partner.dto.output.EnterpriseBizRelationResDTO;
import com.trinasolar.trinax.sharedapi.dto.input.ManagerDealQueryDTO;
import com.trinasolar.trinax.sharedapi.dto.input.SaleDealQueryDTO;
import com.trinasolar.trinax.user.api.SysUserFeign;
import com.trinasolar.trinax.user.constants.BindEnterpriseEnum;
import com.trinasolar.trinax.user.constants.SaleFollowStatusEnum;
import com.trinasolar.trinax.user.constants.UserConstant;
import com.trinasolar.trinax.user.dto.input.ConfigSaleDto;
import com.trinasolar.trinax.user.dto.input.DealUsersQueryDTO;
import com.trinasolar.trinax.user.dto.input.UpdateUserDto;
import com.trinasolar.trinax.user.dto.output.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequiredArgsConstructor
@Slf4j
@Tag(name = "新用户跟进接口")
@RequestMapping("/dealUser")
public class DealUserController {

    private final SysUserFeign sysUserFeign;

    @ApiOperation("区域总用户跟进列表")
    @Operation(description = "区域总用户跟进列表")
    @PostMapping(value = "/dealUser")
    public Result<PageResponse<DealUserDto>> getDealUserPage(@RequestBody PageRequest<ManagerDealQueryDTO>  request) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();

        Result<SysUserRespDTO> userByUserResult = sysUserFeign.getUserByUserId(authUser.getUserIdStr());
        Assert.isTrue(userByUserResult.getSuccess(),"用户当前查询失败");

        SysUserRespDTO sysUserRespDTO=userByUserResult.getData();
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
            // 销售区域总能查看到自己所在战区的所有客户
            PageRequest<DealUsersQueryDTO>  queryRequest=new PageRequest<>();
            DealUsersQueryDTO dealUsersQueryDTO=new DealUsersQueryDTO();
            dealUsersQueryDTO.setOrganizationCode(sysUserRespDTO.getOrganizationCode());
            if(request.getQuery().getIsAssigned().equals(0)){
                dealUsersQueryDTO.setIsAssignManager(1);//已分配大区总
                dealUsersQueryDTO.setIsAssignSale(0);//未分配销售
            }else{
                dealUsersQueryDTO.setIsAssignManager(1);//已分配大区总
                dealUsersQueryDTO.setIsAssignSale(1);//未分配销售
            }

            dealUsersQueryDTO.setIsBindEnterprise(BindEnterpriseEnum.NO.getCode());//发起绑定企业的，就从大区总列表中移除
            dealUsersQueryDTO.setKeyword(request.getQuery().getKeyword());
            dealUsersQueryDTO.setUserProvinceCode(request.getQuery().getUserProvinceCode());
            dealUsersQueryDTO.setUserCityCode(request.getQuery().getUserCityCode());
            queryRequest.setQuery(dealUsersQueryDTO);

            //分页参数赋值
            queryRequest.setSize(request.getSize());
            queryRequest.setIndex(request.getIndex());

            return sysUserFeign.getDealUserPage(queryRequest);
        }else{
            //否则返回空数据
            return  Result.ok(new PageResponse());
        }
    }

    @ApiOperation("销售用户跟进列表")
    @Operation(description = "销售用户跟进列表")
    @PostMapping(value = "/sale/dealUser")
    public Result<PageResponse<DealUserDto>> getSaleDealUserPage(@RequestBody PageRequest<SaleDealQueryDTO>  request) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();

        Result<SysUserRespDTO> userByUserResult = sysUserFeign.getUserByUserId(authUser.getUserIdStr());
        Assert.isTrue(userByUserResult.getSuccess(),"用户当前查询失败");

        SysUserRespDTO sysUserRespDTO=userByUserResult.getData();
//        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.SALES_ROLE_ID)) {
            // 销售区域总能查看到自己所在战区的所有客户
            PageRequest<DealUsersQueryDTO>  queryRequest=new PageRequest<>();
            DealUsersQueryDTO dealUsersQueryDTO=new DealUsersQueryDTO();
            dealUsersQueryDTO.setOrganizationCode(sysUserRespDTO.getOrganizationCode());
            dealUsersQueryDTO.setIsAssignManager(1);//已分配大区总
            dealUsersQueryDTO.setIsAssignSale(1);//已分配销售
            dealUsersQueryDTO.setExclusiveSale(sysUserRespDTO.getUserId());
            if(request.getQuery().getSaleFollowStatus()!=null){
                //此处需要做转换
                if(request.getQuery().getSaleFollowStatus().equals(0)||request.getQuery().getSaleFollowStatus().equals(1)){
                    dealUsersQueryDTO.setSaleFollowStatus(Arrays.asList(request.getQuery().getSaleFollowStatus()));
                }else{
                    dealUsersQueryDTO.setSaleFollowStatus(Arrays.asList(SaleFollowStatusEnum.VALID.getCode(),SaleFollowStatusEnum.UNVALID.getCode()));
                }
            }
            dealUsersQueryDTO.setIsBindEnterprise(BindEnterpriseEnum.NO.getCode());//发起绑定企业的，就从销售列表中移除
            dealUsersQueryDTO.setKeyword(request.getQuery().getKeyword());
            dealUsersQueryDTO.setUserProvinceCode(request.getQuery().getUserProvinceCode());
            dealUsersQueryDTO.setUserCityCode(request.getQuery().getUserCityCode());
            queryRequest.setQuery(dealUsersQueryDTO);

            //分页参数赋值
            queryRequest.setSize(request.getSize());
            queryRequest.setIndex(request.getIndex());

            return sysUserFeign.getDealUserPage(queryRequest);
//        }else{
//            //否则返回空数据
//            return  Result.ok(new PageResponse());
//        }
    }


    @ApiOperation("单人分配销售")
    @Operation(description = "单人分配销售")
    @PostMapping(value = "/configSale")
    public Result configSale(@RequestBody @Validated ConfigSaleDto dto) {
        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        Result<SysUserRespDTO> userByUserResult = sysUserFeign.getUserByUserId(authUser.getUserIdStr());
        Assert.isTrue(userByUserResult.getSuccess(),"用户当前查询失败");

        SysUserRespDTO sysUserRespDTO=userByUserResult.getData();
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
            dto.setManageId(authUser.getUserIdStr());
            return sysUserFeign.configSale(dto);
        }else{
            return  Result.fail("非区域总无法操作该功能");
        }
    }

    @ApiOperation("批量分配销售")
    @Operation(description = "批量分配销售")
    @PostMapping(value = "/configSaleBatch")
    public Result configSaleBatch(@RequestBody ManagerDealQueryDTO dto) {

        AuthUserDetails authUser = AuthUserHelper.getAuthUser();
        Result<SysUserRespDTO> userByUserResult = sysUserFeign.getUserByUserId(authUser.getUserIdStr());
        Assert.isTrue(userByUserResult.getSuccess(),"用户当前查询失败");

        SysUserRespDTO sysUserRespDTO=userByUserResult.getData();
        if (ObjectUtil.isNotEmpty(sysUserRespDTO.getRoleIds()) && sysUserRespDTO.getRoleIds().contains(UserConstant.SALES_AREA_MANAGER_ROLE_ID)) {
            // 销售区域总能查看到自己所在战区的所有客户
            PageRequest<DealUsersQueryDTO>  queryRequest=new PageRequest<>();
            DealUsersQueryDTO dealUsersQueryDTO=new DealUsersQueryDTO();
            dealUsersQueryDTO.setOrganizationCode(sysUserRespDTO.getOrganizationCode());
            dealUsersQueryDTO.setIsAssignManager(1);//已分配大区总
            dealUsersQueryDTO.setIsAssignSale(0);//未分配销售

            dealUsersQueryDTO.setIsBindEnterprise(BindEnterpriseEnum.NO.getCode());//发起绑定企业的，就从大区总列表中移除
            dealUsersQueryDTO.setKeyword(dto.getKeyword());
            dealUsersQueryDTO.setUserProvinceCode(dto.getUserProvinceCode());
            dealUsersQueryDTO.setUserCityCode(dto.getUserCityCode());
            queryRequest.setQuery(dealUsersQueryDTO);

            //分页参数赋值
            queryRequest.setSize(Integer.MAX_VALUE);
            queryRequest.setIndex(1);
            Result<PageResponse<DealUserDto>> result= sysUserFeign.getDealUserPage(queryRequest);

            if(result.getSuccess()){
                if(CollUtil.isNotEmpty(result.getData().getRecords())){
                    ConfigSaleDto configSaleDto = new ConfigSaleDto();
                    configSaleDto.setSaleId(dto.getSaleId());
                    configSaleDto.setManageId(authUser.getUserIdStr());
                    configSaleDto.setUserId(result.getData().getRecords().stream().map(DealUserDto::getUserId).toList());
                    return sysUserFeign.configSale(configSaleDto);
                }else{
                    return Result.fail("没有符合条件的数据");
                }
            }else{
                return Result.fail("查询待分配用户信息失败");
            }
        }else{
            return  Result.fail("非区域总无法操作该功能");
        }
    }

    @ApiOperation("大区总获取销售列表")
    @Operation(description = "大区总获取销售列表")
    @PostMapping(value = "/getSaleList")
    public Result<List<SalesUserSelectRespDTO>> getSaleList() {
        return sysUserFeign.listMainSalesUserSelect(AuthUserHelper.getAuthUser().getUserIdStr(), "null");
    }

    @ApiOperation("获取用户详情")
    @Operation(description = "获取用户详情")
    @GetMapping(value = "/getUserDetail")
    public Result<UserDetailRespDTO> getUserDetail(@RequestParam String userId) {
        return sysUserFeign.pcUserDetail(userId);
    }

    @ApiOperation("区域总/销售编辑用户")
    @Operation(description = "区域总/销售编辑用户")
    @PostMapping(value = "/updateUserInfo")
    public Result<Boolean> updateUserInfo(@Valid @RequestBody UpdateUserDto dto) {
       return sysUserFeign.updateUserInfo(dto);
    }
}
