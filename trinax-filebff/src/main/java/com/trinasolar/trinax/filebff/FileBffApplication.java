package com.trinasolar.trinax.filebff;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableFeignClients(basePackages = "com.trinasolar")
@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.trinasolar.trinax")
public class FileBffApplication {

	public static void main(String[] args) {
		SpringApplication.run(FileBffApplication.class, args);
	}

}
