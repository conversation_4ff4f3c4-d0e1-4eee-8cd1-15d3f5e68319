package com.trinasolar.trinax.filebff.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class Knife4jConfiguration {

    @Bean
    public OpenAPI api() {
        return new OpenAPI().info(
                new Info().title("filebff-API文档")
                .description("filebff API")
                .version("1.0"));
    }

}