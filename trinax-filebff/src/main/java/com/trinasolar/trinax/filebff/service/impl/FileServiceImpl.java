package com.trinasolar.trinax.filebff.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.trinasolar.trinax.basic.api.UploadFileFeign;
import com.trinasolar.trinax.basic.dto.output.GetUploadFileResDTO;
import com.trinasolar.trinax.common.dto.output.Result;
import com.trinasolar.trinax.filebff.service.FileService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

@Service
@AllArgsConstructor
public class FileServiceImpl implements FileService {

    private static final GetUploadFileResDTO NULL_OPTIONAL = new GetUploadFileResDTO();

    private static final Cache<String, GetUploadFileResDTO> UPLOADFILE_CACHE = Caffeine.newBuilder()
                // 设置最后一次写入或访问后经过固定时间过期
                .expireAfterWrite(24, TimeUnit.HOURS)
                // 初始的缓存空间大小
                .initialCapacity(500)
                // 缓存的最大条数
                .maximumSize(2000)
                .build();

    private final UploadFileFeign uploadFileFeign;
    private final ConcurrentMap<String, ReentrantLock> lockMap = new ConcurrentHashMap<>();
    @Override
    public GetUploadFileResDTO getPubByFileIdWithCache(String fileId) {
        if (StrUtil.isEmpty(fileId)) {
            return null;
        }

        GetUploadFileResDTO optional = UPLOADFILE_CACHE.getIfPresent(fileId);
        if (optional != null) {
            return convertToReturn(optional);
        }

        ReentrantLock lock = lockMap.computeIfAbsent(fileId, k -> new ReentrantLock());
        lock.lock();
        try {
            optional = UPLOADFILE_CACHE.getIfPresent(fileId);
            if (optional != null) {
                return convertToReturn(optional);
            }

            Result<GetUploadFileResDTO> result = uploadFileFeign.getPubByFileId(fileId);
            if (Boolean.FALSE.equals(result.getSuccess())) {
                return null;
            }

            optional = result.getData();
            if (optional == null) {
                optional = NULL_OPTIONAL;
            }
            UPLOADFILE_CACHE.put(fileId, optional);
        } finally {
            lock.unlock();
        }
        return convertToReturn(optional);
    }

    private GetUploadFileResDTO convertToReturn(GetUploadFileResDTO optional) {
        if (optional == NULL_OPTIONAL) {
            return null;
        }
        return optional;
    }

}
